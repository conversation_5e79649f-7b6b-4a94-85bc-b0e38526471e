<template>
  <div class="message-detail">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>消息详情</span>
          <el-button type="primary" @click="goBack">返回</el-button>
        </div>
      </template>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="消息标题">
          {{ messageInfo.title }}
        </el-descriptions-item>
        <el-descriptions-item label="消息类型">
          <el-tag :type="getMessageTypeTag(messageInfo.messageType)">
            {{ getMessageTypeText(messageInfo.messageType) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="发送人">
          {{ messageInfo.senderName }}
        </el-descriptions-item>
        <el-descriptions-item label="接收人">
          {{ messageInfo.receiverName }}
        </el-descriptions-item>
        <el-descriptions-item label="发送时间">
          {{ messageInfo.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="阅读状态">
          <el-tag :type="messageInfo.isRead === '1' ? 'success' : 'warning'">
            {{ messageInfo.isRead === '1' ? '已读' : '未读' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
      
      <el-divider content-position="left">消息内容</el-divider>
      
      <div class="message-content">
        {{ messageInfo.content }}
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()

const messageInfo = ref({
  messageId: '',
  title: '',
  content: '',
  messageType: '',
  senderName: '',
  receiverName: '',
  isRead: '0',
  createTime: ''
})

const getMessageTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    '1': '系统消息',
    '2': '通知消息',
    '3': '私信消息',
    '4': '提醒消息'
  }
  return typeMap[type] || '未知'
}

const getMessageTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    '1': 'info',
    '2': 'success',
    '3': 'primary',
    '4': 'warning'
  }
  return tagMap[type] || 'info'
}

const goBack = () => {
  router.back()
}

onMounted(() => {
  const messageId = route.params.id
  if (messageId) {
    // TODO: 调用API获取消息详情
    console.log('获取消息详情:', messageId)
  }
})
</script>

<style scoped>
.message-detail {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message-content {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
  line-height: 1.6;
  min-height: 200px;
}
</style>
