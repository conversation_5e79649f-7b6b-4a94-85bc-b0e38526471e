package com.research.common.constant;

/**
 * 操作日志业务类型常量
 *
 * <AUTHOR>
 */
public class BusinessType {

    /** 其他 */
    public static final int OTHER = 0;

    /** 新增 */
    public static final int INSERT = 1;

    /** 修改 */
    public static final int UPDATE = 2;

    /** 删除 */
    public static final int DELETE = 3;

    /** 授权 */
    public static final int GRANT = 4;

    /** 导出 */
    public static final int EXPORT = 5;

    /** 导入 */
    public static final int IMPORT = 6;

    /** 强退 */
    public static final int FORCE = 7;

    /** 生成代码 */
    public static final int GENCODE = 8;

    /** 清空数据 */
    public static final int CLEAN = 9;
}