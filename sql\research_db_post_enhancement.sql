-- 岗位管理功能增强SQL脚本
-- 兼容MySQL 8.0+

USE research_db;

-- 为岗位表添加扩展字段
ALTER TABLE sys_post ADD COLUMN post_category VARCHAR(50) COMMENT '岗位类别（management=管理岗,technical=技术岗,teaching=教学岗,research=科研岗,service=服务岗,other=其他）' AFTER post_name;
ALTER TABLE sys_post ADD COLUMN unit_name VARCHAR(100) COMMENT '所在单位' AFTER post_category;
ALTER TABLE sys_post ADD COLUMN dept_id BIGINT COMMENT '所在部门ID' AFTER unit_name;
ALTER TABLE sys_post ADD COLUMN person_category VARCHAR(50) COMMENT '人员类别（teacher=教师,student=学生,staff=职工,expert=专家,other=其他）' AFTER dept_id;
ALTER TABLE sys_post ADD COLUMN post_number VARCHAR(50) COMMENT '岗位号' AFTER person_category;
ALTER TABLE sys_post ADD COLUMN home_template VARCHAR(100) COMMENT '首页模板' AFTER post_number;
ALTER TABLE sys_post ADD COLUMN menu_template VARCHAR(100) COMMENT '菜单模板' AFTER home_template;
ALTER TABLE sys_post ADD COLUMN shortcut_template VARCHAR(100) COMMENT '快捷方式模板' AFTER menu_template;
ALTER TABLE sys_post ADD COLUMN chart_template VARCHAR(100) COMMENT '统计图模板' AFTER shortcut_template;
ALTER TABLE sys_post ADD COLUMN post_level INT DEFAULT 1 COMMENT '岗位级别' AFTER chart_template;
ALTER TABLE sys_post ADD COLUMN salary_range VARCHAR(50) COMMENT '薪资范围' AFTER post_level;
ALTER TABLE sys_post ADD COLUMN requirements TEXT COMMENT '岗位要求' AFTER salary_range;
ALTER TABLE sys_post ADD COLUMN responsibilities TEXT COMMENT '岗位职责' AFTER requirements;
ALTER TABLE sys_post ADD COLUMN staff_count INT DEFAULT 0 COMMENT '在岗人数' AFTER responsibilities;
ALTER TABLE sys_post ADD COLUMN max_count INT DEFAULT 0 COMMENT '最大人数' AFTER staff_count;

-- 创建岗位类别字典表
CREATE TABLE sys_post_category (
    category_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '类别ID',
    category_code VARCHAR(50) NOT NULL UNIQUE COMMENT '类别编码',
    category_name VARCHAR(100) NOT NULL COMMENT '类别名称',
    category_desc VARCHAR(200) COMMENT '类别描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='岗位类别字典表';

-- 插入岗位类别数据
INSERT INTO sys_post_category (category_code, category_name, category_desc, sort_order) VALUES
('management', '管理岗', '管理类岗位', 1),
('technical', '技术岗', '技术类岗位', 2),
('teaching', '教学岗', '教学类岗位', 3),
('research', '科研岗', '科研类岗位', 4),
('service', '服务岗', '服务类岗位', 5),
('other', '其他', '其他类型岗位', 6);

-- 创建人员类别字典表
CREATE TABLE sys_person_category (
    category_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '类别ID',
    category_code VARCHAR(50) NOT NULL UNIQUE COMMENT '类别编码',
    category_name VARCHAR(100) NOT NULL COMMENT '类别名称',
    category_desc VARCHAR(200) COMMENT '类别描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='人员类别字典表';

-- 插入人员类别数据
INSERT INTO sys_person_category (category_code, category_name, category_desc, sort_order) VALUES
('teacher', '教师', '教师人员', 1),
('student', '学生', '学生人员', 2),
('staff', '职工', '职工人员', 3),
('expert', '专家', '专家人员', 4),
('other', '其他', '其他人员', 5);

-- 创建模板配置表
CREATE TABLE sys_template_config (
    template_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '模板ID',
    template_type VARCHAR(50) NOT NULL COMMENT '模板类型（home=首页,menu=菜单,shortcut=快捷方式,chart=统计图）',
    template_code VARCHAR(100) NOT NULL COMMENT '模板编码',
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    template_desc VARCHAR(200) COMMENT '模板描述',
    template_config TEXT COMMENT '模板配置JSON',
    template_preview VARCHAR(200) COMMENT '模板预览图',
    is_default CHAR(1) DEFAULT '0' COMMENT '是否默认模板',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='模板配置表';

-- 插入模板配置数据
INSERT INTO sys_template_config (template_type, template_code, template_name, template_desc, is_default, sort_order) VALUES
-- 首页模板
('home', 'home_default', '默认首页', '系统默认首页模板', '1', 1),
('home', 'home_admin', '管理员首页', '管理员专用首页模板', '0', 2),
('home', 'home_teacher', '教师首页', '教师专用首页模板', '0', 3),
('home', 'home_student', '学生首页', '学生专用首页模板', '0', 4),
('home', 'home_researcher', '科研人员首页', '科研人员专用首页模板', '0', 5),

-- 菜单模板
('menu', 'menu_full', '完整菜单', '包含所有功能的菜单模板', '1', 1),
('menu', 'menu_admin', '管理员菜单', '管理员专用菜单模板', '0', 2),
('menu', 'menu_teacher', '教师菜单', '教师专用菜单模板', '0', 3),
('menu', 'menu_student', '学生菜单', '学生专用菜单模板', '0', 4),
('menu', 'menu_simple', '简化菜单', '简化版菜单模板', '0', 5),

-- 快捷方式模板
('shortcut', 'shortcut_default', '默认快捷方式', '系统默认快捷方式模板', '1', 1),
('shortcut', 'shortcut_admin', '管理员快捷方式', '管理员专用快捷方式', '0', 2),
('shortcut', 'shortcut_teacher', '教师快捷方式', '教师专用快捷方式', '0', 3),
('shortcut', 'shortcut_student', '学生快捷方式', '学生专用快捷方式', '0', 4),
('shortcut', 'shortcut_researcher', '科研快捷方式', '科研人员专用快捷方式', '0', 5),

-- 统计图模板
('chart', 'chart_dashboard', '仪表盘图表', '综合仪表盘统计图模板', '1', 1),
('chart', 'chart_admin', '管理统计图', '管理员专用统计图模板', '0', 2),
('chart', 'chart_research', '科研统计图', '科研数据统计图模板', '0', 3),
('chart', 'chart_teaching', '教学统计图', '教学数据统计图模板', '0', 4),
('chart', 'chart_simple', '简单统计图', '简化版统计图模板', '0', 5);

-- 创建岗位扩展视图
CREATE VIEW v_post_detail AS
SELECT 
    p.post_id,
    p.post_code,
    p.post_name,
    p.post_category,
    pc.category_name as post_category_name,
    p.unit_name,
    p.dept_id,
    d.dept_name,
    p.person_category,
    prc.category_name as person_category_name,
    p.post_number,
    p.home_template,
    ht.template_name as home_template_name,
    p.menu_template,
    mt.template_name as menu_template_name,
    p.shortcut_template,
    st.template_name as shortcut_template_name,
    p.chart_template,
    ct.template_name as chart_template_name,
    p.post_level,
    p.salary_range,
    p.requirements,
    p.responsibilities,
    p.staff_count,
    p.max_count,
    p.post_sort,
    p.status,
    p.create_time,
    p.update_time,
    p.remark
FROM sys_post p
LEFT JOIN sys_post_category pc ON p.post_category = pc.category_code
LEFT JOIN sys_dept d ON p.dept_id = d.dept_id
LEFT JOIN sys_person_category prc ON p.person_category = prc.category_code
LEFT JOIN sys_template_config ht ON p.home_template = ht.template_code AND ht.template_type = 'home'
LEFT JOIN sys_template_config mt ON p.menu_template = mt.template_code AND mt.template_type = 'menu'
LEFT JOIN sys_template_config st ON p.shortcut_template = st.template_code AND st.template_type = 'shortcut'
LEFT JOIN sys_template_config ct ON p.chart_template = ct.template_code AND ct.template_type = 'chart';

-- 创建岗位统计视图
CREATE VIEW v_post_statistics AS
SELECT 
    post_category,
    pc.category_name,
    COUNT(*) as post_count,
    SUM(staff_count) as total_staff,
    SUM(max_count) as total_capacity,
    AVG(staff_count) as avg_staff_per_post
FROM sys_post p
LEFT JOIN sys_post_category pc ON p.post_category = pc.category_code
WHERE p.status = '0'
GROUP BY post_category, pc.category_name
ORDER BY post_count DESC;

-- 更新现有岗位数据的扩展字段
UPDATE sys_post SET 
    post_category = CASE 
        WHEN post_name LIKE '%主任%' OR post_name LIKE '%经理%' OR post_name LIKE '%总监%' THEN 'management'
        WHEN post_name LIKE '%工程师%' OR post_name LIKE '%开发%' THEN 'technical'
        WHEN post_name LIKE '%教师%' OR post_name LIKE '%教授%' THEN 'teaching'
        WHEN post_name LIKE '%研究%' OR post_name LIKE '%科研%' THEN 'research'
        ELSE 'other'
    END,
    unit_name = '龙湖大学',
    person_category = CASE 
        WHEN post_name LIKE '%教师%' OR post_name LIKE '%教授%' THEN 'teacher'
        WHEN post_name LIKE '%学生%' THEN 'student'
        ELSE 'staff'
    END,
    post_number = CONCAT('POST', LPAD(post_id, 4, '0')),
    home_template = 'home_default',
    menu_template = 'menu_full',
    shortcut_template = 'shortcut_default',
    chart_template = 'chart_dashboard',
    post_level = CASE 
        WHEN post_name LIKE '%主任%' OR post_name LIKE '%经理%' THEN 3
        WHEN post_name LIKE '%副%' THEN 2
        ELSE 1
    END,
    staff_count = FLOOR(RAND() * 10) + 1,
    max_count = FLOOR(RAND() * 20) + 10
WHERE post_id > 0;

-- 插入示例岗位数据
INSERT INTO sys_post (post_code, post_name, post_category, unit_name, dept_id, person_category, post_number, home_template, menu_template, shortcut_template, chart_template, post_level, salary_range, requirements, responsibilities, staff_count, max_count, post_sort, status, create_by, remark) VALUES
-- 管理岗位
('ADMIN001', '系统管理员', 'management', '龙湖大学', 100, 'staff', 'POST1001', 'home_admin', 'menu_admin', 'shortcut_admin', 'chart_admin', 3, '8000-12000', '计算机相关专业，3年以上工作经验', '负责系统运维和管理工作', 2, 3, 1, '0', 'admin', '系统管理岗位'),
('ADMIN002', '科研处处长', 'management', '龙湖大学', 100, 'staff', 'POST1002', 'home_admin', 'menu_admin', 'shortcut_admin', 'chart_admin', 4, '12000-18000', '管理学或相关专业，5年以上管理经验', '负责科研工作的统筹管理', 1, 1, 2, '0', 'admin', '科研管理岗位'),

-- 教学岗位
('TEACH001', '计算机学院教授', 'teaching', '龙湖大学', 2, 'teacher', 'POST2001', 'home_teacher', 'menu_teacher', 'shortcut_teacher', 'chart_teaching', 4, '10000-20000', '博士学位，计算机相关专业', '承担本科生和研究生教学工作', 15, 20, 3, '0', 'admin', '教学岗位'),
('TEACH002', '数学学院副教授', 'teaching', '龙湖大学', 3, 'teacher', 'POST2002', 'home_teacher', 'menu_teacher', 'shortcut_teacher', 'chart_teaching', 3, '8000-15000', '博士学位，数学相关专业', '承担本科生教学和科研工作', 12, 15, 4, '0', 'admin', '教学岗位'),

-- 科研岗位
('RESEARCH001', '人工智能研究员', 'research', '龙湖大学', 2, 'staff', 'POST3001', 'home_researcher', 'menu_full', 'shortcut_researcher', 'chart_research', 3, '10000-18000', '博士学位，人工智能相关专业', '从事人工智能前沿技术研究', 8, 12, 5, '0', 'admin', '科研岗位'),
('RESEARCH002', '软件工程研究员', 'research', '龙湖大学', 2, 'staff', 'POST3002', 'home_researcher', 'menu_full', 'shortcut_researcher', 'chart_research', 2, '8000-15000', '硕士以上学位，软件工程专业', '从事软件工程技术研究', 6, 10, 6, '0', 'admin', '科研岗位'),

-- 技术岗位
('TECH001', '系统开发工程师', 'technical', '龙湖大学', 2, 'staff', 'POST4001', 'home_default', 'menu_full', 'shortcut_default', 'chart_dashboard', 2, '6000-12000', '本科以上学位，计算机相关专业', '负责系统开发和维护工作', 5, 8, 7, '0', 'admin', '技术岗位'),
('TECH002', '网络管理工程师', 'technical', '龙湖大学', 2, 'staff', 'POST4002', 'home_default', 'menu_full', 'shortcut_default', 'chart_dashboard', 2, '5000-10000', '本科以上学位，网络工程专业', '负责网络设备管理和维护', 3, 5, 8, '0', 'admin', '技术岗位'),

-- 服务岗位
('SERVICE001', '实验室管理员', 'service', '龙湖大学', 2, 'staff', 'POST5001', 'home_default', 'menu_simple', 'shortcut_default', 'chart_simple', 1, '4000-6000', '本科以上学位，相关专业', '负责实验室日常管理工作', 4, 6, 9, '0', 'admin', '服务岗位'),
('SERVICE002', '图书管理员', 'service', '龙湖大学', 1, 'staff', 'POST5002', 'home_default', 'menu_simple', 'shortcut_default', 'chart_simple', 1, '3500-5500', '本科以上学位，图书馆学专业优先', '负责图书资料管理工作', 6, 8, 10, '0', 'admin', '服务岗位');

-- 创建索引优化查询性能
CREATE INDEX idx_post_category ON sys_post(post_category);
CREATE INDEX idx_post_dept_id ON sys_post(dept_id);
CREATE INDEX idx_post_person_category ON sys_post(person_category);
CREATE INDEX idx_post_status ON sys_post(status);
CREATE INDEX idx_post_number ON sys_post(post_number);

-- 查询验证数据
SELECT 
    post_id,
    post_code,
    post_name,
    post_category_name,
    dept_name,
    person_category_name,
    post_number,
    staff_count,
    max_count,
    status
FROM v_post_detail
ORDER BY post_sort;

-- 查询岗位统计
SELECT * FROM v_post_statistics;
