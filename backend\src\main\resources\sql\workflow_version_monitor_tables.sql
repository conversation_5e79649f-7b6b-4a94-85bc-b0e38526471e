-- =============================================
-- 工作流版本管理和监控相关表结构
-- =============================================

-- 1. 工作流版本表
CREATE TABLE IF NOT EXISTS workflow_version (
    version_id VARCHAR(64) NOT NULL COMMENT '版本ID',
    process_definition_key VARCHAR(255) NOT NULL COMMENT '流程定义Key',
    process_definition_id VARCHAR(255) COMMENT '流程定义ID',
    version_tag VARCHAR(100) NOT NULL COMMENT '版本标签',
    version_name VARCHAR(200) NOT NULL COMMENT '版本名称',
    version_description TEXT COMMENT '版本描述',
    publish_status TINYINT DEFAULT 0 COMMENT '发布状态（0草稿 1已发布 2已停用 3已归档）',
    publish_strategy TINYINT DEFAULT 0 COMMENT '发布策略（0全量发布 1灰度发布 2A/B测试）',
    traffic_ratio INT DEFAULT 0 COMMENT '流量比例（灰度发布时使用）',
    target_users TEXT COMMENT '目标用户（A/B测试时使用）',
    target_departments TEXT COMMENT '目标部门（部门路由时使用）',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认版本',
    running_instance_count INT DEFAULT 0 COMMENT '运行中实例数',
    completed_instance_count INT DEFAULT 0 COMMENT '已完成实例数',
    publish_time DATETIME COMMENT '发布时间',
    deprecate_time DATETIME COMMENT '停用时间',
    version_config JSON COMMENT '版本配置JSON',
    route_rules JSON COMMENT '路由规则JSON',
    version INT DEFAULT 1 COMMENT '版本号',
    parent_version_id VARCHAR(64) COMMENT '父版本ID',
    branch_type TINYINT DEFAULT 0 COMMENT '分支类型（0主分支 1特性分支 2修复分支）',
    merge_status TINYINT DEFAULT 0 COMMENT '合并状态（0未合并 1已合并 2冲突）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (version_id),
    INDEX idx_process_key (process_definition_key),
    INDEX idx_publish_status (publish_status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流版本表';

-- 2. 版本路由规则表
CREATE TABLE IF NOT EXISTS workflow_version_route (
    route_id VARCHAR(64) NOT NULL COMMENT '路由ID',
    process_definition_key VARCHAR(255) NOT NULL COMMENT '流程定义Key',
    version_id VARCHAR(64) NOT NULL COMMENT '版本ID',
    route_type TINYINT NOT NULL COMMENT '路由类型（0全量 1灰度 2A/B测试 3部门路由）',
    route_condition JSON COMMENT '路由条件',
    priority INT DEFAULT 0 COMMENT '优先级',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    effective_time DATETIME COMMENT '生效时间',
    expire_time DATETIME COMMENT '过期时间',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (route_id),
    INDEX idx_process_key (process_definition_key),
    INDEX idx_version_id (version_id),
    INDEX idx_route_type (route_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='版本路由规则表';

-- 3. 版本使用统计表
CREATE TABLE IF NOT EXISTS workflow_version_stats (
    stat_id VARCHAR(64) NOT NULL COMMENT '统计ID',
    version_id VARCHAR(64) NOT NULL COMMENT '版本ID',
    process_definition_key VARCHAR(255) NOT NULL COMMENT '流程定义Key',
    stat_date DATE NOT NULL COMMENT '统计日期',
    started_instances INT DEFAULT 0 COMMENT '启动实例数',
    completed_instances INT DEFAULT 0 COMMENT '完成实例数',
    failed_instances INT DEFAULT 0 COMMENT '失败实例数',
    avg_duration BIGINT DEFAULT 0 COMMENT '平均执行时长（毫秒）',
    success_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '成功率',
    user_count INT DEFAULT 0 COMMENT '使用用户数',
    department_count INT DEFAULT 0 COMMENT '使用部门数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (stat_id),
    UNIQUE KEY uk_version_date (version_id, stat_date),
    INDEX idx_process_key (process_definition_key),
    INDEX idx_stat_date (stat_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='版本使用统计表';

-- 4. 工作流监控指标表
CREATE TABLE IF NOT EXISTS workflow_monitor_metrics (
    metric_id VARCHAR(64) NOT NULL COMMENT '指标ID',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    metric_type VARCHAR(50) NOT NULL COMMENT '指标类型',
    metric_value DECIMAL(15,4) NOT NULL COMMENT '指标值',
    metric_unit VARCHAR(20) COMMENT '指标单位',
    process_definition_key VARCHAR(255) COMMENT '流程定义Key',
    version_id VARCHAR(64) COMMENT '版本ID',
    collect_time DATETIME NOT NULL COMMENT '采集时间',
    tags JSON COMMENT '标签信息',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (metric_id),
    INDEX idx_metric_name (metric_name),
    INDEX idx_collect_time (collect_time),
    INDEX idx_process_key (process_definition_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流监控指标表';

-- 5. 工作流告警规则表
CREATE TABLE IF NOT EXISTS workflow_alert_rules (
    rule_id VARCHAR(64) NOT NULL COMMENT '规则ID',
    rule_name VARCHAR(200) NOT NULL COMMENT '规则名称',
    rule_type VARCHAR(50) NOT NULL COMMENT '规则类型',
    metric_name VARCHAR(100) NOT NULL COMMENT '监控指标',
    threshold_value DECIMAL(15,4) NOT NULL COMMENT '阈值',
    comparison_operator VARCHAR(10) NOT NULL COMMENT '比较操作符（>, <, =, >=, <=）',
    alert_level VARCHAR(20) DEFAULT 'warning' COMMENT '告警级别（info, warning, error, critical）',
    alert_message TEXT COMMENT '告警消息模板',
    is_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    notification_channels JSON COMMENT '通知渠道配置',
    process_definition_key VARCHAR(255) COMMENT '流程定义Key',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (rule_id),
    INDEX idx_rule_type (rule_type),
    INDEX idx_metric_name (metric_name),
    INDEX idx_process_key (process_definition_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流告警规则表';

-- 6. 工作流告警记录表
CREATE TABLE IF NOT EXISTS workflow_alert_records (
    alert_id VARCHAR(64) NOT NULL COMMENT '告警ID',
    rule_id VARCHAR(64) NOT NULL COMMENT '规则ID',
    alert_level VARCHAR(20) NOT NULL COMMENT '告警级别',
    alert_title VARCHAR(500) NOT NULL COMMENT '告警标题',
    alert_message TEXT COMMENT '告警消息',
    alert_time DATETIME NOT NULL COMMENT '告警时间',
    metric_value DECIMAL(15,4) COMMENT '触发时的指标值',
    process_definition_key VARCHAR(255) COMMENT '流程定义Key',
    version_id VARCHAR(64) COMMENT '版本ID',
    status TINYINT DEFAULT 0 COMMENT '处理状态（0未处理 1已处理 2已忽略）',
    handle_by VARCHAR(64) COMMENT '处理人',
    handle_time DATETIME COMMENT '处理时间',
    handle_remark TEXT COMMENT '处理备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (alert_id),
    INDEX idx_rule_id (rule_id),
    INDEX idx_alert_time (alert_time),
    INDEX idx_status (status),
    INDEX idx_process_key (process_definition_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流告警记录表';

-- 7. 工作流性能分析表
CREATE TABLE IF NOT EXISTS workflow_performance_analysis (
    analysis_id VARCHAR(64) NOT NULL COMMENT '分析ID',
    process_definition_key VARCHAR(255) NOT NULL COMMENT '流程定义Key',
    version_id VARCHAR(64) COMMENT '版本ID',
    analysis_date DATE NOT NULL COMMENT '分析日期',
    total_instances INT DEFAULT 0 COMMENT '总实例数',
    avg_duration BIGINT DEFAULT 0 COMMENT '平均执行时长（毫秒）',
    min_duration BIGINT DEFAULT 0 COMMENT '最短执行时长（毫秒）',
    max_duration BIGINT DEFAULT 0 COMMENT '最长执行时长（毫秒）',
    bottleneck_activities JSON COMMENT '瓶颈活动分析',
    performance_score DECIMAL(5,2) DEFAULT 0.00 COMMENT '性能评分',
    optimization_suggestions JSON COMMENT '优化建议',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (analysis_id),
    UNIQUE KEY uk_process_date (process_definition_key, analysis_date),
    INDEX idx_analysis_date (analysis_date),
    INDEX idx_version_id (version_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流性能分析表';

-- 8. 版本变更历史表
CREATE TABLE IF NOT EXISTS workflow_version_history (
    history_id VARCHAR(64) NOT NULL COMMENT '历史ID',
    version_id VARCHAR(64) NOT NULL COMMENT '版本ID',
    change_type VARCHAR(50) NOT NULL COMMENT '变更类型',
    change_description TEXT COMMENT '变更描述',
    change_content JSON COMMENT '变更内容',
    change_by VARCHAR(64) NOT NULL COMMENT '变更人',
    change_time DATETIME NOT NULL COMMENT '变更时间',
    before_value JSON COMMENT '变更前值',
    after_value JSON COMMENT '变更后值',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (history_id),
    INDEX idx_version_id (version_id),
    INDEX idx_change_type (change_type),
    INDEX idx_change_time (change_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='版本变更历史表';

-- 插入初始数据
INSERT INTO workflow_version (version_id, process_definition_key, version_tag, version_name, version_description, publish_status, is_default, create_by) VALUES
('v1', 'research_project_approval', 'v1.0', '科研项目申请流程v1.0', '初始版本', 1, TRUE, 'admin'),
('v2', 'expense_approval', 'v1.0', '经费审批流程v1.0', '初始版本', 1, TRUE, 'admin'),
('v3', 'equipment_purchase', 'v1.0', '设备采购流程v1.0', '初始版本', 1, TRUE, 'admin');

-- 插入监控告警规则
INSERT INTO workflow_alert_rules (rule_id, rule_name, rule_type, metric_name, threshold_value, comparison_operator, alert_level, alert_message, create_by) VALUES
('rule1', '流程执行超时告警', 'duration', 'avg_duration', 3600000, '>', 'warning', '流程平均执行时间超过1小时', 'admin'),
('rule2', '流程失败率告警', 'failure_rate', 'failure_rate', 0.1, '>', 'error', '流程失败率超过10%', 'admin'),
('rule3', '任务积压告警', 'task_backlog', 'pending_tasks', 100, '>', 'warning', '待处理任务数量超过100个', 'admin');

COMMIT;
