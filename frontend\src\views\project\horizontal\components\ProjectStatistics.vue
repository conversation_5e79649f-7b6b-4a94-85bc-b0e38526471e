<template>
  <el-dialog
    title="横向项目统计分析"
    v-model="visible"
    width="1400px"
    append-to-body
    @close="handleClose"
  >
    <div v-loading="loading">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="mb-4">
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.totalCount || 0 }}</div>
              <div class="stat-label">项目总数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ formatMoney(statistics.totalFund) }}</div>
              <div class="stat-label">总经费(万元)</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ formatMoney(statistics.receivedFund) }}</div>
              <div class="stat-label">已到账经费(万元)</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.expiringCount || 0 }}</div>
              <div class="stat-label">即将到期项目</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 图表区域 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>项目状态分布</span>
            </template>
            <div ref="statusChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>项目类型分布</span>
            </template>
            <div ref="typeChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="mt-4">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>部门项目统计</span>
            </template>
            <div ref="deptChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>合作单位统计</span>
            </template>
            <div ref="partnerChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="mt-4">
        <el-col :span="24">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>年度项目趋势</span>
                <el-select v-model="selectedYear" @change="getYearlyData" style="width: 120px;">
                  <el-option
                    v-for="year in yearOptions"
                    :key="year"
                    :label="year + '年'"
                    :value="year"
                  />
                </el-select>
              </div>
            </template>
            <div ref="yearlyChartRef" style="height: 400px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 详细统计表格 -->
      <el-row :gutter="20" class="mt-4">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>项目状态详情</span>
            </template>
            <el-table :data="statusData" style="width: 100%">
              <el-table-column prop="statusName" label="状态" />
              <el-table-column prop="projectCount" label="项目数量" />
              <el-table-column label="占比">
                <template #default="scope">
                  {{ ((scope.row.projectCount / statistics.totalCount) * 100).toFixed(1) }}%
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>项目类型详情</span>
            </template>
            <el-table :data="typeData" style="width: 100%">
              <el-table-column prop="projectType" label="类型" />
              <el-table-column prop="projectCount" label="项目数量" />
              <el-table-column prop="totalFund" label="总经费">
                <template #default="scope">
                  {{ formatMoney(scope.row.totalFund) }}
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="exportStatistics">导出统计</el-button>
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { 
  getProjectStatistics,
  getStatusStatistics,
  getTypeStatistics,
  getDeptStatistics,
  getPartnerStatistics,
  getYearlyStatistics
} from "@/api/project/horizontal"
import * as echarts from 'echarts'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const loading = ref(false)
const statistics = ref({})
const statusData = ref([])
const typeData = ref([])
const deptData = ref([])
const partnerData = ref([])
const yearlyData = ref([])
const selectedYear = ref(new Date().getFullYear())

// 图表引用
const statusChartRef = ref()
const typeChartRef = ref()
const deptChartRef = ref()
const partnerChartRef = ref()
const yearlyChartRef = ref()

// 年份选项
const yearOptions = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let i = currentYear; i >= currentYear - 5; i--) {
    years.push(i)
  }
  return years
})

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal) {
    loadStatistics()
  }
})

/** 加载统计数据 */
async function loadStatistics() {
  loading.value = true
  try {
    // 并行加载所有统计数据
    const [
      statisticsRes,
      statusRes,
      typeRes,
      deptRes,
      partnerRes,
      yearlyRes
    ] = await Promise.all([
      getProjectStatistics(),
      getStatusStatistics(),
      getTypeStatistics(),
      getDeptStatistics(),
      getPartnerStatistics(),
      getYearlyStatistics(selectedYear.value)
    ])

    statistics.value = statisticsRes.data
    statusData.value = statusRes.data
    typeData.value = typeRes.data
    deptData.value = deptRes.data
    partnerData.value = partnerRes.data
    yearlyData.value = yearlyRes.data

    // 渲染图表
    nextTick(() => {
      renderStatusChart()
      renderTypeChart()
      renderDeptChart()
      renderPartnerChart()
      renderYearlyChart()
    })
  } catch (error) {
    console.error('加载统计数据失败:', error)
  } finally {
    loading.value = false
  }
}

/** 获取年度数据 */
async function getYearlyData() {
  try {
    const response = await getYearlyStatistics(selectedYear.value)
    yearlyData.value = response.data
    renderYearlyChart()
  } catch (error) {
    console.error('获取年度数据失败:', error)
  }
}

/** 渲染状态分布图表 */
function renderStatusChart() {
  if (!statusChartRef.value || !statusData.value.length) return
  
  const chart = echarts.init(statusChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '项目状态',
        type: 'pie',
        radius: '50%',
        data: statusData.value.map(item => ({
          value: item.projectCount,
          name: item.statusName
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 渲染类型分布图表 */
function renderTypeChart() {
  if (!typeChartRef.value || !typeData.value.length) return
  
  const chart = echarts.init(typeChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: typeData.value.map(item => item.projectType),
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '项目数量',
        type: 'bar',
        data: typeData.value.map(item => item.projectCount),
        itemStyle: {
          color: '#409eff'
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 渲染部门统计图表 */
function renderDeptChart() {
  if (!deptChartRef.value || !deptData.value.length) return
  
  const chart = echarts.init(deptChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: deptData.value.slice(0, 10).map(item => item.deptName),
      axisLabel: {
        width: 80,
        overflow: 'truncate'
      }
    },
    series: [
      {
        name: '项目数量',
        type: 'bar',
        data: deptData.value.slice(0, 10).map(item => item.projectCount),
        itemStyle: {
          color: '#67c23a'
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 渲染合作单位统计图表 */
function renderPartnerChart() {
  if (!partnerChartRef.value || !partnerData.value.length) return
  
  const chart = echarts.init(partnerChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: partnerData.value.slice(0, 10).map(item => item.partnerName),
      axisLabel: {
        width: 80,
        overflow: 'truncate'
      }
    },
    series: [
      {
        name: '项目数量',
        type: 'bar',
        data: partnerData.value.slice(0, 10).map(item => item.projectCount),
        itemStyle: {
          color: '#e6a23c'
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 渲染年度趋势图表 */
function renderYearlyChart() {
  if (!yearlyChartRef.value || !yearlyData.value.length) return
  
  const chart = echarts.init(yearlyChartRef.value)
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  
  // 构造12个月的数据
  const monthlyCount = new Array(12).fill(0)
  const monthlyFund = new Array(12).fill(0)
  
  yearlyData.value.forEach(item => {
    const monthIndex = item.month - 1
    monthlyCount[monthIndex] = item.projectCount
    monthlyFund[monthIndex] = item.totalFund
  })
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['项目数量', '项目经费']
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: [
      {
        type: 'value',
        name: '项目数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '项目经费(万元)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '项目数量',
        type: 'line',
        data: monthlyCount,
        itemStyle: {
          color: '#409eff'
        }
      },
      {
        name: '项目经费',
        type: 'bar',
        yAxisIndex: 1,
        data: monthlyFund,
        itemStyle: {
          color: '#67c23a'
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 格式化金额 */
function formatMoney(money) {
  if (!money) return '0.00'
  return (parseFloat(money) / 10000).toFixed(2)
}

/** 导出统计 */
function exportStatistics() {
  // 这里应该调用导出接口
  console.log('导出统计数据')
}

/** 关闭对话框 */
function handleClose() {
  visible.value = false
}
</script>

<style scoped>
.stat-card {
  text-align: center;
}

.stat-item {
  padding: 20px 0;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}
</style>
