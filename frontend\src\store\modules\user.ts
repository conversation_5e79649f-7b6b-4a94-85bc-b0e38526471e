import { defineStore } from 'pinia'
import { ref } from 'vue'
import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { useMenuStore } from './menu'
import router from '@/router'

export interface UserInfo {
  userId?: number
  userName?: string
  nickName?: string
  email?: string
  phonenumber?: string
  sex?: string
  avatar?: string
  deptId?: number
  deptName?: string
  roles?: string[]
  permissions?: string[]
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>(getToken() || '')
  const userInfo = ref<UserInfo>({})
  const roles = ref<string[]>([])
  const permissions = ref<string[]>([])

  // 登录
  const loginAction = async (loginForm: any) => {
    try {
      const response = await login(loginForm)
      const { token: newToken } = response

      if (!newToken) {
        throw new Error('登录响应中没有token')
      }

      token.value = newToken
      setToken(newToken)

      return Promise.resolve()
    } catch (error) {
      return Promise.reject(error)
    }
  }

  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const response = await getInfo()
      const { user, roles: userRoles, permissions: userPermissions, menus } = response

      userInfo.value = user
      roles.value = userRoles
      permissions.value = userPermissions

      // 设置菜单数据
      if (menus) {
        const menuStore = useMenuStore()
        menuStore.setMenus(menus)
      }

      return Promise.resolve(response)
    } catch (error) {
      return Promise.reject(error)
    }
  }

  // 登出
  const logoutAction = async () => {
    try {
      console.log('🔄 开始调用登出接口...')
      await logout()
      console.log('✅ 登出接口调用成功')
    } catch (error) {
      console.error('❌ 登出接口调用失败:', error)
      // 检查错误详情
      if (error.response) {
        console.error('响应状态:', error.response.status)
        console.error('响应数据:', error.response.data)
        console.error('请求方法:', error.config?.method)
        console.error('请求URL:', error.config?.url)
      }
    } finally {
      console.log('🧹 清除本地数据...')
      // 清除本地数据
      token.value = ''
      userInfo.value = {}
      roles.value = []
      permissions.value = []
      removeToken()

      // 清除菜单数据
      const menuStore = useMenuStore()
      menuStore.clearMenus()

      console.log('✅ 登出处理完成')
    }
  }

  // 重置用户信息
  const resetUserInfo = () => {
    token.value = ''
    userInfo.value = {}
    roles.value = []
    permissions.value = []
    removeToken()

    // 清除菜单数据
    const menuStore = useMenuStore()
    menuStore.clearMenus()
  }

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    return permissions.value.includes(permission) || roles.value.includes('admin')
  }

  // 检查角色
  const hasRole = (role: string): boolean => {
    return roles.value.includes(role)
  }

  return {
    // 状态
    token,
    userInfo,
    roles,
    permissions,
    
    // 方法
    login: loginAction,
    logout: logoutAction,
    getUserInfo,
    resetUserInfo,
    hasPermission,
    hasRole
  }
})
