<template>
  <div class="dynamic-content">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-container" v-if="currentPage.title">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>首页</el-breadcrumb-item>
        <el-breadcrumb-item v-if="currentPage.parentTitle">{{ currentPage.parentTitle }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ currentPage.title }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 动态内容区域 -->
    <div class="content-wrapper">
      <component 
        :is="currentComponent" 
        v-if="currentComponent"
        :key="currentPage.key"
      />
      <div v-else class="welcome-page">
        <el-empty description="请选择左侧菜单">
          <el-button type="primary" @click="loadDefaultPage">进入工作台</el-button>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineAsyncComponent } from 'vue'
import { useMenuStore } from '@/store/modules/menu'

// 页面信息接口
interface PageInfo {
  key: string
  title: string
  parentTitle?: string
  component: string
}

// 当前页面信息 - 默认加载个人工作台
const currentPage = ref<PageInfo>({
  key: 'workspace',
  title: '个人工作台',
  component: 'workspace/index'
})

// 菜单store
const menuStore = useMenuStore()

// 动态组件缓存
const componentCache = new Map()

// 使用Vite的glob导入自动发现所有组件
const modules = import.meta.glob('@/views/**/*.vue')
console.log('🔍 自动发现的组件模块:', modules)

// 获取组件
const getComponent = (componentPath: string) => {
  console.log('🔄 获取组件:', componentPath)

  // 构建完整的模块路径
  const fullPath = `/src/views/${componentPath}.vue`
  console.log('🔍 查找模块路径:', fullPath)

  // 查找匹配的模块
  const moduleLoader = modules[fullPath]

  if (moduleLoader) {
    console.log('✅ 找到组件模块:', fullPath)
    return defineAsyncComponent({
      loader: moduleLoader,
      loadingComponent: () => import('@/components/Loading/index.vue'),
      errorComponent: () => import('@/views/error/404.vue'),
      delay: 200,
      timeout: 3000
    })
  } else {
    console.warn('❌ 未找到组件模块:', fullPath)
    console.log('📋 可用的模块路径:', Object.keys(modules))

    // 尝试一些常见的路径变体
    const alternatives = [
      `/src/views/${componentPath}/index.vue`,
      `/src/views/${componentPath.replace('/index', '')}.vue`
    ]

    for (const altPath of alternatives) {
      if (modules[altPath]) {
        console.log('✅ 找到替代路径:', altPath)
        return defineAsyncComponent({
          loader: modules[altPath],
          loadingComponent: () => import('@/components/Loading/index.vue'),
          errorComponent: () => import('@/views/error/404.vue'),
          delay: 200,
          timeout: 3000
        })
      }
    }

    // 如果都找不到，返回404页面
    return defineAsyncComponent(() => import('@/views/error/404.vue'))
  }
}

// 当前组件
const currentComponent = computed(() => {
  if (!currentPage.value.component) return null

  const componentPath = currentPage.value.component

  // 检查缓存
  if (componentCache.has(componentPath)) {
    console.log('📦 从缓存中获取组件:', componentPath)
    return componentCache.get(componentPath)
  }

  // 获取组件
  const component = getComponent(componentPath)

  // 缓存组件
  componentCache.set(componentPath, component)
  return component
})

// 加载页面
const loadPage = (pageInfo: PageInfo) => {
  currentPage.value = {
    ...pageInfo,
    key: `${pageInfo.component}_${Date.now()}` // 确保组件重新渲染
  }
}

// 加载默认页面（个人工作台）
const loadDefaultPage = () => {
  loadPage({
    key: 'workspace',
    title: '个人工作台',
    component: 'workspace/index'
  })
}

// 根据菜单路径加载页面 - 动态查找组件
const loadPageByPath = (path: string, menuName: string, parentName?: string) => {
  console.log('loadPageByPath 调用:', { path, menuName, parentName })

  // 首先尝试从菜单数据中动态查找组件路径
  const component = findComponentByPath(path)

  if (component) {
    console.log('找到组件:', component)
    loadPage({
      key: path.replace(/\//g, '_'),
      title: menuName,
      parentTitle: parentName,
      component
    })
  } else {
    console.warn(`未找到路径 ${path} 对应的组件`)
    // 显示错误页面或默认页面
    loadPage({
      key: 'error',
      title: '页面未找到',
      parentTitle: parentName,
      component: 'error/404'
    })
  }
}

// 动态查找组件路径
const findComponentByPath = (targetPath: string): string | null => {
  console.log('查找路径:', targetPath)
  console.log('当前菜单数据:', menuStore.menus)

  // 递归查找菜单项
  const findInMenus = (menus: any[], parentPath = ''): string | null => {
    for (const menu of menus) {
      // 构建完整路径
      let fullPath = menu.path
      if (parentPath && !fullPath.startsWith('/')) {
        fullPath = `${parentPath}/${fullPath}`
      }
      if (!fullPath.startsWith('/')) {
        fullPath = `/${fullPath}`
      }

      console.log('检查菜单:', { menuName: menu.menuName, fullPath, targetPath, component: menu.component })

      // 如果路径匹配且有组件
      if (fullPath === targetPath && menu.component) {
        console.log('找到匹配的菜单:', menu)
        return menu.component
      }

      // 递归查找子菜单
      if (menu.children && menu.children.length > 0) {
        const result = findInMenus(menu.children, fullPath)
        if (result) return result
      }
    }
    return null
  }

  // 从菜单数据中查找
  const result = findInMenus(menuStore.menus)

  // 如果没找到，尝试一些默认的映射规则
  if (!result) {
    console.log('菜单中未找到，尝试默认映射规则')

    // 默认映射规则：路径直接对应组件路径
    if (targetPath === '/workspace') {
      return 'workspace/index'
    }

    // 移除开头的斜杠，添加 /index 后缀
    const pathWithoutSlash = targetPath.substring(1)
    if (pathWithoutSlash) {
      return `${pathWithoutSlash}/index`
    }
  }

  return result
}

// 暴露方法给父组件
defineExpose({
  loadPage,
  loadPageByPath,
  loadDefaultPage
})
</script>

<style scoped>
.dynamic-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.breadcrumb-container {
  padding: 12px 20px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
}

.content-wrapper {
  flex: 1;
  overflow: auto;
  background: #f0f2f5;
}

.welcome-page {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  margin: 20px;
  border-radius: 4px;
}
</style>
