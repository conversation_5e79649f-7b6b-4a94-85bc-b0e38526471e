-- 修复系统管理菜单配置SQL脚本
-- 确保部门管理、岗位管理、通知公告、待办事项、站内消息菜单正确配置

-- 1. 更新菜单基本信息，确保配置正确
UPDATE sys_menu SET 
    menu_name = '部门管理',
    parent_id = 100,
    order_num = 4,
    path = 'dept',
    component = 'system/dept/index',
    is_frame = 1,
    is_cache = 0,
    menu_type = 'C',
    visible = '0',
    status = '0',
    perms = 'system:dept:list',
    icon = 'tree',
    remark = '部门管理菜单'
WHERE menu_id = 104;

UPDATE sys_menu SET 
    menu_name = '岗位管理',
    parent_id = 100,
    order_num = 5,
    path = 'post',
    component = 'system/post/index',
    is_frame = 1,
    is_cache = 0,
    menu_type = 'C',
    visible = '0',
    status = '0',
    perms = 'system:post:list',
    icon = 'post',
    remark = '岗位管理菜单'
WHERE menu_id = 105;

UPDATE sys_menu SET 
    menu_name = '通知公告',
    parent_id = 100,
    order_num = 6,
    path = 'notice',
    component = 'system/notice/index',
    is_frame = 1,
    is_cache = 0,
    menu_type = 'C',
    visible = '0',
    status = '0',
    perms = 'system:notice:list',
    icon = 'message',
    remark = '通知公告菜单'
WHERE menu_id = 106;

UPDATE sys_menu SET 
    menu_name = '待办事项',
    parent_id = 100,
    order_num = 7,
    path = 'todo',
    component = 'system/todo/index',
    is_frame = 1,
    is_cache = 0,
    menu_type = 'C',
    visible = '0',
    status = '0',
    perms = 'system:todo:list',
    icon = 'list',
    remark = '待办事项菜单'
WHERE menu_id = 107;

UPDATE sys_menu SET 
    menu_name = '站内消息',
    parent_id = 100,
    order_num = 8,
    path = 'message',
    component = 'system/message/index',
    is_frame = 1,
    is_cache = 0,
    menu_type = 'C',
    visible = '0',
    status = '0',
    perms = 'system:message:list',
    icon = 'chat',
    remark = '站内消息菜单'
WHERE menu_id = 108;

-- 2. 确保角色菜单关联存在（超级管理员角色ID=1）
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES 
(1, 104), -- 部门管理
(1, 105), -- 岗位管理  
(1, 106), -- 通知公告
(1, 107), -- 待办事项
(1, 108); -- 站内消息

-- 3. 添加部门管理按钮权限
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, menu_type, perms, create_by, create_time) VALUES
('部门查询', 104, 1, 'F', 'system:dept:query', 'admin', NOW()),
('部门新增', 104, 2, 'F', 'system:dept:add', 'admin', NOW()),
('部门修改', 104, 3, 'F', 'system:dept:edit', 'admin', NOW()),
('部门删除', 104, 4, 'F', 'system:dept:remove', 'admin', NOW());

-- 4. 添加岗位管理按钮权限
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, menu_type, perms, create_by, create_time) VALUES
('岗位查询', 105, 1, 'F', 'system:post:query', 'admin', NOW()),
('岗位新增', 105, 2, 'F', 'system:post:add', 'admin', NOW()),
('岗位修改', 105, 3, 'F', 'system:post:edit', 'admin', NOW()),
('岗位删除', 105, 4, 'F', 'system:post:remove', 'admin', NOW()),
('岗位导出', 105, 5, 'F', 'system:post:export', 'admin', NOW());

-- 5. 添加通知公告按钮权限
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, menu_type, perms, create_by, create_time) VALUES
('公告查询', 106, 1, 'F', 'system:notice:query', 'admin', NOW()),
('公告新增', 106, 2, 'F', 'system:notice:add', 'admin', NOW()),
('公告修改', 106, 3, 'F', 'system:notice:edit', 'admin', NOW()),
('公告删除', 106, 4, 'F', 'system:notice:remove', 'admin', NOW());

-- 6. 添加待办事项按钮权限
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, menu_type, perms, create_by, create_time) VALUES
('待办查询', 107, 1, 'F', 'system:todo:query', 'admin', NOW()),
('待办新增', 107, 2, 'F', 'system:todo:add', 'admin', NOW()),
('待办修改', 107, 3, 'F', 'system:todo:edit', 'admin', NOW()),
('待办删除', 107, 4, 'F', 'system:todo:remove', 'admin', NOW()),
('待办完成', 107, 5, 'F', 'system:todo:complete', 'admin', NOW());

-- 7. 添加站内消息按钮权限
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, menu_type, perms, create_by, create_time) VALUES
('消息查询', 108, 1, 'F', 'system:message:query', 'admin', NOW()),
('消息发送', 108, 2, 'F', 'system:message:send', 'admin', NOW()),
('消息删除', 108, 3, 'F', 'system:message:remove', 'admin', NOW()),
('消息标记已读', 108, 4, 'F', 'system:message:read', 'admin', NOW());

-- 8. 为超级管理员角色分配新增的按钮权限
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu 
WHERE parent_id IN (104, 105, 106, 107, 108) AND menu_type = 'F';

-- 9. 验证配置结果
SELECT 
    m.menu_id,
    m.menu_name,
    m.parent_id,
    m.path,
    m.component,
    m.menu_type,
    m.visible,
    m.status,
    CASE WHEN rm.role_id IS NOT NULL THEN '已分配' ELSE '未分配' END as role_assigned
FROM sys_menu m
LEFT JOIN sys_role_menu rm ON m.menu_id = rm.role_id AND rm.role_id = 1
WHERE m.menu_id IN (104, 105, 106, 107, 108)
ORDER BY m.order_num;
