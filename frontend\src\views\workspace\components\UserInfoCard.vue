<template>
  <el-card class="user-info-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span class="card-title">个人信息</span>
        <el-button type="text" @click="editProfile">
          <el-icon><Edit /></el-icon>
        </el-button>
      </div>
    </template>
    
    <div class="user-info-content">
      <div class="avatar-section">
        <el-avatar 
          :size="80" 
          :src="userInfo.avatar" 
          class="user-avatar"
          @click="previewAvatar"
        >
          <el-icon><User /></el-icon>
        </el-avatar>
        <div class="user-basic">
          <h3 class="user-name">{{ userInfo.nickName || userInfo.userName }}</h3>
          <p class="user-dept">{{ userInfo.deptName }}</p>
        </div>
      </div>
      
      <div class="user-details">
        <div class="detail-item">
          <el-icon><Message /></el-icon>
          <span class="detail-label">邮箱：</span>
          <span class="detail-value">{{ userInfo.email || '未设置' }}</span>
        </div>
        <div class="detail-item">
          <el-icon><Phone /></el-icon>
          <span class="detail-label">电话：</span>
          <span class="detail-value">{{ userInfo.phonenumber || '未设置' }}</span>
        </div>
        <div class="detail-item">
          <el-icon><Calendar /></el-icon>
          <span class="detail-label">最后登录：</span>
          <span class="detail-value">{{ formatDate(userInfo.loginDate) }}</span>
        </div>
      </div>
      
      <div class="user-status">
        <el-tag 
          :type="userInfo.status === '0' ? 'success' : 'danger'"
          size="small"
        >
          {{ userInfo.status === '0' ? '正常' : '停用' }}
        </el-tag>
      </div>
    </div>

    <!-- 头像预览对话框 -->
    <el-dialog v-model="avatarVisible" title="头像预览" width="400px">
      <div class="avatar-preview">
        <el-image 
          :src="userInfo.avatar" 
          fit="cover"
          style="width: 100%; height: 300px;"
        >
          <template #error>
            <div class="image-slot">
              <el-icon><Picture /></el-icon>
            </div>
          </template>
        </el-image>
      </div>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Edit, User, Message, Phone, Calendar, Picture } from '@element-plus/icons-vue'

// Props
interface Props {
  userInfo: any
}

const props = defineProps<Props>()
const router = useRouter()

// 响应式数据
const avatarVisible = ref(false)

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '未知'
  return new Date(date).toLocaleString('zh-CN')
}

// 编辑个人资料
const editProfile = () => {
  router.push('/profile')
}

// 预览头像
const previewAvatar = () => {
  if (props.userInfo.avatar) {
    avatarVisible.value = true
  }
}
</script>

<style scoped>
.user-info-card {
  height: 100%;
  min-height: 300px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: 600;
  color: #303133;
}

.user-info-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.avatar-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.user-avatar {
  cursor: pointer;
  transition: transform 0.3s;
}

.user-avatar:hover {
  transform: scale(1.05);
}

.user-basic {
  margin-left: 15px;
  flex: 1;
}

.user-name {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.user-dept {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.user-details {
  flex: 1;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.detail-item .el-icon {
  margin-right: 8px;
  color: #909399;
}

.detail-label {
  color: #606266;
  margin-right: 5px;
  min-width: 50px;
}

.detail-value {
  color: #303133;
  flex: 1;
  word-break: break-all;
}

.user-status {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
  text-align: center;
}

.avatar-preview {
  text-align: center;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 30px;
}

@media (max-width: 768px) {
  .avatar-section {
    flex-direction: column;
    text-align: center;
  }
  
  .user-basic {
    margin-left: 0;
    margin-top: 10px;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .detail-label {
    margin-bottom: 2px;
  }
}
</style>
