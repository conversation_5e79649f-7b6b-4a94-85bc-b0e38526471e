import request from '@/utils/request'

// 登录表单接口
export interface LoginForm {
  username: string
  password: string
  code?: string
  uuid?: string
}

// 登录响应接口
export interface LoginResponse {
  token: string
}

// 用户信息响应接口
export interface UserInfoResponse {
  user: {
    userId: number
    userName: string
    nickName: string
    email: string
    phonenumber: string
    sex: string
    avatar: string
    deptId: number
    deptName: string
  }
  roles: string[]
  permissions: string[]
}

/**
 * 用户登录
 */
export function login(data: LoginForm) {
  return request<LoginResponse>({
    url: '/login',
    method: 'post',
    data
  })
}

/**
 * 获取用户信息
 */
export function getInfo() {
  return request<UserInfoResponse>({
    url: '/getInfo',
    method: 'get'
  })
}

/**
 * 退出登录
 */
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

/**
 * 获取验证码
 */
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    method: 'get'
  })
}
