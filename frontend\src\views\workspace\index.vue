<template>
  <div class="workspace-container">
    <!-- 顶部快捷操作 -->
    <div class="top-actions">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="action-button" @click="handleAction('new-article')">
            <el-icon class="action-icon"><EditPen /></el-icon>
            <span class="action-text">新增文章</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="action-button" @click="handleAction('new-project')">
            <el-icon class="action-icon"><FolderAdd /></el-icon>
            <span class="action-text">新增项目</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="action-button" @click="handleAction('new-user')">
            <el-icon class="action-icon"><UserFilled /></el-icon>
            <span class="action-text">新增用户</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="action-button" @click="handleAction('new-review')">
            <el-icon class="action-icon"><DocumentChecked /></el-icon>
            <span class="action-text">新增评核</span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 四个大块布局 -->
    <el-row :gutter="20" class="main-blocks">
      <!-- 第一行：我的消息、我的待办、我的项目 -->
      <el-col :span="8">
        <div class="main-block messages-block">
          <div class="block-header">
            <el-icon class="block-icon"><Bell /></el-icon>
            <span class="block-title">我的消息</span>
            <span class="block-count">3</span>
          </div>
          <div class="block-content">
            <div class="content-item" v-for="msg in messages" :key="msg.id">
              <div class="item-title">{{ msg.title }}</div>
              <div class="item-time">{{ msg.time }}</div>
            </div>
            <div class="view-more">查看更多</div>
          </div>
        </div>
      </el-col>

      <el-col :span="8">
        <div class="main-block todos-block">
          <div class="block-header">
            <el-icon class="block-icon"><Clock /></el-icon>
            <span class="block-title">我的待办</span>
            <span class="block-count">1</span>
          </div>
          <div class="block-content">
            <div class="content-item" v-for="todo in todos" :key="todo.id">
              <div class="item-title">{{ todo.title }}</div>
              <div class="item-time">{{ todo.time }}</div>
            </div>
            <div class="view-more">查看更多</div>
          </div>
        </div>
      </el-col>

      <el-col :span="8">
        <div class="main-block projects-block">
          <div class="block-header">
            <el-icon class="block-icon"><Folder /></el-icon>
            <span class="block-title">我的项目</span>
            <span class="block-count">12</span>
          </div>
          <div class="block-content">
            <div class="content-item" v-for="project in projects" :key="project.id">
              <div class="item-title">{{ project.title }}</div>
              <div class="item-time">{{ project.time }}</div>
            </div>
            <div class="view-more">查看更多</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 第二行：快速访问和数据统计 -->
    <el-row :gutter="20" class="second-row">
      <!-- 快速访问 -->
      <el-col :span="12">
        <div class="main-block quick-access-block">
          <div class="block-header">
            <span class="block-title">快速访问</span>
          </div>
          <div class="block-content">
            <el-row :gutter="15">
              <el-col :span="8" v-for="access in quickAccess" :key="access.key">
                <div class="access-item" @click="handleQuickAccess(access)">
                  <div class="access-icon" :style="{ color: access.color }">
                    <el-icon :size="28">
                      <component :is="access.icon" />
                    </el-icon>
                  </div>
                  <div class="access-label">{{ access.label }}</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-col>

      <!-- 数据统计和系统信息 -->
      <el-col :span="12">
        <div class="main-block stats-system-block">
          <!-- 数据统计 -->
          <div class="stats-section">
            <div class="section-title">数据统计</div>
            <el-row :gutter="15">
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-number">22</div>
                  <div class="stat-label">动态数量</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-number">16</div>
                  <div class="stat-label">活跃数量</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-number">93</div>
                  <div class="stat-label">今日访问</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-number">16843</div>
                  <div class="stat-label">累计访问</div>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 系统信息 -->
          <div class="system-section">
            <div class="section-title">系统信息</div>
            <div class="system-info-content">
              <div class="info-item">
                <span class="info-label">系统版本:</span>
                <span class="info-value">v2.1.0</span>
              </div>
              <div class="info-item">
                <span class="info-label">最后更新:</span>
                <span class="info-value">2024-12-23</span>
              </div>
              <div class="info-item">
                <span class="info-label">当前在线用户:</span>
                <span class="info-value">8</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'
import {
  EditPen,
  FolderAdd,
  UserFilled,
  DocumentChecked,
  Bell,
  Clock,
  Folder,
  User,
  Document,
  Setting,
  OfficeBuilding,
  TrendCharts,
  Monitor
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 我的消息
const messages = ref([
  { id: 1, title: '2024年科研项目申报截止提醒', time: '2024-07-27' },
  { id: 2, title: '2024年科研项目申报截止提醒', time: '2024-07-27' },
  { id: 3, title: '2024年科研项目申报截止提醒', time: '2024-07-27' }
])

// 我的待办
const todos = ref([
  { id: 1, title: '2024年科研项目申报截止提醒', time: '2024-07-27' }
])

// 我的项目
const projects = ref([
  { id: 1, title: '2024年科研项目申报截止提醒', time: '2024-07-27' },
  { id: 2, title: '2024年科研项目申报截止提醒', time: '2024-07-27' },
  { id: 3, title: '2024年科研项目申报截止提醒', time: '2024-07-27' }
])

// 快速访问
const quickAccess = ref([
  { key: 'user-manage', label: '用户管理', icon: User, color: '#4f7cff', path: '/system/user' },
  { key: 'project-manage', label: '机构项目', icon: Document, color: '#4f7cff', path: '/project/list' },
  { key: 'review-manage', label: '项目合同', icon: DocumentChecked, color: '#4f7cff', path: '/project/contract' },
  { key: 'partner-manage', label: '申报配置', icon: Setting, color: '#4f7cff', path: '/system/config' },
  { key: 'notice-manage', label: '通知公告', icon: Bell, color: '#4f7cff', path: '/system/notice' },
  { key: 'dept-manage', label: '通知评', icon: OfficeBuilding, color: '#4f7cff', path: '/system/dept' },
  { key: 'stats', label: '数据统计', icon: TrendCharts, color: '#4f7cff', path: '/data/statistics' }
])

// 处理顶部操作按钮
const handleAction = (action: string) => {
  switch (action) {
    case 'new-article':
      ElMessage.info('新增文章功能开发中...')
      break
    case 'new-project':
      ElMessage.info('新增项目功能开发中...')
      break
    case 'new-user':
      router.push('/system/user')
      break
    case 'new-review':
      ElMessage.info('新增评核功能开发中...')
      break
    default:
      ElMessage.info('功能开发中...')
  }
}

// 处理快速访问点击
const handleQuickAccess = (access: any) => {
  if (access.path) {
    router.push(access.path)
  } else {
    ElMessage.info(`${access.label} 功能开发中...`)
  }
}

// 组件挂载时的初始化
onMounted(() => {
  console.log('个人工作台加载完成')
})
</script>

<style scoped>
.workspace-container {
  padding: 20px;
  min-height: calc(100vh - 84px);
  background-color: #f5f7fa;
}

/* 顶部快捷操作 */
.top-actions {
  margin-bottom: 20px;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.action-button:hover {
  background: #f8f9ff;
  border-color: #4f7cff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 124, 255, 0.15);
}

.action-icon {
  font-size: 20px;
  color: #4f7cff;
  margin-right: 8px;
}

.action-text {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

/* 四个大块布局 */
.main-blocks {
  margin-bottom: 20px;
}

.second-row {
  margin-top: 20px;
}

.main-block {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e4e7ed;
  height: 280px;
  display: flex;
  flex-direction: column;
}

.block-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f2f5;
}

.block-icon {
  font-size: 18px;
  margin-right: 8px;
}

.messages-block .block-icon {
  color: #f39c12;
}

.todos-block .block-icon {
  color: #3498db;
}

.projects-block .block-icon {
  color: #2ecc71;
}

.block-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  flex: 1;
}

.block-count {
  background: #4f7cff;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.block-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.content-item {
  padding: 8px 0;
  border-bottom: 1px solid #f5f7fa;
}

.content-item:last-of-type {
  border-bottom: none;
}

.item-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-time {
  font-size: 12px;
  color: #909399;
}

.view-more {
  margin-top: auto;
  text-align: center;
  color: #4f7cff;
  font-size: 12px;
  cursor: pointer;
  padding: 8px 0;
}

.view-more:hover {
  color: #3d63ff;
}

/* 快速访问块 */
.quick-access-block {
  height: 320px;
}

.access-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 10px;
  background: #f8f9ff;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
  margin-bottom: 10px;
}

.access-item:hover {
  background: #f0f4ff;
  border-color: #4f7cff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 124, 255, 0.15);
}

.access-icon {
  margin-bottom: 8px;
}

.access-label {
  font-size: 12px;
  color: #303133;
  text-align: center;
  line-height: 1.2;
}

/* 数据统计和系统信息块 */
.stats-system-block {
  height: 320px;
  display: flex;
  flex-direction: column;
}

.stats-section {
  flex: 1;
  margin-bottom: 20px;
}

.system-section {
  flex: 1;
}

.section-title {
  font-size: 16px;
  color: #303133;
  margin-bottom: 15px;
  font-weight: 600;
}

.stat-item {
  text-align: center;
  padding: 15px 10px;
  background: #f8f9ff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 6px;
  color: #4f7cff;
}

.stat-label {
  font-size: 12px;
  color: #606266;
}

.system-info-content {
  background: #f8f9ff;
  border-radius: 6px;
  padding: 15px;
  border: 1px solid #e4e7ed;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f2f5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 13px;
  color: #606266;
}

.info-value {
  font-size: 13px;
  color: #303133;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .second-row .el-col {
    margin-top: 20px;
  }

  .main-block {
    height: auto;
    min-height: 250px;
  }

  .quick-access-block,
  .stats-system-block {
    height: auto;
    min-height: 280px;
  }
}

@media (max-width: 768px) {
  .workspace-container {
    padding: 10px;
  }

  .action-button {
    padding: 12px;
  }

  .action-text {
    font-size: 12px;
  }

  .main-block {
    height: auto;
    min-height: 200px;
    margin-bottom: 15px;
  }

  .access-item {
    padding: 12px 8px;
  }

  .access-label {
    font-size: 11px;
  }

  .stat-number {
    font-size: 20px;
  }
}

</style>
