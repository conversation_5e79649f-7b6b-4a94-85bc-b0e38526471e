<template>
  <div class="workspace-container">
    <!-- 顶部快捷操作 -->
    <div class="top-actions">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="action-button" @click="handleAction('new-article')">
            <el-icon class="action-icon"><EditPen /></el-icon>
            <span class="action-text">新增文章</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="action-button" @click="handleAction('new-project')">
            <el-icon class="action-icon"><FolderAdd /></el-icon>
            <span class="action-text">新增项目</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="action-button" @click="handleAction('new-user')">
            <el-icon class="action-icon"><UserFilled /></el-icon>
            <span class="action-text">新增用户</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="action-button" @click="handleAction('new-review')">
            <el-icon class="action-icon"><DocumentChecked /></el-icon>
            <span class="action-text">新增评核</span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <el-row :gutter="20" class="main-content">
      <!-- 左侧内容 -->
      <el-col :span="16">
        <!-- 我的消息、待办、项目 -->
        <el-row :gutter="20" class="info-cards">
          <el-col :span="8">
            <div class="info-card messages-card">
              <div class="card-header">
                <el-icon class="header-icon"><Bell /></el-icon>
                <span class="card-title">我的消息</span>
                <span class="card-count">3</span>
              </div>
              <div class="card-content">
                <div class="message-item" v-for="msg in messages" :key="msg.id">
                  <div class="message-title">{{ msg.title }}</div>
                  <div class="message-time">{{ msg.time }}</div>
                </div>
                <div class="view-more">查看更多</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-card todos-card">
              <div class="card-header">
                <el-icon class="header-icon"><Clock /></el-icon>
                <span class="card-title">我的待办</span>
                <span class="card-count">1</span>
              </div>
              <div class="card-content">
                <div class="todo-item" v-for="todo in todos" :key="todo.id">
                  <div class="todo-title">{{ todo.title }}</div>
                  <div class="todo-time">{{ todo.time }}</div>
                </div>
                <div class="view-more">查看更多</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-card projects-card">
              <div class="card-header">
                <el-icon class="header-icon"><Folder /></el-icon>
                <span class="card-title">我的项目</span>
                <span class="card-count">12</span>
              </div>
              <div class="card-content">
                <div class="project-item" v-for="project in projects" :key="project.id">
                  <div class="project-title">{{ project.title }}</div>
                  <div class="project-time">{{ project.time }}</div>
                </div>
                <div class="view-more">查看更多</div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 快速访问 -->
        <div class="quick-access">
          <h3 class="section-title">快速访问</h3>
          <el-row :gutter="20">
            <el-col :span="4" v-for="access in quickAccess" :key="access.key">
              <div class="access-item" @click="handleQuickAccess(access)">
                <div class="access-icon" :style="{ color: access.color }">
                  <el-icon :size="32">
                    <component :is="access.icon" />
                  </el-icon>
                </div>
                <div class="access-label">{{ access.label }}</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 数据统计 -->
        <div class="data-statistics">
          <h3 class="section-title">数据统计</h3>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number" style="color: #4f7cff;">22</div>
                <div class="stat-label">动态数量</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number" style="color: #4f7cff;">16</div>
                <div class="stat-label">活跃数量</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number" style="color: #4f7cff;">93</div>
                <div class="stat-label">今日访问</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number" style="color: #4f7cff;">16843</div>
                <div class="stat-label">累计访问</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-col>

      <!-- 右侧系统信息 -->
      <el-col :span="8">
        <div class="system-info">
          <h3 class="section-title">系统信息</h3>
          <div class="info-content">
            <div class="info-item">
              <span class="info-label">系统版本:</span>
              <span class="info-value">v2.1.0</span>
            </div>
            <div class="info-item">
              <span class="info-label">最后更新:</span>
              <span class="info-value">2024-12-23</span>
            </div>
            <div class="info-item">
              <span class="info-label">当前在线用户:</span>
              <span class="info-value">8</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'
import {
  EditPen,
  FolderAdd,
  UserFilled,
  DocumentChecked,
  Bell,
  Clock,
  Folder,
  User,
  Document,
  Setting,
  OfficeBuilding,
  TrendCharts,
  Monitor
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 我的消息
const messages = ref([
  { id: 1, title: '2024年科研项目申报截止提醒', time: '2024-07-27' },
  { id: 2, title: '2024年科研项目申报截止提醒', time: '2024-07-27' },
  { id: 3, title: '2024年科研项目申报截止提醒', time: '2024-07-27' }
])

// 我的待办
const todos = ref([
  { id: 1, title: '2024年科研项目申报截止提醒', time: '2024-07-27' }
])

// 我的项目
const projects = ref([
  { id: 1, title: '2024年科研项目申报截止提醒', time: '2024-07-27' },
  { id: 2, title: '2024年科研项目申报截止提醒', time: '2024-07-27' },
  { id: 3, title: '2024年科研项目申报截止提醒', time: '2024-07-27' }
])

// 快速访问
const quickAccess = ref([
  { key: 'user-manage', label: '用户管理', icon: User, color: '#4f7cff', path: '/system/user' },
  { key: 'project-manage', label: '机构项目', icon: Document, color: '#4f7cff', path: '/project/list' },
  { key: 'review-manage', label: '项目合同', icon: DocumentChecked, color: '#4f7cff', path: '/project/contract' },
  { key: 'partner-manage', label: '申报配置', icon: Setting, color: '#4f7cff', path: '/system/config' },
  { key: 'notice-manage', label: '通知公告', icon: Bell, color: '#4f7cff', path: '/system/notice' },
  { key: 'dept-manage', label: '通知评', icon: OfficeBuilding, color: '#4f7cff', path: '/system/dept' },
  { key: 'stats', label: '数据统计', icon: TrendCharts, color: '#4f7cff', path: '/data/statistics' }
])

// 处理顶部操作按钮
const handleAction = (action: string) => {
  switch (action) {
    case 'new-article':
      ElMessage.info('新增文章功能开发中...')
      break
    case 'new-project':
      ElMessage.info('新增项目功能开发中...')
      break
    case 'new-user':
      router.push('/system/user')
      break
    case 'new-review':
      ElMessage.info('新增评核功能开发中...')
      break
    default:
      ElMessage.info('功能开发中...')
  }
}

// 处理快速访问点击
const handleQuickAccess = (access: any) => {
  if (access.path) {
    router.push(access.path)
  } else {
    ElMessage.info(`${access.label} 功能开发中...`)
  }
}

// 组件挂载时的初始化
onMounted(() => {
  console.log('个人工作台加载完成')
})
</script>

<style scoped>
.workspace-container {
  padding: 20px;
  min-height: calc(100vh - 84px);
  background-color: #f5f7fa;
}

/* 顶部快捷操作 */
.top-actions {
  margin-bottom: 20px;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.action-button:hover {
  background: #f8f9ff;
  border-color: #4f7cff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 124, 255, 0.15);
}

.action-icon {
  font-size: 20px;
  color: #4f7cff;
  margin-right: 8px;
}

.action-text {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

/* 主要内容区域 */
.main-content {
  margin-top: 20px;
}

/* 信息卡片 */
.info-cards {
  margin-bottom: 30px;
}

.info-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e4e7ed;
  height: 200px;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f2f5;
}

.header-icon {
  font-size: 18px;
  margin-right: 8px;
}

.messages-card .header-icon {
  color: #f39c12;
}

.todos-card .header-icon {
  color: #3498db;
}

.projects-card .header-icon {
  color: #2ecc71;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  flex: 1;
}

.card-count {
  background: #4f7cff;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.message-item,
.todo-item,
.project-item {
  padding: 8px 0;
  border-bottom: 1px solid #f5f7fa;
}

.message-item:last-of-type,
.todo-item:last-of-type,
.project-item:last-of-type {
  border-bottom: none;
}

.message-title,
.todo-title,
.project-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.message-time,
.todo-time,
.project-time {
  font-size: 12px;
  color: #909399;
}

.view-more {
  margin-top: auto;
  text-align: center;
  color: #4f7cff;
  font-size: 12px;
  cursor: pointer;
  padding: 8px 0;
}

.view-more:hover {
  color: #3d63ff;
}

/* 快速访问 */
.quick-access {
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  color: #303133;
  margin-bottom: 15px;
  font-weight: 600;
}

.access-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.access-item:hover {
  background: #f8f9ff;
  border-color: #4f7cff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 124, 255, 0.15);
}

.access-icon {
  margin-bottom: 10px;
}

.access-label {
  font-size: 14px;
  color: #303133;
  text-align: center;
}

/* 数据统计 */
.data-statistics {
  margin-bottom: 30px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

/* 系统信息 */
.system-info {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.info-content {
  margin-top: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f7fa;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #606266;
}

.info-value {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content .el-col:last-child {
    margin-top: 20px;
  }
}

@media (max-width: 768px) {
  .workspace-container {
    padding: 10px;
  }

  .action-button {
    padding: 12px;
  }

  .action-text {
    font-size: 12px;
  }

  .info-card {
    height: auto;
    min-height: 150px;
  }

  .access-item {
    padding: 15px;
  }
}

</style>
