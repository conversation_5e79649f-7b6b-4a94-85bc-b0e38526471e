package com.research.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.research.system.domain.SysNoticeRead;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公告阅读记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Mapper
public interface SysNoticeReadMapper extends BaseMapper<SysNoticeRead> {

    /**
     * 记录公告阅读
     * 
     * @param noticeRead 阅读记录
     * @return 影响行数
     */
    int insertNoticeRead(SysNoticeRead noticeRead);

    /**
     * 查询用户是否已读公告
     * 
     * @param noticeId 公告ID
     * @param userId 用户ID
     * @return 阅读记录
     */
    SysNoticeRead selectByNoticeAndUser(@Param("noticeId") Long noticeId, @Param("userId") Long userId);

    /**
     * 查询公告的阅读用户列表
     * 
     * @param noticeId 公告ID
     * @return 阅读用户列表
     */
    List<SysNoticeRead> selectReadUsersByNotice(@Param("noticeId") Long noticeId);

    /**
     * 查询公告阅读统计
     * 
     * @param noticeId 公告ID
     * @return 阅读统计
     */
    java.util.Map<String, Object> selectReadStatsByNotice(@Param("noticeId") Long noticeId);

    /**
     * 删除公告的所有阅读记录
     * 
     * @param noticeId 公告ID
     * @return 影响行数
     */
    int deleteByNoticeId(@Param("noticeId") Long noticeId);
}
