import request from '@/utils/request'

// 查询纵向项目列表
export function listVerticalProject(query: any) {
  return request({
    url: '/project/vertical/list',
    method: 'get',
    params: query
  })
}

// 查询纵向项目详细
export function getVerticalProject(id: number) {
  return request({
    url: '/project/vertical/' + id,
    method: 'get'
  })
}

// 根据项目编号查询项目
export function getVerticalProjectByNo(projectNo: string) {
  return request({
    url: '/project/vertical/no/' + projectNo,
    method: 'get'
  })
}

// 新增纵向项目
export function addVerticalProject(data: any) {
  return request({
    url: '/project/vertical',
    method: 'post',
    data: data
  })
}

// 修改纵向项目
export function updateVerticalProject(data: any) {
  return request({
    url: '/project/vertical',
    method: 'put',
    data: data
  })
}

// 删除纵向项目
export function delVerticalProject(id: number | number[]) {
  return request({
    url: '/project/vertical/' + id,
    method: 'delete'
  })
}

// 提交项目申请
export function submitProjectApplication(data: any) {
  return request({
    url: '/project/vertical/submit',
    method: 'post',
    data: data
  })
}

// 项目立项
export function approveProject(id: number, approvalFilePath?: string) {
  return request({
    url: '/project/vertical/approve/' + id,
    method: 'put',
    params: { approvalFilePath }
  })
}

// 项目执行
export function executeProject(id: number) {
  return request({
    url: '/project/vertical/execute/' + id,
    method: 'put'
  })
}

// 项目挂起
export function suspendProject(id: number, reason: string) {
  return request({
    url: '/project/vertical/suspend/' + id,
    method: 'put',
    params: { reason }
  })
}

// 项目恢复
export function resumeProject(id: number) {
  return request({
    url: '/project/vertical/resume/' + id,
    method: 'put'
  })
}

// 项目结项
export function closeProject(id: number) {
  return request({
    url: '/project/vertical/close/' + id,
    method: 'put'
  })
}

// 项目撤销
export function cancelProject(id: number, reason: string) {
  return request({
    url: '/project/vertical/cancel/' + id,
    method: 'put',
    params: { reason }
  })
}

// 更新项目预算使用
export function updateProjectBudgetUsage(id: number, usedBudget: number) {
  return request({
    url: '/project/vertical/budget/' + id,
    method: 'put',
    params: { usedBudget }
  })
}

// 查询我负责的项目
export function getMyProjects(principalId: number) {
  return request({
    url: '/project/vertical/my/' + principalId,
    method: 'get'
  })
}

// 查询我参与的项目
export function getMyParticipatedProjects(userId: number) {
  return request({
    url: '/project/vertical/participated/' + userId,
    method: 'get'
  })
}

// 查询部门项目
export function getDeptProjects(deptId: number) {
  return request({
    url: '/project/vertical/dept/' + deptId,
    method: 'get'
  })
}

// 查询项目统计信息
export function getProjectStatistics() {
  return request({
    url: '/project/vertical/statistics',
    method: 'get'
  })
}

// 查询部门项目统计
export function getDeptStatistics() {
  return request({
    url: '/project/vertical/statistics/dept',
    method: 'get'
  })
}

// 查询项目类型统计
export function getTypeStatistics() {
  return request({
    url: '/project/vertical/statistics/type',
    method: 'get'
  })
}

// 查询项目级别统计
export function getLevelStatistics() {
  return request({
    url: '/project/vertical/statistics/level',
    method: 'get'
  })
}

// 查询即将到期的项目
export function getExpiringProjects(days: number = 30) {
  return request({
    url: '/project/vertical/expiring',
    method: 'get',
    params: { days }
  })
}

// 查询已逾期的项目
export function getOverdueProjects() {
  return request({
    url: '/project/vertical/overdue',
    method: 'get'
  })
}

// 查询预算使用率高的项目
export function getHighBudgetUsageProjects(threshold: number = 80) {
  return request({
    url: '/project/vertical/high-budget-usage',
    method: 'get',
    params: { threshold }
  })
}

// 搜索项目
export function searchProjects(keyword: string) {
  return request({
    url: '/project/vertical/search',
    method: 'get',
    params: { keyword }
  })
}

// 查询年度项目统计
export function getYearlyStatistics(year: number) {
  return request({
    url: '/project/vertical/statistics/yearly',
    method: 'get',
    params: { year }
  })
}

// 生成项目编号
export function generateProjectNo(projectType?: string) {
  return request({
    url: '/project/vertical/generate-no',
    method: 'get',
    params: { projectType }
  })
}

// 导出纵向项目数据
export function exportVerticalProject(query: any) {
  return request({
    url: '/project/vertical/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 导入纵向项目数据
export function importVerticalProject(data: FormData) {
  return request({
    url: '/project/vertical/importData',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 下载导入模板
export function downloadTemplate() {
  return request({
    url: '/project/vertical/importTemplate',
    method: 'post',
    responseType: 'blob'
  })
}
