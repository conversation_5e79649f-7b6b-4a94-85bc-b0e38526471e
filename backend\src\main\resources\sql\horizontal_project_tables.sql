-- 横向项目管理相关表结构
-- 包含横向项目、合同管理、合作单位管理等功能

USE research_db;

-- ===========================================
-- 横向项目管理核心表结构
-- ===========================================

-- 横向项目表（简化版，与现有biz_horizontal_project兼容）
DROP TABLE IF EXISTS horizontal_project;
CREATE TABLE horizontal_project (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '项目ID',
    project_no VARCHAR(50) NOT NULL UNIQUE COMMENT '项目编号',
    project_name VARCHAR(255) NOT NULL COMMENT '项目名称',
    project_type VARCHAR(50) COMMENT '项目类型',
    contract_id BIGINT COMMENT '关联合同ID',
    partner_id BIGINT COMMENT '合作单位ID',
    status INT DEFAULT 0 COMMENT '项目状态：0-申请中，1-立项，2-执行中，3-变更中，4-结项中，5-已结项，6-已撤销',
    start_date DATE COMMENT '项目开始日期',
    end_date DATE COMMENT '项目结束日期',
    total_fund DECIMAL(15,2) COMMENT '项目总经费',
    received_fund DECIMAL(15,2) DEFAULT 0.00 COMMENT '已到账经费',
    principal_id BIGINT COMMENT '项目负责人ID',
    principal_name VARCHAR(100) COMMENT '项目负责人姓名',
    dept_id BIGINT COMMENT '所属部门ID',
    dept_name VARCHAR(100) COMMENT '所属部门名称',
    project_summary TEXT COMMENT '项目简介',
    research_content TEXT COMMENT '研究内容',
    expected_results TEXT COMMENT '预期成果',
    keywords VARCHAR(500) COMMENT '关键词',
    subject_category VARCHAR(100) COMMENT '学科分类',
    application_file_path VARCHAR(500) COMMENT '申请书文件路径',
    contract_file_path VARCHAR(500) COMMENT '合同文件路径',
    process_instance_id VARCHAR(64) COMMENT '工作流实例ID',
    current_node VARCHAR(100) COMMENT '当前审批节点',
    approval_status VARCHAR(50) COMMENT '审批状态',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_project_no (project_no),
    INDEX idx_principal_id (principal_id),
    INDEX idx_dept_id (dept_id),
    INDEX idx_status (status),
    INDEX idx_contract_id (contract_id),
    INDEX idx_partner_id (partner_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='横向项目表';

-- 合同管理表
DROP TABLE IF EXISTS contract;
CREATE TABLE contract (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '合同ID',
    contract_no VARCHAR(50) NOT NULL UNIQUE COMMENT '合同编号',
    contract_name VARCHAR(255) NOT NULL COMMENT '合同名称',
    contract_type VARCHAR(50) COMMENT '合同类型',
    contract_template_id BIGINT COMMENT '合同模板ID',
    partner_id BIGINT COMMENT '合作单位ID',
    partner_name VARCHAR(200) COMMENT '合作单位名称',
    contract_amount DECIMAL(15,2) COMMENT '合同金额',
    signing_date DATE COMMENT '签署日期',
    start_date DATE COMMENT '合同开始日期',
    end_date DATE COMMENT '合同结束日期',
    status INT DEFAULT 0 COMMENT '合同状态：0-草稿，1-审核中，2-已签署，3-执行中，4-已完成，5-已终止',
    principal_id BIGINT COMMENT '项目负责人ID',
    principal_name VARCHAR(100) COMMENT '项目负责人姓名',
    dept_id BIGINT COMMENT '所属部门ID',
    dept_name VARCHAR(100) COMMENT '所属部门名称',
    contract_content TEXT COMMENT '合同内容',
    payment_terms TEXT COMMENT '付款条款',
    delivery_terms TEXT COMMENT '交付条款',
    contract_file_path VARCHAR(500) COMMENT '合同文件路径',
    backup_file_path VARCHAR(500) COMMENT '备案文件路径',
    process_instance_id VARCHAR(64) COMMENT '工作流实例ID',
    current_node VARCHAR(100) COMMENT '当前审批节点',
    approval_status VARCHAR(50) COMMENT '审批状态',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_contract_no (contract_no),
    INDEX idx_partner_id (partner_id),
    INDEX idx_principal_id (principal_id),
    INDEX idx_dept_id (dept_id),
    INDEX idx_status (status),
    INDEX idx_signing_date (signing_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同管理表';

-- 合作单位表
DROP TABLE IF EXISTS partner;
CREATE TABLE partner (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '合作单位ID',
    partner_code VARCHAR(50) NOT NULL UNIQUE COMMENT '单位编码',
    partner_name VARCHAR(255) NOT NULL COMMENT '单位名称',
    partner_type VARCHAR(50) COMMENT '单位类型：企业、高校、科研院所、政府机构等',
    legal_representative VARCHAR(100) COMMENT '法定代表人',
    contact_person VARCHAR(100) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    address VARCHAR(500) COMMENT '单位地址',
    postal_code VARCHAR(10) COMMENT '邮政编码',
    website VARCHAR(200) COMMENT '官方网站',
    business_scope TEXT COMMENT '经营范围',
    registration_capital DECIMAL(15,2) COMMENT '注册资本',
    establishment_date DATE COMMENT '成立日期',
    credit_code VARCHAR(50) COMMENT '统一社会信用代码',
    tax_number VARCHAR(50) COMMENT '税务登记号',
    bank_name VARCHAR(200) COMMENT '开户银行',
    bank_account VARCHAR(50) COMMENT '银行账号',
    status INT DEFAULT 1 COMMENT '状态：0-禁用，1-正常，2-待审核',
    cooperation_level VARCHAR(50) COMMENT '合作等级：A、B、C、D',
    cooperation_count INT DEFAULT 0 COMMENT '合作次数',
    total_contract_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '累计合同金额',
    last_cooperation_date DATE COMMENT '最近合作日期',
    qualification_file_path VARCHAR(500) COMMENT '资质文件路径',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_partner_code (partner_code),
    INDEX idx_partner_name (partner_name),
    INDEX idx_partner_type (partner_type),
    INDEX idx_status (status),
    INDEX idx_cooperation_level (cooperation_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合作单位表';

-- 合同模板表
DROP TABLE IF EXISTS contract_template;
CREATE TABLE contract_template (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '模板ID',
    template_name VARCHAR(255) NOT NULL COMMENT '模板名称',
    template_type VARCHAR(50) COMMENT '模板类型',
    template_category VARCHAR(50) COMMENT '模板分类',
    template_content TEXT COMMENT '模板内容',
    template_file_path VARCHAR(500) COMMENT '模板文件路径',
    version VARCHAR(20) DEFAULT '1.0' COMMENT '版本号',
    status INT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_template_type (template_type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同模板表';

-- 合作单位资质表
DROP TABLE IF EXISTS partner_qualification;
CREATE TABLE partner_qualification (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '资质ID',
    partner_id BIGINT NOT NULL COMMENT '合作单位ID',
    qualification_name VARCHAR(255) NOT NULL COMMENT '资质名称',
    qualification_type VARCHAR(50) COMMENT '资质类型',
    qualification_level VARCHAR(50) COMMENT '资质等级',
    issuing_authority VARCHAR(200) COMMENT '发证机关',
    certificate_no VARCHAR(100) COMMENT '证书编号',
    issue_date DATE COMMENT '发证日期',
    expiry_date DATE COMMENT '有效期至',
    status INT DEFAULT 1 COMMENT '状态：0-已过期，1-有效，2-即将过期',
    qualification_file_path VARCHAR(500) COMMENT '资质文件路径',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_partner_id (partner_id),
    INDEX idx_qualification_type (qualification_type),
    INDEX idx_status (status),
    INDEX idx_expiry_date (expiry_date),
    FOREIGN KEY (partner_id) REFERENCES partner(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合作单位资质表';

-- 合作单位评价表
DROP TABLE IF EXISTS partner_evaluation;
CREATE TABLE partner_evaluation (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '评价ID',
    partner_id BIGINT NOT NULL COMMENT '合作单位ID',
    project_id BIGINT COMMENT '关联项目ID',
    contract_id BIGINT COMMENT '关联合同ID',
    evaluation_date DATE COMMENT '评价日期',
    evaluator_id BIGINT COMMENT '评价人ID',
    evaluator_name VARCHAR(100) COMMENT '评价人姓名',
    cooperation_quality_score INT COMMENT '合作质量评分（1-10）',
    delivery_quality_score INT COMMENT '交付质量评分（1-10）',
    service_attitude_score INT COMMENT '服务态度评分（1-10）',
    communication_score INT COMMENT '沟通协调评分（1-10）',
    overall_score DECIMAL(3,1) COMMENT '综合评分',
    evaluation_content TEXT COMMENT '评价内容',
    improvement_suggestions TEXT COMMENT '改进建议',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_partner_id (partner_id),
    INDEX idx_project_id (project_id),
    INDEX idx_contract_id (contract_id),
    INDEX idx_evaluation_date (evaluation_date),
    FOREIGN KEY (partner_id) REFERENCES partner(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合作单位评价表';

-- 横向项目成员表
DROP TABLE IF EXISTS horizontal_project_member;
CREATE TABLE horizontal_project_member (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '成员ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    user_name VARCHAR(100) NOT NULL COMMENT '用户姓名',
    user_no VARCHAR(50) COMMENT '用户工号',
    dept_id BIGINT COMMENT '所属部门ID',
    dept_name VARCHAR(100) COMMENT '所属部门名称',
    member_role INT DEFAULT 2 COMMENT '成员角色：0-项目负责人，1-主要参与人，2-一般参与人，3-学生参与人',
    member_role_name VARCHAR(50) COMMENT '成员角色名称',
    workload DECIMAL(8,2) COMMENT '工作量（人月）',
    position VARCHAR(100) COMMENT '职务/职称',
    specialty VARCHAR(200) COMMENT '专业领域',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '电子邮箱',
    responsibility TEXT COMMENT '主要职责',
    join_date DATE COMMENT '加入日期',
    leave_date DATE COMMENT '离开日期',
    status INT DEFAULT 0 COMMENT '成员状态：0-正常，1-已离开，2-暂停',
    is_external BOOLEAN DEFAULT FALSE COMMENT '是否外聘专家',
    external_unit VARCHAR(200) COMMENT '外聘单位',
    sort_order INT DEFAULT 0 COMMENT '排序号',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_project_id (project_id),
    INDEX idx_user_id (user_id),
    INDEX idx_member_role (member_role),
    INDEX idx_status (status),
    UNIQUE KEY uk_project_user (project_id, user_id),
    FOREIGN KEY (project_id) REFERENCES horizontal_project(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='横向项目成员表';

-- 合同变更记录表
DROP TABLE IF EXISTS contract_change;
CREATE TABLE contract_change (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '变更ID',
    contract_id BIGINT NOT NULL COMMENT '合同ID',
    change_type VARCHAR(50) COMMENT '变更类型：金额变更、时间变更、内容变更等',
    change_reason TEXT COMMENT '变更原因',
    change_content TEXT COMMENT '变更内容',
    change_before TEXT COMMENT '变更前内容',
    change_after TEXT COMMENT '变更后内容',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '变更状态：pending-待审核，approved-已批准，rejected-已拒绝',
    apply_time DATETIME COMMENT '申请时间',
    approve_time DATETIME COMMENT '审批时间',
    approver_id BIGINT COMMENT '审批人ID',
    approver_name VARCHAR(100) COMMENT '审批人姓名',
    process_instance_id VARCHAR(64) COMMENT '工作流实例ID',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_contract_id (contract_id),
    INDEX idx_status (status),
    INDEX idx_apply_time (apply_time),
    FOREIGN KEY (contract_id) REFERENCES contract(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同变更记录表';
