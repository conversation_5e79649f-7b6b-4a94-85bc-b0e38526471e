import request from '@/utils/request'

// 查询流程版本列表
export function listProcessVersion(processDefinitionKey: string) {
  return request({
    url: '/workflow/version/list',
    method: 'get',
    params: { processDefinitionKey }
  })
}

// 查询版本详细信息
export function getProcessVersion(versionId: string) {
  return request({
    url: '/workflow/version/' + versionId,
    method: 'get'
  })
}

// 创建新版本
export function createVersion(data: any) {
  return request({
    url: '/workflow/version/create',
    method: 'post',
    params: data
  })
}

// 发布版本
export function publishVersion(versionId: string, data: any) {
  return request({
    url: '/workflow/version/publish/' + versionId,
    method: 'put',
    params: data
  })
}

// 停用版本
export function deprecateVersion(versionId: string) {
  return request({
    url: '/workflow/version/deprecate/' + versionId,
    method: 'put'
  })
}

// 版本回滚
export function rollbackVersion(data: any) {
  return request({
    url: '/workflow/version/rollback',
    method: 'put',
    params: data
  })
}

// 获取版本统计信息
export function getVersionStatistics(processDefinitionKey: string) {
  return request({
    url: '/workflow/version/statistics',
    method: 'get',
    params: { processDefinitionKey }
  })
}

// 获取版本路由统计
export function getRouteStatistics(processDefinitionKey: string) {
  return request({
    url: '/workflow/version/route/statistics',
    method: 'get',
    params: { processDefinitionKey }
  })
}

// 创建全量发布路由
export function createFullReleaseRoute(data: any) {
  return request({
    url: '/workflow/version/route/full',
    method: 'post',
    params: data
  })
}

// 创建灰度发布路由
export function createGrayReleaseRoute(data: any) {
  return request({
    url: '/workflow/version/route/gray',
    method: 'post',
    params: data
  })
}

// 创建A/B测试路由
export function createABTestRoute(data: any) {
  return request({
    url: '/workflow/version/route/abtest',
    method: 'post',
    params: {
      processDefinitionKey: data.processDefinitionKey,
      processDefinitionId: data.processDefinitionId,
      versionId: data.versionId
    },
    data: data.targetUsers
  })
}

// 创建基于部门的路由
export function createDepartmentRoute(data: any) {
  return request({
    url: '/workflow/version/route/department',
    method: 'post',
    params: {
      processDefinitionKey: data.processDefinitionKey,
      processDefinitionId: data.processDefinitionId,
      versionId: data.versionId
    },
    data: data.departments
  })
}

// 测试版本路由
export function testVersionRoute(data: any) {
  return request({
    url: '/workflow/version/route/test',
    method: 'post',
    params: {
      processDefinitionKey: data.processDefinitionKey,
      userId: data.userId,
      userGroups: data.userGroups,
      department: data.department
    },
    data: data.context
  })
}
