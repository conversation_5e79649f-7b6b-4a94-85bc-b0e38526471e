# 科研成果多维敏捷管控中心 - 项目结构设计

## 整体项目结构

```
admin-new/
├── backend/                          # 后端项目
│   ├── src/main/java/
│   │   └── com/research/
│   │       ├── ResearchApplication.java    # 主启动类
│   │       ├── common/                     # 公共模块
│   │       │   ├── annotation/             # 自定义注解
│   │       │   ├── config/                 # 配置类
│   │       │   ├── constant/               # 常量定义
│   │       │   ├── core/                   # 核心组件
│   │       │   ├── enums/                  # 枚举类
│   │       │   ├── exception/              # 异常处理
│   │       │   ├── filter/                 # 过滤器
│   │       │   ├── interceptor/            # 拦截器
│   │       │   ├── utils/                  # 工具类
│   │       │   └── xss/                    # XSS防护
│   │       ├── framework/                  # 框架模块
│   │       │   ├── aspectj/                # AOP切面
│   │       │   ├── config/                 # 框架配置
│   │       │   ├── datasource/             # 数据源配置
│   │       │   ├── interceptor/            # 框架拦截器
│   │       │   ├── manager/                # 异步任务管理
│   │       │   ├── security/               # 安全框架
│   │       │   └── web/                    # Web配置
│   │       ├── system/                     # 系统管理模块
│   │       │   ├── controller/             # 控制器
│   │       │   ├── domain/                 # 实体类
│   │       │   ├── mapper/                 # 数据访问层
│   │       │   ├── service/                # 业务逻辑层
│   │       │   └── vo/                     # 视图对象
│   │       ├── portal/                     # 门户模块
│   │       │   ├── controller/
│   │       │   ├── domain/
│   │       │   ├── mapper/
│   │       │   ├── service/
│   │       │   └── vo/
│   │       ├── workflow/                   # 工作流模块
│   │       │   ├── controller/
│   │       │   ├── domain/
│   │       │   ├── mapper/
│   │       │   ├── service/
│   │       │   └── vo/
│   │       ├── project/                    # 项目管理模块
│   │       │   ├── controller/
│   │       │   ├── domain/
│   │       │   ├── mapper/
│   │       │   ├── service/
│   │       │   └── vo/
│   │       ├── research/                   # 科研管理模块
│   │       │   ├── controller/
│   │       │   ├── domain/
│   │       │   ├── mapper/
│   │       │   ├── service/
│   │       │   └── vo/
│   │       ├── forum/                      # 龙湖讲坛模块
│   │       │   ├── controller/
│   │       │   ├── domain/
│   │       │   ├── mapper/
│   │       │   ├── service/
│   │       │   └── vo/
│   │       └── generator/                  # 代码生成模块
│   │           ├── controller/
│   │           ├── domain/
│   │           ├── mapper/
│   │           ├── service/
│   │           └── util/
│   ├── src/main/resources/
│   │   ├── application.yml                 # 主配置文件
│   │   ├── application-dev.yml             # 开发环境配置
│   │   ├── application-prod.yml            # 生产环境配置
│   │   ├── banner.txt                      # 启动横幅
│   │   ├── logback-spring.xml              # 日志配置
│   │   ├── mapper/                         # MyBatis映射文件
│   │   │   ├── system/
│   │   │   ├── portal/
│   │   │   ├── workflow/
│   │   │   ├── project/
│   │   │   ├── research/
│   │   │   ├── forum/
│   │   │   └── generator/
│   │   ├── static/                         # 静态资源
│   │   └── templates/                      # 模板文件
│   ├── src/test/java/                      # 测试代码
│   └── pom.xml                             # Maven配置文件
├── frontend/                         # 前端项目
│   ├── public/                             # 公共资源
│   │   ├── favicon.ico
│   │   └── index.html
│   ├── src/
│   │   ├── api/                            # API接口
│   │   │   ├── system/                     # 系统管理API
│   │   │   ├── portal/                     # 门户API
│   │   │   ├── workflow/                   # 工作流API
│   │   │   ├── project/                    # 项目管理API
│   │   │   ├── research/                   # 科研管理API
│   │   │   └── forum/                      # 龙湖讲坛API
│   │   ├── assets/                         # 静态资源
│   │   │   ├── icons/                      # 图标
│   │   │   ├── images/                     # 图片
│   │   │   └── styles/                     # 样式文件
│   │   ├── components/                     # 公共组件
│   │   │   ├── common/                     # 通用组件
│   │   │   ├── form/                       # 表单组件
│   │   │   ├── table/                      # 表格组件
│   │   │   ├── chart/                      # 图表组件
│   │   │   └── workflow/                   # 工作流组件
│   │   ├── layout/                         # 布局组件
│   │   │   ├── components/                 # 布局子组件
│   │   │   └── index.vue                   # 主布局
│   │   ├── router/                         # 路由配置
│   │   │   ├── index.ts                    # 主路由
│   │   │   └── modules/                    # 模块路由
│   │   ├── store/                          # 状态管理
│   │   │   ├── modules/                    # 状态模块
│   │   │   └── index.ts                    # 主store
│   │   ├── utils/                          # 工具类
│   │   │   ├── auth.ts                     # 认证工具
│   │   │   ├── request.ts                  # 请求工具
│   │   │   ├── validate.ts                 # 验证工具
│   │   │   └── common.ts                   # 通用工具
│   │   ├── views/                          # 页面视图
│   │   │   ├── system/                     # 系统管理页面
│   │   │   │   ├── user/                   # 用户管理
│   │   │   │   ├── role/                   # 角色管理
│   │   │   │   ├── dept/                   # 部门管理
│   │   │   │   └── menu/                   # 菜单管理
│   │   │   ├── portal/                     # 门户页面
│   │   │   │   ├── dashboard/              # 工作台
│   │   │   │   ├── notice/                 # 通知公告
│   │   │   │   ├── todo/                   # 待办事项
│   │   │   │   ├── message/                # 站内消息
│   │   │   │   └── contact/                # 通讯录
│   │   │   ├── workflow/                   # 工作流页面
│   │   │   │   ├── design/                 # 流程设计
│   │   │   │   ├── model/                  # 流程模型
│   │   │   │   ├── instance/               # 流程实例
│   │   │   │   └── task/                   # 任务管理
│   │   │   ├── project/                    # 项目管理页面
│   │   │   │   ├── vertical/               # 纵向项目
│   │   │   │   ├── horizontal/             # 横向项目
│   │   │   │   ├── school/                 # 校级项目
│   │   │   │   └── teaching/               # 教学项目
│   │   │   ├── research/                   # 科研管理页面
│   │   │   │   ├── achievement/            # 科研成果
│   │   │   │   ├── application/            # 申报评审
│   │   │   │   ├── evaluation/             # 职称评审
│   │   │   │   └── statistics/             # 统计分析
│   │   │   ├── forum/                      # 龙湖讲坛页面
│   │   │   │   ├── lecture/                # 讲坛管理
│   │   │   │   ├── resource/               # 资源管理
│   │   │   │   └── display/                # 展示页面

│   │   │   ├── login/                      # 登录页面
│   │   │   └── error/                      # 错误页面
│   │   ├── App.vue                         # 根组件
│   │   ├── main.ts                         # 入口文件
│   │   └── vite-env.d.ts                   # 类型声明
│   ├── .env                                # 环境变量
│   ├── .env.development                    # 开发环境变量
│   ├── .env.production                     # 生产环境变量
│   ├── .gitignore                          # Git忽略文件
│   ├── index.html                          # HTML模板
│   ├── package.json                        # 依赖配置
│   ├── tsconfig.json                       # TypeScript配置
│   └── vite.config.ts                      # Vite配置
├── sql/                              # 数据库脚本
│   ├── research_db_schema.sql              # 数据库结构
│   ├── research_db_init_data.sql           # 初始化数据
│   ├── research_db_user_init_data.sql      # 用户初始化数据
│   ├── research_db_dept_enhancement.sql    # 部门增强
│   ├── research_db_post_enhancement.sql    # 岗位增强
│   ├── research_db_personnel_enhancement.sql # 人员增强
│   ├── research_db_project_tables.sql      # 项目管理表
│   ├── research_db_achievement_tables.sql  # 科研成果表
│   ├── research_db_application_tables.sql  # 申报评审表
│   └── research_db_forum_tables.sql        # 龙湖讲坛表
├── docs/                             # 项目文档
│   ├── 需求分析与开发计划.md
│   ├── 项目结构设计.md
│   ├── API接口文档.md
│   ├── 数据库设计文档.md
│   ├── 部署运维文档.md
│   └── 用户操作手册.md
├── scripts/                          # 脚本文件
│   ├── build.sh                           # 构建脚本
│   ├── deploy.sh                          # 部署脚本
│   └── backup.sh                          # 备份脚本
├── docker/                           # Docker配置
│   ├── Dockerfile                         # Docker镜像
│   ├── docker-compose.yml                 # Docker编排
│   └── nginx.conf                         # Nginx配置
├── .gitignore                        # Git忽略文件
├── README.md                         # 项目说明
└── 需求                              # 需求文档
```

## 模块详细设计

### 1. 后端模块设计

#### 1.1 公共模块 (common)
- **annotation**: 自定义注解，如权限控制、日志记录等
- **config**: 配置类，如数据源配置、Redis配置等
- **constant**: 常量定义，如状态码、错误信息等
- **core**: 核心组件，如分页、树形结构等
- **enums**: 枚举类，如用户状态、消息类型等
- **exception**: 异常处理，统一异常处理机制
- **utils**: 工具类，如字符串处理、日期处理等

#### 1.2 框架模块 (framework)
- **security**: Spring Security配置，认证授权
- **datasource**: 数据源配置，支持多数据源
- **web**: Web配置，如跨域、拦截器等
- **aspectj**: AOP切面，如日志记录、权限控制等

#### 1.3 系统管理模块 (system)
- **用户管理**: 用户CRUD、密码管理、状态管理
- **角色管理**: 角色CRUD、权限分配
- **部门管理**: 部门树形结构管理
- **菜单管理**: 菜单树形结构、权限控制
- **字典管理**: 系统字典数据管理
- **参数配置**: 系统参数配置管理
- **日志管理**: 操作日志、登录日志管理

#### 1.4 门户模块 (portal)
- **工作台**: 个人工作台、待办事项、通知公告
- **通知公告**: 公告发布、分类管理、权限控制
- **待办事项**: 待办创建、分配、状态管理
- **站内消息**: 消息发送、接收、分类管理
- **通讯录**: 机构通讯录管理

#### 1.5 工作流模块 (workflow)
- **流程定义**: 流程模型管理、版本控制
- **流程实例**: 流程启动、执行、监控
- **任务管理**: 任务分配、处理、转办
- **流程设计**: 可视化流程设计器

#### 1.6 项目管理模块 (project)
- **纵向项目**: 立项、变更、结项管理
- **横向项目**: 合同管理、变更管理
- **校级项目**: 校级项目全生命周期管理
- **教学项目**: 教学项目管理

#### 1.7 科研管理模块 (research)
- **科研成果**: 成果录入、认定、统计
- **申报评审**: 申报管理、评审流程
- **职称评审**: 职称评审全流程管理
- **数据统计**: 科研数据统计分析

#### 1.8 龙湖讲坛模块 (forum)
- **讲坛管理**: 讲坛申请、审批、管理
- **资源管理**: 音视频资源管理
- **展示管理**: 讲坛展示页面管理

### 2. 前端模块设计

#### 2.1 公共组件 (components)
- **表单组件**: 动态表单、表单验证
- **表格组件**: 数据表格、分页、排序
- **图表组件**: ECharts图表封装
- **上传组件**: 文件上传、图片上传
- **选择器组件**: 用户选择器、部门选择器

#### 2.2 布局组件 (layout)
- **主布局**: 顶部导航、侧边菜单、内容区域
- **头部组件**: 用户信息、消息通知、设置
- **侧边栏**: 菜单导航、权限控制
- **标签页**: 多标签页管理

#### 2.3 页面视图 (views)
- **系统管理**: 用户、角色、部门、菜单管理页面
- **门户页面**: 工作台、通知、待办、消息页面
- **工作流页面**: 流程设计、模型管理、任务处理
- **项目管理**: 各类项目管理页面
- **科研管理**: 科研成果、申报评审页面
- **龙湖讲坛**: 讲坛管理、展示页面

#### 2.4 工具类 (utils)
- **认证工具**: Token管理、权限验证
- **请求工具**: HTTP请求封装、拦截器
- **验证工具**: 表单验证规则
- **通用工具**: 日期处理、字符串处理等

### 3. 数据库设计

#### 3.1 系统管理表
- **sys_user**: 用户表
- **sys_role**: 角色表
- **sys_dept**: 部门表
- **sys_menu**: 菜单表
- **sys_user_role**: 用户角色关联表
- **sys_role_menu**: 角色菜单关联表

#### 3.2 门户相关表
- **sys_notice**: 通知公告表
- **sys_todo**: 待办事项表
- **sys_message**: 站内消息表

#### 3.3 工作流相关表
- **wf_process_definition**: 流程定义表
- **wf_process_instance**: 流程实例表
- **wf_task**: 任务表
- **wf_task_history**: 任务历史表

#### 3.4 项目管理表
- **project_vertical**: 纵向项目表
- **project_horizontal**: 横向项目表
- **project_school**: 校级项目表
- **project_teaching**: 教学项目表

#### 3.5 科研管理表
- **research_achievement**: 科研成果表
- **research_application**: 申报表
- **research_evaluation**: 评审表
- **research_title_review**: 职称评审表

#### 3.6 龙湖讲坛表
- **forum_lecture**: 讲坛表
- **forum_resource**: 资源表
- **forum_record**: 观看记录表

## 开发规范

### 1. 代码规范
- **命名规范**: 遵循Java和JavaScript命名规范
- **注释规范**: 类、方法、重要逻辑必须有注释
- **代码格式**: 统一代码格式，使用IDE格式化工具

### 2. 接口规范
- **RESTful API**: 遵循RESTful设计原则
- **统一响应格式**: 统一的JSON响应格式
- **错误处理**: 统一的错误码和错误信息

### 3. 数据库规范
- **命名规范**: 表名、字段名使用下划线命名
- **字段规范**: 必要字段如创建时间、更新时间等
- **索引规范**: 合理创建索引，提高查询性能

### 4. 安全规范
- **权限控制**: 接口级别的权限控制
- **数据验证**: 输入数据验证和过滤
- **SQL注入防护**: 使用参数化查询
- **XSS防护**: 输出数据转义

## 部署架构

### 1. 开发环境
- **数据库**: MySQL 8.0
- **缓存**: Redis 6.0
- **应用服务器**: 内置Tomcat
- **前端服务器**: Vite开发服务器

### 2. 生产环境
- **负载均衡**: Nginx
- **应用服务器**: Spring Boot内置Tomcat
- **数据库**: MySQL 8.0集群
- **缓存**: Redis集群
- **文件存储**: 本地存储或OSS

### 3. 监控运维
- **应用监控**: Spring Boot Actuator
- **日志收集**: ELK Stack
- **性能监控**: Prometheus + Grafana
- **健康检查**: 自定义健康检查接口

## 总结

本项目结构设计遵循分层架构和模块化设计原则，具有良好的可扩展性和可维护性。通过合理的目录结构和模块划分，可以支持团队协作开发，提高开发效率。

关键设计原则：
1. **分层架构**: 清晰的分层结构，职责分离
2. **模块化设计**: 按业务功能划分模块，降低耦合
3. **统一规范**: 统一的开发规范和接口规范
4. **安全考虑**: 全面的安全防护措施
5. **性能优化**: 合理的缓存和数据库设计

通过这样的项目结构设计，可以确保项目的高质量交付和后续的稳定运行。
