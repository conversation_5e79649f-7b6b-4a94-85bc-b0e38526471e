// Element Plus 样式覆盖

// 修改主题色
:root {
  --el-color-primary: #409EFF;
  --el-color-primary-light-3: #79bbff;
  --el-color-primary-light-5: #a0cfff;
  --el-color-primary-light-7: #c6e2ff;
  --el-color-primary-light-8: #d9ecff;
  --el-color-primary-light-9: #ecf5ff;
  --el-color-primary-dark-2: #337ecc;
}

// 表格样式
.el-table {
  .el-table__header-wrapper {
    th {
      background-color: #f5f7fa;
    }
  }
}

// 分页样式
.el-pagination {
  .el-pagination__total {
    margin-right: 10px;
  }
}

// 对话框样式
.el-dialog {
  .el-dialog__header {
    padding: 20px 20px 10px;
  }
  
  .el-dialog__body {
    padding: 10px 20px 20px;
  }
}

// 表单样式
.el-form {
  .el-form-item__label {
    font-weight: 500;
  }
}

// 按钮样式增强
.el-button {
  // 确保按钮文字始终显示
  font-weight: 500;
  white-space: nowrap;

  // 文字显示修复
  span {
    display: inline !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: inherit !important;
  }

  &.el-button--primary {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
    color: #fff;
  }

  &.el-button--success {
    background-color: var(--el-color-success);
    border-color: var(--el-color-success);
    color: #fff;
  }

  &.el-button--warning {
    background-color: var(--el-color-warning);
    border-color: var(--el-color-warning);
    color: #fff;
  }

  &.el-button--danger {
    background-color: var(--el-color-danger);
    border-color: var(--el-color-danger);
    color: #fff;
  }

  &.el-button--info {
    background-color: var(--el-color-info);
    border-color: var(--el-color-info);
    color: #fff;
  }

  // 链接按钮样式
  &.is-link {
    font-weight: 500;

    &.el-button--primary {
      color: var(--el-color-primary);
    }

    &.el-button--success {
      color: var(--el-color-success);
    }

    &.el-button--warning {
      color: var(--el-color-warning);
    }

    &.el-button--danger {
      color: var(--el-color-danger);
    }

    &.el-button--info {
      color: var(--el-color-info);
    }
  }
}

// 菜单样式
.el-menu {
  border-right: none;
  
  .el-menu-item {
    &.is-active {
      background-color: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
    }
  }
  
  .el-sub-menu__title {
    &:hover {
      background-color: var(--el-color-primary-light-9);
    }
  }
}
