package com.research.common.utils;

import java.util.UUID;

/**
 * ID生成器工具类
 * 
 * <AUTHOR>
 */
public class IdUtils {
    
    /**
     * 获取随机UUID
     * 
     * @return 随机UUID
     */
    public static String randomUUID() {
        return UUID.randomUUID().toString();
    }
    
    /**
     * 简化的UUID，去掉"-"
     * 
     * @return 简化的UUID
     */
    public static String simpleUUID() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }
    
    /**
     * 获取随机UUID，使用性能更好的算法
     * 
     * @return 随机UUID
     */
    public static String fastUUID() {
        return UUID.randomUUID().toString();
    }
    
    /**
     * 简化的UUID，去掉"-"，使用性能更好的算法
     * 
     * @return 简化的UUID
     */
    public static String fastSimpleUUID() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }
}
