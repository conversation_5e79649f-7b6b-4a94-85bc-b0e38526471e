package com.research.common.utils.redis;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 内存缓存实现（当Redis不可用时使用）
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnMissingBean(RedisTemplate.class)
public class MemoryCache {

    private final ConcurrentHashMap<String, CacheItem> cache = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    public MemoryCache() {
        // 每分钟清理一次过期的缓存
        scheduler.scheduleAtFixedRate(this::cleanExpiredItems, 1, 1, TimeUnit.MINUTES);
    }

    /**
     * 缓存基本的对象
     *
     * @param key 缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value) {
        cache.put(key, new CacheItem(value, -1));
    }

    /**
     * 缓存基本的对象
     *
     * @param key 缓存的键值
     * @param value 缓存的值
     * @param timeout 时间
     * @param timeUnit 时间颗粒度
     */
    public <T> void setCacheObject(final String key, final T value, final Integer timeout, final TimeUnit timeUnit) {
        long expireTime = System.currentTimeMillis() + timeUnit.toMillis(timeout);
        cache.put(key, new CacheItem(value, expireTime));
    }

    /**
     * 设置有效时间
     *
     * @param key Redis键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout) {
        return expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置有效时间
     *
     * @param key Redis键
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout, final TimeUnit unit) {
        CacheItem item = cache.get(key);
        if (item != null) {
            long expireTime = System.currentTimeMillis() + unit.toMillis(timeout);
            item.expireTime = expireTime;
            return true;
        }
        return false;
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    @SuppressWarnings("unchecked")
    public <T> T getCacheObject(final String key) {
        CacheItem item = cache.get(key);
        if (item != null) {
            if (item.expireTime == -1 || item.expireTime > System.currentTimeMillis()) {
                return (T) item.value;
            } else {
                cache.remove(key);
            }
        }
        return null;
    }

    /**
     * 删除单个对象
     *
     * @param key 缓存键
     */
    public boolean deleteObject(final String key) {
        return cache.remove(key) != null;
    }

    /**
     * 判断key是否存在
     *
     * @param key 缓存键
     * @return true=存在；false=不存在
     */
    public boolean hasKey(final String key) {
        CacheItem item = cache.get(key);
        if (item != null) {
            if (item.expireTime == -1 || item.expireTime > System.currentTimeMillis()) {
                return true;
            } else {
                cache.remove(key);
            }
        }
        return false;
    }

    /**
     * 获取剩余过期时间
     *
     * @param key 缓存键
     * @return 剩余时间（秒）
     */
    public long getExpire(final String key) {
        CacheItem item = cache.get(key);
        if (item != null && item.expireTime != -1) {
            long remaining = item.expireTime - System.currentTimeMillis();
            return remaining > 0 ? remaining / 1000 : 0;
        }
        return -1;
    }

    /**
     * 清理过期的缓存项
     */
    private void cleanExpiredItems() {
        long currentTime = System.currentTimeMillis();
        cache.entrySet().removeIf(entry -> {
            CacheItem item = entry.getValue();
            return item.expireTime != -1 && item.expireTime <= currentTime;
        });
    }

    /**
     * 缓存项
     */
    private static class CacheItem {
        Object value;
        long expireTime; // -1表示永不过期

        CacheItem(Object value, long expireTime) {
            this.value = value;
            this.expireTime = expireTime;
        }
    }
}
