<template>
  <div class="todo-detail">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>待办详情</span>
          <div>
            <el-button v-if="todoInfo.status === '0'" type="primary" @click="markAsCompleted">
              标记完成
            </el-button>
            <el-button @click="goBack">返回</el-button>
          </div>
        </div>
      </template>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="待办标题">
          {{ todoInfo.title }}
        </el-descriptions-item>
        <el-descriptions-item label="优先级">
          <el-tag :type="getPriorityTag(todoInfo.priority)">
            {{ getPriorityText(todoInfo.priority) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusTag(todoInfo.status)">
            {{ getStatusText(todoInfo.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="截止时间">
          {{ todoInfo.dueTime }}
        </el-descriptions-item>
        <el-descriptions-item label="分配给">
          {{ todoInfo.assigneeName }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ todoInfo.createTime }}
        </el-descriptions-item>
      </el-descriptions>
      
      <el-divider content-position="left">待办内容</el-divider>
      
      <div class="todo-content">
        {{ todoInfo.content }}
      </div>
      
      <el-divider content-position="left" v-if="todoInfo.remark">备注</el-divider>
      
      <div class="todo-remark" v-if="todoInfo.remark">
        {{ todoInfo.remark }}
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

const route = useRoute()
const router = useRouter()

const todoInfo = ref({
  todoId: '',
  title: '',
  content: '',
  priority: '',
  status: '',
  dueTime: '',
  assigneeName: '',
  createTime: '',
  remark: ''
})

const getPriorityText = (priority: string) => {
  const priorityMap: Record<string, string> = {
    '1': '低',
    '2': '中',
    '3': '高'
  }
  return priorityMap[priority] || '未知'
}

const getPriorityTag = (priority: string) => {
  const tagMap: Record<string, string> = {
    '1': 'info',
    '2': 'warning',
    '3': 'danger'
  }
  return tagMap[priority] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '0': '待处理',
    '1': '进行中',
    '2': '已完成',
    '3': '已取消'
  }
  return statusMap[status] || '未知'
}

const getStatusTag = (status: string) => {
  const tagMap: Record<string, string> = {
    '0': 'warning',
    '1': 'primary',
    '2': 'success',
    '3': 'info'
  }
  return tagMap[status] || 'info'
}

const markAsCompleted = async () => {
  try {
    await ElMessageBox.confirm('确认要标记此待办为已完成吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // TODO: 调用API标记完成
    todoInfo.value.status = '2'
    ElMessage.success('标记完成成功')
  } catch {
    // 用户取消操作
  }
}

const goBack = () => {
  router.back()
}

onMounted(() => {
  const todoId = route.params.id
  if (todoId) {
    // TODO: 调用API获取待办详情
    console.log('获取待办详情:', todoId)
  }
})
</script>

<style scoped>
.todo-detail {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.todo-content,
.todo-remark {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
  line-height: 1.6;
  min-height: 100px;
}

.todo-remark {
  margin-top: 10px;
}
</style>
