-- =============================================
-- 工作流权限检查和验证SQL
-- 用于检查系统中所有工作流权限的配置情况
-- =============================================

-- =============================================
-- 1. 检查所有工作流权限标识
-- =============================================
SELECT 
    '工作流权限统计' as '检查项目',
    COUNT(*) as '权限数量'
FROM sys_menu 
WHERE perms LIKE 'workflow:%' AND menu_type = 'F';

-- =============================================
-- 2. 按模块分组统计权限
-- =============================================
SELECT 
    CASE 
        WHEN perms LIKE 'workflow:designer:%' THEN '流程设计器'
        WHEN perms LIKE 'workflow:process:%' THEN '流程管理'
        WHEN perms LIKE 'workflow:task:%' THEN '任务管理'
        WHEN perms LIKE 'workflow:version:%' THEN '版本管理'
        WHEN perms LIKE 'workflow:monitor:%' THEN '监控大屏'
        WHEN perms LIKE 'workflow:definition:%' THEN '流程定义'
        WHEN perms LIKE 'workflow:instance:%' THEN '流程实例'
        ELSE '其他'
    END as '权限模块',
    COUNT(*) as '权限数量',
    GROUP_CONCAT(perms ORDER BY perms SEPARATOR ', ') as '权限列表'
FROM sys_menu 
WHERE perms LIKE 'workflow:%' AND menu_type = 'F'
GROUP BY 
    CASE 
        WHEN perms LIKE 'workflow:designer:%' THEN '流程设计器'
        WHEN perms LIKE 'workflow:process:%' THEN '流程管理'
        WHEN perms LIKE 'workflow:task:%' THEN '任务管理'
        WHEN perms LIKE 'workflow:version:%' THEN '版本管理'
        WHEN perms LIKE 'workflow:monitor:%' THEN '监控大屏'
        WHEN perms LIKE 'workflow:definition:%' THEN '流程定义'
        WHEN perms LIKE 'workflow:instance:%' THEN '流程实例'
        ELSE '其他'
    END
ORDER BY '权限模块';

-- =============================================
-- 3. 检查具体的权限配置
-- =============================================
SELECT 
    menu_id as '菜单ID',
    menu_name as '权限名称',
    parent_id as '父菜单',
    perms as '权限标识',
    status as '状态',
    visible as '可见性',
    remark as '备注'
FROM sys_menu 
WHERE perms LIKE 'workflow:%' AND menu_type = 'F'
ORDER BY parent_id, order_num;

-- =============================================
-- 4. 检查角色权限分配情况
-- =============================================
SELECT 
    r.role_id as '角色ID',
    r.role_name as '角色名称',
    r.role_key as '角色标识',
    COUNT(rm.menu_id) as '工作流权限数量',
    GROUP_CONCAT(m.perms ORDER BY m.perms SEPARATOR ', ') as '权限列表'
FROM sys_role r
LEFT JOIN sys_role_menu rm ON r.role_id = rm.role_id
LEFT JOIN sys_menu m ON rm.menu_id = m.menu_id AND m.perms LIKE 'workflow:%'
WHERE r.status = '0'
GROUP BY r.role_id, r.role_name, r.role_key
ORDER BY r.role_id;

-- =============================================
-- 5. 检查用户的工作流权限
-- =============================================
SELECT 
    u.user_id as '用户ID',
    u.user_name as '用户名',
    u.nick_name as '昵称',
    GROUP_CONCAT(DISTINCT r.role_name ORDER BY r.role_name SEPARATOR ', ') as '角色列表',
    COUNT(DISTINCT m.menu_id) as '工作流权限数量'
FROM sys_user u
LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
LEFT JOIN sys_role r ON ur.role_id = r.role_id AND r.status = '0'
LEFT JOIN sys_role_menu rm ON r.role_id = rm.role_id
LEFT JOIN sys_menu m ON rm.menu_id = m.menu_id AND m.perms LIKE 'workflow:%'
WHERE u.status = '0'
GROUP BY u.user_id, u.user_name, u.nick_name
ORDER BY u.user_id;

-- =============================================
-- 6. 检查缺失的权限（与代码中的@PreAuthorize对比）
-- =============================================
-- 创建临时表存储代码中的权限
CREATE TEMPORARY TABLE temp_code_permissions (
    permission_code VARCHAR(100) PRIMARY KEY,
    description VARCHAR(200)
);

-- 插入代码中发现的权限
INSERT INTO temp_code_permissions VALUES
-- 流程设计器权限
('workflow:designer:view', '查看流程设计器'),
('workflow:designer:create', '创建流程模型'),
('workflow:designer:edit', '编辑流程模型'),
('workflow:designer:delete', '删除流程模型'),
('workflow:designer:save', '保存流程模型'),
('workflow:designer:deploy', '部署流程模型'),
('workflow:designer:import', '导入流程模型'),
('workflow:designer:export', '导出流程模型'),
('workflow:designer:publish', '发布流程模型'),

-- 流程管理权限
('workflow:process:query', '查询流程定义'),
('workflow:process:list', '查看流程列表'),
('workflow:process:start', '启动流程实例'),
('workflow:process:suspend', '挂起流程定义'),
('workflow:process:activate', '激活流程定义'),
('workflow:process:delete', '删除流程定义'),
('workflow:process:instance', '查看流程实例'),
('workflow:process:history', '查看流程历史'),
('workflow:process:deploy', '部署流程定义'),

-- 任务管理权限
('workflow:task:query', '查询任务信息'),
('workflow:task:list', '查看任务列表'),
('workflow:task:claim', '签收任务'),
('workflow:task:complete', '完成任务'),
('workflow:task:assign', '转办任务'),
('workflow:task:delegate', '委派任务'),
('workflow:task:history', '查看任务历史'),
('workflow:task:detail', '查看任务详情'),

-- 版本管理权限
('workflow:version:query', '查询版本信息'),
('workflow:version:list', '查看版本列表'),
('workflow:version:switch', '切换流程版本'),
('workflow:version:remove', '删除流程版本'),
('workflow:version:compare', '比较流程版本'),

-- 监控大屏权限
('workflow:monitor:view', '查看监控大屏'),
('workflow:monitor:statistics', '查看统计分析'),
('workflow:monitor:performance', '查看性能监控'),
('workflow:monitor:export', '导出监控报表'),
('workflow:monitor:realtime', '实时监控数据'),

-- 流程定义权限
('workflow:definition:query', '查询流程定义'),
('workflow:definition:list', '查看定义列表'),
('workflow:definition:detail', '查看定义详情'),

-- 流程实例权限
('workflow:instance:query', '查询流程实例'),
('workflow:instance:list', '查看实例列表'),
('workflow:instance:detail', '查看实例详情'),
('workflow:instance:suspend', '挂起流程实例'),
('workflow:instance:activate', '激活流程实例'),
('workflow:instance:delete', '删除流程实例');

-- 检查数据库中缺失的权限
SELECT 
    tcp.permission_code as '缺失的权限标识',
    tcp.description as '权限描述'
FROM temp_code_permissions tcp
LEFT JOIN sys_menu sm ON tcp.permission_code = sm.perms
WHERE sm.perms IS NULL
ORDER BY tcp.permission_code;

-- 检查数据库中多余的权限
SELECT 
    sm.perms as '多余的权限标识',
    sm.menu_name as '权限名称'
FROM sys_menu sm
LEFT JOIN temp_code_permissions tcp ON sm.perms = tcp.permission_code
WHERE sm.perms LIKE 'workflow:%' 
  AND sm.menu_type = 'F' 
  AND tcp.permission_code IS NULL
ORDER BY sm.perms;

-- 清理临时表
DROP TEMPORARY TABLE temp_code_permissions;

-- =============================================
-- 7. 权限配置建议
-- =============================================
SELECT 
    '权限配置检查完成' as '检查结果',
    '请根据上述检查结果调整权限配置' as '建议';

-- =============================================
-- 8. 生成权限配置报告
-- =============================================
SELECT 
    '工作流权限配置报告' as '报告标题',
    NOW() as '生成时间',
    (SELECT COUNT(*) FROM sys_menu WHERE perms LIKE 'workflow:%' AND menu_type = 'F') as '总权限数量',
    (SELECT COUNT(DISTINCT role_id) FROM sys_role_menu rm JOIN sys_menu m ON rm.menu_id = m.menu_id WHERE m.perms LIKE 'workflow:%') as '已分配角色数量',
    (SELECT COUNT(DISTINCT user_id) FROM sys_user_role ur JOIN sys_role_menu rm ON ur.role_id = rm.role_id JOIN sys_menu m ON rm.menu_id = m.menu_id WHERE m.perms LIKE 'workflow:%') as '拥有权限用户数量';
