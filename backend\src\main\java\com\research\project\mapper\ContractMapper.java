package com.research.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.research.project.domain.entity.Contract;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 合同Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@Mapper
public interface ContractMapper extends BaseMapper<Contract> {

    /**
     * 查询合同列表
     * 
     * @param contract 合同
     * @return 合同集合
     */
    List<Contract> selectContractList(Contract contract);

    /**
     * 根据ID查询合同
     * 
     * @param id 合同ID
     * @return 合同
     */
    Contract selectContractById(Long id);

    /**
     * 新增合同
     * 
     * @param contract 合同
     * @return 结果
     */
    int insertContract(Contract contract);

    /**
     * 修改合同
     * 
     * @param contract 合同
     * @return 结果
     */
    int updateContract(Contract contract);

    /**
     * 删除合同
     * 
     * @param id 合同ID
     * @return 结果
     */
    int deleteContractById(Long id);

    /**
     * 批量删除合同
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteContractByIds(Long[] ids);

    /**
     * 根据合同编号查询合同
     * 
     * @param contractNo 合同编号
     * @return 合同
     */
    Contract selectContractByNo(@Param("contractNo") String contractNo);

    /**
     * 检查合同编号是否存在
     * 
     * @param contractNo 合同编号
     * @param id 合同ID（排除自身）
     * @return 数量
     */
    int checkContractNoExists(@Param("contractNo") String contractNo, @Param("id") Long id);

    /**
     * 根据合作单位ID查询合同列表
     * 
     * @param partnerId 合作单位ID
     * @return 合同列表
     */
    List<Contract> selectContractsByPartnerId(@Param("partnerId") Long partnerId);

    /**
     * 根据负责人ID查询合同列表
     * 
     * @param principalId 负责人ID
     * @return 合同列表
     */
    List<Contract> selectContractsByPrincipalId(@Param("principalId") Long principalId);

    /**
     * 根据部门ID查询合同列表
     * 
     * @param deptId 部门ID
     * @return 合同列表
     */
    List<Contract> selectContractsByDeptId(@Param("deptId") Long deptId);

    /**
     * 根据状态查询合同列表
     * 
     * @param status 合同状态
     * @return 合同列表
     */
    List<Contract> selectContractsByStatus(@Param("status") Integer status);

    /**
     * 查询即将到期的合同
     * 
     * @param days 天数
     * @return 合同列表
     */
    List<Contract> selectExpiringContracts(@Param("days") Integer days);

    /**
     * 查询已逾期的合同
     * 
     * @return 合同列表
     */
    List<Contract> selectOverdueContracts();

    /**
     * 查询待审核的合同
     * 
     * @return 合同列表
     */
    List<Contract> selectPendingContracts();

    /**
     * 搜索合同
     * 
     * @param keyword 关键词
     * @return 合同列表
     */
    List<Contract> searchContracts(@Param("keyword") String keyword);

    /**
     * 查询合同统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> selectContractStatistics();

    /**
     * 查询合同类型统计
     * 
     * @return 类型统计列表
     */
    List<Map<String, Object>> selectTypeStatistics();

    /**
     * 查询合同状态统计
     * 
     * @return 状态统计列表
     */
    List<Map<String, Object>> selectStatusStatistics();

    /**
     * 查询年度合同统计
     * 
     * @param year 年份
     * @return 年度统计列表
     */
    List<Map<String, Object>> selectYearlyStatistics(@Param("year") Integer year);

    /**
     * 查询月度合同统计
     * 
     * @param year 年份
     * @param month 月份
     * @return 月度统计列表
     */
    List<Map<String, Object>> selectMonthlyStatistics(@Param("year") Integer year, @Param("month") Integer month);

    /**
     * 查询合同金额统计
     * 
     * @return 金额统计信息
     */
    Map<String, Object> selectAmountStatistics();

    /**
     * 查询部门合同统计
     * 
     * @return 部门统计列表
     */
    List<Map<String, Object>> selectDeptStatistics();

    /**
     * 查询合作单位合同统计
     * 
     * @return 合作单位统计列表
     */
    List<Map<String, Object>> selectPartnerStatistics();

    /**
     * 更新合同状态
     * 
     * @param id 合同ID
     * @param status 状态
     * @return 结果
     */
    int updateContractStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 批量更新合同状态
     * 
     * @param ids 合同ID数组
     * @param status 状态
     * @return 结果
     */
    int batchUpdateContractStatus(@Param("ids") Long[] ids, @Param("status") Integer status);

    /**
     * 根据模板ID查询合同数量
     * 
     * @param templateId 模板ID
     * @return 数量
     */
    int countContractsByTemplateId(@Param("templateId") Long templateId);

    /**
     * 查询合同签署趋势
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 趋势数据
     */
    List<Map<String, Object>> selectSigningTrend(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 查询合同执行情况
     * 
     * @return 执行情况统计
     */
    List<Map<String, Object>> selectExecutionStatistics();
}
