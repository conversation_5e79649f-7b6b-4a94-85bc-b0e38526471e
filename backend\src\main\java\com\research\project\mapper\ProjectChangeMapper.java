package com.research.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.research.project.domain.entity.ProjectChange;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 项目变更Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Mapper
public interface ProjectChangeMapper extends BaseMapper<ProjectChange> {

    /**
     * 查询项目变更列表
     * 
     * @param projectChange 项目变更
     * @return 项目变更集合
     */
    List<ProjectChange> selectProjectChangeList(ProjectChange projectChange);

    /**
     * 根据项目ID查询变更列表
     * 
     * @param projectId 项目ID
     * @return 变更列表
     */
    @Select("SELECT * FROM project_change WHERE project_id = #{projectId} ORDER BY create_time DESC")
    List<ProjectChange> selectByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据变更编号查询变更
     * 
     * @param changeNo 变更编号
     * @return 项目变更
     */
    @Select("SELECT * FROM project_change WHERE change_no = #{changeNo}")
    ProjectChange selectByChangeNo(@Param("changeNo") String changeNo);

    /**
     * 根据申请人ID查询变更列表
     * 
     * @param applicantId 申请人ID
     * @return 变更列表
     */
    @Select("SELECT * FROM project_change WHERE applicant_id = #{applicantId} ORDER BY create_time DESC")
    List<ProjectChange> selectByApplicantId(@Param("applicantId") Long applicantId);

    /**
     * 根据变更状态查询变更列表
     * 
     * @param status 变更状态
     * @return 变更列表
     */
    @Select("SELECT * FROM project_change WHERE status = #{status} ORDER BY create_time DESC")
    List<ProjectChange> selectByStatus(@Param("status") Integer status);

    /**
     * 根据变更类型查询变更列表
     * 
     * @param changeType 变更类型
     * @return 变更列表
     */
    @Select("SELECT * FROM project_change WHERE change_type = #{changeType} ORDER BY create_time DESC")
    List<ProjectChange> selectByChangeType(@Param("changeType") Integer changeType);

    /**
     * 查询待审核的变更
     * 
     * @param reviewerId 审核人ID
     * @return 变更列表
     */
    @Select("SELECT * FROM project_change WHERE status = 1 AND reviewer_id = #{reviewerId} ORDER BY create_time ASC")
    List<ProjectChange> selectPendingReview(@Param("reviewerId") Long reviewerId);

    /**
     * 查询待批准的变更
     * 
     * @param approverId 批准人ID
     * @return 变更列表
     */
    @Select("SELECT * FROM project_change WHERE status = 1 AND approver_id = #{approverId} ORDER BY create_time ASC")
    List<ProjectChange> selectPendingApproval(@Param("approverId") Long approverId);

    /**
     * 查询紧急变更
     * 
     * @param urgency 紧急程度
     * @return 变更列表
     */
    @Select("SELECT * FROM project_change WHERE urgency >= #{urgency} AND status IN (0, 1) ORDER BY urgency DESC, create_time ASC")
    List<ProjectChange> selectUrgentChanges(@Param("urgency") Integer urgency);

    /**
     * 统计项目变更数量
     * 
     * @param projectId 项目ID
     * @return 变更数量
     */
    @Select("SELECT COUNT(*) FROM project_change WHERE project_id = #{projectId}")
    Integer countByProjectId(@Param("projectId") Long projectId);

    /**
     * 查询变更统计信息
     * 
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_count, " +
            "SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as applying_count, " +
            "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as reviewing_count, " +
            "SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as approved_count, " +
            "SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as rejected_count, " +
            "SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as cancelled_count " +
            "FROM project_change")
    Map<String, Object> selectChangeStatistics();

    /**
     * 查询变更类型统计
     * 
     * @return 类型统计
     */
    @Select("SELECT " +
            "change_type, " +
            "change_type_name, " +
            "COUNT(*) as change_count " +
            "FROM project_change " +
            "GROUP BY change_type, change_type_name " +
            "ORDER BY change_count DESC")
    List<Map<String, Object>> selectTypeStatistics();

    /**
     * 查询变更状态统计
     * 
     * @return 状态统计
     */
    @Select("SELECT " +
            "status, " +
            "COUNT(*) as change_count " +
            "FROM project_change " +
            "GROUP BY status " +
            "ORDER BY status")
    List<Map<String, Object>> selectStatusStatistics();

    /**
     * 查询月度变更统计
     * 
     * @param year 年份
     * @return 月度统计
     */
    @Select("SELECT " +
            "YEAR(create_time) as year, " +
            "MONTH(create_time) as month, " +
            "COUNT(*) as change_count, " +
            "SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as approved_count " +
            "FROM project_change " +
            "WHERE YEAR(create_time) = #{year} " +
            "GROUP BY YEAR(create_time), MONTH(create_time) " +
            "ORDER BY month")
    List<Map<String, Object>> selectMonthlyStatistics(@Param("year") Integer year);

    /**
     * 查询变更处理时长统计
     * 
     * @return 处理时长统计
     */
    @Select("SELECT " +
            "AVG(DATEDIFF(approve_time, apply_time)) as avg_processing_days, " +
            "MAX(DATEDIFF(approve_time, apply_time)) as max_processing_days, " +
            "MIN(DATEDIFF(approve_time, apply_time)) as min_processing_days " +
            "FROM project_change " +
            "WHERE status = 2 AND approve_time IS NOT NULL AND apply_time IS NOT NULL")
    Map<String, Object> selectProcessingTimeStatistics();

    /**
     * 查询超时未处理的变更
     * 
     * @param days 超时天数
     * @return 变更列表
     */
    @Select("SELECT * FROM project_change " +
            "WHERE status IN (0, 1) " +
            "AND DATEDIFF(NOW(), create_time) > #{days} " +
            "ORDER BY create_time ASC")
    List<ProjectChange> selectOverdueChanges(@Param("days") Integer days);

    /**
     * 查询需要重新评审的变更
     * 
     * @return 变更列表
     */
    @Select("SELECT * FROM project_change WHERE need_review = 1 AND status = 2 ORDER BY approve_time DESC")
    List<ProjectChange> selectNeedReviewChanges();

    /**
     * 根据工作流实例ID查询变更
     * 
     * @param processInstanceId 工作流实例ID
     * @return 项目变更
     */
    @Select("SELECT * FROM project_change WHERE process_instance_id = #{processInstanceId}")
    ProjectChange selectByProcessInstanceId(@Param("processInstanceId") String processInstanceId);

    /**
     * 查询申请人变更统计
     * 
     * @param limit 限制数量
     * @return 申请人统计
     */
    @Select("SELECT " +
            "applicant_id, " +
            "applicant_name, " +
            "COUNT(*) as change_count, " +
            "SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as approved_count " +
            "FROM project_change " +
            "GROUP BY applicant_id, applicant_name " +
            "ORDER BY change_count DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> selectApplicantStatistics(@Param("limit") Integer limit);
}
