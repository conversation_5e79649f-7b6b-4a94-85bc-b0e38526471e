package com.research.workflow.controller;

import com.research.common.annotation.Log;
import com.research.common.constant.BusinessType;
import com.research.common.core.controller.BaseController;
import com.research.common.core.domain.AjaxResult;
import com.research.common.core.page.TableDataInfo;
import com.research.workflow.domain.ProcessDefinition;
import com.research.workflow.service.ProcessDefinitionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 流程定义Controller
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@RestController
@RequestMapping("/workflow/definition")
public class ProcessDefinitionController extends BaseController {
    
    @Autowired
    private ProcessDefinitionService processDefinitionService;

    /**
     * 查询流程定义列表
     */
    @PreAuthorize("@ss.hasPermi('workflow:definition:list')")
    @GetMapping("/list")
    public AjaxResult list(ProcessDefinition processDefinition) {
        List<ProcessDefinition> list = processDefinitionService.selectProcessDefinitionList(processDefinition);
        return success(list);
    }

    /**
     * 导出流程定义列表
     */
    @PreAuthorize("@ss.hasPermi('workflow:definition:export')")
    @Log(title = "流程定义", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProcessDefinition processDefinition) {
        List<ProcessDefinition> list = processDefinitionService.selectProcessDefinitionList(processDefinition);
        // TODO: 实现导出功能
    }

    /**
     * 获取流程定义详细信息
     */
    @PreAuthorize("@ss.hasPermi('workflow:definition:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(processDefinitionService.selectProcessDefinitionById(id));
    }

    /**
     * 新增流程定义
     */
    @PreAuthorize("@ss.hasPermi('workflow:definition:add')")
    @Log(title = "流程定义", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProcessDefinition processDefinition) {
        return toAjax(processDefinitionService.insertProcessDefinition(processDefinition));
    }

    /**
     * 修改流程定义
     */
    @PreAuthorize("@ss.hasPermi('workflow:definition:edit')")
    @Log(title = "流程定义", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProcessDefinition processDefinition) {
        return toAjax(processDefinitionService.updateProcessDefinition(processDefinition));
    }

    /**
     * 删除流程定义
     */
    @PreAuthorize("@ss.hasPermi('workflow:definition:remove')")
    @Log(title = "流程定义", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(processDefinitionService.deleteProcessDefinitionByIds(ids));
    }

    /**
     * 部署流程定义
     */
    @PreAuthorize("@ss.hasPermi('workflow:definition:deploy')")
    @Log(title = "流程定义", businessType = BusinessType.OTHER)
    @PostMapping("/deploy/{id}")
    public AjaxResult deploy(@PathVariable Long id) {
        try {
            processDefinitionService.deployProcessDefinition(id);
            return success("流程部署成功");
        } catch (Exception e) {
            return error("流程部署失败: " + e.getMessage());
        }
    }

    /**
     * 激活/挂起流程定义
     */
    @PreAuthorize("@ss.hasPermi('workflow:definition:status')")
    @Log(title = "流程定义", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{id}")
    public AjaxResult changeStatus(@PathVariable Long id, @RequestParam String status) {
        try {
            processDefinitionService.changeProcessDefinitionStatus(id, status);
            return success("状态更新成功");
        } catch (Exception e) {
            return error("状态更新失败: " + e.getMessage());
        }
    }

    /**
     * 获取流程定义XML
     */
    @PreAuthorize("@ss.hasPermi('workflow:definition:query')")
    @GetMapping("/xml/{id}")
    public AjaxResult getProcessXml(@PathVariable Long id) {
        try {
            String xml = processDefinitionService.getProcessDefinitionXml(id);
            return success(xml);
        } catch (Exception e) {
            return error("获取流程XML失败: " + e.getMessage());
        }
    }

    /**
     * 获取流程定义图片
     */
    @PreAuthorize("@ss.hasPermi('workflow:definition:query')")
    @GetMapping("/image/{id}")
    public AjaxResult getProcessImage(@PathVariable Long id) {
        try {
            String imageBase64 = processDefinitionService.getProcessDefinitionImage(id);
            return success(imageBase64);
        } catch (Exception e) {
            return error("获取流程图片失败: " + e.getMessage());
        }
    }
}
