<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-code">401</div>
      <div class="error-message">访问被拒绝</div>
      <div class="error-description">抱歉，您没有权限访问此页面</div>
      <el-button type="primary" @click="goHome">返回首页</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}
</script>

<style lang="scss" scoped>
.error-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.error-content {
  text-align: center;
  
  .error-code {
    font-size: 120px;
    font-weight: bold;
    color: #f56c6c;
    margin-bottom: 20px;
  }
  
  .error-message {
    font-size: 24px;
    color: #333;
    margin-bottom: 10px;
  }
  
  .error-description {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
  }
}
</style>
