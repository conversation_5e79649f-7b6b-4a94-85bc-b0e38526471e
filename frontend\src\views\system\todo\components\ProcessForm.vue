<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
    <el-form-item label="处理状态" prop="status">
      <el-radio-group v-model="form.status">
        <el-radio label="1">处理中</el-radio>
        <el-radio label="2">已完成</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="处理结果" prop="result">
      <el-input
        v-model="form.result"
        type="textarea"
        :rows="4"
        placeholder="请输入处理结果"
      />
    </el-form-item>
    
    <div class="form-actions">
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
      <el-button @click="handleCancel">取 消</el-button>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { processTodo } from '@/api/system/todo'

// Props
interface Props {
  todoId: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits(['submit', 'cancel'])

// 响应式数据
const formRef = ref()
const form = reactive({
  status: '1',
  result: ''
})

// 表单验证规则
const rules = {
  status: [
    { required: true, message: '请选择处理状态', trigger: 'change' }
  ],
  result: [
    { required: true, message: '请输入处理结果', trigger: 'blur' }
  ]
}

// 提交表单
const handleSubmit = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        await processTodo(props.todoId, form.status, form.result)
        ElMessage.success('处理成功')
        emit('submit')
      } catch (error) {
        console.error('处理失败:', error)
        ElMessage.error('处理失败')
      }
    } else {
      ElMessage.error('请检查表单输入')
    }
  })
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.form-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}
</style>
