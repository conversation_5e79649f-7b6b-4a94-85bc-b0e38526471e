import router from './router'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
import { useUserStore } from '@/store/modules/user'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/404', '/401'] // 不重定向白名单

router.beforeEach(async (to, from, next) => {
  NProgress.start()

  const hasToken = getToken()

  if (hasToken) {
    if (to.path === '/login') {
      // 如果已登录，重定向到首页
      next({ path: '/workspace' })
      NProgress.done()
    } else {
      // 检查是否已获取用户信息
      const userStore = useUserStore()
      if (!userStore.userInfo.userId) {
        try {
          console.log('首次加载，获取用户信息和菜单数据...')
          // 获取用户信息和菜单数据
          await userStore.getUserInfo()
          console.log('用户信息获取成功，继续导航到:', to.path)
          // 用户信息获取成功，直接继续导航，不使用replace
          next()
        } catch (error) {
          console.error('获取用户信息失败:', error)
          // 获取用户信息失败，跳转到登录页
          userStore.resetUserInfo()
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      } else {
        // 用户信息已存在，直接继续
        next()
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      // 其他没有访问权限的页面将重定向到登录页面
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
