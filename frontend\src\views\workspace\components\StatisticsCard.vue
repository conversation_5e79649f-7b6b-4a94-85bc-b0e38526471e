<template>
  <el-card class="statistics-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span class="card-title">数据统计</span>
        <el-button type="text" @click="refreshData">
          <el-icon><Refresh /></el-icon>
        </el-button>
      </div>
    </template>
    
    <div class="statistics-content">
      <!-- 总览数据 -->
      <div class="overview-stats">
        <div class="stat-card todo">
          <div class="stat-icon">
            <el-icon><Tickets /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ statistics.todoTotal || 0 }}</div>
            <div class="stat-label">待办总数</div>
            <div class="stat-detail">
              <span class="pending">待处理: {{ statistics.todoPending || 0 }}</span>
              <span class="completed">已完成: {{ statistics.todoCompleted || 0 }}</span>
            </div>
          </div>
        </div>
        
        <div class="stat-card notice">
          <div class="stat-icon">
            <el-icon><Bell /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ statistics.unreadNotices || 0 }}</div>
            <div class="stat-label">未读公告</div>
          </div>
        </div>
        
        <div class="stat-card message">
          <div class="stat-icon">
            <el-icon><ChatDotRound /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ statistics.unreadMessages || 0 }}</div>
            <div class="stat-label">未读消息</div>
          </div>
        </div>
      </div>
      
      <!-- 图表区域 -->
      <div class="chart-area">
        <div class="chart-tabs">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane label="待办趋势" name="todo">
              <div class="chart-container">
                <div ref="todoChartRef" class="chart"></div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="完成率" name="completion">
              <div class="chart-container">
                <div ref="completionChartRef" class="chart"></div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      
      <!-- 快速链接 -->
      <div class="quick-links">
        <el-button type="text" @click="goToTodo">
          <el-icon><Tickets /></el-icon>
          查看待办
        </el-button>
        <el-button type="text" @click="goToNotice">
          <el-icon><Bell /></el-icon>
          查看公告
        </el-button>
        <el-button type="text" @click="goToMessage">
          <el-icon><ChatDotRound /></el-icon>
          查看消息
        </el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { Refresh, Tickets, Bell, ChatDotRound } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// Props
interface Props {
  statistics: any
}

const props = defineProps<Props>()

const router = useRouter()

// 响应式数据
const activeTab = ref('todo')
const todoChartRef = ref<HTMLElement>()
const completionChartRef = ref<HTMLElement>()
let todoChart: echarts.ECharts | null = null
let completionChart: echarts.ECharts | null = null

// 刷新数据
const refreshData = () => {
  // 重新渲染图表
  nextTick(() => {
    if (activeTab.value === 'todo') {
      initTodoChart()
    } else {
      initCompletionChart()
    }
  })
}

// 切换标签页
const handleTabChange = (tabName: string) => {
  nextTick(() => {
    if (tabName === 'todo') {
      initTodoChart()
    } else {
      initCompletionChart()
    }
  })
}

// 初始化待办趋势图表
const initTodoChart = () => {
  if (!todoChartRef.value) return
  
  if (todoChart) {
    todoChart.dispose()
  }
  
  todoChart = echarts.init(todoChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['新增', '完成'],
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      axisLabel: {
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 10
      }
    },
    series: [
      {
        name: '新增',
        type: 'line',
        data: [5, 3, 8, 6, 4, 7, 2],
        smooth: true,
        itemStyle: {
          color: '#409EFF'
        }
      },
      {
        name: '完成',
        type: 'line',
        data: [3, 4, 6, 5, 6, 5, 4],
        smooth: true,
        itemStyle: {
          color: '#67C23A'
        }
      }
    ]
  }
  
  todoChart.setOption(option)
}

// 初始化完成率图表
const initCompletionChart = () => {
  if (!completionChartRef.value) return
  
  if (completionChart) {
    completionChart.dispose()
  }
  
  completionChart = echarts.init(completionChartRef.value)
  
  const total = props.statistics.todoTotal || 0
  const completed = props.statistics.todoCompleted || 0
  const pending = total - completed
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '待办状态',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        data: [
          { value: completed, name: '已完成', itemStyle: { color: '#67C23A' } },
          { value: pending, name: '待处理', itemStyle: { color: '#E6A23C' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  completionChart.setOption(option)
}

// 跳转到相关页面
const goToTodo = () => {
  router.push('/system/todo')
}

const goToNotice = () => {
  router.push('/system/notice')
}

const goToMessage = () => {
  router.push('/system/message')
}

// 组件挂载时初始化图表
onMounted(() => {
  nextTick(() => {
    initTodoChart()
  })
})
</script>

<style scoped>
.statistics-card {
  height: 100%;
  min-height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: 600;
  color: #303133;
}

.statistics-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.overview-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.stat-card.todo {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-card.notice {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.stat-card.message {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stat-icon {
  font-size: 32px;
  margin-right: 15px;
  opacity: 0.8;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
  margin-bottom: 4px;
}

.stat-detail {
  font-size: 10px;
  opacity: 0.8;
}

.stat-detail span {
  margin-right: 8px;
}

.chart-area {
  flex: 1;
  margin-bottom: 15px;
}

.chart-container {
  height: 200px;
}

.chart {
  width: 100%;
  height: 100%;
}

.quick-links {
  display: flex;
  justify-content: space-around;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.quick-links .el-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.quick-links .el-icon {
  font-size: 16px;
}

@media (max-width: 768px) {
  .overview-stats {
    flex-direction: column;
    gap: 10px;
  }
  
  .stat-card {
    padding: 10px;
  }
  
  .stat-icon {
    font-size: 24px;
    margin-right: 10px;
  }
  
  .stat-number {
    font-size: 18px;
  }
  
  .chart-container {
    height: 150px;
  }
  
  .quick-links {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
