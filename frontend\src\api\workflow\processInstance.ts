import request from '@/utils/request'

// 查询运行中的流程实例列表
export function listRunningProcessInstance(query: any) {
  return request({
    url: '/workflow/processInstance/running/list',
    method: 'get',
    params: query
  })
}

// 查询历史流程实例列表
export function listHistoryProcessInstance(query: any) {
  return request({
    url: '/workflow/processInstance/history/list',
    method: 'get',
    params: query
  })
}

// 查询流程实例详细
export function getProcessInstance(processInstanceId: string) {
  return request({
    url: '/workflow/processInstance/' + processInstanceId,
    method: 'get'
  })
}

// 启动流程实例
export function startProcessInstance(data: any) {
  return request({
    url: '/workflow/processInstance/start',
    method: 'post',
    params: {
      processDefinitionKey: data.processDefinitionKey,
      businessKey: data.businessKey
    },
    data: data.variables
  })
}

// 删除流程实例
export function delProcessInstance(processInstanceId: string, deleteReason?: string) {
  return request({
    url: '/workflow/processInstance/' + processInstanceId,
    method: 'delete',
    params: {
      deleteReason
    }
  })
}

// 挂起流程实例
export function suspendProcessInstance(processInstanceId: string) {
  return request({
    url: '/workflow/processInstance/suspend/' + processInstanceId,
    method: 'put'
  })
}

// 激活流程实例
export function activateProcessInstance(processInstanceId: string) {
  return request({
    url: '/workflow/processInstance/activate/' + processInstanceId,
    method: 'put'
  })
}

// 获取流程变量
export function getProcessVariables(processInstanceId: string) {
  return request({
    url: '/workflow/processInstance/variables/' + processInstanceId,
    method: 'get'
  })
}

// 设置流程变量
export function setProcessVariables(processInstanceId: string, variables: any) {
  return request({
    url: '/workflow/processInstance/variables/' + processInstanceId,
    method: 'put',
    data: variables
  })
}
