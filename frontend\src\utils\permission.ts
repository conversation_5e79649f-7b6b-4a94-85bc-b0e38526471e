import { useUserStore } from '@/store/modules/user'

/**
 * 权限工具类
 * 用于替换模板中的 @ss.hasPermi 表达式
 */
class PermissionUtils {
  /**
   * 检查是否有指定权限
   * @param permission 权限字符串或权限数组
   * @returns 是否有权限
   */
  hasPermi(permission: string | string[]): boolean {
    try {
      const userStore = useUserStore()
      const permissions = userStore.permissions
      
      // 如果是管理员，拥有所有权限
      if (userStore.roles.includes('admin')) {
        return true
      }
      
      if (typeof permission === 'string') {
        return permissions.includes(permission)
      }
      
      if (Array.isArray(permission)) {
        return permission.some(p => permissions.includes(p))
      }
      
      return false
    } catch (error) {
      console.warn('权限检查失败:', error)
      return true // 权限检查失败时默认允许访问
    }
  }

  /**
   * 检查是否有指定角色
   * @param role 角色字符串或角色数组
   * @returns 是否有角色
   */
  hasRole(role: string | string[]): boolean {
    try {
      const userStore = useUserStore()
      const roles = userStore.roles
      
      if (typeof role === 'string') {
        return roles.includes(role)
      }
      
      if (Array.isArray(role)) {
        return role.some(r => roles.includes(r))
      }
      
      return false
    } catch (error) {
      console.warn('角色检查失败:', error)
      return true // 角色检查失败时默认允许访问
    }
  }

  /**
   * 检查是否有任意一个权限
   * @param permissions 权限数组
   * @returns 是否有权限
   */
  hasAnyPermi(permissions: string[]): boolean {
    return this.hasPermi(permissions)
  }

  /**
   * 检查是否有所有权限
   * @param permissions 权限数组
   * @returns 是否有权限
   */
  hasAllPermi(permissions: string[]): boolean {
    try {
      const userStore = useUserStore()
      const userPermissions = userStore.permissions
      
      // 如果是管理员，拥有所有权限
      if (userStore.roles.includes('admin')) {
        return true
      }
      
      return permissions.every(p => userPermissions.includes(p))
    } catch (error) {
      console.warn('权限检查失败:', error)
      return true
    }
  }
}

// 创建全局实例
const permissionUtils = new PermissionUtils()

// 导出实例和类
export { PermissionUtils }
export default permissionUtils

// 为了兼容模板中的 @ss.hasPermi 表达式，创建全局对象
declare global {
  interface Window {
    ss: {
      hasPermi: (permission: string | string[]) => boolean
      hasRole: (role: string | string[]) => boolean
    }
  }
}

// 在浏览器环境中设置全局对象
if (typeof window !== 'undefined') {
  window.ss = {
    hasPermi: (permission: string | string[]) => permissionUtils.hasPermi(permission),
    hasRole: (role: string | string[]) => permissionUtils.hasRole(role)
  }
}
