#!/bin/bash

# 科研成果多维敏捷管控中心 - 部署状态检查脚本

echo "=========================================="
echo "科研成果多维敏捷管控中心 - 部署状态检查"
echo "=========================================="

# 设置变量
APP_NAME="research-management"
APP_DIR="/opt/$APP_NAME"
WEB_DIR="/var/www/$APP_NAME"
JAR_FILE="research-management-1.0.0.jar"
BACKEND_PORT="8989"
FRONTEND_PORT="3000"
NGINX_CONF="/usr/local/nginx/conf/nginx.conf"
NGINX_RESEARCH_CONF="/usr/local/nginx/conf/research-management.conf"

echo "检查时间: $(date)"
echo ""

# 1. 检查目录结构
echo "1. 检查目录结构..."
if [ -d "$APP_DIR" ]; then
    echo "✓ 应用目录存在: $APP_DIR"
    ls -la "$APP_DIR"
else
    echo "✗ 应用目录不存在: $APP_DIR"
fi

if [ -d "$WEB_DIR" ]; then
    echo "✓ 网站目录存在: $WEB_DIR"
    echo "  文件数量: $(find $WEB_DIR -type f | wc -l)"
else
    echo "✗ 网站目录不存在: $WEB_DIR"
fi
echo ""

# 2. 检查Java环境
echo "2. 检查Java环境..."
if command -v java &> /dev/null; then
    echo "✓ Java已安装: $(java -version 2>&1 | head -n 1)"
else
    echo "✗ Java未安装"
fi
echo ""

# 3. 检查后端服务
echo "3. 检查后端服务..."
if [ -f "$APP_DIR/app.pid" ]; then
    PID=$(cat "$APP_DIR/app.pid")
    if kill -0 $PID 2>/dev/null; then
        echo "✓ 后端进程运行中 (PID: $PID)"
        echo "  进程信息: $(ps -p $PID -o pid,ppid,cmd --no-headers)"
    else
        echo "✗ 后端进程不存在 (PID文件存在但进程已停止)"
    fi
else
    echo "✗ 后端PID文件不存在"
fi

# 检查后端端口
if netstat -tlnp | grep ":$BACKEND_PORT " > /dev/null; then
    echo "✓ 后端端口监听中: $BACKEND_PORT"
    netstat -tlnp | grep ":$BACKEND_PORT "
else
    echo "✗ 后端端口未监听: $BACKEND_PORT"
fi

# 测试后端API
if curl -s -o /dev/null -w "%{http_code}" "http://localhost:$BACKEND_PORT/actuator/health" | grep -q "200"; then
    echo "✓ 后端API响应正常"
else
    echo "✗ 后端API无响应或异常"
fi
echo ""

# 4. 检查Nginx配置
echo "4. 检查Nginx配置..."
if [ -f "$NGINX_CONF" ]; then
    echo "✓ Nginx主配置文件存在: $NGINX_CONF"
else
    echo "✗ Nginx主配置文件不存在: $NGINX_CONF"
fi

if [ -f "$NGINX_RESEARCH_CONF" ]; then
    echo "✓ 科研管理系统配置文件存在: $NGINX_RESEARCH_CONF"
else
    echo "✗ 科研管理系统配置文件不存在: $NGINX_RESEARCH_CONF"
fi

# 检查配置是否被包含
if grep -q "research-management.conf" "$NGINX_CONF"; then
    echo "✓ 科研管理系统配置已包含在主配置中"
else
    echo "✗ 科研管理系统配置未包含在主配置中"
    echo "  请在 $NGINX_CONF 的 http 块中添加: include research-management.conf;"
fi

# 测试Nginx配置
if nginx -t &>/dev/null; then
    echo "✓ Nginx配置语法正确"
else
    echo "✗ Nginx配置语法错误"
    echo "  详细错误信息:"
    nginx -t
fi
echo ""

# 5. 检查前端服务
echo "5. 检查前端服务..."
if netstat -tlnp | grep ":$FRONTEND_PORT " > /dev/null; then
    echo "✓ 前端端口监听中: $FRONTEND_PORT"
    netstat -tlnp | grep ":$FRONTEND_PORT "
else
    echo "✗ 前端端口未监听: $FRONTEND_PORT"
fi

# 测试前端访问
if curl -s -o /dev/null -w "%{http_code}" "http://localhost:$FRONTEND_PORT" | grep -q "200"; then
    echo "✓ 前端页面响应正常"
else
    echo "✗ 前端页面无响应或异常"
fi
echo ""

# 6. 检查进程状态
echo "6. 检查相关进程..."
echo "Java进程:"
ps aux | grep java | grep -v grep || echo "  无Java进程"

echo "Nginx进程:"
ps aux | grep nginx | grep -v grep || echo "  无Nginx进程"
echo ""

# 7. 检查日志文件
echo "7. 检查日志文件..."
if [ -f "$APP_DIR/app.log" ]; then
    echo "✓ 应用日志文件存在: $APP_DIR/app.log"
    echo "  文件大小: $(du -h $APP_DIR/app.log | cut -f1)"
    echo "  最后10行:"
    tail -n 10 "$APP_DIR/app.log" | sed 's/^/    /'
else
    echo "✗ 应用日志文件不存在"
fi
echo ""

# 8. 检查磁盘空间
echo "8. 检查磁盘空间..."
df -h | grep -E "(Filesystem|/$|/opt|/var)"
echo ""

# 9. 检查内存使用
echo "9. 检查内存使用..."
free -h
echo ""

# 10. 网络连接测试
echo "10. 网络连接测试..."
SERVER_IP=$(hostname -I | awk '{print $1}')
echo "服务器IP: $SERVER_IP"

if [ -n "$SERVER_IP" ]; then
    echo "外部访问地址:"
    echo "  前端: http://$SERVER_IP:$FRONTEND_PORT"
    echo "  后端: http://$SERVER_IP:$BACKEND_PORT"
fi
echo ""

# 11. 总结
echo "=========================================="
echo "部署状态总结"
echo "=========================================="

# 统计检查结果
TOTAL_CHECKS=0
PASSED_CHECKS=0

# 这里可以添加更详细的状态统计逻辑

echo "检查完成时间: $(date)"
echo ""
echo "如果发现问题，请参考以下文件："
echo "- 部署说明: 部署说明.md"
echo "- Nginx配置指南: nginx-config-guide.md"
echo "- 应用日志: $APP_DIR/app.log"
echo "- Nginx错误日志: /usr/local/nginx/logs/error.log"
echo "=========================================="
