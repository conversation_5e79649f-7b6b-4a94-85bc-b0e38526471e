package com.research.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.research.project.domain.entity.VerticalProject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 纵向项目Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Mapper
public interface VerticalProjectMapper extends BaseMapper<VerticalProject> {

    /**
     * 查询纵向项目列表
     * 
     * @param verticalProject 纵向项目
     * @return 纵向项目集合
     */
    List<VerticalProject> selectVerticalProjectList(VerticalProject verticalProject);

    /**
     * 根据项目编号查询项目
     * 
     * @param projectNo 项目编号
     * @return 纵向项目
     */
    @Select("SELECT * FROM vertical_project WHERE project_no = #{projectNo}")
    VerticalProject selectByProjectNo(@Param("projectNo") String projectNo);

    /**
     * 根据负责人ID查询项目列表
     * 
     * @param principalId 负责人ID
     * @return 项目列表
     */
    @Select("SELECT * FROM vertical_project WHERE principal_id = #{principalId} ORDER BY create_time DESC")
    List<VerticalProject> selectByPrincipalId(@Param("principalId") Long principalId);

    /**
     * 根据部门ID查询项目列表
     * 
     * @param deptId 部门ID
     * @return 项目列表
     */
    @Select("SELECT * FROM vertical_project WHERE dept_id = #{deptId} ORDER BY create_time DESC")
    List<VerticalProject> selectByDeptId(@Param("deptId") Long deptId);

    /**
     * 根据项目状态查询项目列表
     * 
     * @param status 项目状态
     * @return 项目列表
     */
    @Select("SELECT * FROM vertical_project WHERE status = #{status} ORDER BY create_time DESC")
    List<VerticalProject> selectByStatus(@Param("status") Integer status);

    /**
     * 查询项目统计信息
     * 
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_count, " +
            "SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as applying_count, " +
            "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as approved_count, " +
            "SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as executing_count, " +
            "SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as changing_count, " +
            "SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as closing_count, " +
            "SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as closed_count, " +
            "SUM(CASE WHEN status = 6 THEN 1 ELSE 0 END) as cancelled_count, " +
            "SUM(budget) as total_budget, " +
            "SUM(used_budget) as total_used_budget " +
            "FROM vertical_project")
    Map<String, Object> selectProjectStatistics();

    /**
     * 查询部门项目统计
     * 
     * @return 部门统计信息
     */
    @Select("SELECT " +
            "dept_id, " +
            "dept_name, " +
            "COUNT(*) as project_count, " +
            "SUM(budget) as total_budget, " +
            "SUM(used_budget) as total_used_budget " +
            "FROM vertical_project " +
            "GROUP BY dept_id, dept_name " +
            "ORDER BY project_count DESC")
    List<Map<String, Object>> selectDeptStatistics();

    /**
     * 查询项目类型统计
     * 
     * @return 项目类型统计信息
     */
    @Select("SELECT " +
            "project_type, " +
            "COUNT(*) as project_count, " +
            "SUM(budget) as total_budget " +
            "FROM vertical_project " +
            "GROUP BY project_type " +
            "ORDER BY project_count DESC")
    List<Map<String, Object>> selectTypeStatistics();

    /**
     * 查询项目级别统计
     * 
     * @return 项目级别统计信息
     */
    @Select("SELECT " +
            "project_level, " +
            "COUNT(*) as project_count, " +
            "SUM(budget) as total_budget " +
            "FROM vertical_project " +
            "GROUP BY project_level " +
            "ORDER BY project_count DESC")
    List<Map<String, Object>> selectLevelStatistics();

    /**
     * 查询即将到期的项目
     * 
     * @param days 天数
     * @return 项目列表
     */
    @Select("SELECT * FROM vertical_project " +
            "WHERE status IN (1, 2) " +
            "AND end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL #{days} DAY) " +
            "ORDER BY end_date ASC")
    List<VerticalProject> selectExpiringProjects(@Param("days") Integer days);

    /**
     * 查询已逾期的项目
     * 
     * @return 项目列表
     */
    @Select("SELECT * FROM vertical_project " +
            "WHERE status IN (1, 2) " +
            "AND end_date < CURDATE() " +
            "ORDER BY end_date ASC")
    List<VerticalProject> selectOverdueProjects();

    /**
     * 查询预算使用率高的项目
     * 
     * @param threshold 阈值（百分比）
     * @return 项目列表
     */
    @Select("SELECT * FROM vertical_project " +
            "WHERE budget > 0 " +
            "AND (used_budget / budget * 100) >= #{threshold} " +
            "ORDER BY (used_budget / budget) DESC")
    List<VerticalProject> selectHighBudgetUsageProjects(@Param("threshold") Integer threshold);

    /**
     * 根据关键词搜索项目
     * 
     * @param keyword 关键词
     * @return 项目列表
     */
    @Select("SELECT * FROM vertical_project " +
            "WHERE project_name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR project_summary LIKE CONCAT('%', #{keyword}, '%') " +
            "OR keywords LIKE CONCAT('%', #{keyword}, '%') " +
            "OR principal_name LIKE CONCAT('%', #{keyword}, '%') " +
            "ORDER BY create_time DESC")
    List<VerticalProject> searchProjects(@Param("keyword") String keyword);

    /**
     * 查询年度项目统计
     * 
     * @param year 年份
     * @return 统计信息
     */
    @Select("SELECT " +
            "YEAR(create_time) as year, " +
            "MONTH(create_time) as month, " +
            "COUNT(*) as project_count, " +
            "SUM(budget) as total_budget " +
            "FROM vertical_project " +
            "WHERE YEAR(create_time) = #{year} " +
            "GROUP BY YEAR(create_time), MONTH(create_time) " +
            "ORDER BY month")
    List<Map<String, Object>> selectYearlyStatistics(@Param("year") Integer year);
}
