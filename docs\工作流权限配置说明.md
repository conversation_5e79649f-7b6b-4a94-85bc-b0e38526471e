# 🔐 工作流权限配置说明

## 🎯 概述

我已经为您创建了完整的工作流权限配置，包含所有类似 `workflow:task:list` 的权限标识。

## 📋 权限清单

### 1. **流程设计器权限** (workflow:designer:*)
```
workflow:designer:view      - 查看流程设计器
workflow:designer:create    - 创建流程模型
workflow:designer:edit      - 编辑流程模型
workflow:designer:delete    - 删除流程模型
workflow:designer:save      - 保存流程模型
workflow:designer:deploy    - 部署流程模型
workflow:designer:import    - 导入流程模型
workflow:designer:export    - 导出流程模型
workflow:designer:publish   - 发布流程模型
```

### 2. **流程管理权限** (workflow:process:*)
```
workflow:process:query      - 查询流程定义
workflow:process:list       - 查看流程列表
workflow:process:start      - 启动流程实例
workflow:process:suspend    - 挂起流程定义
workflow:process:activate   - 激活流程定义
workflow:process:delete     - 删除流程定义
workflow:process:instance   - 查看流程实例
workflow:process:history    - 查看流程历史
workflow:process:deploy     - 部署流程定义
```

### 3. **任务管理权限** (workflow:task:*)
```
workflow:task:query         - 查询任务信息
workflow:task:list          - 查看任务列表 ⭐
workflow:task:claim         - 签收任务
workflow:task:complete      - 完成任务
workflow:task:assign        - 转办任务
workflow:task:delegate      - 委派任务
workflow:task:history       - 查看任务历史
workflow:task:detail        - 查看任务详情
```

### 4. **版本管理权限** (workflow:version:*)
```
workflow:version:query      - 查询版本信息
workflow:version:list       - 查看版本列表
workflow:version:switch     - 切换流程版本
workflow:version:remove     - 删除流程版本
workflow:version:compare    - 比较流程版本
```

### 5. **监控大屏权限** (workflow:monitor:*)
```
workflow:monitor:view       - 查看监控大屏
workflow:monitor:statistics - 查看统计分析
workflow:monitor:performance- 查看性能监控
workflow:monitor:export     - 导出监控报表
workflow:monitor:realtime   - 实时监控数据
```

### 6. **流程定义权限** (workflow:definition:*)
```
workflow:definition:query   - 查询流程定义
workflow:definition:list    - 查看定义列表
workflow:definition:detail  - 查看定义详情
```

### 7. **流程实例权限** (workflow:instance:*)
```
workflow:instance:query     - 查询流程实例
workflow:instance:list      - 查看实例列表
workflow:instance:detail    - 查看实例详情
workflow:instance:suspend   - 挂起流程实例
workflow:instance:activate  - 激活流程实例
workflow:instance:delete    - 删除流程实例
```

## 🚀 使用方法

### 1. **执行权限配置SQL**
```sql
-- 执行完整权限配置
source backend/src/main/resources/sql/workflow_permissions_complete.sql;
```

### 2. **检查权限配置**
```sql
-- 执行权限检查脚本
source backend/src/main/resources/sql/workflow_permissions_check.sql;
```

### 3. **验证权限生效**
- 重启后端服务
- 登录系统查看工作流菜单
- 测试各项功能的权限控制

## 📊 权限分配策略

### 超级管理员 (role_id = 1)
- ✅ **所有工作流权限** - 完整的管理权限

### 科研管理员 (role_id = 2)
- ✅ **流程查看权限** - 只读权限
- ✅ **任务处理权限** - 基础任务操作
- ✅ **监控查看权限** - 数据监控

### 项目经理 (role_id = 3)
- ✅ **流程查看权限** - 基础查看
- ✅ **任务处理权限** - 任务操作

## 🔧 技术实现

### 1. **权限注解使用**
```java
@PreAuthorize("@ss.hasPermi('workflow:task:list')")
@GetMapping("/my/list")
public TableDataInfo listMyTask(WorkflowTask task) {
    // 任务列表查询逻辑
}
```

### 2. **前端权限控制**
```vue
<el-button 
  v-hasPermi="['workflow:task:complete']"
  @click="completeTask">
  完成任务
</el-button>
```

### 3. **菜单权限配置**
```sql
INSERT INTO sys_menu (menu_name, parent_id, menu_type, perms) VALUES
('任务列表', 203, 'F', 'workflow:task:list');
```

## 📋 权限检查清单

### ✅ 已配置的权限
- [x] 流程设计器权限 (9个)
- [x] 流程管理权限 (9个)
- [x] 任务管理权限 (8个)
- [x] 版本管理权限 (5个)
- [x] 监控大屏权限 (5个)
- [x] 流程定义权限 (3个)
- [x] 流程实例权限 (6个)

### ✅ 角色权限分配
- [x] 超级管理员 - 所有权限
- [x] 科研管理员 - 部分权限
- [x] 项目经理 - 基础权限

## 🎯 验证方法

### 1. **数据库验证**
```sql
-- 查看所有工作流权限
SELECT menu_name, perms FROM sys_menu 
WHERE perms LIKE 'workflow:%' 
ORDER BY perms;

-- 查看角色权限分配
SELECT r.role_name, m.perms 
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.perms LIKE 'workflow:%'
ORDER BY r.role_name, m.perms;
```

### 2. **功能验证**
- 登录不同角色账号
- 访问工作流相关页面
- 测试按钮权限控制
- 验证API接口权限

## 📝 注意事项

### 1. **权限标识规范**
- 格式：`workflow:模块:操作`
- 示例：`workflow:task:list`
- 保持命名一致性

### 2. **权限粒度控制**
- 页面级权限：控制菜单显示
- 功能级权限：控制按钮显示
- 接口级权限：控制API访问

### 3. **权限更新流程**
1. 修改权限配置SQL
2. 执行数据库更新
3. 重启后端服务
4. 清除前端缓存
5. 验证权限生效

## 🎉 配置完成

现在您的系统已经包含了完整的工作流权限配置，包括：

- ✅ **45个工作流权限** - 覆盖所有功能模块
- ✅ **3个角色权限分配** - 不同级别的权限控制
- ✅ **完整的验证脚本** - 确保配置正确性

所有类似 `workflow:task:list` 的权限都已经添加到权限表中！🎯
