package com.research.system.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户与岗位关联表 数据层
 * 
 * <AUTHOR>
 */
@Mapper
public interface SysUserPostMapper {

    /**
     * 通过岗位ID查询岗位使用数量
     * 
     * @param postId 岗位ID
     * @return 结果
     */
    int countUserPostByPostId(Long postId);

    /**
     * 通过用户ID删除用户和岗位关联
     * 
     * @param userId 用户ID
     * @return 结果
     */
    int deleteUserPostByUserId(Long userId);

    /**
     * 通过岗位ID删除用户和岗位关联
     * 
     * @param postId 岗位ID
     * @return 结果
     */
    int deleteUserPostByPostId(Long postId);

    /**
     * 批量删除用户和岗位关联
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteUserPost(Long[] ids);

    /**
     * 批量新增用户岗位信息
     * 
     * @param userId 用户ID
     * @param postIds 岗位组
     * @return 结果
     */
    int batchUserPost(@Param("userId") Long userId, @Param("postIds") Long[] postIds);
}
