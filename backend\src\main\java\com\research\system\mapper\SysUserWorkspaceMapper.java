package com.research.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.research.system.domain.SysUserWorkspace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户工作台配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Mapper
public interface SysUserWorkspaceMapper extends BaseMapper<SysUserWorkspace> {

    /**
     * 查询用户工作台配置
     * 
     * @param userId 用户ID
     * @param configType 配置类型
     * @return 配置列表
     */
    List<SysUserWorkspace> selectUserConfig(@Param("userId") Long userId, @Param("configType") String configType);

    /**
     * 查询用户特定配置
     * 
     * @param userId 用户ID
     * @param configType 配置类型
     * @param configKey 配置键
     * @return 配置信息
     */
    SysUserWorkspace selectUserConfigByKey(@Param("userId") Long userId, @Param("configType") String configType, @Param("configKey") String configKey);

    /**
     * 保存或更新用户配置
     * 
     * @param config 配置信息
     * @return 影响行数
     */
    int saveOrUpdateConfig(SysUserWorkspace config);

    /**
     * 删除用户配置
     * 
     * @param userId 用户ID
     * @param configType 配置类型
     * @param configKey 配置键
     * @return 影响行数
     */
    int deleteUserConfig(@Param("userId") Long userId, @Param("configType") String configType, @Param("configKey") String configKey);

    /**
     * 批量更新配置排序
     * 
     * @param configs 配置列表
     * @return 影响行数
     */
    int batchUpdateSort(@Param("configs") List<SysUserWorkspace> configs);

    /**
     * 删除用户所有配置
     * 
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteAllUserConfig(@Param("userId") Long userId);
}
