-- 科研管理系统数据库结构设计
-- 兼容原有Oracle表结构，适配MySQL 8.0+

-- 创建数据库
CREATE DATABASE IF NOT EXISTS research_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE research_db;

-- ===========================================
-- 系统管理模块表结构
-- ===========================================

-- 用户表
CREATE TABLE sys_user (
    user_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    dept_id BIGINT COMMENT '部门ID',
    user_name VARCHAR(30) NOT NULL UNIQUE COMMENT '用户账号',
    nick_name VARCHAR(30) NOT NULL COMMENT '用户昵称',
    user_type VARCHAR(2) DEFAULT '00' COMMENT '用户类型（00系统用户）',
    email VARCHAR(50) DEFAULT '' COMMENT '用户邮箱',
    phonenumber VARCHAR(11) DEFAULT '' COMMENT '手机号码',
    sex CHAR(1) DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
    avatar VARCHAR(100) DEFAULT '' COMMENT '头像地址',
    password VARCHAR(100) DEFAULT '' COMMENT '密码',
    status CHAR(1) DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    login_ip VARCHAR(128) DEFAULT '' COMMENT '最后登录IP',
    login_date DATETIME COMMENT '最后登录时间',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB COMMENT='用户信息表';

-- 角色表
CREATE TABLE sys_role (
    role_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',
    role_name VARCHAR(30) NOT NULL COMMENT '角色名称',
    role_key VARCHAR(100) NOT NULL COMMENT '角色权限字符串',
    role_sort INT NOT NULL COMMENT '显示顺序',
    data_scope CHAR(1) DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
    menu_check_strictly TINYINT(1) DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
    dept_check_strictly TINYINT(1) DEFAULT 1 COMMENT '部门树选择项是否关联显示',
    status CHAR(1) NOT NULL COMMENT '角色状态（0正常 1停用）',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB COMMENT='角色信息表';

-- 部门表
CREATE TABLE sys_dept (
    dept_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '部门id',
    parent_id BIGINT DEFAULT 0 COMMENT '父部门id',
    ancestors VARCHAR(50) DEFAULT '' COMMENT '祖级列表',
    dept_name VARCHAR(30) DEFAULT '' COMMENT '部门名称',
    order_num INT DEFAULT 0 COMMENT '显示顺序',
    leader VARCHAR(20) DEFAULT NULL COMMENT '负责人',
    phone VARCHAR(11) DEFAULT NULL COMMENT '联系电话',
    email VARCHAR(50) DEFAULT NULL COMMENT '邮箱',
    status CHAR(1) DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='部门表';

-- 菜单权限表
CREATE TABLE sys_menu (
    menu_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '菜单ID',
    menu_name VARCHAR(50) NOT NULL COMMENT '菜单名称',
    parent_id BIGINT DEFAULT 0 COMMENT '父菜单ID',
    order_num INT DEFAULT 0 COMMENT '显示顺序',
    path VARCHAR(200) DEFAULT '' COMMENT '路由地址',
    component VARCHAR(255) DEFAULT NULL COMMENT '组件路径',
    query VARCHAR(255) DEFAULT NULL COMMENT '路由参数',
    is_frame INT DEFAULT 1 COMMENT '是否为外链（0是 1否）',
    is_cache INT DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
    menu_type CHAR(1) DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
    visible CHAR(1) DEFAULT 0 COMMENT '菜单状态（0显示 1隐藏）',
    status CHAR(1) DEFAULT 0 COMMENT '菜单状态（0正常 1停用）',
    perms VARCHAR(100) DEFAULT NULL COMMENT '权限标识',
    icon VARCHAR(100) DEFAULT '#' COMMENT '菜单图标',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注'
) ENGINE=InnoDB COMMENT='菜单权限表';

-- 用户和角色关联表
CREATE TABLE sys_user_role (
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    PRIMARY KEY (user_id, role_id)
) ENGINE=InnoDB COMMENT='用户和角色关联表';

-- 角色和菜单关联表
CREATE TABLE sys_role_menu (
    role_id BIGINT NOT NULL COMMENT '角色ID',
    menu_id BIGINT NOT NULL COMMENT '菜单ID',
    PRIMARY KEY (role_id, menu_id)
) ENGINE=InnoDB COMMENT='角色和菜单关联表';

-- 角色和部门关联表
CREATE TABLE sys_role_dept (
    role_id BIGINT NOT NULL COMMENT '角色ID',
    dept_id BIGINT NOT NULL COMMENT '部门ID',
    PRIMARY KEY (role_id, dept_id)
) ENGINE=InnoDB COMMENT='角色和部门关联表';

-- 用户与岗位关联表
CREATE TABLE sys_user_post (
    user_id BIGINT NOT NULL COMMENT '用户ID',
    post_id BIGINT NOT NULL COMMENT '岗位ID',
    PRIMARY KEY (user_id, post_id)
) ENGINE=InnoDB COMMENT='用户与岗位关联表';

-- 岗位信息表
CREATE TABLE sys_post (
    post_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '岗位ID',
    post_code VARCHAR(64) NOT NULL COMMENT '岗位编码',
    post_name VARCHAR(50) NOT NULL COMMENT '岗位名称',
    post_sort INT NOT NULL COMMENT '显示顺序',
    status CHAR(1) NOT NULL COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB COMMENT='岗位信息表';

-- 字典类型表
CREATE TABLE sys_dict_type (
    dict_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '字典主键',
    dict_name VARCHAR(100) DEFAULT '' COMMENT '字典名称',
    dict_type VARCHAR(100) DEFAULT '' COMMENT '字典类型',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB COMMENT='字典类型表';

-- 字典数据表
CREATE TABLE sys_dict_data (
    dict_code BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '字典编码',
    dict_sort INT DEFAULT 0 COMMENT '字典排序',
    dict_label VARCHAR(100) DEFAULT '' COMMENT '字典标签',
    dict_value VARCHAR(100) DEFAULT '' COMMENT '字典键值',
    dict_type VARCHAR(100) DEFAULT '' COMMENT '字典类型',
    css_class VARCHAR(100) DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
    list_class VARCHAR(100) DEFAULT NULL COMMENT '表格回显样式',
    is_default CHAR(1) DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB COMMENT='字典数据表';

-- 参数配置表
CREATE TABLE sys_config (
    config_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '参数主键',
    config_name VARCHAR(100) DEFAULT '' COMMENT '参数名称',
    config_key VARCHAR(100) DEFAULT '' COMMENT '参数键名',
    config_value VARCHAR(500) DEFAULT '' COMMENT '参数键值',
    config_type CHAR(1) DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB COMMENT='参数配置表';

-- 系统访问记录
CREATE TABLE sys_logininfor (
    info_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '访问ID',
    user_name VARCHAR(50) DEFAULT '' COMMENT '用户账号',
    ipaddr VARCHAR(128) DEFAULT '' COMMENT '登录IP地址',
    login_location VARCHAR(255) DEFAULT '' COMMENT '登录地点',
    browser VARCHAR(50) DEFAULT '' COMMENT '浏览器类型',
    os VARCHAR(50) DEFAULT '' COMMENT '操作系统',
    status CHAR(1) DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
    msg VARCHAR(255) DEFAULT '' COMMENT '提示消息',
    login_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间'
) ENGINE=InnoDB COMMENT='系统访问记录';

-- 操作日志记录
CREATE TABLE sys_oper_log (
    oper_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志主键',
    title VARCHAR(50) DEFAULT '' COMMENT '模块标题',
    business_type INT DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
    method VARCHAR(100) DEFAULT '' COMMENT '方法名称',
    request_method VARCHAR(10) DEFAULT '' COMMENT '请求方式',
    operator_type INT DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
    oper_name VARCHAR(50) DEFAULT '' COMMENT '操作人员',
    dept_name VARCHAR(50) DEFAULT '' COMMENT '部门名称',
    oper_url VARCHAR(255) DEFAULT '' COMMENT '请求URL',
    oper_ip VARCHAR(128) DEFAULT '' COMMENT '主机地址',
    oper_location VARCHAR(255) DEFAULT '' COMMENT '操作地点',
    oper_param VARCHAR(2000) DEFAULT '' COMMENT '请求参数',
    json_result VARCHAR(2000) DEFAULT '' COMMENT '返回参数',
    status INT DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
    error_msg VARCHAR(2000) DEFAULT '' COMMENT '错误消息',
    oper_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间'
) ENGINE=InnoDB COMMENT='操作日志记录';

-- 定时任务调度表
CREATE TABLE sys_job (
    job_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID',
    job_name VARCHAR(64) NOT NULL DEFAULT '' COMMENT '任务名称',
    job_group VARCHAR(64) NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
    invoke_target VARCHAR(500) NOT NULL COMMENT '调用目标字符串',
    cron_expression VARCHAR(255) DEFAULT '' COMMENT 'cron执行表达式',
    misfire_policy VARCHAR(20) DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
    concurrent CHAR(1) DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1暂停）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注信息'
) ENGINE=InnoDB COMMENT='定时任务调度表';

-- 定时任务调度日志表
CREATE TABLE sys_job_log (
    job_log_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务日志ID',
    job_name VARCHAR(64) NOT NULL COMMENT '任务名称',
    job_group VARCHAR(64) NOT NULL COMMENT '任务组名',
    invoke_target VARCHAR(500) NOT NULL COMMENT '调用目标字符串',
    job_message VARCHAR(500) DEFAULT NULL COMMENT '日志信息',
    status CHAR(1) DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
    exception_info VARCHAR(2000) DEFAULT '' COMMENT '异常信息',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB COMMENT='定时任务调度日志表';

-- 通知公告表
CREATE TABLE sys_notice (
    notice_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '公告ID',
    notice_title VARCHAR(50) NOT NULL COMMENT '公告标题',
    notice_type CHAR(1) NOT NULL COMMENT '公告类型（1通知 2公告）',
    notice_content LONGTEXT DEFAULT NULL COMMENT '公告内容',
    status CHAR(1) DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
    publish_by VARCHAR(64) DEFAULT '' COMMENT '发布人',
    publish_time DATETIME DEFAULT NULL COMMENT '发布时间',
    is_top CHAR(1) DEFAULT '0' COMMENT '是否置顶（0否 1是）',
    read_count BIGINT DEFAULT 0 COMMENT '阅读次数',
    attachment_path VARCHAR(500) DEFAULT NULL COMMENT '附件路径',
    importance CHAR(1) DEFAULT '1' COMMENT '重要程度（1普通 2重要 3紧急）',
    valid_start_time DATETIME DEFAULT NULL COMMENT '有效期开始时间',
    valid_end_time DATETIME DEFAULT NULL COMMENT '有效期结束时间',
    target_type CHAR(1) DEFAULT '1' COMMENT '目标用户类型（1全部 2指定部门 3指定用户）',
    target_users TEXT DEFAULT NULL COMMENT '目标用户ID列表',
    target_depts TEXT DEFAULT NULL COMMENT '目标部门ID列表',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB COMMENT='通知公告表';

-- 待办事项表
CREATE TABLE sys_todo (
    todo_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '待办ID',
    title VARCHAR(200) NOT NULL COMMENT '待办标题',
    content TEXT DEFAULT NULL COMMENT '待办内容',
    todo_type CHAR(1) DEFAULT '1' COMMENT '待办类型（1个人待办 2工作流待办 3系统待办）',
    priority CHAR(1) DEFAULT '2' COMMENT '优先级（1低 2中 3高 4紧急）',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0待处理 1处理中 2已完成 3已取消）',
    assignee_id BIGINT NOT NULL COMMENT '负责人ID',
    assignee_name VARCHAR(30) DEFAULT '' COMMENT '负责人姓名',
    due_time DATETIME DEFAULT NULL COMMENT '截止时间',
    complete_time DATETIME DEFAULT NULL COMMENT '完成时间',
    business_id VARCHAR(64) DEFAULT NULL COMMENT '关联业务ID',
    business_type VARCHAR(50) DEFAULT NULL COMMENT '关联业务类型',
    business_title VARCHAR(200) DEFAULT NULL COMMENT '关联业务标题',
    result TEXT DEFAULT NULL COMMENT '处理结果',
    is_read CHAR(1) DEFAULT '0' COMMENT '是否已读（0未读 1已读）',
    remind_time DATETIME DEFAULT NULL COMMENT '提醒时间',
    is_reminded CHAR(1) DEFAULT '0' COMMENT '是否已提醒（0未提醒 1已提醒）',
    tags VARCHAR(200) DEFAULT NULL COMMENT '标签',
    attachment_path VARCHAR(500) DEFAULT NULL COMMENT '附件路径',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    INDEX idx_assignee_status (assignee_id, status),
    INDEX idx_due_time (due_time),
    INDEX idx_business (business_type, business_id)
) ENGINE=InnoDB COMMENT='待办事项表';

-- 站内消息表
CREATE TABLE sys_message (
    message_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '消息ID',
    title VARCHAR(200) NOT NULL COMMENT '消息标题',
    content TEXT DEFAULT NULL COMMENT '消息内容',
    message_type CHAR(1) DEFAULT '1' COMMENT '消息类型（1系统消息 2通知消息 3私信消息 4提醒消息）',
    sender_id BIGINT DEFAULT NULL COMMENT '发送人ID',
    sender_name VARCHAR(30) DEFAULT '' COMMENT '发送人姓名',
    receiver_id BIGINT NOT NULL COMMENT '接收人ID',
    receiver_name VARCHAR(30) DEFAULT '' COMMENT '接收人姓名',
    is_read CHAR(1) DEFAULT '0' COMMENT '是否已读（0未读 1已读）',
    read_time DATETIME DEFAULT NULL COMMENT '阅读时间',
    priority CHAR(1) DEFAULT '2' COMMENT '优先级（1低 2中 3高 4紧急）',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1删除）',
    business_id VARCHAR(64) DEFAULT NULL COMMENT '关联业务ID',
    business_type VARCHAR(50) DEFAULT NULL COMMENT '关联业务类型',
    business_title VARCHAR(200) DEFAULT NULL COMMENT '关联业务标题',
    attachment_path VARCHAR(500) DEFAULT NULL COMMENT '附件路径',
    need_reply CHAR(1) DEFAULT '0' COMMENT '是否需要回复（0否 1是）',
    reply_content TEXT DEFAULT NULL COMMENT '回复内容',
    reply_time DATETIME DEFAULT NULL COMMENT '回复时间',
    tags VARCHAR(200) DEFAULT NULL COMMENT '标签',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    INDEX idx_receiver_read (receiver_id, is_read),
    INDEX idx_sender (sender_id),
    INDEX idx_message_type (message_type),
    INDEX idx_business (business_type, business_id)
) ENGINE=InnoDB COMMENT='站内消息表';

-- 表单模板表
CREATE TABLE form_template (
    template_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '模板ID',
    template_name VARCHAR(200) NOT NULL COMMENT '模板名称',
    template_code VARCHAR(100) NOT NULL UNIQUE COMMENT '模板编码',
    template_type CHAR(1) DEFAULT '1' COMMENT '模板类型（1申报表单 2评审表单 3通用表单）',
    category VARCHAR(50) DEFAULT NULL COMMENT '模板分类',
    description TEXT DEFAULT NULL COMMENT '模板描述',
    form_config LONGTEXT DEFAULT NULL COMMENT '表单配置JSON',
    form_fields LONGTEXT DEFAULT NULL COMMENT '表单字段JSON',
    form_style LONGTEXT DEFAULT NULL COMMENT '表单样式JSON',
    validation_rules LONGTEXT DEFAULT NULL COMMENT '表单验证规则JSON',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0草稿 1已发布 2已停用）',
    version INT DEFAULT 1 COMMENT '版本号',
    is_main CHAR(1) DEFAULT '1' COMMENT '是否主版本（0否 1是）',
    publish_time DATETIME DEFAULT NULL COMMENT '发布时间',
    publish_by VARCHAR(64) DEFAULT '' COMMENT '发布人',
    use_count BIGINT DEFAULT 0 COMMENT '使用次数',
    order_num INT DEFAULT 0 COMMENT '排序',
    enabled CHAR(1) DEFAULT '1' COMMENT '是否启用（0否 1是）',
    form_width VARCHAR(20) DEFAULT '100%' COMMENT '表单宽度',
    form_height VARCHAR(20) DEFAULT 'auto' COMMENT '表单高度',
    label_width VARCHAR(20) DEFAULT '120px' COMMENT '标签宽度',
    label_position VARCHAR(20) DEFAULT 'left' COMMENT '标签位置（left/right/top）',
    form_layout VARCHAR(20) DEFAULT 'horizontal' COMMENT '表单布局（horizontal/vertical/inline）',
    show_title CHAR(1) DEFAULT '1' COMMENT '是否显示标题（0否 1是）',
    show_reset CHAR(1) DEFAULT '1' COMMENT '是否显示重置按钮（0否 1是）',
    show_submit CHAR(1) DEFAULT '1' COMMENT '是否显示提交按钮（0否 1是）',
    submit_text VARCHAR(20) DEFAULT '提交' COMMENT '提交按钮文本',
    reset_text VARCHAR(20) DEFAULT '重置' COMMENT '重置按钮文本',
    theme VARCHAR(50) DEFAULT 'default' COMMENT '表单主题',
    custom_css LONGTEXT DEFAULT NULL COMMENT '自定义CSS',
    custom_js LONGTEXT DEFAULT NULL COMMENT '自定义JS',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    INDEX idx_template_code (template_code),
    INDEX idx_template_type (template_type),
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_create_by (create_by),
    INDEX idx_use_count (use_count)
) ENGINE=InnoDB COMMENT='表单模板表';

-- 代码生成业务表
CREATE TABLE gen_table (
    table_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '编号',
    table_name VARCHAR(200) DEFAULT '' COMMENT '表名称',
    table_comment VARCHAR(500) DEFAULT '' COMMENT '表描述',
    sub_table_name VARCHAR(64) DEFAULT NULL COMMENT '关联子表的表名',
    sub_table_fk_name VARCHAR(64) DEFAULT NULL COMMENT '子表关联的外键名',
    class_name VARCHAR(100) DEFAULT '' COMMENT '实体类名称',
    tpl_category VARCHAR(200) DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
    package_name VARCHAR(100) DEFAULT NULL COMMENT '生成包路径',
    module_name VARCHAR(30) DEFAULT NULL COMMENT '生成模块名',
    business_name VARCHAR(30) DEFAULT NULL COMMENT '生成业务名',
    function_name VARCHAR(50) DEFAULT NULL COMMENT '生成功能名',
    function_author VARCHAR(50) DEFAULT NULL COMMENT '生成功能作者',
    gen_type CHAR(1) DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
    gen_path VARCHAR(200) DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
    options VARCHAR(1000) DEFAULT NULL COMMENT '其它生成选项',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB COMMENT='代码生成业务表';

-- 代码生成业务表字段
CREATE TABLE gen_table_column (
    column_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '编号',
    table_id VARCHAR(64) DEFAULT NULL COMMENT '归属表编号',
    column_name VARCHAR(200) DEFAULT NULL COMMENT '列名称',
    column_comment VARCHAR(500) DEFAULT NULL COMMENT '列描述',
    column_type VARCHAR(100) DEFAULT NULL COMMENT '列类型',
    java_type VARCHAR(500) DEFAULT NULL COMMENT 'JAVA类型',
    java_field VARCHAR(200) DEFAULT NULL COMMENT 'JAVA字段名',
    is_pk CHAR(1) DEFAULT NULL COMMENT '是否主键（1是）',
    is_increment CHAR(1) DEFAULT NULL COMMENT '是否自增（1是）',
    is_required CHAR(1) DEFAULT NULL COMMENT '是否必填（1是）',
    is_insert CHAR(1) DEFAULT NULL COMMENT '是否为插入字段（1是）',
    is_edit CHAR(1) DEFAULT NULL COMMENT '是否编辑字段（1是）',
    is_list CHAR(1) DEFAULT NULL COMMENT '是否列表字段（1是）',
    is_query CHAR(1) DEFAULT NULL COMMENT '是否查询字段（1是）',
    query_type VARCHAR(200) DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
    html_type VARCHAR(200) DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
    dict_type VARCHAR(200) DEFAULT '' COMMENT '字典类型',
    sort INT DEFAULT NULL COMMENT '排序',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='代码生成业务表字段';

-- ===========================================
-- 项目管理模块表结构（兼容原有Oracle表结构）
-- ===========================================

-- 机构表（兼容BIZ_AGENCY）
CREATE TABLE biz_agency (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    name VARCHAR(1024) COMMENT '机构名称',
    contacts VARCHAR(500) COMMENT '联系人',
    contact_tel VARCHAR(500) COMMENT '联系电话',
    address VARCHAR(2048) COMMENT '地址',
    invalid_flag VARCHAR(32) COMMENT '无效标志',
    email VARCHAR(500) COMMENT '邮箱',
    begin_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    note TEXT COMMENT '备注',
    sys_user_id VARCHAR(32) COMMENT '系统用户ID',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='机构表';

-- 专利代理维护表（兼容BIZ_AGENCY_MAINTAIN）
CREATE TABLE biz_agency_maintain (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    patent_id VARCHAR(32) COMMENT '专利ID',
    apply_code VARCHAR(500) COMMENT '申请号',
    apply_date DATE COMMENT '申请日期',
    authorize_code VARCHAR(500) COMMENT '授权号',
    authorize_date DATE COMMENT '授权日期',
    invent_name VARCHAR(1024) COMMENT '发明名称',
    patent_status VARCHAR(32) COMMENT '专利状态',
    agency_phase VARCHAR(32) COMMENT '代理阶段',
    change_date DATE COMMENT '变更日期',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    note TEXT COMMENT '备注',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    last_edit_user_id VARCHAR(50) COMMENT '最后编辑用户ID',
    create_date DATE COMMENT '创建日期',
    last_edit_date DATE COMMENT '最后编辑日期',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    last_edit_user_name VARCHAR(50) COMMENT '最后编辑用户名',
    invalid_flag VARCHAR(32) COMMENT '无效标志',
    remove_flag VARCHAR(32) COMMENT '删除标志',
    file_state VARCHAR(32) COMMENT '文件状态',
    patent_ipc VARCHAR(32) COMMENT '专利IPC分类',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='专利代理维护表';

-- 鉴定成果表（兼容BIZ_APPRAISAL_PRODUCT）
CREATE TABLE biz_appraisal_product (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    name VARCHAR(500) COMMENT '成果名称',
    unit_id VARCHAR(32) COMMENT '单位ID',
    appraisal_unit VARCHAR(500) COMMENT '鉴定单位',
    appraisal_date DATE COMMENT '鉴定日期',
    appraisal_result_id VARCHAR(32) COMMENT '鉴定结果ID',
    appraisal_code VARCHAR(125) COMMENT '鉴定编号',
    complete_type_id VARCHAR(32) COMMENT '完成类型ID',
    subject_class_id VARCHAR(32) COMMENT '学科分类ID',
    subject_id VARCHAR(32) COMMENT '学科ID',
    subject_2_id VARCHAR(32) COMMENT '二级学科ID',
    project_source_id VARCHAR(32) COMMENT '项目来源ID',
    school_sign VARCHAR(32) COMMENT '学校标识',
    complete_units TEXT COMMENT '完成单位',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    note TEXT COMMENT '备注',
    first_author_id VARCHAR(32) COMMENT '第一作者ID',
    first_author_name VARCHAR(500) COMMENT '第一作者姓名',
    first_author_account VARCHAR(50) COMMENT '第一作者账号',
    first_author_title_id VARCHAR(32) COMMENT '第一作者职称ID',
    first_author_sex_id VARCHAR(32) COMMENT '第一作者性别ID',
    first_author_edu_level_id VARCHAR(32) COMMENT '第一作者学历ID',
    first_author_edu_degree_id VARCHAR(32) COMMENT '第一作者学位ID',
    division_id VARCHAR(32) COMMENT '部门ID',
    author_number INT COMMENT '作者数量',
    check_status VARCHAR(32) COMMENT '审核状态',
    check_date VARCHAR(32) COMMENT '审核日期',
    checker VARCHAR(50) COMMENT '审核人',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    last_edit_user_id VARCHAR(50) COMMENT '最后编辑用户ID',
    create_date DATE COMMENT '创建日期',
    last_edit_date DATE COMMENT '最后编辑日期',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    last_edit_user_name VARCHAR(50) COMMENT '最后编辑用户名',
    author_pids VARCHAR(500) COMMENT '作者PIDs',
    author_unit_ids VARCHAR(500) COMMENT '作者单位IDs',
    complete_data_status VARCHAR(20) COMMENT '完成数据状态',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='鉴定成果表';

-- 鉴定成果作者表（兼容BIZ_APPRAISAL_PRODUCT_AUTHOR）
CREATE TABLE biz_appraisal_product_author (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    person_id VARCHAR(32) COMMENT '人员ID',
    order_id INT COMMENT '排序ID',
    author_name VARCHAR(50) COMMENT '作者姓名',
    author_type VARCHAR(32) COMMENT '作者类型',
    author_unit VARCHAR(500) COMMENT '作者单位',
    title_id VARCHAR(32) COMMENT '职称ID',
    edu_level_id VARCHAR(32) COMMENT '学历ID',
    edu_degree_id VARCHAR(32) COMMENT '学位ID',
    work_ratio DECIMAL(10,2) COMMENT '工作比例',
    appraisal_product_id VARCHAR(32) COMMENT '鉴定成果ID',
    author_unit_id VARCHAR(32) COMMENT '作者单位ID',
    author_account VARCHAR(50) COMMENT '作者账号',
    sex_id VARCHAR(32) COMMENT '性别ID',
    subject_id VARCHAR(32) COMMENT '学科ID',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='鉴定成果作者表';
