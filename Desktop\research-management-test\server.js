// 横向项目管理功能测试服务器
const http = require('http');
const url = require('url');

console.log('🚀 启动横向项目管理测试服务器...');

// 模拟数据
const mockProjects = [
  {
    id: 1,
    projectNo: 'HX2024001',
    projectName: '智能制造系统开发项目',
    projectType: '技术开发',
    partnerName: '华为技术有限公司',
    status: 2,
    statusName: '执行中',
    totalFund: 2500000,
    receivedFund: 1500000,
    fundReceiveRate: 60.0,
    progressPercentage: 75,
    remainingDays: 120,
    isExpiringSoon: false,
    principalName: '张教授',
    deptName: '计算机学院',
    startDate: '2024-01-15',
    endDate: '2024-12-31',
    createTime: '2024-01-15 10:00:00'
  },
  {
    id: 2,
    projectNo: 'HX2024002',
    projectName: '大数据分析平台建设项目',
    projectType: '技术服务',
    partnerName: '清华大学',
    status: 2,
    statusName: '执行中',
    totalFund: 1800000,
    receivedFund: 900000,
    fundReceiveRate: 50.0,
    progressPercentage: 60,
    remainingDays: 90,
    isExpiringSoon: false,
    principalName: '李教授',
    deptName: '软件学院',
    startDate: '2024-02-20',
    endDate: '2024-11-30',
    createTime: '2024-02-20 09:00:00'
  },
  {
    id: 3,
    projectNo: 'HX2024003',
    projectName: '人工智能算法优化项目',
    projectType: '技术咨询',
    partnerName: '中科院计算技术研究所',
    status: 5,
    statusName: '已结项',
    totalFund: 800000,
    receivedFund: 800000,
    fundReceiveRate: 100.0,
    progressPercentage: 100,
    remainingDays: -30,
    isExpiringSoon: false,
    principalName: '王教授',
    deptName: '人工智能学院',
    startDate: '2024-03-10',
    endDate: '2024-09-10',
    createTime: '2024-03-10 14:00:00'
  }
];

// 设置CORS头
function setCORSHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
}

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  setCORSHeaders(res);
  
  // 处理OPTIONS预检请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const query = parsedUrl.query;
  
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  
  console.log(`📝 ${req.method} ${path}`);
  
  try {
    // 路由处理
    if (path === '/captchaImage') {
      // 验证码接口
      res.writeHead(200);
      res.end(JSON.stringify({
        code: 200,
        msg: '操作成功',
        data: {
          uuid: 'test-uuid-123',
          img: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
        }
      }));
      
    } else if (path === '/project/horizontal/list') {
      // 横向项目列表
      const pageNum = parseInt(query.pageNum) || 1;
      const pageSize = parseInt(query.pageSize) || 10;
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;
      
      res.writeHead(200);
      res.end(JSON.stringify({
        code: 200,
        msg: '查询成功',
        rows: mockProjects.slice(start, end),
        total: mockProjects.length
      }));
      
    } else if (path.startsWith('/project/horizontal/') && path !== '/project/horizontal/list') {
      // 获取单个项目详情
      const id = parseInt(path.split('/').pop());
      const project = mockProjects.find(p => p.id === id);
      
      if (project) {
        res.writeHead(200);
        res.end(JSON.stringify({
          code: 200,
          msg: '查询成功',
          data: project
        }));
      } else {
        res.writeHead(404);
        res.end(JSON.stringify({
          code: 404,
          msg: '项目不存在'
        }));
      }
      
    } else if (path === '/project/horizontal/statistics') {
      // 项目统计
      res.writeHead(200);
      res.end(JSON.stringify({
        code: 200,
        msg: '查询成功',
        data: {
          totalCount: mockProjects.length,
          totalFund: mockProjects.reduce((sum, p) => sum + p.totalFund, 0),
          receivedFund: mockProjects.reduce((sum, p) => sum + p.receivedFund, 0),
          expiringCount: mockProjects.filter(p => p.isExpiringSoon).length
        }
      }));
      
    } else if (path === '/project/horizontal/generate-no') {
      // 生成项目编号
      const year = new Date().getFullYear();
      const count = mockProjects.length + 1;
      const projectNo = `HX${year}${String(count).padStart(3, '0')}`;
      
      res.writeHead(200);
      res.end(JSON.stringify({
        code: 200,
        msg: '生成成功',
        data: projectNo
      }));
      
    } else if (path === '/project/contract/list') {
      // 合同列表
      res.writeHead(200);
      res.end(JSON.stringify({
        code: 200,
        msg: '查询成功',
        rows: [
          {
            id: 1,
            contractNo: 'HT2024001',
            contractName: '智能制造系统开发合同',
            contractType: '技术开发',
            partnerName: '华为技术有限公司',
            contractAmount: 2500000,
            status: 3,
            statusName: '执行中',
            progressPercentage: 75,
            remainingDays: 120,
            createTime: '2024-01-15 10:00:00'
          }
        ],
        total: 1
      }));
      
    } else if (path === '/project/partner/list') {
      // 合作单位列表
      res.writeHead(200);
      res.end(JSON.stringify({
        code: 200,
        msg: '查询成功',
        rows: [
          {
            id: 1,
            partnerCode: 'P001',
            partnerName: '华为技术有限公司',
            partnerType: '企业',
            cooperationLevel: 'A',
            cooperationCount: 5,
            totalContractAmount: 12500000,
            lastCooperationDate: '2024-06-15',
            status: 1,
            statusName: '正常',
            createTime: '2024-01-01 10:00:00'
          },
          {
            id: 2,
            partnerCode: 'P002',
            partnerName: '清华大学',
            partnerType: '高校',
            cooperationLevel: 'A',
            cooperationCount: 8,
            totalContractAmount: 8600000,
            lastCooperationDate: '2024-07-10',
            status: 1,
            statusName: '正常',
            createTime: '2024-01-01 10:00:00'
          }
        ],
        total: 2
      }));
      
    } else {
      // 404 未找到
      res.writeHead(404);
      res.end(JSON.stringify({
        code: 404,
        msg: `接口不存在: ${path}`
      }));
    }
    
  } catch (error) {
    console.error('❌ 服务器错误:', error);
    res.writeHead(500);
    res.end(JSON.stringify({
      code: 500,
      msg: '服务器内部错误: ' + error.message
    }));
  }
});

// 启动服务器
const port = 8080;
server.listen(port, () => {
  console.log(`✅ 横向项目管理测试服务器已启动！`);
  console.log(`🌐 服务地址: http://localhost:${port}`);
  console.log('📋 可用接口:');
  console.log('  GET  /captchaImage - 验证码');
  console.log('  GET  /project/horizontal/list - 项目列表');
  console.log('  GET  /project/horizontal/{id} - 项目详情');
  console.log('  GET  /project/horizontal/statistics - 项目统计');
  console.log('  GET  /project/horizontal/generate-no - 生成项目编号');
  console.log('  GET  /project/contract/list - 合同列表');
  console.log('  GET  /project/partner/list - 合作单位列表');
  console.log('\n🎯 现在可以测试前端功能了！');
});

// 错误处理
server.on('error', (err) => {
  console.error('❌ 服务器启动失败:', err);
});

process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
