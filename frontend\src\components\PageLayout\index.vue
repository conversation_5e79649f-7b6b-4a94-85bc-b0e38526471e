<template>
  <div class="app-container">
    <div class="page-content">
      <!-- 查询表单区域 -->
      <div v-if="$slots.search" class="search-form">
        <slot name="search"></slot>
      </div>
      
      <!-- 操作按钮区域 -->
      <div v-if="$slots.actions" class="action-bar">
        <slot name="actions"></slot>
      </div>
      
      <!-- 主要内容区域 -->
      <div class="table-container">
        <slot name="content"></slot>
        
        <!-- 分页区域 -->
        <div v-if="$slots.pagination" class="pagination-container">
          <slot name="pagination"></slot>
        </div>
      </div>
    </div>
    
    <!-- 对话框区域 -->
    <slot name="dialogs"></slot>
  </div>
</template>

<script setup lang="ts">
// 页面布局组件，提供统一的页面结构
defineOptions({
  name: 'PageLayout'
})
</script>

<style scoped>
/* 组件内部样式已在全局样式中定义 */
</style>
