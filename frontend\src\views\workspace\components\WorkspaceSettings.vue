<template>
  <div class="workspace-settings">
    <el-tabs v-model="activeTab" tab-position="left">
      <!-- 布局设置 -->
      <el-tab-pane label="布局设置" name="layout">
        <div class="setting-section">
          <h4>卡片布局</h4>
          <el-radio-group v-model="localLayoutConfig.cardLayout">
            <el-radio label="grid">网格布局</el-radio>
            <el-radio label="list">列表布局</el-radio>
          </el-radio-group>
        </div>
        
        <div class="setting-section">
          <h4>卡片大小</h4>
          <el-radio-group v-model="localLayoutConfig.cardSize">
            <el-radio label="small">小</el-radio>
            <el-radio label="medium">中</el-radio>
            <el-radio label="large">大</el-radio>
          </el-radio-group>
        </div>
        
        <div class="setting-section">
          <h4>显示模块</h4>
          <el-checkbox v-model="localLayoutConfig.showUserInfo">用户信息卡片</el-checkbox>
          <el-checkbox v-model="localLayoutConfig.showTodoStats">待办事项卡片</el-checkbox>
          <el-checkbox v-model="localLayoutConfig.showNoticeStats">通知公告卡片</el-checkbox>
          <el-checkbox v-model="localLayoutConfig.showMessageStats">站内消息卡片</el-checkbox>
          <el-checkbox v-model="localLayoutConfig.showQuickApps">快捷应用卡片</el-checkbox>
          <el-checkbox v-model="localLayoutConfig.showStatistics">统计图表卡片</el-checkbox>
        </div>
        
        <div class="setting-actions">
          <el-button type="primary" @click="saveLayout">保存布局</el-button>
          <el-button @click="resetLayout">重置布局</el-button>
        </div>
      </el-tab-pane>
      
      <!-- 主题设置 -->
      <el-tab-pane label="主题设置" name="theme">
        <div class="setting-section">
          <h4>主题模式</h4>
          <el-radio-group v-model="localThemeConfig.theme">
            <el-radio label="light">浅色主题</el-radio>
            <el-radio label="dark">深色主题</el-radio>
            <el-radio label="auto">跟随系统</el-radio>
          </el-radio-group>
        </div>
        
        <div class="setting-section">
          <h4>主色调</h4>
          <div class="color-picker-group">
            <div class="color-options">
              <div 
                v-for="color in colorOptions" 
                :key="color.value"
                class="color-option"
                :class="{ active: localThemeConfig.primaryColor === color.value }"
                :style="{ backgroundColor: color.value }"
                @click="localThemeConfig.primaryColor = color.value"
              >
                <el-icon v-if="localThemeConfig.primaryColor === color.value"><Check /></el-icon>
              </div>
            </div>
            <el-color-picker 
              v-model="localThemeConfig.primaryColor" 
              show-alpha 
              :predefine="predefineColors"
            />
          </div>
        </div>
        
        <div class="setting-section">
          <h4>侧边栏样式</h4>
          <el-radio-group v-model="localThemeConfig.sidebarStyle">
            <el-radio label="light">浅色</el-radio>
            <el-radio label="dark">深色</el-radio>
          </el-radio-group>
        </div>
        
        <div class="setting-section">
          <h4>顶栏样式</h4>
          <el-radio-group v-model="localThemeConfig.headerStyle">
            <el-radio label="light">浅色</el-radio>
            <el-radio label="dark">深色</el-radio>
          </el-radio-group>
        </div>
        
        <div class="setting-actions">
          <el-button type="primary" @click="saveTheme">保存主题</el-button>
          <el-button @click="resetTheme">重置主题</el-button>
        </div>
      </el-tab-pane>
      
      <!-- 其他设置 -->
      <el-tab-pane label="其他设置" name="other">
        <div class="setting-section">
          <h4>自动刷新</h4>
          <el-switch 
            v-model="autoRefresh" 
            active-text="开启"
            inactive-text="关闭"
          />
          <p class="setting-desc">开启后工作台数据将每5分钟自动刷新</p>
        </div>
        
        <div class="setting-section">
          <h4>消息提醒</h4>
          <el-switch 
            v-model="messageNotification" 
            active-text="开启"
            inactive-text="关闭"
          />
          <p class="setting-desc">开启后有新消息时会显示桌面通知</p>
        </div>
        
        <div class="setting-section">
          <h4>数据导出</h4>
          <el-button @click="exportData">导出工作台数据</el-button>
          <p class="setting-desc">导出个人工作台的配置和数据</p>
        </div>
        
        <div class="setting-section danger">
          <h4>重置工作台</h4>
          <el-button type="danger" @click="confirmReset">完全重置</el-button>
          <p class="setting-desc">将清除所有个人配置，恢复到默认状态</p>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check } from '@element-plus/icons-vue'

// Props
interface Props {
  layoutConfig: any
  themeConfig: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits(['save-layout', 'save-theme', 'reset'])

// 响应式数据
const activeTab = ref('layout')
const autoRefresh = ref(true)
const messageNotification = ref(true)

// 本地配置副本
const localLayoutConfig = reactive({
  cardLayout: 'grid',
  cardSize: 'medium',
  showUserInfo: true,
  showTodoStats: true,
  showNoticeStats: true,
  showMessageStats: true,
  showQuickApps: true,
  showStatistics: true,
  ...props.layoutConfig
})

const localThemeConfig = reactive({
  primaryColor: '#409EFF',
  theme: 'light',
  sidebarStyle: 'dark',
  headerStyle: 'light',
  ...props.themeConfig
})

// 颜色选项
const colorOptions = [
  { name: '默认蓝', value: '#409EFF' },
  { name: '成功绿', value: '#67C23A' },
  { name: '警告橙', value: '#E6A23C' },
  { name: '危险红', value: '#F56C6C' },
  { name: '信息灰', value: '#909399' },
  { name: '紫色', value: '#722ED1' },
  { name: '青色', value: '#13C2C2' },
  { name: '粉色', value: '#EB2F96' }
]

const predefineColors = [
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  'rgba(255, 69, 0, 0.68)',
  'rgb(255, 120, 0)',
  'hsv(51, 100, 98)',
  'hsva(120, 40, 94, 0.5)',
  'hsl(181, 100%, 37%)',
  'hsla(209, 100%, 56%, 0.73)',
  '#c7158577'
]

// 监听props变化
watch(() => props.layoutConfig, (newVal) => {
  Object.assign(localLayoutConfig, newVal)
}, { deep: true })

watch(() => props.themeConfig, (newVal) => {
  Object.assign(localThemeConfig, newVal)
}, { deep: true })

// 保存布局配置
const saveLayout = () => {
  emit('save-layout', { ...localLayoutConfig })
}

// 重置布局配置
const resetLayout = () => {
  Object.assign(localLayoutConfig, {
    cardLayout: 'grid',
    cardSize: 'medium',
    showUserInfo: true,
    showTodoStats: true,
    showNoticeStats: true,
    showMessageStats: true,
    showQuickApps: true,
    showStatistics: true
  })
  ElMessage.success('布局配置已重置')
}

// 保存主题配置
const saveTheme = () => {
  emit('save-theme', { ...localThemeConfig })
}

// 重置主题配置
const resetTheme = () => {
  Object.assign(localThemeConfig, {
    primaryColor: '#409EFF',
    theme: 'light',
    sidebarStyle: 'dark',
    headerStyle: 'light'
  })
  ElMessage.success('主题配置已重置')
}

// 导出数据
const exportData = () => {
  const data = {
    layoutConfig: localLayoutConfig,
    themeConfig: localThemeConfig,
    exportTime: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `workspace-config-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('数据导出成功')
}

// 确认重置
const confirmReset = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作将清除所有个人工作台配置，是否继续？',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    emit('reset')
  } catch {
    // 用户取消
  }
}
</script>

<style scoped>
.workspace-settings {
  height: 100%;
}

.setting-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.setting-section:last-child {
  border-bottom: none;
}

.setting-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.setting-section .el-checkbox {
  display: block;
  margin-bottom: 8px;
}

.setting-section .el-radio {
  display: block;
  margin-bottom: 8px;
}

.setting-desc {
  margin: 8px 0 0 0;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.color-picker-group {
  display: flex;
  align-items: center;
  gap: 16px;
}

.color-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.color-option {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid transparent;
  transition: all 0.3s;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.active {
  border-color: #303133;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.color-option .el-icon {
  color: white;
  font-size: 16px;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

.setting-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.setting-section.danger {
  border-color: #fde2e2;
  background-color: #fef2f2;
  padding: 16px;
  border-radius: 6px;
}

.setting-section.danger h4 {
  color: #dc2626;
}

@media (max-width: 768px) {
  .workspace-settings :deep(.el-tabs--left .el-tabs__header) {
    width: 80px;
  }
  
  .workspace-settings :deep(.el-tabs--left .el-tabs__nav) {
    width: 80px;
  }
  
  .workspace-settings :deep(.el-tabs--left .el-tabs__item) {
    padding: 0 8px;
    font-size: 12px;
  }
  
  .color-options {
    flex-direction: column;
  }
  
  .color-picker-group {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .setting-actions {
    flex-direction: column;
  }
}
</style>
