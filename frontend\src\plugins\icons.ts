import type { App } from 'vue'

// 按需导入需要的图标
import {
  // 基础图标
  Search,
  Edit,
  Delete,
  Plus,
  Refresh,
  Download,
  Upload,
  View,
  Close,
  Check,
  
  // 导航图标
  Fold,
  Expand,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  
  // 功能图标
  FullScreen,
  Aim,
  Setting,
  User,
  Lock,
  Unlock,
  
  // 状态图标
  SuccessFilled,
  WarningFilled,
  InfoFilled,
  CircleCloseFilled,
  
  // 其他常用图标
  Document,
  Folder,
  FolderOpened,
  Files,
  Operation,
  Monitor,
  DataLine,
  PieChart,
  Trophy,
  Bell,
  Message,
  QuestionFilled
} from '@element-plus/icons-vue'

// 图标映射
const icons = {
  Search,
  Edit,
  Delete,
  Plus,
  Refresh,
  Download,
  Upload,
  View,
  Close,
  Check,
  Fold,
  Expand,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  FullScreen,
  Aim,
  Setting,
  User,
  Lock,
  Unlock,
  SuccessFilled,
  WarningFilled,
  InfoFilled,
  CircleCloseFilled,
  Document,
  Folder,
  FolderOpened,
  Files,
  Operation,
  Monitor,
  DataLine,
  <PERSON><PERSON>hart,
  Trophy,
  Bell,
  Message,
  QuestionFilled
}

export default function installIcons(app: App) {
  // 注册图标组件
  Object.entries(icons).forEach(([key, component]) => {
    app.component(key, component)
  })
}

// 导出图标供组件使用
export {
  Search,
  Edit,
  Delete,
  Plus,
  Refresh,
  Download,
  Upload,
  View,
  Close,
  Check,
  Fold,
  Expand,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  FullScreen,
  Aim,
  Setting,
  User,
  Lock,
  Unlock,
  SuccessFilled,
  WarningFilled,
  InfoFilled,
  CircleCloseFilled,
  Document,
  Folder,
  FolderOpened,
  Files,
  Operation,
  Monitor,
  DataLine,
  PieChart,
  Trophy,
  Bell,
  Message,
  QuestionFilled
}
