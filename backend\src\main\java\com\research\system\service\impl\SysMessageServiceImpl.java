package com.research.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.research.common.utils.SecurityUtils;
import com.research.system.domain.SysMessage;
import com.research.system.domain.SysUser;
import com.research.system.mapper.SysMessageMapper;
import com.research.system.mapper.SysUserMapper;
import com.research.system.service.ISysMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 站内消息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Service
public class SysMessageServiceImpl extends ServiceImpl<SysMessageMapper, SysMessage> implements ISysMessageService {

    @Autowired
    private SysMessageMapper messageMapper;

    @Autowired
    private SysUserMapper userMapper;

    /**
     * 查询收件箱消息列表
     */
    @Override
    public IPage<SysMessage> selectInboxMessageList(Page<SysMessage> page, SysMessage message, Long userId) {
        try {
            // 禁用自动COUNT查询，避免SQL语法错误
            page.setSearchCount(false);

            // 手动执行COUNT查询
            com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<SysMessage> countWrapper =
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
            countWrapper.eq("receiver_id", userId).eq("status", "0");
            if (message != null) {
                if (message.getTitle() != null && !message.getTitle().trim().isEmpty()) {
                    countWrapper.like("title", message.getTitle().trim());
                }
                if (message.getMessageType() != null && !message.getMessageType().trim().isEmpty()) {
                    countWrapper.eq("message_type", message.getMessageType().trim());
                }
                if (message.getPriority() != null && !message.getPriority().trim().isEmpty()) {
                    countWrapper.eq("priority", message.getPriority().trim());
                }
                if (message.getIsRead() != null && !message.getIsRead().trim().isEmpty()) {
                    countWrapper.eq("is_read", message.getIsRead().trim());
                }
            }
            // 使用Mapper直接查询总数，避免MyBatis-Plus版本兼容问题
            long total = messageMapper.selectInboxMessageCount(message, userId);

            // 执行分页查询
            IPage<SysMessage> result = messageMapper.selectInboxMessageList(page, message, userId);

            // 手动设置总数
            result.setTotal(total);

            return result;
        } catch (Exception e) {
            System.err.println("查询收件箱消息列表出错: " + e.getMessage());
            e.printStackTrace();

            // 返回空的分页结果
            IPage<SysMessage> emptyPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(page.getCurrent(), page.getSize());
            emptyPage.setRecords(new java.util.ArrayList<>());
            emptyPage.setTotal(0);
            return emptyPage;
        }
    }

    /**
     * 查询发件箱消息列表
     */
    @Override
    public IPage<SysMessage> selectOutboxMessageList(Page<SysMessage> page, SysMessage message, Long userId) {
        try {
            // 禁用自动COUNT查询，避免SQL语法错误
            page.setSearchCount(false);

            // 手动执行COUNT查询
            com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<SysMessage> countWrapper =
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
            countWrapper.eq("sender_id", userId).eq("status", "0");
            if (message != null) {
                if (message.getTitle() != null && !message.getTitle().trim().isEmpty()) {
                    countWrapper.like("title", message.getTitle().trim());
                }
                if (message.getMessageType() != null && !message.getMessageType().trim().isEmpty()) {
                    countWrapper.eq("message_type", message.getMessageType().trim());
                }
                if (message.getPriority() != null && !message.getPriority().trim().isEmpty()) {
                    countWrapper.eq("priority", message.getPriority().trim());
                }
                if (message.getReceiverName() != null && !message.getReceiverName().trim().isEmpty()) {
                    countWrapper.like("receiver_name", message.getReceiverName().trim());
                }
            }
            // 使用Mapper直接查询总数，避免MyBatis-Plus版本兼容问题
            long total = messageMapper.selectOutboxMessageCount(message, userId);

            // 执行分页查询
            IPage<SysMessage> result = messageMapper.selectOutboxMessageList(page, message, userId);

            // 手动设置总数
            result.setTotal(total);

            return result;
        } catch (Exception e) {
            System.err.println("查询发件箱消息列表出错: " + e.getMessage());
            e.printStackTrace();

            // 返回空的分页结果
            IPage<SysMessage> emptyPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(page.getCurrent(), page.getSize());
            emptyPage.setRecords(new java.util.ArrayList<>());
            emptyPage.setTotal(0);
            return emptyPage;
        }
    }

    /**
     * 查询消息详情
     */
    @Override
    @Transactional
    public SysMessage selectMessageDetail(Long messageId, Long userId) {
        SysMessage message = messageMapper.selectMessageDetail(messageId, userId);
        if (message != null && message.getReceiverId().equals(userId) && "0".equals(message.getIsRead())) {
            // 自动标记为已读
            markAsRead(messageId, userId);
            message.setIsRead("1");
            message.setReadTime(LocalDateTime.now());
        }
        return message;
    }

    /**
     * 发送消息
     */
    @Override
    @Transactional
    public boolean sendMessage(SysMessage message) {
        // 获取发送人信息
        Long senderId = SecurityUtils.getUserId();
        String senderName = SecurityUtils.getUsername();
        
        // 获取接收人信息
        SysUser receiver = userMapper.selectById(message.getReceiverId());
        if (receiver == null) {
            throw new RuntimeException("接收人不存在");
        }

        message.setSenderId(senderId);
        message.setSenderName(senderName);
        message.setReceiverName(receiver.getUserName());
        message.setCreateBy(senderName);
        message.setCreateTime(LocalDateTime.now());
        
        return save(message);
    }

    /**
     * 发送消息给多个用户
     */
    @Override
    @Transactional
    public boolean sendMessageToUsers(SysMessage message, List<Long> receiverIds) {
        // 设置发送人信息
        message.setSenderId(SecurityUtils.getUserId());
        message.setSenderName(SecurityUtils.getUsername());
        message.setCreateBy(SecurityUtils.getUsername());
        
        return messageMapper.sendMessageToUsers(message, receiverIds) > 0;
    }

    /**
     * 发送消息给部门
     */
    @Override
    @Transactional
    public boolean sendMessageToDepts(SysMessage message, List<Long> deptIds) {
        // 设置发送人信息
        message.setSenderId(SecurityUtils.getUserId());
        message.setSenderName(SecurityUtils.getUsername());
        message.setCreateBy(SecurityUtils.getUsername());
        
        return messageMapper.sendMessageToDepts(message, deptIds) > 0;
    }

    /**
     * 回复消息
     */
    @Override
    @Transactional
    public boolean replyMessage(Long originalMessageId, String replyContent) {
        SysMessage originalMessage = getById(originalMessageId);
        if (originalMessage == null) {
            return false;
        }

        // 创建回复消息
        SysMessage replyMessage = new SysMessage();
        replyMessage.setTitle("回复：" + originalMessage.getTitle());
        replyMessage.setContent(replyContent);
        replyMessage.setMessageType("3"); // 私信消息
        replyMessage.setReceiverId(originalMessage.getSenderId());
        replyMessage.setReceiverName(originalMessage.getSenderName());
        replyMessage.setBusinessId(originalMessageId.toString());
        replyMessage.setBusinessType("message_reply");
        
        boolean result = sendMessage(replyMessage);
        
        if (result) {
            // 更新原消息的回复信息
            originalMessage.setReplyContent(replyContent);
            originalMessage.setReplyTime(LocalDateTime.now());
            updateById(originalMessage);
        }
        
        return result;
    }

    /**
     * 转发消息
     */
    @Override
    @Transactional
    public boolean forwardMessage(Long messageId, List<Long> receiverIds, String forwardContent) {
        SysMessage originalMessage = getById(messageId);
        if (originalMessage == null) {
            return false;
        }

        // 创建转发消息
        SysMessage forwardMessage = new SysMessage();
        forwardMessage.setTitle("转发：" + originalMessage.getTitle());
        forwardMessage.setContent(forwardContent + "\n\n--- 转发内容 ---\n" + originalMessage.getContent());
        forwardMessage.setMessageType("3"); // 私信消息
        forwardMessage.setBusinessId(messageId.toString());
        forwardMessage.setBusinessType("message_forward");
        
        return sendMessageToUsers(forwardMessage, receiverIds);
    }

    /**
     * 标记消息为已读
     */
    @Override
    @Transactional
    public boolean markAsRead(Long messageId, Long userId) {
        return messageMapper.markAsRead(messageId, userId) > 0;
    }

    /**
     * 批量标记消息为已读
     */
    @Override
    @Transactional
    public boolean batchMarkAsRead(List<Long> messageIds, Long userId) {
        return messageMapper.batchMarkAsRead(messageIds, userId) > 0;
    }

    /**
     * 全部标记为已读
     */
    @Override
    @Transactional
    public boolean markAllAsRead(Long userId) {
        return messageMapper.markAllAsRead(userId) > 0;
    }

    /**
     * 删除消息
     */
    @Override
    @Transactional
    public boolean deleteMessage(Long messageId, Long userId) {
        return messageMapper.deleteMessage(messageId, userId) > 0;
    }

    /**
     * 批量删除消息
     */
    @Override
    @Transactional
    public boolean batchDeleteMessage(List<Long> messageIds, Long userId) {
        return messageMapper.batchDeleteMessage(messageIds, userId) > 0;
    }

    /**
     * 搜索消息
     */
    @Override
    public IPage<SysMessage> searchMessages(Page<SysMessage> page, String keyword, Long userId, String messageType) {
        return messageMapper.searchMessages(page, keyword, userId, messageType);
    }

    /**
     * 查询用户未读消息数量
     */
    @Override
    public Long selectUnreadMessageCount(Long userId) {
        return messageMapper.selectUnreadMessageCount(userId);
    }

    /**
     * 查询消息统计信息
     */
    @Override
    public Map<String, Object> selectMessageStatistics(Long userId) {
        return messageMapper.selectMessageStatistics(userId);
    }

    /**
     * 查询最新消息（用于工作台展示）
     */
    @Override
    public List<SysMessage> selectLatestMessages(Long userId, Integer limit) {
        return messageMapper.selectLatestMessages(userId, limit != null ? limit : 5);
    }

    /**
     * 发送系统消息
     */
    @Override
    @Transactional
    public boolean sendSystemMessage(String title, String content, List<Long> receiverIds) {
        SysMessage message = new SysMessage();
        message.setTitle(title);
        message.setContent(content);
        message.setMessageType("1"); // 系统消息
        message.setPriority("2"); // 中等优先级
        
        return sendMessageToUsers(message, receiverIds);
    }

    /**
     * 发送通知消息
     */
    @Override
    @Transactional
    public boolean sendNotificationMessage(String title, String content, List<Long> receiverIds, 
                                         String businessId, String businessType) {
        SysMessage message = new SysMessage();
        message.setTitle(title);
        message.setContent(content);
        message.setMessageType("2"); // 通知消息
        message.setPriority("3"); // 高优先级
        message.setBusinessId(businessId);
        message.setBusinessType(businessType);
        
        return sendMessageToUsers(message, receiverIds);
    }

    /**
     * 发送提醒消息
     */
    @Override
    @Transactional
    public boolean sendReminderMessage(String title, String content, Long receiverId, 
                                     String businessId, String businessType) {
        SysMessage message = new SysMessage();
        message.setTitle(title);
        message.setContent(content);
        message.setMessageType("4"); // 提醒消息
        message.setPriority("3"); // 高优先级
        message.setReceiverId(receiverId);
        message.setBusinessId(businessId);
        message.setBusinessType(businessType);
        
        return sendMessage(message);
    }
}
