package com.research.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.research.system.domain.SysQuickApp;
import com.research.system.domain.SysUser;
import com.research.system.domain.SysUserWorkspace;
import com.research.system.mapper.SysQuickAppMapper;
import com.research.system.mapper.SysUserMapper;
import com.research.system.mapper.SysUserWorkspaceMapper;
import com.research.system.service.ISysMessageService;
import com.research.system.service.ISysNoticeService;
import com.research.system.service.ISysTodoService;
import com.research.system.service.ISysUserWorkspaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户工作台Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Service
public class SysUserWorkspaceServiceImpl extends ServiceImpl<SysUserWorkspaceMapper, SysUserWorkspace> implements ISysUserWorkspaceService {

    @Autowired
    private SysUserWorkspaceMapper userWorkspaceMapper;

    @Autowired
    private SysQuickAppMapper quickAppMapper;

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private ISysTodoService todoService;

    @Autowired
    private ISysNoticeService noticeService;

    @Autowired
    private ISysMessageService messageService;

    /**
     * 获取用户工作台数据
     */
    @Override
    public Map<String, Object> getUserWorkspaceData(Long userId) {
        Map<String, Object> workspaceData = new HashMap<>();
        
        // 获取用户基本信息
        workspaceData.put("userInfo", getUserInfo(userId));
        
        // 获取待办统计
        workspaceData.put("todoStats", getUserTodoStats(userId));
        
        // 获取公告统计
        workspaceData.put("noticeStats", getUserNoticeStats(userId));
        
        // 获取消息统计
        workspaceData.put("messageStats", getUserMessageStats(userId));
        
        // 获取快捷应用
        workspaceData.put("quickApps", getUserQuickApps(userId));
        
        // 获取布局配置
        workspaceData.put("layoutConfig", getUserLayoutConfig(userId));
        
        // 获取主题配置
        workspaceData.put("themeConfig", getUserThemeConfig(userId));
        
        return workspaceData;
    }

    /**
     * 获取用户个人信息
     */
    @Override
    public Map<String, Object> getUserInfo(Long userId) {
        SysUser user = userMapper.selectById(userId);
        Map<String, Object> userInfo = new HashMap<>();
        
        if (user != null) {
            userInfo.put("userId", user.getUserId());
            userInfo.put("userName", user.getUserName());
            userInfo.put("nickName", user.getNickName());
            userInfo.put("email", user.getEmail());
            userInfo.put("phonenumber", user.getPhonenumber());
            userInfo.put("sex", user.getSex());
            userInfo.put("avatar", user.getAvatar());
            userInfo.put("deptName", user.getDept() != null ? user.getDept().getDeptName() : "");
            userInfo.put("status", user.getStatus());
            userInfo.put("loginDate", user.getLoginDate());
            userInfo.put("createTime", user.getCreateTime());
        }
        
        return userInfo;
    }

    /**
     * 获取用户待办统计
     */
    @Override
    public Map<String, Object> getUserTodoStats(Long userId) {
        Map<String, Object> todoStats = todoService.selectTodoStatistics(userId);
        
        // 获取最近待办
        List<Object> recentTodos = new ArrayList<>(todoService.selectRecentTodos(userId, 5));
        todoStats.put("recentTodos", recentTodos);
        
        // 获取即将到期的待办
        List<Object> dueSoonTodos = new ArrayList<>(todoService.selectDueSoonTodos(userId, 3));
        todoStats.put("dueSoonTodos", dueSoonTodos);
        
        // 获取逾期待办
        List<Object> overdueTodos = new ArrayList<>(todoService.selectOverdueTodos(userId));
        todoStats.put("overdueTodos", overdueTodos);
        
        return todoStats;
    }

    /**
     * 获取用户公告统计
     */
    @Override
    public Map<String, Object> getUserNoticeStats(Long userId) {
        Map<String, Object> noticeStats = new HashMap<>();
        
        // 获取未读公告数量
        Long unreadCount = noticeService.selectUnreadNoticeCount(userId);
        noticeStats.put("unreadCount", unreadCount);
        
        // 获取最新公告
        List<Object> latestNotices = new ArrayList<>(noticeService.selectLatestNotices(userId, 5));
        noticeStats.put("latestNotices", latestNotices);
        
        return noticeStats;
    }

    /**
     * 获取用户消息统计
     */
    @Override
    public Map<String, Object> getUserMessageStats(Long userId) {
        Map<String, Object> messageStats = messageService.selectMessageStatistics(userId);
        
        // 获取最新消息
        List<Object> latestMessages = new ArrayList<>(messageService.selectLatestMessages(userId, 5));
        messageStats.put("latestMessages", latestMessages);
        
        return messageStats;
    }

    /**
     * 获取用户快捷应用配置
     */
    @Override
    public List<SysQuickApp> getUserQuickApps(Long userId) {
        // 获取用户配置的快捷应用
        List<SysUserWorkspace> userConfigs = userWorkspaceMapper.selectUserConfig(userId, "quick_apps");
        
        if (userConfigs.isEmpty()) {
            // 如果用户没有配置，返回默认的快捷应用
            return quickAppMapper.selectEnabledApps().stream()
                    .limit(8)
                    .collect(Collectors.toList());
        }
        
        // 根据用户配置获取快捷应用
        List<String> appCodes = userConfigs.stream()
                .map(SysUserWorkspace::getConfigKey)
                .collect(Collectors.toList());
        
        List<SysQuickApp> quickApps = new ArrayList<>();
        for (String appCode : appCodes) {
            SysQuickApp app = quickAppMapper.selectByAppCode(appCode);
            if (app != null && "1".equals(app.getIsEnabled())) {
                quickApps.add(app);
            }
        }
        
        return quickApps;
    }

    /**
     * 保存用户快捷应用配置
     */
    @Override
    @Transactional
    public boolean saveUserQuickApps(Long userId, List<String> appCodes) {
        // 删除原有配置
        userWorkspaceMapper.deleteUserConfig(userId, "quick_apps", null);
        
        // 保存新配置
        for (int i = 0; i < appCodes.size(); i++) {
            SysUserWorkspace config = new SysUserWorkspace();
            config.setUserId(userId);
            config.setConfigType("quick_apps");
            config.setConfigKey(appCodes.get(i));
            config.setConfigValue("1");
            config.setSortOrder(i + 1);
            config.setIsEnabled("1");
            config.setCreateTime(LocalDateTime.now());
            config.setUpdateTime(LocalDateTime.now());
            
            userWorkspaceMapper.saveOrUpdateConfig(config);
        }
        
        return true;
    }

    /**
     * 获取用户工作台布局配置
     */
    @Override
    public Map<String, Object> getUserLayoutConfig(Long userId) {
        List<SysUserWorkspace> configs = userWorkspaceMapper.selectUserConfig(userId, "layout");
        Map<String, Object> layoutConfig = new HashMap<>();
        
        for (SysUserWorkspace config : configs) {
            layoutConfig.put(config.getConfigKey(), config.getConfigValue());
        }
        
        // 设置默认布局
        if (layoutConfig.isEmpty()) {
            layoutConfig.put("cardLayout", "grid");
            layoutConfig.put("cardSize", "medium");
            layoutConfig.put("showUserInfo", true);
            layoutConfig.put("showTodoStats", true);
            layoutConfig.put("showNoticeStats", true);
            layoutConfig.put("showMessageStats", true);
            layoutConfig.put("showQuickApps", true);
        }
        
        return layoutConfig;
    }

    /**
     * 保存用户工作台布局配置
     */
    @Override
    @Transactional
    public boolean saveUserLayoutConfig(Long userId, Map<String, Object> layoutConfig) {
        for (Map.Entry<String, Object> entry : layoutConfig.entrySet()) {
            saveOrUpdateUserConfig(userId, "layout", entry.getKey(), 
                    entry.getValue().toString(), 0);
        }
        return true;
    }

    /**
     * 获取用户主题配置
     */
    @Override
    public Map<String, Object> getUserThemeConfig(Long userId) {
        List<SysUserWorkspace> configs = userWorkspaceMapper.selectUserConfig(userId, "theme");
        Map<String, Object> themeConfig = new HashMap<>();
        
        for (SysUserWorkspace config : configs) {
            themeConfig.put(config.getConfigKey(), config.getConfigValue());
        }
        
        // 设置默认主题
        if (themeConfig.isEmpty()) {
            themeConfig.put("primaryColor", "#409EFF");
            themeConfig.put("theme", "light");
            themeConfig.put("sidebarStyle", "dark");
            themeConfig.put("headerStyle", "light");
        }
        
        return themeConfig;
    }

    /**
     * 保存用户主题配置
     */
    @Override
    @Transactional
    public boolean saveUserThemeConfig(Long userId, Map<String, Object> themeConfig) {
        for (Map.Entry<String, Object> entry : themeConfig.entrySet()) {
            saveOrUpdateUserConfig(userId, "theme", entry.getKey(), 
                    entry.getValue().toString(), 0);
        }
        return true;
    }

    /**
     * 获取用户配置
     */
    @Override
    public List<SysUserWorkspace> getUserConfig(Long userId, String configType) {
        return userWorkspaceMapper.selectUserConfig(userId, configType);
    }

    /**
     * 获取用户特定配置
     */
    @Override
    public SysUserWorkspace getUserConfigByKey(Long userId, String configType, String configKey) {
        return userWorkspaceMapper.selectUserConfigByKey(userId, configType, configKey);
    }

    /**
     * 保存或更新用户配置
     */
    @Override
    @Transactional
    public boolean saveOrUpdateUserConfig(Long userId, String configType, String configKey, 
                                        String configValue, Integer sortOrder) {
        SysUserWorkspace config = new SysUserWorkspace();
        config.setUserId(userId);
        config.setConfigType(configType);
        config.setConfigKey(configKey);
        config.setConfigValue(configValue);
        config.setSortOrder(sortOrder != null ? sortOrder : 0);
        config.setIsEnabled("1");
        config.setCreateTime(LocalDateTime.now());
        config.setUpdateTime(LocalDateTime.now());
        
        return userWorkspaceMapper.saveOrUpdateConfig(config) > 0;
    }

    /**
     * 删除用户配置
     */
    @Override
    @Transactional
    public boolean deleteUserConfig(Long userId, String configType, String configKey) {
        return userWorkspaceMapper.deleteUserConfig(userId, configType, configKey) > 0;
    }

    /**
     * 重置用户工作台配置
     */
    @Override
    @Transactional
    public boolean resetUserWorkspace(Long userId) {
        return userWorkspaceMapper.deleteAllUserConfig(userId) > 0;
    }

    /**
     * 获取系统可用的快捷应用
     */
    @Override
    public List<SysQuickApp> getAvailableQuickApps(Long userId) {
        return quickAppMapper.selectUserAccessibleApps(userId);
    }

    /**
     * 获取工作台统计数据
     */
    @Override
    public Map<String, Object> getWorkspaceStatistics(Long userId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 待办统计
        Map<String, Object> todoStats = todoService.selectTodoStatistics(userId);
        statistics.put("todoTotal", todoStats.get("total"));
        statistics.put("todoPending", todoStats.get("pending"));
        statistics.put("todoCompleted", todoStats.get("completed"));
        
        // 公告统计
        Long unreadNoticeCount = noticeService.selectUnreadNoticeCount(userId);
        statistics.put("unreadNotices", unreadNoticeCount);
        
        // 消息统计
        Long unreadMessageCount = messageService.selectUnreadMessageCount(userId);
        statistics.put("unreadMessages", unreadMessageCount);
        
        return statistics;
    }
}
