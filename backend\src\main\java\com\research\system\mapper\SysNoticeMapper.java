package com.research.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.research.system.domain.SysNotice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通知公告Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Mapper
public interface SysNoticeMapper extends BaseMapper<SysNotice> {

    /**
     * 查询通知公告列表 - 暂时注释，使用MyBatis Plus标准方式
     *
     * @param page 分页参数
     * @param notice 查询条件
     * @param userId 当前用户ID（用于查询阅读状态）
     * @return 通知公告列表
     */
    // IPage<SysNotice> selectNoticeList(Page<SysNotice> page, @Param("notice") SysNotice notice, @Param("userId") Long userId);

    /**
     * 查询用户可见的通知公告列表
     * 
     * @param page 分页参数
     * @param notice 查询条件
     * @param userId 用户ID
     * @param deptId 部门ID
     * @return 通知公告列表
     */
    IPage<SysNotice> selectUserNoticeList(Page<SysNotice> page, @Param("notice") SysNotice notice, 
                                         @Param("userId") Long userId, @Param("deptId") Long deptId);

    /**
     * 查询通知公告详情（包含阅读状态）
     * 
     * @param noticeId 公告ID
     * @param userId 用户ID
     * @return 通知公告详情
     */
    SysNotice selectNoticeDetail(@Param("noticeId") Long noticeId, @Param("userId") Long userId);

    /**
     * 查询最新公告列表（用于工作台展示）
     * 
     * @param userId 用户ID
     * @param deptId 部门ID
     * @param limit 限制数量
     * @return 最新公告列表
     */
    List<SysNotice> selectLatestNotices(@Param("userId") Long userId, @Param("deptId") Long deptId, @Param("limit") Integer limit);

    /**
     * 查询用户未读公告数量
     * 
     * @param userId 用户ID
     * @param deptId 部门ID
     * @return 未读公告数量
     */
    Long selectUnreadNoticeCount(@Param("userId") Long userId, @Param("deptId") Long deptId);

    /**
     * 增加公告阅读次数
     * 
     * @param noticeId 公告ID
     * @return 影响行数
     */
    int incrementReadCount(@Param("noticeId") Long noticeId);

    /**
     * 查询公告阅读统计
     * 
     * @param noticeId 公告ID
     * @return 阅读统计信息
     */
    List<java.util.Map<String, Object>> selectNoticeReadStats(@Param("noticeId") Long noticeId);

    /**
     * 全文搜索公告
     * 
     * @param page 分页参数
     * @param keyword 搜索关键词
     * @param userId 用户ID
     * @param deptId 部门ID
     * @return 搜索结果
     */
    IPage<SysNotice> searchNotices(Page<SysNotice> page, @Param("keyword") String keyword, 
                                  @Param("userId") Long userId, @Param("deptId") Long deptId);
}
