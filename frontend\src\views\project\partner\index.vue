<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="单位编码" prop="partnerCode">
        <el-input
          v-model="queryParams.partnerCode"
          placeholder="请输入单位编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单位名称" prop="partnerName">
        <el-input
          v-model="queryParams.partnerName"
          placeholder="请输入单位名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单位类型" prop="partnerType">
        <el-select v-model="queryParams.partnerType" placeholder="请选择单位类型" clearable>
          <el-option label="企业" value="企业" />
          <el-option label="高校" value="高校" />
          <el-option label="科研院所" value="科研院所" />
          <el-option label="政府机构" value="政府机构" />
        </el-select>
      </el-form-item>
      <el-form-item label="合作等级" prop="cooperationLevel">
        <el-select v-model="queryParams.cooperationLevel" placeholder="请选择合作等级" clearable>
          <el-option label="A级" value="A" />
          <el-option label="B级" value="B" />
          <el-option label="C级" value="C" />
          <el-option label="D级" value="D" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="禁用" value="0" />
          <el-option label="正常" value="1" />
          <el-option label="待审核" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['project:partner:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['project:partner:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:partner:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['project:partner:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['project:partner:import']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="TrendCharts"
          @click="handleStatistics"
          v-hasPermi="['project:partner:statistics']"
        >统计</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="partnerList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="单位编码" align="center" prop="partnerCode" width="120" />
      <el-table-column label="单位名称" align="center" prop="partnerName" :show-overflow-tooltip="true" />
      <el-table-column label="单位类型" align="center" prop="partnerType" width="100" />
      <el-table-column label="法定代表人" align="center" prop="legalRepresentative" width="120" />
      <el-table-column label="联系人" align="center" prop="contactPerson" width="100" />
      <el-table-column label="联系电话" align="center" prop="contactPhone" width="120" />
      <el-table-column label="合作等级" align="center" prop="cooperationLevel" width="100">
        <template #default="scope">
          <el-tag :type="getLevelType(scope.row.cooperationLevel)">
            {{ scope.row.cooperationLevel }}级
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="合作次数" align="center" prop="cooperationCount" width="100" />
      <el-table-column label="累计金额" align="center" prop="totalContractAmount" width="120">
        <template #default="scope">
          <span>{{ formatMoney(scope.row.totalContractAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最近合作" align="center" prop="lastCooperationDate" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.lastCooperationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template #default="scope">
          <dict-tag :options="statusOptions" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['project:partner:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['project:partner:edit']">修改</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['project:partner:manage']">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="enable" v-if="scope.row.status === 0">启用</el-dropdown-item>
                <el-dropdown-item command="disable" v-if="scope.row.status === 1">禁用</el-dropdown-item>
                <el-dropdown-item command="audit" v-if="scope.row.status === 2">审核</el-dropdown-item>
                <el-dropdown-item command="level">调整等级</el-dropdown-item>
                <el-dropdown-item command="evaluate">评价</el-dropdown-item>
                <el-dropdown-item command="projects">查看项目</el-dropdown-item>
                <el-dropdown-item command="contracts">查看合同</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['project:partner:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改合作单位对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="partnerRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="单位编码" prop="partnerCode">
              <el-input v-model="form.partnerCode" placeholder="请输入单位编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位名称" prop="partnerName">
              <el-input v-model="form.partnerName" placeholder="请输入单位名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="单位类型" prop="partnerType">
              <el-select v-model="form.partnerType" placeholder="请选择单位类型">
                <el-option label="企业" value="企业" />
                <el-option label="高校" value="高校" />
                <el-option label="科研院所" value="科研院所" />
                <el-option label="政府机构" value="政府机构" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合作等级" prop="cooperationLevel">
              <el-select v-model="form.cooperationLevel" placeholder="请选择合作等级">
                <el-option label="A级" value="A" />
                <el-option label="B级" value="B" />
                <el-option label="C级" value="C" />
                <el-option label="D级" value="D" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="法定代表人" prop="legalRepresentative">
              <el-input v-model="form.legalRepresentative" placeholder="请输入法定代表人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="统一社会信用代码" prop="creditCode">
              <el-input v-model="form.creditCode" placeholder="请输入统一社会信用代码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="form.contactPerson" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系邮箱" prop="contactEmail">
              <el-input v-model="form.contactEmail" placeholder="请输入联系邮箱" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮政编码" prop="postalCode">
              <el-input v-model="form.postalCode" placeholder="请输入邮政编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="注册资本" prop="registrationCapital">
              <el-input-number v-model="form.registrationCapital" :min="0" :precision="2" placeholder="请输入注册资本" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="成立日期" prop="establishmentDate">
              <el-date-picker
                v-model="form.establishmentDate"
                type="date"
                placeholder="选择成立日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开户银行" prop="bankName">
              <el-input v-model="form.bankName" placeholder="请输入开户银行" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="银行账号" prop="bankAccount">
              <el-input v-model="form.bankAccount" placeholder="请输入银行账号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="单位地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入单位地址" />
        </el-form-item>
        <el-form-item label="官方网站" prop="website">
          <el-input v-model="form.website" placeholder="请输入官方网站" />
        </el-form-item>
        <el-form-item label="经营范围" prop="businessScope">
          <el-input v-model="form.businessScope" type="textarea" :rows="3" placeholder="请输入经营范围" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 合作单位详情对话框 -->
    <partner-detail v-model="detailOpen" :partner-id="currentPartnerId" />

    <!-- 统计分析对话框 -->
    <partner-statistics v-model="statisticsOpen" />

    <!-- 导入对话框 -->
    <el-dialog title="合作单位导入" v-model="importOpen" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的合作单位数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="importOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Partner">
import { 
  listPartner, 
  getPartner, 
  delPartner, 
  delPartners,
  addPartner, 
  updatePartner,
  exportPartner,
  enablePartner,
  disablePartner,
  auditPartner,
  updateCooperationLevel,
  generatePartnerCode,
  importPartners
} from "@/api/project/partner"
import PartnerDetail from './components/PartnerDetail.vue'
import PartnerStatistics from './components/PartnerStatistics.vue'

const { proxy } = getCurrentInstance()

const partnerList = ref([])
const open = ref(false)
const detailOpen = ref(false)
const statisticsOpen = ref(false)
const importOpen = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const currentPartnerId = ref(null)

// 上传参数
const upload = reactive({
  open: false,
  title: "",
  isUploading: false,
  updateSupport: 0,
  headers: { Authorization: "Bearer " + getToken() },
  url: import.meta.env.VITE_APP_BASE_API + "/project/partner/import"
})

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    partnerCode: null,
    partnerName: null,
    partnerType: null,
    cooperationLevel: null,
    status: null
  },
  rules: {
    partnerCode: [
      { required: true, message: "单位编码不能为空", trigger: "blur" }
    ],
    partnerName: [
      { required: true, message: "单位名称不能为空", trigger: "blur" }
    ],
    partnerType: [
      { required: true, message: "单位类型不能为空", trigger: "change" }
    ],
    legalRepresentative: [
      { required: true, message: "法定代表人不能为空", trigger: "blur" }
    ],
    contactPerson: [
      { required: true, message: "联系人不能为空", trigger: "blur" }
    ],
    contactPhone: [
      { required: true, message: "联系电话不能为空", trigger: "blur" }
    ]
  }
})

const { queryParams, form, rules } = toRefs(data)

// 状态选项
const statusOptions = ref([
  { label: "禁用", value: "0" },
  { label: "正常", value: "1" },
  { label: "待审核", value: "2" }
])

/** 查询合作单位列表 */
function getList() {
  loading.value = true
  listPartner(queryParams.value).then(response => {
    partnerList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    partnerCode: null,
    partnerName: null,
    partnerType: null,
    cooperationLevel: 'C',
    legalRepresentative: null,
    contactPerson: null,
    contactPhone: null,
    contactEmail: null,
    address: null,
    postalCode: null,
    website: null,
    businessScope: null,
    registrationCapital: null,
    establishmentDate: null,
    creditCode: null,
    bankName: null,
    bankAccount: null,
    remark: null
  }
  proxy.resetForm("partnerRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
async function handleAdd() {
  reset()
  // 生成单位编码
  try {
    const response = await generatePartnerCode()
    form.value.partnerCode = response.data
  } catch (error) {
    console.error('生成单位编码失败:', error)
  }
  open.value = true
  title.value = "添加合作单位"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value
  getPartner(id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改合作单位"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["partnerRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updatePartner(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addPartner(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const partnerIds = row.id || ids.value
  proxy.$modal.confirm('是否确认删除合作单位编号为"' + partnerIds + '"的数据项？').then(function() {
    return delPartners(partnerIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('project/partner/export', {
    ...queryParams.value
  }, `partner_${new Date().getTime()}.xlsx`)
}

/** 导入按钮操作 */
function handleImport() {
  importOpen.value = true
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download('project/partner/importTemplate', {}, `partner_template_${new Date().getTime()}.xlsx`)
}

/** 文件上传中处理 */
function handleFileUploadProgress(event, file, fileList) {
  upload.isUploading = true
}

/** 文件上传成功处理 */
function handleFileSuccess(response, file, fileList) {
  upload.isUploading = false
  proxy.$refs["uploadRef"].clearFiles()
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true })
  getList()
}

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit()
}

/** 详情按钮操作 */
function handleDetail(row) {
  currentPartnerId.value = row.id
  detailOpen.value = true
}

/** 统计按钮操作 */
function handleStatistics() {
  statisticsOpen.value = true
}

/** 更多操作命令处理 */
function handleCommand(command, row) {
  switch (command) {
    case 'enable':
      handleEnable(row)
      break
    case 'disable':
      handleDisable(row)
      break
    case 'audit':
      handleAudit(row)
      break
    case 'level':
      handleUpdateLevel(row)
      break
    case 'evaluate':
      handleEvaluate(row)
      break
    case 'projects':
      handleViewProjects(row)
      break
    case 'contracts':
      handleViewContracts(row)
      break
  }
}

/** 启用合作单位 */
function handleEnable(row) {
  proxy.$modal.confirm('确认启用该合作单位？').then(() => {
    return enablePartner(row.id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("启用成功")
  })
}

/** 禁用合作单位 */
function handleDisable(row) {
  proxy.$modal.confirm('确认禁用该合作单位？').then(() => {
    return disablePartner(row.id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("禁用成功")
  })
}

/** 审核合作单位 */
function handleAudit(row) {
  proxy.$modal.confirm('确认审核通过该合作单位？').then(() => {
    return auditPartner(row.id, true)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("审核通过")
  })
}

/** 调整合作等级 */
function handleUpdateLevel(row) {
  proxy.$prompt('请选择新的合作等级', '调整等级', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'select',
    inputOptions: [
      { value: 'A', label: 'A级' },
      { value: 'B', label: 'B级' },
      { value: 'C', label: 'C级' },
      { value: 'D', label: 'D级' }
    ]
  }).then(({ value }) => {
    return updateCooperationLevel(row.id, value)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("等级调整成功")
  })
}

/** 评价合作单位 */
function handleEvaluate(row) {
  // 这里应该打开评价对话框
  proxy.$modal.msgInfo("评价功能开发中...")
}

/** 查看项目 */
function handleViewProjects(row) {
  // 这里应该跳转到项目列表页面，并筛选该合作单位的项目
  proxy.$modal.msgInfo("查看项目功能开发中...")
}

/** 查看合同 */
function handleViewContracts(row) {
  // 这里应该跳转到合同列表页面，并筛选该合作单位的合同
  proxy.$modal.msgInfo("查看合同功能开发中...")
}

// 获取合作等级类型
function getLevelType(level) {
  const levelMap = {
    'A': 'success',
    'B': 'primary',
    'C': 'warning',
    'D': 'danger'
  }
  return levelMap[level] || 'info'
}

// 格式化金额
function formatMoney(money) {
  if (!money) return '0.00'
  return parseFloat(money).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

onMounted(() => {
  getList()
})
</script>
