package com.research.workflow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.research.workflow.domain.WorkflowModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作流模型Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface WorkflowModelMapper extends BaseMapper<WorkflowModel> {

    /**
     * 查询工作流模型列表
     * 
     * @param page 分页对象
     * @param model 工作流模型
     * @return 工作流模型集合
     */
    IPage<WorkflowModel> selectModelList(Page<WorkflowModel> page, @Param("model") WorkflowModel model);

    /**
     * 查询工作流模型详情
     * 
     * @param id 模型ID
     * @return 工作流模型
     */
    WorkflowModel selectModelById(@Param("id") String id);

    /**
     * 根据模型Key查询模型
     * 
     * @param key 模型Key
     * @return 工作流模型
     */
    WorkflowModel selectModelByKey(@Param("key") String key);

    /**
     * 查询模型的最大版本号
     * 
     * @param key 模型Key
     * @return 最大版本号
     */
    Integer selectMaxVersionByKey(@Param("key") String key);

    /**
     * 查询已部署的模型列表
     * 
     * @return 已部署的模型列表
     */
    List<WorkflowModel> selectDeployedModels();

    /**
     * 查询模型统计信息
     * 
     * @return 统计信息
     */
    java.util.Map<String, Object> selectModelStatistics();

    /**
     * 根据分类查询模型数量
     * 
     * @param category 分类
     * @return 模型数量
     */
    Long selectCountByCategory(@Param("category") String category);

    /**
     * 查询最近创建的模型
     * 
     * @param limit 限制数量
     * @return 模型列表
     */
    List<WorkflowModel> selectRecentModels(@Param("limit") Integer limit);

    /**
     * 查询最近更新的模型
     * 
     * @param limit 限制数量
     * @return 模型列表
     */
    List<WorkflowModel> selectRecentUpdatedModels(@Param("limit") Integer limit);

    /**
     * 批量更新模型状态
     * 
     * @param ids 模型ID列表
     * @param status 状态
     * @param updateBy 更新人
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("ids") List<String> ids, @Param("status") String status, @Param("updateBy") String updateBy);

    /**
     * 清理未部署的草稿模型
     * 
     * @param days 天数
     * @return 清理数量
     */
    int cleanDraftModels(@Param("days") Integer days);
}
