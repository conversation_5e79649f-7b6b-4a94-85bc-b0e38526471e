package com.research.common.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.research.common.core.page.TableDataInfo;

/**
 * MyBatis Plus 分页工具类
 * 统一处理分页相关的问题
 * 
 * <AUTHOR>
 */
public class MybatisPlusPageUtils {

    /**
     * 创建安全的分页对象
     * 
     * @return Page对象
     */
    public static <T> Page<T> createSafePage() {
        return createSafePage(PageUtils.getPageNum(), PageUtils.getPageSize());
    }

    /**
     * 创建安全的分页对象
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return Page对象
     */
    public static <T> Page<T> createSafePage(int pageNum, int pageSize) {
        // 分页参数安全检查
        if (pageNum <= 0) pageNum = 1;
        if (pageSize <= 0) pageSize = 10;
        if (pageSize > 100) pageSize = 100; // 限制最大分页大小
        
        Page<T> page = new Page<>(pageNum, pageSize);
        
        // 禁用自动COUNT查询，避免SQL语法错误
        page.setSearchCount(false);
        
        return page;
    }

    /**
     * 执行分页查询（带手动COUNT）
     * 
     * @param page 分页对象
     * @param queryFunction 查询函数
     * @param countFunction COUNT查询函数
     * @return 分页结果
     */
    public static <T> IPage<T> executePageQuery(Page<T> page, 
                                               java.util.function.Function<Page<T>, IPage<T>> queryFunction,
                                               java.util.function.Supplier<Long> countFunction) {
        try {
            // 手动执行COUNT查询
            long total = countFunction.get();
            
            // 执行分页查询
            IPage<T> result = queryFunction.apply(page);
            
            // 手动设置总数
            result.setTotal(total);
            
            return result;
        } catch (Exception e) {
            System.err.println("分页查询失败: " + e.getMessage());
            e.printStackTrace();
            
            // 返回空的分页结果
            IPage<T> emptyPage = new Page<>(page.getCurrent(), page.getSize());
            emptyPage.setRecords(new java.util.ArrayList<>());
            emptyPage.setTotal(0);
            return emptyPage;
        }
    }

    /**
     * 将IPage转换为TableDataInfo
     *
     * @param page IPage对象
     * @return TableDataInfo对象
     */
    public static TableDataInfo convertToTableDataInfo(IPage<?> page) {
        return convertToTableDataInfo(page, "查询成功");
    }

    /**
     * 将IPage转换为TableDataInfo
     *
     * @param page IPage对象
     * @param message 消息
     * @return TableDataInfo对象
     */
    public static TableDataInfo convertToTableDataInfo(IPage<?> page, String message) {
        TableDataInfo result = new TableDataInfo();
        result.setRows(page.getRecords());
        result.setTotal(page.getTotal());
        result.setCode(200);
        result.setMsg(message);
        return result;
    }

    /**
     * 创建空的TableDataInfo
     * 
     * @return 空的TableDataInfo对象
     */
    public static TableDataInfo createEmptyTableDataInfo() {
        return createEmptyTableDataInfo("查询成功");
    }

    /**
     * 创建空的TableDataInfo
     * 
     * @param message 消息
     * @return 空的TableDataInfo对象
     */
    public static TableDataInfo createEmptyTableDataInfo(String message) {
        TableDataInfo result = new TableDataInfo();
        result.setCode(200);
        result.setMsg(message);
        result.setTotal(0L);
        result.setRows(new java.util.ArrayList<>());
        return result;
    }

    /**
     * 安全执行分页查询（Controller层使用）
     *
     * @param queryFunction 查询函数，接收分页参数，返回IPage结果
     * @return TableDataInfo对象
     */
    @SuppressWarnings("unchecked")
    public static TableDataInfo safePageQuery(java.util.function.Function<Page<?>, IPage<?>> queryFunction) {
        return safePageQuery(queryFunction, "查询成功");
    }

    /**
     * 安全执行分页查询（Controller层使用）
     *
     * @param queryFunction 查询函数，接收分页参数，返回IPage结果
     * @param successMessage 成功消息
     * @return TableDataInfo对象
     */
    @SuppressWarnings("unchecked")
    public static TableDataInfo safePageQuery(java.util.function.Function<Page<?>, IPage<?>> queryFunction,
                                              String successMessage) {
        try {
            // 创建安全的分页对象
            Page<?> page = createSafePage();

            // 执行查询
            IPage<?> result = queryFunction.apply(page);

            // 转换为TableDataInfo
            return convertToTableDataInfo(result, successMessage);
        } catch (Exception e) {
            System.err.println("分页查询失败: " + e.getMessage());
            e.printStackTrace();

            // 返回空结果
            return createEmptyTableDataInfo("查询失败，请稍后重试");
        }
    }

    /**
     * 安全执行分页查询（带手动COUNT，Controller层使用）
     * 
     * @param queryFunction 查询函数
     * @param countFunction COUNT查询函数
     * @return TableDataInfo对象
     */
    public static <T> TableDataInfo safePageQueryWithCount(
            java.util.function.Function<Page<T>, IPage<T>> queryFunction,
            java.util.function.Supplier<Long> countFunction) {
        return safePageQueryWithCount(queryFunction, countFunction, "查询成功");
    }

    /**
     * 安全执行分页查询（带手动COUNT，Controller层使用）
     * 
     * @param queryFunction 查询函数
     * @param countFunction COUNT查询函数
     * @param successMessage 成功消息
     * @return TableDataInfo对象
     */
    public static <T> TableDataInfo safePageQueryWithCount(
            java.util.function.Function<Page<T>, IPage<T>> queryFunction,
            java.util.function.Supplier<Long> countFunction,
            String successMessage) {
        try {
            // 创建安全的分页对象
            Page<T> page = createSafePage();
            
            // 执行带COUNT的分页查询
            IPage<T> result = executePageQuery(page, queryFunction, countFunction);
            
            // 转换为TableDataInfo
            return convertToTableDataInfo(result, successMessage);
        } catch (Exception e) {
            System.err.println("分页查询失败: " + e.getMessage());
            e.printStackTrace();
            
            // 返回空结果
            return createEmptyTableDataInfo("查询失败，请稍后重试");
        }
    }
}
