package com.research.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.research.system.domain.SysTodoHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 待办处理历史Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Mapper
public interface SysTodoHistoryMapper extends BaseMapper<SysTodoHistory> {

    /**
     * 查询待办处理历史
     * 
     * @param todoId 待办ID
     * @return 处理历史列表
     */
    List<SysTodoHistory> selectHistoryByTodoId(@Param("todoId") Long todoId);

    /**
     * 插入处理历史记录
     * 
     * @param history 历史记录
     * @return 影响行数
     */
    int insertTodoHistory(SysTodoHistory history);

    /**
     * 删除待办的所有历史记录
     * 
     * @param todoId 待办ID
     * @return 影响行数
     */
    int deleteByTodoId(@Param("todoId") Long todoId);

    /**
     * 查询用户的操作历史
     * 
     * @param operatorId 操作人ID
     * @param limit 限制数量
     * @return 操作历史列表
     */
    List<SysTodoHistory> selectHistoryByOperator(@Param("operatorId") Long operatorId, @Param("limit") Integer limit);
}
