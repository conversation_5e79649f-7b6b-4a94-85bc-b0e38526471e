序号	设备名称／支出项目	型号规格／支出用途概述	单位	数量
1	科研成果多维敏捷管控中心（基础成熟平台）	一、敏捷支撑平台
通过敏捷平台建设一套学校科研成果管理服务平台，基于敏捷平台开发的流程引擎及低代码工具，构建学校科研成果量化管理服务平台应用，并通过流程引擎、低代码工具创建科研服务，丰富平台的应用中心，提供更多的基于科研成果的应用服务，例如：技术职称评审、科研成果奖励、科研档案查询等，为学校各个部门、院系提供更便捷的科研应用服务。
★1、融合门户（个人工作台）
学校业务综合门户，支持门户配置构建，包括用户待办、发起、常用应用、公告、日程、关注、传阅、课程表等首页部件。实现PC端和移动端的首页展示，确保用户在不同设备上都能方便使用。（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
2、用户登录的页面（个人工作台）包含：通知公告，展示相关的通知公告；待办事项，根据用户权限向用户展示用户所有的待办事项；站内消息，展示系统向用户推送的消息；通讯录，系统管理员管理机构通讯录的管理，及用户可以查看系统管理员发布的通讯录。
3、通知公告
3.1、全文检索查询
按分类树、标题、日期、作者等方式进行全文检索和查询。
3.2、通知公告栏
展示当前用户权限下前十条通知公告标题。通知公告需具有最新和未读的标记。点击单条通知，跳转到该通知公告的详情页面。点击“更多”，跳转到通知公告列表。
3.3、通知公告列表
分页展示所有通知公告标题以及发布时间。可通过标题关键字和发布时间实现通知公告查询。
通知公告详情显示单条通知公告的详情。包括标题、主体、发布时间、来源。可设置通知公告字体。具有打印功能。
3.4、通知公告管理
系统内通知公告的新增、删除、修改和发布。
4、待办事项
4.1、待办事项栏
展示当前用户权限下前十条待办事项标题。待办事项需具有最新和未办的标记。点击单条待办，跳转到该待办所对应的应用子系统。点击“更多”，跳转到待办事项列表。
4.2、待办事项列表
分页展示所有待办事项标题以及对应子系统。可通过标题关键字和子系统实现待办事项查询。
4.3、待办事项详情
显示单条待办事项的详情。包括标题、主体、发布时间、来源。可设置待办事项字体。具有打印功能。
5、站内消息
5.1、站内消息栏
展示当前用户权限下前十条站内消息标题。站内消息需具有最新和未办的标记。点击单条待办，跳转到该待办所对应的应用子系统。点击“更多”，跳转到站内消息列表。
5.2、站内消息列表
分页展示所有站内消息标题以及对应子系统。可通过标题关键字和子系统实现站内消息查询。
5.3、站内消息详情
显示单条站内消息的详情。包括标题、主体、发布时间、来源。可设置站内消息字体。具有打印功能。
5.4、站内消息发送
消息创建和发送。可选定接收对象。
在已发送消息列表查看本账号已发送的消息
6、通讯录
6.1、通讯录列表
用户可查看管理员发布的机构通讯录。可根据所属机构/单位、姓名等进行通讯录的查询。
6.2、通讯录详情
点击查看各组织的通讯录
通讯录管理用户对本机构通讯录新增/修改/删除
7、身份认证与权限管理
支持多种认证方式，包括账号密码、数字证书、微信等。实现统一身份认证，确保系统安全性和用户管理的便捷性。
实现教师、学生、职工等不同用户角色统一登录认证，支持多种认证方式，与学校其他系统身份认证集成。
7.1、身份认证：统一身份认证平台是一种集中的身份验证系统，用于管理和控制对多个应用程序或服务的访问，使用单点登录（SSO）机制，用户只需在平台上进行一次身份验证就可以访问所有相互信任的应用系统，无需再次登录。
7.2、所有应用系统共享一个身份认证系统
7.3、所有应用系统能够识别和提取ticket信息
★7.4、用户可以实现重置密码、绑定、解绑等功能。
8、权限管理
★8.1、批量授权：对于某个权限支持快速的进行授权管理，支持人、岗位、部门、人员类别等不同维度的设置。（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
★8.2、复制权限：将某个人、部门、岗位的权限快速的复制给其他人、岗位、部门、人员类别。（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
9、流程引擎
9.1、工作流系统是面向合作伙伴及最终用户提供的新一代工作流产品，产品全面涵盖流程建模、运行、管理、监控等业务流程管理功能。利用工作流系统提供的各类功能，可以很好地满足学校、政府、企业等各行业业务流程电子化及管理需求。
9.2、工作流以“柔性工作流”作为产品设计指导思想，所谓“柔性”指通过流程建模、流程实现、组织架构、系统权限等方面的灵活调整，使组织内部的各类流程能够对内外部环境做出快速适应，在调整过程中尽可能地减少冗余无用的损耗，达到用户在时间和投资等诸多方面的效益最大化。
9.3、工作流通过对用户内部及外部的业务流程的整个生命周期进行规划设计、自动化、管理监控和优化，加快用户各类业务流程运转效率，降低用户成本，最终形成端到端的业务流程管理平台。
9.4、工作流不是一个简单的应用系统，而是一个业务流程的管理平台，利用工作流系统,可以快速实现各类业务流程自动化并与用户组织机构内部大多数的应用系统集成，实现各类业务协作以及数据共享。
流程在线编辑
9.5、支持流程通过零代码、拖拉拽的方式进行在线编辑。并支持使用页面设计的绘制节点页面，设置办理人等满足复杂流程设置。（投标时提供系统演示）
9.6、★流程模型管理（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
1）分角色管理流程：支持管理员统一管理系统的全部流程，支持业务负责人管理具体业务流程；
业务对象流程模型默认支持增加审核流程、变动审核流程、停用审核流程、启用审核流程的流程类别，能快速设置流程，对应的流程未设置时默认未开始--结束。
2）支持业务对象添加多个相同流程类别的流程模型，通过配置流程属性中业务对象的自定义分支、字段属性、申请人信息（如部门、人员类别）等配置条件，匹配到对应的流程模型，能通过这种方式分类拆解应对复杂的流程需求。如学生的请假中的事假、病假两个类型，可以通过请假类型分成事假、病假两个流程模型。
★9.7、流程版本管理（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
支持维护多流程版本，根据需要可以新建新版本或者启用旧版本。支持流程版本复制，快速调整流程。且旧版本的实列不受新版本流程影响。
★9.8、运行流程监控（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
支持管理系统运行流程的监控，可以查看全部流程实列的状态和流程图，可以通过流程名、流程模型、版本、节点名、流程状态、申请时间段、申请部门、申请人、审核部门、审核人等搜索实列，对于异常流程可及时中止，提供调整流程的办理规则设置、调整各个节点的pc、移动端的审核页面设计、调整相关流程页面设计（发起、已办、抄送）、流程详情控制、审批意见控制、高级流程设置（批量审批控制、手动收回控制、自动收回控制、手动越过控制、自动越过控制、抄送设置、关注设置）等入口
★9.10、流程群组管理（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
支持设置公共的流程群组，便于通用流程办理人范围快速设置。提供流程绘制检测功能，可实时提供流程绘制过程中的异常提示。并绘制完成后，可通过动画对流程走向进行模拟演示，确认流程绘制是否符合需求。
9.11、可视化流程定制
1)通过简单的拖拽即可实现复杂的工作流，包括开始节点、连线、办理节点、互斥网关、并行网关、结束节点等；
2)支持设置每个节点的pc端、移动端审核页面，审核页面设计提供审核信息、变动对比信息、对象查看、对象字段、对象视图字段等组件并提供字段/组件属性设置，包括标签名、提示信息、校验规则、字段值（常规、表达式、固定值）、文本颜色、编辑权限、事件设置、宽度等属性；提供审核表单属性设置，包括标题、宽高、表单列数、字段显示条件设置、组合校验器、审核意见设置、流程按钮/分支/提交设置、底部按钮设置等。
3)节点办理设置：支持单人办理、多人并行、多人顺序、多人任意四种办理规则；多维度设置办理人，包括人员、岗位、部门、人员类别、关系、管理级别、群组（业务）、流程群组、入学年度、院系、班级、学生等维度，且可以设置是否忽略上一步选人；
4)支持增加审核人、下一步处理人设置、设置关注人、加签、抄送、同意、退回、指定退回规则、中止流程、设置默认同意意见内容、审核操作提示语设置等各种常用操作；
5)★支持流程模型及流程版本停用、启用功能；（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
6)支持流程实例与流程修改的更新同步。
10、操作界面完全基于浏览器
10.1、使用人员只要会用浏览器，就可以方便的使用本系统。管理人员可以在浏览器进行各种管理操作，维护更方便。
★10.2、设计人员可以通过浏览器对表单、查询列表、应用、菜单进行灵活地设计修改。（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
10.3、表单设计：支持pc端、移动端页面设计，提供审核信息、对象查看、对象字段、对象视图字段等组件并提供字段/组件属性设置，包括标签名、提示信息、校验规则、字段值（常规、表达式、固定值）、文本颜色、编辑权限、事件设置、宽度等属性；提供审核表单属性设置，包括标题、宽高、表单列数、字段显示条件设置、组合校验器、拟办意见设置、流程按钮/分支/提交设置、底部按钮设置（保存、提交、删除、停用、取消等按钮控制及操作提示语设置）等。
10.4、查询列表设计：由按钮区、搜索区、选项卡区、结果列表区组成；按钮区包括按钮是否显示及显示方式（弹窗、选项卡、新标签）等设置；搜索区包括关键字关联字段挑选、搜索字段挑选、搜索框长度、下拉搜索字段是否多选、结果排序等设置；选项卡区包括是否显示设置、默认显示方式（卡片、表格）、默认每页行数、对象资格筛选开关等设置；结果列表区包括字段显示设置（最小宽度、宽度、列固定、对齐方式、字体大小颜色、绑定点击事件（查看、修改、打印、变动、停用、启用、审核、取消申请等））、批量修改列、操作按钮设置（查看、修改、打印、变动、停用、启用、审核、取消申请等）等设置；
10.5、应用设置：pc、移动端端的常用应用、全部应用设置，包括设置应用图标颜色、状态、打开方式、支持跳转菜单相关授权及查询页面设计；
10.6、菜单设置：用拖拉方式能快速挑选并调整菜单结构，支持菜单搜索，支持修改菜单状态（启用、停用等），支持设置菜单打开方式（弹窗、选项卡、新标签），支持跳转菜单相关授权及查询页面设计；支持自定义菜单（链接、业务接口等）；
11、支持移动端应用
系统支持移动端应用。
12、完备的安全体系
从数据安全和系统安全两个层次进行设计。
满足国家三级等保要求，如双因子登录、密码加密、密码复杂度要求、登录错误次数锁定、登录超时退出、数据脱敏等网络安全和数据安全功能
支持请求参数和返回内容进行加解密操作，支持国产加密算法和其他国外加密算法
13、全面的流程数据分析
13.1、可以按日报、月报、季报、年报、自定义方式，对办件量、办件状态进行统计；
13.2、通过统计分析，可以帮助管理人员分析工作流状态，定位工作瓶颈，改进工作流程，提高工作效率。
14、页面设计工具
★14.1、为了简化页面开发人员的编写过程，实现部分页面的快速生成。页面设计有PC端和APP端，它们的使用方法是一样的。（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
14.2、该功能是为了简化页面开发人员的编写过程，实现部分页面的快速生成。页面设计有PC端和APP端，它们的使用方法是一样的。
14.2.1、字段、组件区域
该区域展示所有可以编排到页面上的字段元素、组件，鼠标左键点击或者按住拖动到中间页面编辑区域即可完成编辑排版。
14.2.2、功能按钮区域
该区域中的按钮能对页面编辑状态进行控制，包括撤销、重做、重新加载（重新读取表单）、保存并发布、重置（还原系统设置页面）、清空表单（清空页面编辑区域的表单设计）。可以点击自动编排来进行快速编排页面，但无法保证页面的美观体验。
14.2.3、页面编辑区域
使用方法：鼠标左键点击或拖拽左侧字段、组件区域即可把组件拖入到编辑区进行编辑。
排序：拖拽编辑区的组件调整顺序。
调整：拖动组件右下角把手调整组件大小。
删除：点击组件右上角删除按钮。
14.2.4、属性面板
分为字段属性和表单属性。其中字段属性是在编辑区域选中某个字段后，显示的该字段标签的内容、校验规则等各种属性的描述和修改。如设置字段或组件为只读、必填、显示标签，还可以自定义字段或组件的宽度。设置字段的占位内容，还可设置为录入搜索。
表单属性是对整个页面上编辑中的表单的各种属性展示和编辑，包括表单部件的标题、尺寸、表单中标签的尺寸、间距、单元格属性等等。
查看模式：只能查看信息，不能编辑信息。
水平分隔：分隔成两个页面，相当于分屏，下面分隔的页面不会被覆盖。点击空白分隔的页面，然后鼠标左键点击或者拖拽左侧字段、组件区域即可把组件拖入到分隔的页面。
审核意见框：填写审核意见。
14.2.5、流程按钮：
增加处理人：当前审核人可以设置增加处理人，但要注意办理规则是多人顺序时不生效。增加限定处理人范围，这样处理人才会在“待办”中查看对应的审核流程。其他流程按钮的限定处理人范围操作与增加处理人的一致。
下一步处理人：设置当前节点的下一步处理人。
设置关注人：设置当前流程节点的关注人，关注人可在“我的关注”中查看详情。
加签：给当前节点多加审核人员审核该流程节点。
14.2.6、流程分支
在当前节点可指定流程分支和限定分支组，属于配置文件编写的特殊流程分支。
14.2.7、底部按钮
底部按钮包括同意、退回、取消等；退回可以限定选择发起人、上一审核节点、任意审核节点；退回方式可以选择结束流程、重走流程、返回本节点。
15、统一报表工具ai
15.1、提供报表生成工具，可根据需求生成各种统计报表，支持自定义格式、数据来源和统计维度，可导出、打印和在线查看。
15.2、统计报表信息管理，针对对在统计报表子系统创建好的统计报表信息进行维护，支持新增、编辑、删除等常规操作，并能增加所属业务、对象信息。
15.3、统计报表资格管理，维护当前统计报表可查看人员的范围
15.4、打印报表信息管理，支持对在打印报表子系统创建好的打印报表信息，进行新增、编辑、删除等操作
15.5、打印报表编辑资格管理，维护当前打印报表的编辑资格人员范围
15.6、打印报表打印资格管理，维护当前打印报表运行查看预览打印的人员范围
15.7、统计报表工具
15.8、报表工具支持设置数据源、数据集、仪表盘等；还可以根据需求，设计和制作各类报表
15.9、统计报表设置，支持设置统计报表，包括报表名称、统计报表类型、报表路径、业务、对象等设置
★15.10、统计报表权限，支持对统计报表进行管理，可管理报表的基本信息，通过授权控制统计表资格，有相应的授权才可查看对应的报表。（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
16、低代码组件
16.1、提供业务数据集生成、页面拖拽配置工具和页面模板管理功能，简化开发流程，降低技术门槛，提高建设和运维的效率。
★16.2、集成在线文档编辑、附件预览等工具。（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
16.3、经过简单定制操作，无需编码，就可以实现常见的增、删、改、查、流程审批等业务应用。
1)可视化创建数据表和表单，保存、打印和关闭等操作功能自动生成；
2)可视化创建视图列表，视图查询条件、操作按钮与视图链接；
3)可视化配置菜单，菜单链接通过鼠标点击选择即可；
4)可视化工作流设计，可以挂接任意定制表单和外部表单。
利用上述功能，项目可以快速定制可操作原型，辅助需求确认，使用户提前感受系统的实际使用情况，有效提高需求确认的精确度，避免项目后期返工。1)支持多表关联；2)支持子表单；3)支持页面上动态多行录入；4)支持多种数据录入类型。
17、应用管理
支持菜单、应用大厅、应用授权等管理，涵盖多个应用系统，如学校协同办公、公文流转、学工、教务、科研等。支持管理第三方应用登录管理。
17.1、应用分组
★17.1.1、支持对系统应用任意组合分组，满足用户要求与习惯的分组分类。（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
★17.1.2、支持对应用名称编辑、状态控制、展示方式设置（选项卡、弹窗、新标签页）、图标颜色、授权设置及页面定制。（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
17.1.3、支持菜单、应用大厅、应用授权等管理，涵盖多个应用系统，如学校协同办公、公文流转、学工、教务、科研等。支持管理第三方应用登录管理。
17.2、常用应用模板管理
17.2.1、支持新建常用应用模板，用于不同岗位的用户默认不同的常用应用。
★17.2.2、首页模板：支持新建首页模板，用于不同岗位的用户默认不同的首页（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
17.2.3、个人首页设置：允许个人针对自己需求对首页进行编辑和修改。
★17.2.4、个人常用应用设置：允许个人针对自己日常使用的场景对常用应用进行编辑和修改。模板的常用应用不允许移除。（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
18、文件管理
提供文件资源管理服务，包括文件分类、上传、预览、下载管理以及文件使用授权。
19、统一消息
提供即时沟通和消息提醒功能，方便用户之间的交流和协作。可及时了解推送消息的状态，对于发送不成功的微信消息支持重新发送；支持短信消息（不含运营商发送费用）
20、搜索引擎
实现系统内搜索功能，包括网站内容搜索和页面搜索，提供便捷的信息查找体验。
21、系统配置和管理
21.1、参数配置：管理系统参数配置，根据需要进行调整。记录系统操作日志，分析和监控系统运行情况。定期备份系统数据，快速恢复系统遇到问题。系统持续迭代，以保证功能的稳定性和安全性。
21.2、安全管理：数据加密和安全传输，确保数据传输的安全性。访问控制和权限管理，管理用户对系统资源的访问权限，确保数据安全和合规性。风险识别和防范，识别潜在安全风险，采取预防措施确保系统安全。
22、数据导入/导出
业务应用场景的数据共享主要通过三种方式实现：数据录入、数据导入/导出及数据库及API接口方式。
22.1、数据录入,通过工具配置一个用户填报的表单界面，使授权用户能够轻松录入信息并支持流程审批。
★22.2、数据导入/导出:支持批量导入/导出功能，可从对应的业务系统或数据源集成数据并汇集成数据表。导入数据表时，系统自动执行格式验证、完整性检查、数据范围及唯一性校验，确保数据一致性，防止出现数据质量问题。（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
22.3、数据库及API接口方式
通过集成第三方API服务，我们能够自动从数据源获取所需信息，并将其导入到我们的系统中进行处理和存储。
23、日志监控工具
★23.1、日志收集分析：对系统运行日志进行搜集、分析、过滤。将日志详细转换成各种图表，为用户提供强大的数据可视化支持。（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
23.2、系统监测：针对系统运行主机的CPU负荷、内存使用、磁盘使用、网络状况、等信息进行监控，并对异常情况可通过微信、QQ、Email等方式提供预警及提醒，确保系统运行稳定。
24、统计分析
24.1、对学校各类数据进行统计分析，为决策提供数据支持，如科研项目、科研成果、校园资源使用分析等，以图表展示结果。
★24.2、实时展示学校关键数据和指标，可定制布局和内容，支持多种数据源接入和动态数据更新。（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
25，纸质文档的数据扫描识别
25.1 数据页面可以对纸质文档用手机进行拍照扫描之后，将数据回显到页面上
25.2 在回显的数据中，可以对数据进行敏感词检测和错别字检测并提醒
26，AI数据报告生成
26.1 可以根据用户按照月度，季度，半年，年度以及自定义时间段，按照用户的规则对数据进行报告生成
26.2 可以进行生成的报告和内容数据对业务的工作量进行评级以及打分
二、基础设置
1、部门管理
需在PC端和手机端支持对部门信息进行录入、维护等管理，可以对部门的名称、部门号、所在单位 、上级部门 、部门类别等信息进行管理；实现对部门结构的体现，根据层级结构进行排序，体现部门间管理与被管理的关系。
2、岗位管理
需在PC端和手机端支持对岗位信息进行录入、维护等管理，可以对岗位的名称、岗位类别、所在单位、所在部门、人员类别 、岗位号、首页模	板、菜单模板、 快捷方式模板 、统计图模板等信息进行管理。
3、人员管理
需在PC端和手机端支持对人员（老师、学生等）信息进行录入、维护等管理，可以对部门、岗位里人员的姓名、年龄、性别等基本信息进行管理，实现对学校不同类别的人员进行分类管理，包括在职人员、退休人员、离职人员等，并能查看到整体人员的基础信息的全览。
4、个人信息管理
需在PC端和手机端支持对个人信息进行录入、维护等管理，可以对个信息的姓名、年龄、性别、出生日期、照片、民族、账号密码等信息进行管理，支持个人进行修改信息，由管理部门进行审批。
三、项目管理
1、纵向项目
基于敏捷平台的页面设计工具快速配置纵向项目管理。实现各级各类纵向项目精细化、差异化管理，包括立项登记、变更、验收结项管理，提供增、删、改、查、审、Excel导入导出以及统计分析功能。
1.1、立项管理：项目基本信息、成员信息、预算信息（批复、配套、外拨）、合作单位等信息管理以及项目文档管理；支持项目立项信息 Excel数据批量导入，可对导入数据完整性和有效性进行校验；支持项目计划书导入；
1.2、变更管理：项目成员、延期、中止等变更提交、审核和备案；
1.3、结项管理：项目验收前置提醒，结项材料及成果提交、审核、电子归档等；
1.4、分类管理：可建立项目分类结构树，配置各类项目级别、预算标准、编号规则等。
2、	横向项目
基于敏捷平台的页面设计工具快速配置横向项目管理。
2.1、合同签审管理：可上传合同电子版提交审核，可浏览合同历史版本和审核意见，支持合同在线浏览和打印；
2.2、合同备案管理：已签署合同登记备案，提供合同增、删、改、查、审、Excel导入导出以及统计分析功能；
2.3、合同变更管理：合同变更申请、审核和备案；
2.4、合作单位资质管理：合作单位基本信息和相关资质材料管理；
2.5、合同分类管理：可设置合同类别，可上传和维护各类合同模板。
3、校级项目
基于敏捷平台的页面设计工具快速配置纵向校级项目管理。校级项目立项登记、变更、验收结项管理，提供增、删、改、查、审、Excel导入导出以及统计分析功能。
3.1、项目变更：支持在项目执行过程中对项目内容、人员及预算等进行调整。提交变更申请，进行备案或审批，完成变更。系统自动记录所有变更过程记录。
3.2、项目结项：可设置项目结项要求和结项日期，系统自动提醒项目负责人，由项目负责人登记项目结项信息，提交项目成果及结项材料等，审核通过后备案。
3.3、项目分类：可设置项目分类结构，包括大类和子类，管理和统计各类项目信息等。
3.4、成果展示与分类：可以指定项目进行查阅及导出。
4、教学项目
基于敏捷平台的页面设计工具快速配置教学项目管理。
★4.1、项目立项:各类教学项目立项信息管理，包括项目基本信息、成员信息、预算信息以及项目文档等，提供新增、删除、修改、查询、审核、Excel导入导出以及统计功能。
4.2、项目变更：支持在项目执行过程中对项目内容、人员及预算等进行调整。提交变更申请，进行备案或审批，完成变更。系统自动记录所有变更过程记录。
4.2、项目结项：可设置项目结项要求和结项日期，系统自动提醒项目负责人，由项目负责人登记项目结项信息，提交项目成果及结项材料等，由教务处审核通过后备案。
4.4、项目分类：可设置项目分类结构，包括大类和子类，管理和统计各类项目信息，涉及国家级、省级、市级以及校级教研项目等。
4.5、成果展示与分类：可以指定项目进行查阅及导出。
四、网上申报评审
★1、网上申报管理：基于项目申报方案，服务于校内项目申报工作，包括申报计划编制、申报条件设定、申报信息登记、申报材料提交和审核等。可定制申报书模板。（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
2、网上评审功能，包括评审方案制定，评审专家指派，评审计划发布，专家网上评审，评审过程跟踪，评审结果汇总以及统计分析等功能。
★为确保本基础平台的成熟度和知识产权，投标人需提供本系统加盖原厂公章的软件著作权证书。	套	1
2	科研服务应用综合系统（含移动端）	科研数据仓库
基于敏捷平台的页面设计工具快速配置信息采集表单页面，采集学校论文、著作、奖项、科研项目、专利类等数据，规范学校的各类科研成果分类、级别、年度，建立学校标准的科研成果库，建立学校统一的科研成果数据标准，为智慧校园其他系统相关的科研数据服务。	套	1
		科研成果认定（需定制开发）
基于敏捷平台实现学校科研成果进行申报，通过成果申报计划，以教师申请为科研成果精准的原始数据来源，建设学校PC端、移动端的申报入口，同时提供科研成果的搜索、查询、打印等应用服务。	套	1
		专业技术职务任职资格评审（需定制开发）
基于敏捷平台实现发布职称评审计划、教师职称评审报名、科研处职称评审、职称评审数据归档、职称评审表打印、职称评审数据查阅、职称评审数据统计等职称评审提供全生命周期的应用服务。	套	1
		以敏捷平台为核心支撑，通过数据交换和身份效验，为学校教师提供基于敏捷平台的基础操作、科研成果展示、成果查询、审批进度及通知的手机端科研服务。	套	1
3	龙湖讲坛	1、讲坛通过邀请校内外的名家、名师、名人等文化人士进行讲座，传播文化知识、专业理念和社会发展趋势，提升师生的文化品位和行业、专业人格，同时厚重学校的文化底蕴，提升学校的文化品位和格调‌；另外，通过搭建干部讲堂、红色会堂、身边学堂等平台，有效提升了理论宣讲的吸引力和感染力，促进了党的创新理论落地生根，增强了校内干部、师生的整体素质和履职能力。该系统‌基于敏捷平台实现龙湖讲坛的独立业务流的申请及审批，审批后自动生成讲坛编号，龙湖讲坛各类音视频资源的上传下载、点播学习，以及各类数据信息的归档查阅、打印。
★2、基于敏捷平台实现龙湖讲坛的申请及审批，审批后自动生成讲坛编号，龙湖讲坛相关信息的数据归档查阅、打印。（投标时提供所投产品的功能截图并加盖厂家公章，投标时提供系统演示）
★3、在现有的学校网站上建设龙湖讲坛专题网站。龙湖论坛活动审批后，在学校官网相关栏目直接展示每次讲坛的内容及介绍。针对当前页面，提供响应式设计，完成首页、二级页、文章页、特殊页面等整套网站设计，根据定稿的页面设计原尺寸效果图，采用H5+CSS3+JS动态效果，写出符合后台管理系统的规范代码。（投标时需提供不少于2份设计效果图）
★4、网站页面必纳入现有网站群平台防火墙防控体系内，得到全方位安全防护。网站实现面向站群内各子站的跨站点跨栏目的信息便捷投递，无需调用任何接口，实现站群的信息互通共享。网站支持在站群平台实现对网站的停止、启用、发布等方面的功能。严格遵循安全性原则，包含：网站备份、网站体检、应用防火墙、运维监控、网站安全补丁、网站授权监控、防篡改、危险文件扫描等信息的推送和提醒。
5、讲坛服务可实现在讲坛开始前提前发布讲坛的组织信息：题目、时间、地址、报告人简介等信息到网站上。	套	1
4	龙湖讲坛展示系统终端	1、终端具有模拟交互场景、终端上网、具备可视互动同传，终端为图形化界面、可直接鼠标操作；
2、处理器：intel八线程处理器，主频≥2.0GHz，最大支持2.8GHz；
3、内存：实配≥8GB DDR4内存，最大支持16GB，后盖独立开盖设计，内存易拆易升级；
4、硬盘：≥256G SSD高速固态硬盘；
5、显示接口：2个HDMI口，1个RJ45接口；配备显示器:≥75寸；最佳分辨率3840*2160
6、USB接口：≥4个USB接口，其中前置2个，后置2个USB3.2 Gen1；
7、网络架构：标准以太网，采用实时传输协议，支持WiFi6协议；
8、支持加电自动启动，可设置自动启动应用程序；
9、★终端应用程序展示数据必须无缝与科研成果多维管控中心对接，且与龙湖论坛数据实时同步，支持用户自定义启动程序的顺序。
10．龙湖讲坛活动资源，支持通过大屏展示每期论坛的相关资源，支持观看论坛资源并记录论坛的观看记录等。	套	1
5	科研成果综合态势感知系统	实现科研管理的科研成果与平台的教师、二级学院进行关联，查看二级学院教师的科研成果，实现科研成果数据、年度数据、成果类别、教师情况等可视化态势，全面掌握全校科研成果情况。通过教师科研成果的认定成果，掌控学校整体科研成果情况，打造科研成果一张图，提升学校科研成果管理效率，提升科研成果的应用推广，利用态势工具展示学校整体科研的总趋势。	套	1
6	科研成果数据处理服务	2024年前科研成果认定数据的梳理、检验等人工服务工作。	套	1
7	数据接口对接	统一身份认证：与学校统一身份认证有效对接，需要厂商免费配合认证改造，使用校内网络信息中心提供的统一身份认证本平台接入接口，校内网络信息中心方提供技术支持，将本系统应用接入统一门户。	套	1
		信息中心：按照《学院信息系统集成方案》相关规定执行，本平台可提供相关数据给数据中心，需要厂商配合，选择数据同步方案。	套	1
		人事部门：与学校人事系统对接，实现部门、人事数据关联。	套	1
		教务对接：依据用户方业务应用需求，实现与教务系统对接。	套	1
8	个人数字证书服务	鉴于龙湖讲坛的外网访问安全性和访问人员的规范使用，保障龙湖讲坛模块的安全有序运，个人数字证书允许授权使用人员在校外访问业务系统，为外网访问提供安全保障及数据加密传输。提供个人数字证书 100个，含1 年证书服务。
一、智能密码钥匙UKEY
1、支持ISO7816标准，支持CSP、PKCS11、国家密码管理局智能密钥钥匙接口等接口规范，支持X.509v3，SSLv3，IPSec等规范。
★2、支持USB接口，支持操作系统包括Windows 2000/XP/Server 2003/Vista/Server 2008/7/10/11，Linux，统信，中标麒麟等。
3、USBKey自身的安全要求：具备完善的PIN校验保护功能。
4、投标人应保证其USBKey产品为一个完整系列的产品，能够支持数字签名等证书应用。
5、支持证书的导入、安全存储；支持密钥的签名、验证、加密、解密等功能。
6、密钥的安全存储和保护功能。
7、密码算法支持公钥1024/2048位RSA算法、SM2算法、SM1算法、SM3算法、SM4算法。
8、支持标准的DES、RSA、SAH-1、MD5等加密算法支持国密局的有关指定SSF33算法，支持第三方算法下载，同时应支持RSA算法向SM2算法的过渡，可实现同一介质中算法和证书的无缝切换升级。
★9、数据存储时间不小于10年，可擦写30万次以上。
10、能无缝挂接Microsoft Internet Explorer、Google Chrome，Firefox等提供标准CSP等主流浏览器，并完全支持HTTPS协议的应用。
二、个人数字证书技术参数要求
1、证书格式遵循X.509v3标准。
2、支持USB Key、软证书、文件证书等证书存放介质。
3、支持自定义证书扩展域管理。
4、签发证书的CA系统密码算法必须通过国密局审批，CA系统能签发基于SM2国产算法的数字证书。
5、支持证书申请、证书批准、证书查询、证书下载、证书吊销、证书更新等证书生命周期管理。
6、支持具备CRL证书吊销列表服务，根据配置的CRL下载地址，同时支持应用系统自动下载该CRL或手工下载该CRL。
7、支持国际、国密标准算法，如RSA、SM2等。
8、支持数字签名及验证、随机密钥的生成；支持散列运算、支持MD5、SHA1算法。
★9、提供国家工业和信息化部颁发的《电子认证服务许可证》，并加盖原厂公章。
★10、提供国家密码管理局颁发的《电子认证服务使用密码许可证》，并加盖原厂公章。	项	1
9	技术路线要求	一、技术路线要求
本项目整合、利用Internet、无线网、移动终端，为学校用户提供科研管理等综合服务。
本项目的技术路线要求如下：
1、采用面向用户的服务体系，服务作为软件基础架构发展的必然趋势，针对学校业务特点和业务目标，遵循统一性、抽象性、符合性及业务驱动、可迭代的设计原则完成项目的分析、设计和开发，提供一个统一展现、一体化协作、应用集成平台集中管理的协同工作环境。
2、采用Java开发语言，基于J2EE开发平台，采用B/S架构。
3、采用工作流技术，业务流程支持用户单位业务管理人员根据实际业务应用自主配置、变动。
4、支持国产数据库或开源数据库。
5、服务器操作系统，支持麒麟、统信等主流的国产服务器操作系统或Linux系统；
二、性能要求
系统需要能够满足相关的服务性能需求，并有一定的前瞻性考虑，在系统建设和运维过程中通过各种手段对系统进行不断的优化和调整，在现有硬件配置环境下，系统在性能等方面系统需达到如下指标：
1、可用性：系统需要支持注册用户数10000以上，能支撑同时在线用户300人以上。
2、扩展性：系统具备良好的扩展性，进行可插拔式的功能扩展。
3、性能：在现有硬件配置环境下，一般页面单数据源平均响应时间不超过5秒，对于多数据源复杂查询平均响应时间<10s；对于涉及实时接口页面显示，平均响应时间不超过各实时接口最长响应时间2倍。	项	1
10	实施要求	基本要求：中标人应负责在项目完成时将项目的全部有关技术文件、安装和维护手册、测试报告、用户操作说明书等文档汇集成册提交给采购人。
一、项目人员配置
1、投标人须成立合理的组织机构，建立健全保障项目顺利实施的各项管理制度和质量保证体系，安排好足够的高素质人才参加本项目的建设；在项目机构中应明确各岗位的职责、任职经验及成果，确保工程顺利实施。根据工作的业务性质，应配备有优势的项目经理、技术团队人员承担本项目工作。
2、项目组人员必须具有类似职责的开发和实施经验，能够与用户进行良好的沟通。
3、参与此项目的技术人员必须具有强烈的服务意识和高度的责任感。
4、在建设期内，项目经理应在接到业主（招标）单位通知2小时内到达业主（招标）单位现场处理问题或交流情况。
二、实施方案
该项目系统结构及需求复杂，要保证顺利有序实施，必须对实施工作做出详尽的组织实施方案。方案的内容包括以下几个层面：
1、组织架构与职责
本建设项目是一项需要“领导重视、部门协作”的项目，领导、沟通和配合是建设实施成功的必要因素，因此，规划建立一个良好的建设实施组织架构，明确实施项目组成员的各自职责。
2、实施阶段与过程
根据本项目的实施策略和建设时间要求，列出详细的实施计划，将该项目的建设实施过程科学、合理、有效地分成若干个主要阶段，将建设内容和范围分成若干部分。考虑应用实施的各种内部、外部因素，原有应用和人员基础等情况，制定分阶段实施计划，明确和细化每个阶段的工作范围、内容、人力投入、过程、责任、交付成果等，确保项目有序建设。
3、质量管理体系
投标人在投标文件中应对本组织的质量保证体系做出说明。提出本工程的质量管理措施和保障措施。
项目实施结束后，实施单位应对应用服务进行全面的综合测试，并形成测试报告，配合采购人组织项目验收。
4、成果交付与验收
在项目建设实施的整个过程中，制定明确的总体和分阶段成果交付与验收内容、准则、程序、监控手段等。
5、文档管理
根据开发进度及时提供有关开发文档，包括需求说明书、系统安装部署手册等。
三、安装和测试要求
1、部署与安装
投标人必须向业主（招标）单位提供本项目采购的所有软件系统的部署与安装、调试和己有的应用服务集成及后期维护服务的全部内容。
对投标人要求：
1、要求投标人必须具有良好信誉和相关实力的技术队伍。
2、投标人应本着认真负责态度，组织技术队伍，做好投标的整体方案，并书面提出长期保修、维护、服务以及今后技术支持的措施计划和承诺。
3、自系统安装工作一开始，投标人应允许采购人的工作人员一起参与系统的安装、测试、诊断及解决遇到的问题等各项工作。
四、兼容性要求：
为保证系统兼容性和稳定性，要求所有软件系统为同一品牌产品及开发商提供，不允许多厂家品牌或产品拼凑而成。	项	1
11	售后服务和培训要求	一、售后服务
★1、售后服务从系统验收之日起计算，投标人在项目验收后，须提供3年的免费质保维护。
2、中标人需要提供技术支持、操作培训、远程协助、电子邮件、电话等其他及时服务方式，系统一般性问题在4小时内解决，系统重大问题时间在1小时内响应，2小时内解决。
3、若业主行政组织调整或业务流程变更，投标人应提供及时的技术支撑服务。
4、本服务不包含基于功能增加的模块扩展和定制开发工作。
二、项目培训
1、投标人必须提供相应的应用软件技术、系统操作等方面的培训。有关应用软件的操作培训课程，培训应该在系统验收前完成。
2、投标人在实施过程中提供免费、全面的培训，包括面向系统管理员和业务人员的相应的培训、配置管理和系统维护培训、客户端操作培训等。
3、培训项目结束之时，安排学员进行培训测试，以检验学员对系统的基本操作能力和掌握水平；同时学员也可对于整个培训项目做出评价，当学员普遍反映对培训课程不满意时，业主可要求投标人重新安排培训，并承担全部费用。
4、投标人必须为所有被培训人员提供文字资料和讲义等培训教材，所有的资料需要是中文书写。
5、培训方式：集中授课、现场演示和辅助操作。
三、技术转移和成果交付要求
在本期项目的开发过程中和交付使用后，各个阶段都需有各种成果和文档资料。这些成果和文档资料对所开发系统的维护和持续发展起着非常重大的作用。因此，要求将全面、规范的成果和文档资料交付给采购人，而且要提供明确的交付清单。同时，成果和文档资料必须符合软件工程的相关要求。要交付的成果和文档资料主要包括以下部分：
1、技术文档：包括项目开发中的各种技术文档，如，开发环境配置说明、需求说明、系统培训资料等等。
2、管理文档：包括项目开发中的一些工作文档，如计划、报告、讨论纲要、会议记录等。	项	1