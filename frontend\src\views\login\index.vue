<template>
  <!-- 占满整个视口的容器，使用背景图片 -->
  <div class="login-page-container">
    <!-- Logo放在左上角 -->
    <div class="logo-container">
      <img src="/logo1.png" alt="学校logo" class="logo-image" />
    </div>

    <!-- 登录表单区域，定位在左侧 -->
    <div class="login-form-area">
      <div class="login-card">

        <!-- 登录表单 -->
        <div class="form-section">
          <h2 class="login-title">登录</h2>

          <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form">
            <!-- 用户名输入框 -->
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                type="text"
                size="large"
                auto-complete="off"
                placeholder="admin123"
                class="login-input"
                @keyup.enter="handleLogin"
              >
                <template #prefix>
                  <el-icon class="input-icon">
                    <User />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <!-- 密码输入框 -->
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                size="large"
                auto-complete="off"
                placeholder="请输入密码"
                class="login-input"
                show-password
                @keyup.enter="handleLogin"
              >
                <template #prefix>
                  <el-icon class="input-icon">
                    <Lock />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <!-- 验证码输入框 -->
            <!-- <el-form-item prop="code" v-if="captchaEnabled">
              <div class="captcha-container">
                <el-input
                  v-model="loginForm.code"
                  size="large"
                  auto-complete="off"
                  placeholder="请输入验证码"
                  class="login-input captcha-input"
                  @keyup.enter="handleLogin"
                >
                  <template #prefix>
                    <el-icon class="input-icon">
                      <Key />
                    </el-icon>
                  </template>
                </el-input>
                <div class="captcha-code">
                  <span class="captcha-digit digit-7">7</span>
                  <span class="captcha-digit digit-3">3</span>
                  <span class="captcha-digit digit-6">6</span>
                  <span class="captcha-digit digit-4">4</span>
                </div>
              </div>
            </el-form-item> -->

            <!-- 记住密码选项和忘记密码链接 -->
            <!-- <div class="form-options">
              <el-checkbox v-model="loginForm.rememberMe" class="remember-me">
                记住密码
              </el-checkbox>
              <a href="#" class="forgot-password">忘记密码</a>
            </div> -->

            <!-- 登录按钮 -->
            <el-form-item class="login-button-item">
              <el-button
                :loading="loading"
                size="large"
                type="primary"
                class="login-button"
                @click.prevent="handleLogin"
              >
                <span v-if="!loading">登录</span>
                <span v-else>登录中...</span>
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 底部版权信息 -->
    <div class="copyright-info">
      <p>版权所有：河南检察职业学院 河南省郑州市郑东新区龙子湖高校园区祭城路</p>
      <p>地址：河南省郑州市郑东新区龙子湖高校园区祭城路 邮编：450000 电话：0371-69970000</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock, Key } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/modules/user'
import { getCodeImg } from '@/api/login'

const router = useRouter()
const userStore = useUserStore()
const loginFormRef = ref()
const codeUrl = ref('')
const loading = ref(false)
const captchaEnabled = ref(false)
const redirect = ref<string | undefined>(undefined)

const loginForm = reactive({
  username: 'admin',
  password: 'admin123',
  rememberMe: false,
  code: '',
  uuid: ''
})

const loginRules = {
  username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
  password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
  code: [{ required: true, trigger: 'change', message: '请输入验证码' }]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true
      try {
        // 真实登录
        await userStore.login(loginForm)

        // 获取用户信息
        await userStore.getUserInfo()

        ElMessage.success('登录成功')

        // 登录后跳转到个人工作台
        router.push({ path: redirect.value || '/workspace' })
      } catch (error: any) {
        ElMessage.error(error.message || '登录失败')
        // 如果启用了验证码，重新获取验证码
        if (captchaEnabled.value) {
          getCode()
        }
      } finally {
        loading.value = false
      }
    }
  })
}

const getCode = async () => {
  try {
    const response = await getCodeImg()
    captchaEnabled.value = response.captchaEnabled || false
    if (response.captchaEnabled) {
      codeUrl.value = 'data:image/gif;base64,' + response.img
      loginForm.uuid = response.uuid
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
  }
}

const getCookie = () => {
  // 简化版本，不使用加密
  const username = localStorage.getItem('username')
  const password = localStorage.getItem('password')
  const rememberMe = localStorage.getItem('rememberMe')
  if (username) loginForm.username = username
  if (password) loginForm.password = password
  if (rememberMe) loginForm.rememberMe = Boolean(rememberMe)
}

onMounted(() => {
  getCode()
  getCookie()
  redirect.value = router.currentRoute.value.query?.redirect as string
})
</script>

<style lang='scss' scoped>
/* 重置样式，防止滚动条 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  overflow: hidden;
}
/* 1. 占满整个视口的容器div，使用背景图片 */
.login-page-container {
  width: 100vw;
  height: 100vh;
  background: url('/login.png') center center / cover no-repeat;
  position: relative;
  overflow: hidden;
  margin: -8px;
  padding: 0;
}

/* 2. 登录表单区域，定位在左侧 */
.login-form-area {
  position: absolute;
  left: 80px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.login-form-area {
  left: 25%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 400px;
}

/* 左上角Logo容器 */
.logo-container {
  position: absolute;
  top: 40px;
  left: 40px;
  z-index: 20;
}

.logo-image {
  width: 50vw;
  height: auto;
  object-fit: contain;
  border-radius: 0;
}

/* 3. 登录卡片，去掉背景和边框 */
.login-card {
  background: transparent;
  border-radius: 0;
  padding: 50px 40px;
  width: 380px;
  box-shadow: none;
  backdrop-filter: none;
}



/* 登录标题 */
.login-title {
  font-size: 28px;
  color: #ffffff;
  margin: 0 0 40px 0;
  font-weight: 400;
  text-align: center;
}

/* 表单样式 - 大圆角 #f0f2f8 背景 */
.login-form {
  .login-input {
    margin-bottom: 24px;

    :deep(.el-input__wrapper) {
      height: 52px;
      border-radius: 26px;
      border: none;
      box-shadow: none;
      transition: all 0.3s ease;
      background: #f0f2f8;

      &:hover {
        background: #e8ecf4;
      }

      &.is-focus {
        background: #e8ecf4;
        box-shadow: 0 0 0 2px rgba(79, 124, 255, 0.2);
      }
    }

    :deep(.el-input__inner) {
      color: #2c3e50;
      font-size: 14px;
      background: transparent;

      &::placeholder {
        color: #8b95a5;
      }
    }
  }

  .input-icon {
    color: #8b95a5;
    font-size: 16px;
  }
}

/* 验证码容器 */
.captcha-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.captcha-input {
  flex: 1;
}

.captcha-code {
  width: 100px;
  height: 52px;
  background: #f0f2f8;
  border: none;
  border-radius: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  gap: 2px;

  .captcha-digit {
    font-size: 20px;
    font-weight: 600;
    letter-spacing: 1px;

    &.digit-7 { color: #ff6b6b; }
    &.digit-3 { color: #4ecdc4; }
    &.digit-6 { color: #45b7d1; }
    &.digit-4 { color: #f39c12; }
  }
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;

  .remember-me {
    :deep(.el-checkbox__label) {
      color: rgba(255, 255, 255, 0.8);
      font-size: 14px;
    }

    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
      background-color: #4f7cff;
      border-color: #4f7cff;
    }

    :deep(.el-checkbox__inner) {
      border-color: rgba(255, 255, 255, 0.5);
    }
  }

  .forgot-password {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 14px;

    &:hover {
      color: #ffffff;
      text-decoration: underline;
    }
  }
}

/* 登录按钮 */
.login-button-item {
  margin-bottom: 0;

  .login-button {
    width: 100%;
    height: 52px;
    background: #4f7cff;
    border: none;
    border-radius: 26px;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      background: #3d63ff;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(79, 124, 255, 0.3);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

/* 底部版权信息 */
.copyright-info {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  line-height: 1.5;
  background: rgba(0, 0, 0, 0.2);
  padding: 8px 16px;
  border-radius: 6px;
  backdrop-filter: blur(5px);
  z-index: 5;

  p {
    margin: 2px 0;
  }
}

// 动画（保留可能需要的动画）

/* 4. 响应式设计，适配不同屏幕尺寸 */
@media (max-width: 1024px) {
  .logo-container {
    top: 30px;
    left: 30px;
  }

  .logo-image {
    width: 35vw;
    max-width: 300px;
    height: auto;
  }

  

  .login-card {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 40px 30px;
  }
}

@media (max-width: 768px) {
  .logo-container {
    top: 20px;
    left: 20px;
  }

  .logo-image {
    width: 40vw;
    max-width: 250px;
    height: auto;
  }

  

  .login-card {
    width: 100%;
    padding: 40px 30px;
  }
}

@media (max-width: 480px) {
  .logo-container {
    top: 15px;
    left: 15px;
  }

  .logo-image {
    width: 50vw;
    max-width: 200px;
    height: auto;
  }

  .login-card {
    padding: 30px 20px;
  }

  .login-title {
    font-size: 24px;
  }

  .copyright-info {
    font-size: 11px;
    padding: 6px 12px;
  }
}
</style>
