<template>
  <div class="app-container">
    <div class="page-content">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="角色名称" prop="roleName">
        <el-input
          v-model="queryParams.roleName"
          placeholder="请输入角色名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="权限字符" prop="roleKey">
        <el-input
          v-model="queryParams.roleKey"
          placeholder="请输入权限字符"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="角色状态" clearable style="width: 240px">
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          class="top-action-btn"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          class="top-action-btn"
        >
          修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          class="top-action-btn"
        >
          删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="Download"
          @click="handleExport"
          class="top-action-btn"
        >
          导出
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 角色表格 -->
    <div class="table-container">
      <el-table v-loading="loading" :data="roleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="角色编号" prop="roleId" align="center" min-width="100" />
      <el-table-column label="角色名称" prop="roleName" :show-overflow-tooltip="true" min-width="120" />
      <el-table-column label="权限字符" prop="roleKey" :show-overflow-tooltip="true" min-width="120" />
      <el-table-column label="显示顺序" prop="roleSort" align="center" min-width="80" />
      <el-table-column label="状态" align="center" min-width="80">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="320" class-name="small-padding fixed-width">
        <template #default="scope">
          <div class="action-buttons">
            <el-button v-if="scope.row.roleId !== 1" type="primary" @click="handleUpdate(scope.row)" size="small" class="table-action-btn">
              修改
            </el-button>
            <el-button type="warning" @click="handleMenuScope(scope.row)" size="small" class="table-action-btn">
              菜单权限
            </el-button>
            <el-button v-if="scope.row.roleId !== 1" type="success" @click="handleDataScope(scope.row)" size="small" class="table-action-btn">
              数据权限
            </el-button>
            <el-button v-if="scope.row.roleId !== 1" type="info" @click="handleAuthUser(scope.row)" size="small" class="table-action-btn">
              分配用户
            </el-button>
            <el-button v-if="scope.row.roleId !== 1" type="danger" @click="handleDelete(scope.row)" size="small" class="table-action-btn">
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form :model="form" :rules="rules" ref="roleRef" label-width="100px">
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="form.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item prop="roleKey">
          <template #label>
            <span>
              <el-tooltip content="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)" placement="top">
                <el-icon><question-filled /></el-icon>
              </el-tooltip>
              权限字符
            </span>
          </template>
          <el-input v-model="form.roleKey" placeholder="请输入权限字符" />
        </el-form-item>
        <el-form-item label="角色顺序" prop="roleSort">
          <el-input-number v-model="form.roleSort" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 分配角色菜单对话框 -->
    <el-dialog title="分配菜单" v-model="openMenu" width="600px" append-to-body>
      <el-form :model="form" label-width="100px">
        <el-form-item label="角色名称">
          <el-input v-model="form.roleName" :disabled="true" />
        </el-form-item>
        <el-form-item label="权限字符">
          <el-input v-model="form.roleKey" :disabled="true" />
        </el-form-item>
        <el-form-item label="菜单权限">
          <el-checkbox v-model="menuExpand" @change="handleCheckedTreeExpand($event, 'menu')">展开/折叠</el-checkbox>
          <el-checkbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll($event, 'menu')">全选/全不选</el-checkbox>
          <el-checkbox v-model="menuCheckStrictly" @change="handleCheckedTreeConnect($event, 'menu')">父子联动</el-checkbox>
          <el-tree
            class="tree-border"
            :data="menuOptions"
            show-checkbox
            ref="menuRef"
            node-key="id"
            :check-strictly="!menuCheckStrictly"
            empty-text="加载中，请稍候"
            :props="{ label: 'label', children: 'children' }"
          >
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <span>{{ node.label }}</span>
                <span v-if="data.menuType === 'F'" class="permission-tag">{{ data.perms }}</span>
              </span>
            </template>
          </el-tree>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitMenuForm">确 定</el-button>
          <el-button @click="cancelMenu">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { QuestionFilled } from '@element-plus/icons-vue'
import { listRole, getRole, delRole, addRole, updateRole, changeRoleStatus, getMenuTreeselect, authMenu } from '@/api/system/role'
import Pagination from '@/components/Pagination/index.vue'
import RightToolbar from '@/components/RightToolbar/index.vue'

const router = useRouter()

// 遮罩层
const loading = ref(true)
// 选中数组
const ids = ref<Array<string | number>>([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 总条数
const total = ref(0)
// 角色表格数据
const roleList = ref([])
// 弹出层标题
const title = ref('')
// 是否显示弹出层
const open = ref(false)
// 是否显示菜单权限弹出层
const openMenu = ref(false)
// 菜单列表
const menuOptions = ref([])
// 菜单树选项
const menuExpand = ref(false)
const menuNodeAll = ref(false)
const menuCheckStrictly = ref(true)
// 菜单树引用
const menuRef = ref()
// 日期范围
const dateRange = ref([])
// 表单参数
const form = ref({})
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  roleName: '',
  roleKey: '',
  status: ''
})

// 表单校验
const rules = reactive({
  roleName: [
    { required: true, message: '角色名称不能为空', trigger: 'blur' }
  ],
  roleKey: [
    { required: true, message: '权限字符不能为空', trigger: 'blur' }
  ],
  roleSort: [
    { required: true, message: '角色顺序不能为空', trigger: 'blur' }
  ]
})

/** 查询角色列表 */
function getList() {
  loading.value = true
  listRole(addDateRange(queryParams.value, dateRange.value)).then((response: any) => {
    console.log('角色列表响应:', response)
    roleList.value = response?.rows || []
    total.value = response?.total || 0
    loading.value = false
  }).catch((error: any) => {
    console.error('获取角色列表失败:', error)
    roleList.value = []
    total.value = 0
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    roleName: '',
    roleKey: '',
    status: ''
  }
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.roleId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = '添加角色'
}

/** 修改按钮操作 */
function handleUpdate(row?: any) {
  reset()
  const roleId = row?.roleId || ids.value[0]
  console.log('编辑角色ID:', roleId)
  getRole(roleId).then((response: any) => {
    console.log('角色详情响应:', response)
    form.value = response
    open.value = true
    title.value = '修改角色'
  }).catch((error: any) => {
    console.error('获取角色详情失败:', error)
    ElMessage.error('获取角色信息失败')
  })
}

/** 提交按钮 */
function submitForm() {
  // 验证必填字段
  if (!form.value.roleName) {
    ElMessage.error('角色名称不能为空')
    return
  }
  if (!form.value.roleKey) {
    ElMessage.error('权限字符不能为空')
    return
  }
  if (form.value.roleSort === undefined || form.value.roleSort === null) {
    ElMessage.error('角色顺序不能为空')
    return
  }

  console.log('提交角色数据:', form.value)

  if (form.value.roleId != undefined) {
    updateRole(form.value).then(() => {
      ElMessage.success('修改成功')
      open.value = false
      getList()
    }).catch((error: any) => {
      console.error('修改角色失败:', error)
      ElMessage.error('修改失败')
    })
  } else {
    addRole(form.value).then(() => {
      ElMessage.success('新增成功')
      open.value = false
      getList()
    }).catch((error: any) => {
      console.error('新增角色失败:', error)
      ElMessage.error('新增失败')
    })
  }
}

/** 删除按钮操作 */
function handleDelete(row?: any) {
  const roleIds = row?.roleId || ids.value
  const roleName = row?.roleName || '选中的角色'

  ElMessageBox.confirm(`是否确认删除角色"${roleName}"？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    console.log('删除角色ID:', roleIds)
    return delRole(roleIds)
  }).then(() => {
    getList()
    ElMessage.success('删除成功')
  }).catch((error: any) => {
    if (error !== 'cancel') {
      console.error('删除角色失败:', error)
      ElMessage.error('删除失败')
    }
  })
}

/** 角色状态修改 */
function handleStatusChange(row: any) {
  let text = row.status === '0' ? '启用' : '停用'
  ElMessageBox.confirm(`确认要"${text}"角色"${row.roleName}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    console.log('修改角色状态:', row.roleId, row.status)
    return changeRoleStatus(row.roleId, row.status)
  }).then(() => {
    ElMessage.success(text + '成功')
  }).catch((error: any) => {
    if (error !== 'cancel') {
      console.error('修改角色状态失败:', error)
      ElMessage.error(text + '失败')
    }
    // 恢复原状态
    row.status = row.status === '0' ? '1' : '0'
  })
}

/** 分配数据权限操作 */
function handleDataScope(row: any) {
  ElMessage.info('数据权限分配功能待开发')
}

/** 分配用户操作 */
function handleAuthUser(row: any) {
  const roleId = row.roleId
  router.push(`/system/role-auth-user/${roleId}`)
}

/** 分配菜单权限操作 */
function handleMenuScope(row: any) {
  reset()
  const roleId = row.roleId || ids.value
  console.log('菜单权限角色ID:', roleId)
  getMenuTreeselect(roleId).then(response => {
    console.log('菜单权限响应数据:', response)
    // 处理响应数据，response可能是data字段，也可能包含menus和checkedKeys
    const responseData = response || {}
    menuOptions.value = responseData.menus || []
    openMenu.value = true
    nextTick(() => {
      const checkedKeys = responseData.checkedKeys || []
      checkedKeys.forEach((v: any) => {
        nextTick(() => {
          menuRef.value.setChecked(v, true, false)
        })
      })
    })
  }).catch(error => {
    console.error('获取菜单权限失败:', error)
    ElMessage.error('获取菜单权限失败')
  })
  form.value = Object.assign({}, row)
}

// 树权限（展开/折叠）
function handleCheckedTreeExpand(value: any, type: string) {
  if (type === 'menu') {
    const treeList = menuOptions.value
    for (let i = 0; i < treeList.length; i++) {
      const nodeKey = treeList[i].id || treeList[i].menuId
      if (nodeKey && menuRef.value.store.nodesMap[nodeKey]) {
        menuRef.value.store.nodesMap[nodeKey].expanded = value
      }
    }
  }
}

// 树权限（全选/全不选）
function handleCheckedTreeNodeAll(value: any, type: string) {
  if (type === 'menu') {
    if (value) {
      // 全选：获取所有节点的key
      const allKeys = getAllMenuKeys(menuOptions.value)
      menuRef.value.setCheckedKeys(allKeys)
    } else {
      // 全不选：清空所有选中
      menuRef.value.setCheckedKeys([])
    }
  }
}

// 树权限（父子联动）
function handleCheckedTreeConnect(value: any, type: string) {
  if (type === 'menu') {
    menuCheckStrictly.value = !!value
  }
}

// 获取所有菜单的key
function getAllMenuKeys(menus: any[]): any[] {
  let keys: any[] = []
  menus.forEach(menu => {
    const key = menu.id || menu.menuId
    if (key) {
      keys.push(key)
    }
    if (menu.children && menu.children.length > 0) {
      keys = keys.concat(getAllMenuKeys(menu.children))
    }
  })
  return keys
}

/** 导出按钮操作 */
function handleExport() {
  ElMessage.info('导出功能待开发')
}

// 表单重置
function reset() {
  form.value = {
    roleId: undefined,
    roleName: '',
    roleKey: '',
    roleSort: 0,
    status: '0',
    remark: ''
  }
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 菜单权限取消按钮
function cancelMenu() {
  openMenu.value = false
}

// 菜单权限提交
function submitMenuForm() {
  if (form.value.roleId != undefined) {
    const roleId = form.value.roleId
    const menuIds = getMenuAllCheckedKeys()
    // 只发送必要的字段，避免触发角色权限检查
    const roleData = {
      roleId: roleId,
      menuIds: menuIds
    }
    console.log('提交菜单权限数据:', roleData)
    authMenu(roleData).then(response => {
      ElMessage.success('分配成功')
      openMenu.value = false
      getList()
    }).catch(error => {
      console.error('菜单权限配置失败:', error)
      ElMessage.error('菜单权限配置失败')
    })
  }
}

// 所有菜单节点数据
function getMenuAllCheckedKeys() {
  // 目前被选中的菜单节点
  const checkedKeys = menuRef.value.getCheckedKeys()
  // 半选中的菜单节点
  const halfCheckedKeys = menuRef.value.getHalfCheckedKeys()
  checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys)
  return checkedKeys
}

// 时间格式化
function parseTime(time: any, pattern?: string) {
  if (!time) return ''
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  const date = new Date(time)
  const formatObj: any = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

// 添加日期范围
function addDateRange(params: any, dateRange: any, propName?: string) {
  const search = { ...params }
  const dateRangeName = propName || 'params'
  if (dateRange != null && dateRange !== '') {
    if (typeof dateRangeName === 'string') {
      search['beginTime'] = dateRange[0]
      search['endTime'] = dateRange[1]
    } else {
      search[dateRangeName[0]] = dateRange[0]
      search[dateRangeName[1]] = dateRange[1]
    }
  }
  return search
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
/* 页面特定样式，全局样式已在 styles/ 目录中定义 */

.tree-border {
  margin-top: 5px;
  border: 1px solid #e5e6e7;
  background: #FFFFFF none;
  border-radius: 4px;
  width: 100%;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.permission-tag {
  font-size: 12px;
  color: #909399;
  background: #f4f4f5;
  padding: 2px 6px;
  border-radius: 3px;
  margin-left: 8px;
  font-family: 'Courier New', monospace;
}
</style>
