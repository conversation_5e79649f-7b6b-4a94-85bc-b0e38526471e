import request from '@/utils/request'

// 查询合同列表
export function listContract(query: any) {
  return request({
    url: '/project/contract/list',
    method: 'get',
    params: query
  })
}

// 查询合同详细
export function getContract(id: number) {
  return request({
    url: '/project/contract/' + id,
    method: 'get'
  })
}

// 根据合同编号查询合同
export function getContractByNo(contractNo: string) {
  return request({
    url: '/project/contract/no/' + contractNo,
    method: 'get'
  })
}

// 新增合同
export function addContract(data: any) {
  return request({
    url: '/project/contract',
    method: 'post',
    data: data
  })
}

// 修改合同
export function updateContract(data: any) {
  return request({
    url: '/project/contract',
    method: 'put',
    data: data
  })
}

// 删除合同
export function delContract(id: number) {
  return request({
    url: '/project/contract/' + id,
    method: 'delete'
  })
}

// 批量删除合同
export function delContracts(ids: number[]) {
  return request({
    url: '/project/contract/' + ids.join(','),
    method: 'delete'
  })
}

// 导出合同
export function exportContract(query: any) {
  return request({
    url: '/project/contract/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 提交合同审核
export function submitContractForApproval(data: any) {
  return request({
    url: '/project/contract/submit/' + data.id,
    method: 'put'
  })
}

// 合同审核通过
export function approveContract(id: number) {
  return request({
    url: '/project/contract/approve/' + id,
    method: 'put'
  })
}

// 合同审核拒绝
export function rejectContract(id: number, reason: string) {
  return request({
    url: '/project/contract/reject/' + id,
    method: 'put',
    params: { reason }
  })
}

// 合同签署
export function signContract(id: number, signingDate: Date) {
  return request({
    url: '/project/contract/sign/' + id,
    method: 'put',
    params: { signingDate }
  })
}

// 合同执行
export function executeContract(id: number) {
  return request({
    url: '/project/contract/execute/' + id,
    method: 'put'
  })
}

// 合同完成
export function completeContract(id: number) {
  return request({
    url: '/project/contract/complete/' + id,
    method: 'put'
  })
}

// 合同终止
export function terminateContract(id: number, reason: string) {
  return request({
    url: '/project/contract/terminate/' + id,
    method: 'put',
    params: { reason }
  })
}

// 合同备案
export function backupContract(id: number, backupFilePath: string) {
  return request({
    url: '/project/contract/backup/' + id,
    method: 'put',
    params: { backupFilePath }
  })
}

// 根据合作单位查询合同
export function getContractsByPartner(partnerId: number) {
  return request({
    url: '/project/contract/partner/' + partnerId,
    method: 'get'
  })
}

// 查询我负责的合同
export function getMyContracts(principalId: number) {
  return request({
    url: '/project/contract/my/' + principalId,
    method: 'get'
  })
}

// 查询部门合同
export function getDeptContracts(deptId: number) {
  return request({
    url: '/project/contract/dept/' + deptId,
    method: 'get'
  })
}

// 根据状态查询合同
export function getContractsByStatus(status: number) {
  return request({
    url: '/project/contract/status/' + status,
    method: 'get'
  })
}

// 查询即将到期的合同
export function getExpiringContracts(days: number = 30) {
  return request({
    url: '/project/contract/expiring',
    method: 'get',
    params: { days }
  })
}

// 查询已逾期的合同
export function getOverdueContracts() {
  return request({
    url: '/project/contract/overdue',
    method: 'get'
  })
}

// 查询待审核的合同
export function getPendingContracts() {
  return request({
    url: '/project/contract/pending',
    method: 'get'
  })
}

// 搜索合同
export function searchContracts(keyword: string) {
  return request({
    url: '/project/contract/search',
    method: 'get',
    params: { keyword }
  })
}

// 查询合同统计信息
export function getContractStatistics() {
  return request({
    url: '/project/contract/statistics',
    method: 'get'
  })
}

// 查询合同类型统计
export function getContractTypeStatistics() {
  return request({
    url: '/project/contract/statistics/type',
    method: 'get'
  })
}

// 查询合同状态统计
export function getContractStatusStatistics() {
  return request({
    url: '/project/contract/statistics/status',
    method: 'get'
  })
}

// 查询年度合同统计
export function getContractYearlyStatistics(year: number) {
  return request({
    url: '/project/contract/statistics/yearly',
    method: 'get',
    params: { year }
  })
}

// 查询月度合同统计
export function getContractMonthlyStatistics(year: number, month: number) {
  return request({
    url: '/project/contract/statistics/monthly',
    method: 'get',
    params: { year, month }
  })
}

// 查询合同金额统计
export function getContractAmountStatistics() {
  return request({
    url: '/project/contract/statistics/amount',
    method: 'get'
  })
}

// 查询部门合同统计
export function getContractDeptStatistics() {
  return request({
    url: '/project/contract/statistics/dept',
    method: 'get'
  })
}

// 查询合作单位合同统计
export function getContractPartnerStatistics() {
  return request({
    url: '/project/contract/statistics/partner',
    method: 'get'
  })
}

// 查询合同签署趋势
export function getContractSigningTrend(startDate: string, endDate: string) {
  return request({
    url: '/project/contract/statistics/signing-trend',
    method: 'get',
    params: { startDate, endDate }
  })
}

// 查询合同执行情况
export function getContractExecutionStatistics() {
  return request({
    url: '/project/contract/statistics/execution',
    method: 'get'
  })
}

// 生成合同编号
export function generateContractNo(contractType?: string) {
  return request({
    url: '/project/contract/generate-no',
    method: 'get',
    params: { contractType }
  })
}

// 检查合同编号是否存在
export function checkContractNo(contractNo: string, id?: number) {
  return request({
    url: '/project/contract/check-no',
    method: 'get',
    params: { contractNo, id }
  })
}
