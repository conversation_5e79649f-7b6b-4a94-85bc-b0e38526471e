package com.research.framework.config;

import org.activiti.engine.ProcessEngineConfiguration;


import org.activiti.spring.SpringProcessEngineConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * Activiti工作流引擎配置
 * 
 * <AUTHOR>
 */
@Configuration
public class ActivitiConfig {

    private static final Logger logger = LoggerFactory.getLogger(ActivitiConfig.class);

    @Autowired
    private DataSource dataSource;

    @Autowired
    private PlatformTransactionManager transactionManager;



    /**
     * 流程引擎配置
     */
    @Bean
    @Primary
    public SpringProcessEngineConfiguration processEngineConfiguration() {
        logger.info("开始配置Activiti流程引擎...");
        
        SpringProcessEngineConfiguration configuration = new SpringProcessEngineConfiguration();
        
        // 数据源配置
        configuration.setDataSource(dataSource);
        configuration.setTransactionManager(transactionManager);
        
        // 数据库配置
        configuration.setDatabaseSchemaUpdate(ProcessEngineConfiguration.DB_SCHEMA_UPDATE_TRUE);
        configuration.setDatabaseType("mysql");
        
        // 历史记录级别
        configuration.setHistory("full");
        
        // 异步执行器配置
        configuration.setAsyncExecutorActivate(false);
        
        // 流程定义文件位置
        try {
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resolver.getResources("classpath*:processes/*.bpmn");
            if (resources.length > 0) {
                configuration.setDeploymentResources(resources);
                logger.info("找到 {} 个流程定义文件", resources.length);
            } else {
                logger.info("未找到流程定义文件，将在运行时动态部署");
            }
        } catch (Exception e) {
            logger.warn("加载流程定义文件时出错: {}", e.getMessage());
        }
        
        // 禁用异步执行器
        configuration.setAsyncExecutorActivate(false);
        
        logger.info("Activiti流程引擎配置完成");
        return configuration;
    }

    // 注意：使用Spring Boot Starter时，ProcessEngine会自动配置
    // 我们只需要配置ProcessEngineConfiguration即可

    // 注意：使用activiti-spring-boot-starter时，以下服务会自动配置：
    // - ProcessEngine
    // - RepositoryService
    // - RuntimeService
    // - TaskService
    // - HistoryService
    // - ManagementService
    //
    // 无需手动配置这些Bean，可以直接在其他类中@Autowired注入使用
}
