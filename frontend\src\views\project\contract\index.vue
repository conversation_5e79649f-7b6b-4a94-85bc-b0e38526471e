<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="合同编号" prop="contractNo">
        <el-input
          v-model="queryParams.contractNo"
          placeholder="请输入合同编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="合同名称" prop="contractName">
        <el-input
          v-model="queryParams.contractName"
          placeholder="请输入合同名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="合同类型" prop="contractType">
        <el-select v-model="queryParams.contractType" placeholder="请选择合同类型" clearable>
          <el-option label="技术开发" value="技术开发" />
          <el-option label="技术服务" value="技术服务" />
          <el-option label="技术咨询" value="技术咨询" />
          <el-option label="技术转让" value="技术转让" />
        </el-select>
      </el-form-item>
      <el-form-item label="合同状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择合同状态" clearable>
          <el-option label="草稿" value="0" />
          <el-option label="审核中" value="1" />
          <el-option label="已签署" value="2" />
          <el-option label="执行中" value="3" />
          <el-option label="已完成" value="4" />
          <el-option label="已终止" value="5" />
        </el-select>
      </el-form-item>
      <el-form-item label="合作单位" prop="partnerName">
        <el-input
          v-model="queryParams.partnerName"
          placeholder="请输入合作单位名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['project:contract:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['project:contract:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:contract:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['project:contract:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="TrendCharts"
          @click="handleStatistics"
          v-hasPermi="['project:contract:statistics']"
        >统计</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="contractList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="合同编号" align="center" prop="contractNo" width="120" />
      <el-table-column label="合同名称" align="center" prop="contractName" :show-overflow-tooltip="true" />
      <el-table-column label="合同类型" align="center" prop="contractType" width="100" />
      <el-table-column label="合作单位" align="center" prop="partnerName" :show-overflow-tooltip="true" />
      <el-table-column label="合同金额" align="center" prop="contractAmount" width="120">
        <template #default="scope">
          <span>{{ formatMoney(scope.row.contractAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="签署日期" align="center" prop="signingDate" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.signingDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="合同状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="statusOptions" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="负责人" align="center" prop="principalName" width="100" />
      <el-table-column label="所属部门" align="center" prop="deptName" width="120" />
      <el-table-column label="执行进度" align="center" width="120">
        <template #default="scope">
          <el-progress 
            :percentage="scope.row.progressPercentage || 0" 
            :color="getProgressColor(scope.row.progressPercentage)"
            :stroke-width="6"
          />
        </template>
      </el-table-column>
      <el-table-column label="剩余天数" align="center" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.remainingDays !== null" :type="getRemainingDaysType(scope.row.remainingDays)">
            {{ scope.row.remainingDays > 0 ? scope.row.remainingDays + '天' : '已逾期' }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['project:contract:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['project:contract:edit']">修改</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['project:contract:manage']">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="submit" v-if="scope.row.status === 0">提交审核</el-dropdown-item>
                <el-dropdown-item command="approve" v-if="scope.row.status === 1">审核通过</el-dropdown-item>
                <el-dropdown-item command="reject" v-if="scope.row.status === 1">审核拒绝</el-dropdown-item>
                <el-dropdown-item command="sign" v-if="scope.row.status === 1">签署合同</el-dropdown-item>
                <el-dropdown-item command="execute" v-if="scope.row.status === 2">开始执行</el-dropdown-item>
                <el-dropdown-item command="complete" v-if="scope.row.status === 3">完成合同</el-dropdown-item>
                <el-dropdown-item command="terminate" v-if="[2,3].includes(scope.row.status)">终止合同</el-dropdown-item>
                <el-dropdown-item command="backup" v-if="[2,3,4].includes(scope.row.status)">合同备案</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['project:contract:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改合同对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="contractRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="合同编号" prop="contractNo">
              <el-input v-model="form.contractNo" placeholder="请输入合同编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同名称" prop="contractName">
              <el-input v-model="form.contractName" placeholder="请输入合同名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="合同类型" prop="contractType">
              <el-select v-model="form.contractType" placeholder="请选择合同类型">
                <el-option label="技术开发" value="技术开发" />
                <el-option label="技术服务" value="技术服务" />
                <el-option label="技术咨询" value="技术咨询" />
                <el-option label="技术转让" value="技术转让" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合作单位" prop="partnerName">
              <el-input v-model="form.partnerName" placeholder="请输入合作单位名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="合同负责人" prop="principalName">
              <el-input v-model="form.principalName" placeholder="请输入合同负责人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属部门" prop="deptName">
              <el-input v-model="form.deptName" placeholder="请输入所属部门" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="合同开始时间" prop="startDate">
              <el-date-picker
                v-model="form.startDate"
                type="date"
                placeholder="选择开始时间"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同结束时间" prop="endDate">
              <el-date-picker
                v-model="form.endDate"
                type="date"
                placeholder="选择结束时间"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="合同金额" prop="contractAmount">
              <el-input-number v-model="form.contractAmount" :min="0" :precision="2" placeholder="请输入合同金额" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="签署日期" prop="signingDate">
              <el-date-picker
                v-model="form.signingDate"
                type="date"
                placeholder="选择签署日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="合同内容" prop="contractContent">
          <el-input v-model="form.contractContent" type="textarea" :rows="4" placeholder="请输入合同内容" />
        </el-form-item>
        <el-form-item label="付款条款" prop="paymentTerms">
          <el-input v-model="form.paymentTerms" type="textarea" :rows="3" placeholder="请输入付款条款" />
        </el-form-item>
        <el-form-item label="交付条款" prop="deliveryTerms">
          <el-input v-model="form.deliveryTerms" type="textarea" :rows="3" placeholder="请输入交付条款" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 合同详情对话框 -->
    <contract-detail v-model="detailOpen" :contract-id="currentContractId" />

    <!-- 统计分析对话框 -->
    <contract-statistics v-model="statisticsOpen" />
  </div>
</template>

<script setup name="Contract">
import { 
  listContract, 
  getContract, 
  delContract, 
  delContracts,
  addContract, 
  updateContract,
  exportContract,
  submitContractForApproval,
  approveContract,
  rejectContract,
  signContract,
  executeContract,
  completeContract,
  terminateContract,
  backupContract,
  generateContractNo
} from "@/api/project/contract"
import ContractDetail from './components/ContractDetail.vue'
import ContractStatistics from './components/ContractStatistics.vue'

const { proxy } = getCurrentInstance()

const contractList = ref([])
const open = ref(false)
const detailOpen = ref(false)
const statisticsOpen = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const currentContractId = ref(null)

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    contractNo: null,
    contractName: null,
    contractType: null,
    status: null,
    partnerName: null,
    principalName: null
  },
  rules: {
    contractNo: [
      { required: true, message: "合同编号不能为空", trigger: "blur" }
    ],
    contractName: [
      { required: true, message: "合同名称不能为空", trigger: "blur" }
    ],
    contractType: [
      { required: true, message: "合同类型不能为空", trigger: "change" }
    ],
    partnerName: [
      { required: true, message: "合作单位不能为空", trigger: "blur" }
    ],
    principalName: [
      { required: true, message: "合同负责人不能为空", trigger: "blur" }
    ],
    contractAmount: [
      { required: true, message: "合同金额不能为空", trigger: "blur" }
    ]
  }
})

const { queryParams, form, rules } = toRefs(data)

// 状态选项
const statusOptions = ref([
  { label: "草稿", value: "0" },
  { label: "审核中", value: "1" },
  { label: "已签署", value: "2" },
  { label: "执行中", value: "3" },
  { label: "已完成", value: "4" },
  { label: "已终止", value: "5" }
])

/** 查询合同列表 */
function getList() {
  loading.value = true
  listContract(queryParams.value).then(response => {
    contractList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    contractNo: null,
    contractName: null,
    contractType: null,
    partnerName: null,
    principalName: null,
    deptName: null,
    contractAmount: null,
    signingDate: null,
    startDate: null,
    endDate: null,
    contractContent: null,
    paymentTerms: null,
    deliveryTerms: null,
    remark: null
  }
  proxy.resetForm("contractRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
async function handleAdd() {
  reset()
  // 生成合同编号
  try {
    const response = await generateContractNo()
    form.value.contractNo = response.data
  } catch (error) {
    console.error('生成合同编号失败:', error)
  }
  open.value = true
  title.value = "添加合同"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value
  getContract(id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改合同"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["contractRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateContract(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addContract(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const contractIds = row.id || ids.value
  proxy.$modal.confirm('是否确认删除合同编号为"' + contractIds + '"的数据项？').then(function() {
    return delContracts(contractIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('project/contract/export', {
    ...queryParams.value
  }, `contract_${new Date().getTime()}.xlsx`)
}

/** 详情按钮操作 */
function handleDetail(row) {
  currentContractId.value = row.id
  detailOpen.value = true
}

/** 统计按钮操作 */
function handleStatistics() {
  statisticsOpen.value = true
}

/** 更多操作命令处理 */
function handleCommand(command, row) {
  switch (command) {
    case 'submit':
      handleSubmit(row)
      break
    case 'approve':
      handleApprove(row)
      break
    case 'reject':
      handleReject(row)
      break
    case 'sign':
      handleSign(row)
      break
    case 'execute':
      handleExecute(row)
      break
    case 'complete':
      handleComplete(row)
      break
    case 'terminate':
      handleTerminate(row)
      break
    case 'backup':
      handleBackup(row)
      break
  }
}

/** 提交审核 */
function handleSubmit(row) {
  proxy.$modal.confirm('确认提交该合同进行审核？').then(() => {
    return submitContractForApproval({ id: row.id })
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("提交成功")
  })
}

/** 审核通过 */
function handleApprove(row) {
  proxy.$modal.confirm('确认审核通过该合同？').then(() => {
    return approveContract(row.id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("审核通过")
  })
}

/** 审核拒绝 */
function handleReject(row) {
  proxy.$prompt('请输入拒绝原因', '审核拒绝', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(({ value }) => {
    return rejectContract(row.id, value)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("审核拒绝")
  })
}

/** 签署合同 */
function handleSign(row) {
  proxy.$modal.confirm('确认签署该合同？').then(() => {
    return signContract(row.id, new Date())
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("合同已签署")
  })
}

/** 开始执行 */
function handleExecute(row) {
  proxy.$modal.confirm('确认开始执行该合同？').then(() => {
    return executeContract(row.id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("合同已开始执行")
  })
}

/** 完成合同 */
function handleComplete(row) {
  proxy.$modal.confirm('确认完成该合同？').then(() => {
    return completeContract(row.id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("合同已完成")
  })
}

/** 终止合同 */
function handleTerminate(row) {
  proxy.$prompt('请输入终止原因', '终止合同', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(({ value }) => {
    return terminateContract(row.id, value)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("合同已终止")
  })
}

/** 合同备案 */
function handleBackup(row) {
  proxy.$prompt('请输入备案文件路径', '合同备案', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(({ value }) => {
    return backupContract(row.id, value)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("备案成功")
  })
}

// 格式化金额
function formatMoney(money) {
  if (!money) return '0.00'
  return parseFloat(money).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 获取进度颜色
function getProgressColor(percentage) {
  if (!percentage) return '#909399'
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 50) return '#e6a23c'
  return '#f56c6c'
}

// 获取剩余天数类型
function getRemainingDaysType(days) {
  if (days < 0) return 'danger'
  if (days <= 7) return 'danger'
  if (days <= 30) return 'warning'
  return 'success'
}

onMounted(() => {
  getList()
})
</script>
