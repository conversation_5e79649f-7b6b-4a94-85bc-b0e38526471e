import Cookies from 'js-cookie'

const Token<PERSON>ey = 'Admin-Token'

/**
 * 获取token
 */
export function getToken(): string | undefined {
  return Cookies.get(TokenKey)
}

/**
 * 设置token
 */
export function setToken(token: string): string | undefined {
  return Cookies.set(Token<PERSON><PERSON>, token)
}

/**
 * 移除token
 */
export function removeToken(): void {
  return Cookies.remove(TokenKey)
}

/**
 * 检查是否已登录
 */
export function isLoggedIn(): boolean {
  return !!getToken()
}
