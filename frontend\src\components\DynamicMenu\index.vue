<template>
  <div>
    <template v-for="menu in visibleMenus" :key="menu.menuId">
      <!-- 如果有子菜单，显示为子菜单 -->
      <el-sub-menu v-if="hasChildren(menu)" :index="getMenuPath(menu)">
        <template #title>
          <el-icon v-if="menu.icon">
            <component :is="getIconComponent(menu.icon)" />
          </el-icon>
          <span>{{ menu.menuName }}</span>
        </template>
        <DynamicMenu :menus="menu.children" :parent-path="getMenuPath(menu)" />
      </el-sub-menu>

      <!-- 如果没有子菜单，显示为菜单项 -->
      <el-menu-item v-else :index="getMenuPath(menu)">
        <el-icon v-if="menu.icon">
          <component :is="getIconComponent(menu.icon)" />
        </el-icon>
        <span>{{ menu.menuName }}</span>
      </el-menu-item>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { MenuItem } from '@/store/modules/menu'
import {
  House,
  Setting,
  User,
  UserFilled,
  OfficeBuilding,
  Document,
  Folder,
  Monitor,
  DataAnalysis,
  Management,
  Files,
  Connection,
  Promotion,
  Money,
  Star,
  Bell,
  ChatDotRound,
  Tools
} from '@element-plus/icons-vue'

// 定义组件名称以支持递归调用
defineOptions({
  name: 'DynamicMenu'
})

interface Props {
  menus: MenuItem[]
  parentPath?: string
}

const props = withDefaults(defineProps<Props>(), {
  parentPath: ''
})

// 计算可见菜单
const visibleMenus = computed(() => {
  return props.menus.filter(menu => menu.visible === '0')
})

// 获取菜单完整路径
const getMenuPath = (menu: MenuItem): string => {
  if (menu.path.startsWith('/')) {
    return menu.path
  }

  if (props.parentPath) {
    return `${props.parentPath}/${menu.path}`
  }

  // 如果是顶级菜单且没有以/开头，添加/
  return `/${menu.path}`
}

// 判断是否有子菜单
const hasChildren = (menu: MenuItem): boolean => {
  return menu.children && menu.children.length > 0 && 
         menu.children.some(child => child.visible === '0')
}

// 获取图标组件
const getIconComponent = (iconName: string) => {
  const iconMap: Record<string, any> = {
    'house': House,
    'monitor': Monitor,
    'setting': Setting,
    'user': User,
    'user-filled': UserFilled,
    'office-building': OfficeBuilding,
    'document': Document,
    'folder': Folder,
    'data-analysis': DataAnalysis,
    'management': Management,
    'files': Files,
    'connection': Connection,
    'promotion': Promotion,
    'money': Money,
    'star': Star,
    'bell': Bell,
    'chat-dot-round': ChatDotRound,
    'tools': Tools
  }
  
  return iconMap[iconName] || Document
}
</script>
