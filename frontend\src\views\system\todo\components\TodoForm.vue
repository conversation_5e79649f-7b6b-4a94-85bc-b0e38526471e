<template>
  <el-form ref="formRef" :model="localForm" :rules="rules" label-width="80px">
    <el-row>
      <el-col :span="24">
        <el-form-item label="待办标题" prop="title">
          <el-input v-model="localForm.title" placeholder="请输入待办标题" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="localForm.priority" placeholder="请选择优先级">
            <el-option label="低" value="1" />
            <el-option label="中" value="2" />
            <el-option label="高" value="3" />
            <el-option label="紧急" value="4" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="截止时间" prop="dueTime">
          <el-date-picker
            v-model="localForm.dueTime"
            type="datetime"
            placeholder="选择截止时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="!isEdit">
        <el-form-item label="负责人" prop="assigneeName">
          <el-input v-model="localForm.assigneeName" placeholder="请输入负责人" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="分类" prop="category">
          <el-input v-model="localForm.category" placeholder="请输入分类" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="标签" prop="tags">
          <el-input v-model="localForm.tags" placeholder="请输入标签，多个标签用逗号分隔" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="待办内容" prop="content">
          <el-input
            v-model="localForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入待办内容"
          />
        </el-form-item>
      </el-col>
    </el-row>
    
    <div class="form-actions">
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
      <el-button @click="handleCancel">取 消</el-button>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
interface Props {
  form: any
  isEdit: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits(['submit', 'cancel'])

// 响应式数据
const formRef = ref()
const localForm = reactive({
  todoId: undefined,
  title: '',
  content: '',
  priority: '2',
  assigneeId: undefined,
  assigneeName: '',
  dueTime: '',
  category: '',
  tags: ''
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入待办标题', trigger: 'blur' },
    { min: 2, max: 200, message: '标题长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入待办内容', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.form, (newVal) => {
  if (newVal) {
    Object.assign(localForm, newVal)
  }
}, { immediate: true, deep: true })

// 提交表单
const handleSubmit = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      emit('submit', { ...localForm })
    } else {
      ElMessage.error('请检查表单输入')
    }
  })
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
}

// 暴露方法
defineExpose({
  resetForm
})
</script>

<style scoped>
.form-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}
</style>
