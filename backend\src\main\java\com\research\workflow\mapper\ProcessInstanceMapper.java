package com.research.workflow.mapper;

import com.research.workflow.domain.ProcessInstance;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流程实例Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@Mapper
public interface ProcessInstanceMapper {
    
    /**
     * 查询流程实例
     * 
     * @param id 流程实例主键
     * @return 流程实例
     */
    ProcessInstance selectProcessInstanceById(Long id);

    /**
     * 查询流程实例列表
     * 
     * @param processInstance 流程实例
     * @return 流程实例集合
     */
    List<ProcessInstance> selectProcessInstanceList(ProcessInstance processInstance);

    /**
     * 新增流程实例
     * 
     * @param processInstance 流程实例
     * @return 结果
     */
    int insertProcessInstance(ProcessInstance processInstance);

    /**
     * 修改流程实例
     * 
     * @param processInstance 流程实例
     * @return 结果
     */
    int updateProcessInstance(ProcessInstance processInstance);

    /**
     * 删除流程实例
     * 
     * @param id 流程实例主键
     * @return 结果
     */
    int deleteProcessInstanceById(Long id);

    /**
     * 批量删除流程实例
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteProcessInstanceByIds(Long[] ids);

    /**
     * 根据流程实例ID查询流程实例
     * 
     * @param processInstanceId 流程实例ID
     * @return 流程实例
     */
    ProcessInstance selectProcessInstanceByProcessInstanceId(@Param("processInstanceId") String processInstanceId);

    /**
     * 根据业务Key查询流程实例
     * 
     * @param businessKey 业务Key
     * @return 流程实例
     */
    ProcessInstance selectProcessInstanceByBusinessKey(@Param("businessKey") String businessKey);

    /**
     * 查询用户相关的流程实例
     * 
     * @param userId 用户ID
     * @return 流程实例集合
     */
    List<ProcessInstance> selectProcessInstanceByUserId(@Param("userId") String userId);

    /**
     * 查询正在运行的流程实例
     * 
     * @return 流程实例集合
     */
    List<ProcessInstance> selectRunningProcessInstances();

    /**
     * 查询已完成的流程实例
     * 
     * @return 流程实例集合
     */
    List<ProcessInstance> selectFinishedProcessInstances();

    /**
     * 根据流程定义Key查询流程实例
     * 
     * @param processDefinitionKey 流程定义Key
     * @return 流程实例集合
     */
    List<ProcessInstance> selectProcessInstanceByDefinitionKey(@Param("processDefinitionKey") String processDefinitionKey);

    /**
     * 更新流程实例状态
     * 
     * @param processInstanceId 流程实例ID
     * @param status 状态
     * @return 结果
     */
    int updateProcessInstanceStatus(@Param("processInstanceId") String processInstanceId, @Param("status") Integer status);

    /**
     * 更新流程实例挂起状态
     * 
     * @param processInstanceId 流程实例ID
     * @param suspended 是否挂起
     * @return 结果
     */
    int updateProcessInstanceSuspended(@Param("processInstanceId") String processInstanceId, @Param("suspended") Boolean suspended);

    /**
     * 更新流程实例当前节点
     * 
     * @param processInstanceId 流程实例ID
     * @param currentActivityId 当前节点ID
     * @param currentActivityName 当前节点名称
     * @return 结果
     */
    int updateCurrentActivity(@Param("processInstanceId") String processInstanceId, 
                             @Param("currentActivityId") String currentActivityId, 
                             @Param("currentActivityName") String currentActivityName);

    /**
     * 更新流程实例当前处理人
     * 
     * @param processInstanceId 流程实例ID
     * @param currentAssignee 当前处理人ID
     * @param currentAssigneeName 当前处理人名称
     * @return 结果
     */
    int updateCurrentAssignee(@Param("processInstanceId") String processInstanceId, 
                             @Param("currentAssignee") String currentAssignee, 
                             @Param("currentAssigneeName") String currentAssigneeName);

    /**
     * 完成流程实例
     * 
     * @param processInstanceId 流程实例ID
     * @param endTime 结束时间
     * @param processResult 流程结果
     * @return 结果
     */
    int completeProcessInstance(@Param("processInstanceId") String processInstanceId, 
                               @Param("endTime") java.util.Date endTime, 
                               @Param("processResult") String processResult);

    /**
     * 查询超时的流程实例
     * 
     * @return 流程实例集合
     */
    List<ProcessInstance> selectOvertimeProcessInstances();

    /**
     * 统计流程实例数量
     * 
     * @param processDefinitionKey 流程定义Key
     * @param status 状态
     * @return 数量
     */
    Long countProcessInstances(@Param("processDefinitionKey") String processDefinitionKey, @Param("status") Integer status);
}
