<template>
  <div class="workflow-designer">
    <!-- 顶部工具栏 -->
    <div class="designer-toolbar">
      <div class="toolbar-left">
        <div class="toolbar-title">
          <el-icon class="title-icon"><Edit /></el-icon>
          <span>可视化流程设计器</span>
        </div>
      </div>
      <div class="toolbar-center">
        <el-input
          v-model="processName"
          placeholder="请输入流程名称"
          class="process-name-input"
          :prefix-icon="Document"
        />
      </div>
      <div class="toolbar-right">
        <el-button-group>
          <el-button @click="clearCanvas" :icon="Delete">清空</el-button>
          <el-button @click="validateProcess" :icon="Check">验证</el-button>
          <el-button type="primary" @click="saveProcess" :icon="FolderOpened">保存</el-button>
          <el-button type="success" @click="deployProcess" :icon="Upload">部署</el-button>
        </el-button-group>
      </div>
    </div>

    <div class="designer-layout">
      <!-- 左侧组件面板 -->
      <div class="left-panel">
        <div class="panel-header">
          <el-icon><Grid /></el-icon>
          <span>组件面板</span>
        </div>

        <!-- 流程节点 -->
        <div class="component-section">
          <div class="section-header">
            <el-icon><Connection /></el-icon>
            <span>流程节点</span>
          </div>
          <div class="component-grid">
            <div
              class="component-card node-start"
              draggable="true"
              @dragstart="startDrag('start', $event)"
            >
              <div class="card-icon">
                <el-icon><VideoPlay /></el-icon>
              </div>
              <div class="card-label">开始节点</div>
            </div>
            <div
              class="component-card node-task"
              draggable="true"
              @dragstart="startDrag('task', $event)"
            >
              <div class="card-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="card-label">用户任务</div>
            </div>
            <div
              class="component-card node-gateway"
              draggable="true"
              @dragstart="startDrag('gateway', $event)"
            >
              <div class="card-icon">
                <el-icon><Share /></el-icon>
              </div>
              <div class="card-label">网关节点</div>
            </div>
            <div
              class="component-card node-end"
              draggable="true"
              @dragstart="startDrag('end', $event)"
            >
              <div class="card-icon">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="card-label">结束节点</div>
            </div>
          </div>
        </div>

        <!-- 审批人员 -->
        <div class="component-section" v-loading="loadingUsers">
          <div class="section-header">
            <el-icon><User /></el-icon>
            <span>审批人员</span>
            <el-button size="small" text @click="refreshUsers">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
          <div class="component-list">
            <div
              class="component-item user-item"
              draggable="true"
              @dragstart="startDrag('user', $event, user)"
              v-for="user in users"
              :key="user.userId"
            >
              <el-avatar :size="24" :src="user.avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="item-info">
                <div class="item-name">{{ user.nickName }}</div>
                <div class="item-desc">{{ user.deptName || '未分配部门' }}</div>
              </div>
            </div>

            <!-- 无数据提示 -->
            <div v-if="!loadingUsers && users.length === 0" class="empty-tip">
              <el-empty :image-size="60" description="暂无用户数据">
                <el-button size="small" @click="refreshUsers">
                  <el-icon><Refresh /></el-icon>
                  重新加载
                </el-button>
              </el-empty>
            </div>
          </div>
        </div>

        <!-- 部门 -->
        <div class="component-section" v-loading="loadingDepts">
          <div class="section-header">
            <el-icon><OfficeBuilding /></el-icon>
            <span>部门</span>
            <el-button size="small" text @click="refreshDepts">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
          <div class="component-list">
            <div
              class="component-item dept-item"
              draggable="true"
              @dragstart="startDrag('dept', $event, dept)"
              v-for="dept in departments"
              :key="dept.deptId"
            >
              <div class="dept-icon">
                <el-icon><OfficeBuilding /></el-icon>
              </div>
              <div class="item-info">
                <div class="item-name">{{ dept.deptName }}</div>
                <div class="item-desc">{{ dept.leader || '暂无负责人' }}</div>
              </div>
            </div>

            <!-- 无数据提示 -->
            <div v-if="!loadingDepts && departments.length === 0" class="empty-tip">
              <el-empty :image-size="60" description="暂无部门数据">
                <el-button size="small" @click="refreshDepts">
                  <el-icon><Refresh /></el-icon>
                  重新加载
                </el-button>
              </el-empty>
            </div>
          </div>
        </div>

        <!-- 角色 -->
        <div class="component-section" v-loading="loadingRoles">
          <div class="section-header">
            <el-icon><UserFilled /></el-icon>
            <span>角色</span>
            <el-button size="small" text @click="refreshRoles">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
          <div class="component-list">
            <div
              class="component-item role-item"
              draggable="true"
              @dragstart="startDrag('role', $event, role)"
              v-for="role in roles"
              :key="role.roleId"
            >
              <div class="role-icon">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="item-info">
                <div class="item-name">{{ role.roleName }}</div>
                <div class="item-desc">{{ role.remark || '系统角色' }}</div>
              </div>
            </div>

            <!-- 无数据提示 -->
            <div v-if="!loadingRoles && roles.length === 0" class="empty-tip">
              <el-empty :image-size="60" description="暂无角色数据">
                <el-button size="small" @click="refreshRoles">
                  <el-icon><Refresh /></el-icon>
                  重新加载
                </el-button>
              </el-empty>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间画布区域 -->
      <div class="canvas-area">
        <div class="canvas-header">
          <div class="canvas-title">
            <el-icon><Brush /></el-icon>
            <span>设计画布</span>
          </div>
          <div class="canvas-tools">
            <el-button-group size="small">
              <el-button @click="zoomOut" :disabled="zoomLevel <= 0.5">
                <el-icon><ZoomOut /></el-icon>
              </el-button>
              <el-button disabled>{{ Math.round(zoomLevel * 100) }}%</el-button>
              <el-button @click="zoomIn" :disabled="zoomLevel >= 2">
                <el-icon><ZoomIn /></el-icon>
              </el-button>
            </el-button-group>
            <el-button @click="fitToScreen" size="small">
              <el-icon><FullScreen /></el-icon>
              适应屏幕
            </el-button>
          </div>
        </div>

        <div class="canvas-wrapper">
          <div
            class="canvas"
            ref="canvasRef"
            @drop="handleDrop"
            @dragover="allowDrop"
            @click="clearSelection"
            :style="{ transform: `scale(${zoomLevel})` }"
          >
            <!-- 网格背景 -->
            <div class="grid-background"></div>

            <!-- 流程节点 -->
            <div
              v-for="node in nodes"
              :key="node.id"
              class="flow-node"
              :class="[
                `node-${node.type}`,
                { 'selected': selectedNode?.id === node.id }
              ]"
              :style="{
                left: node.x + 'px',
                top: node.y + 'px',
                zIndex: selectedNode?.id === node.id ? 1000 : 1
              }"
              @click.stop="selectNode(node)"
              @mousedown="startNodeDrag(node, $event)"
            >
              <div class="node-content">
                <div class="node-icon">
                  <el-icon>
                    <component :is="getNodeIcon(node.type)" />
                  </el-icon>
                </div>
                <div class="node-label">{{ node.label }}</div>
                <div v-if="node.assignee" class="node-assignee">
                  {{ getAssigneeDisplay(node.assignee) }}
                </div>
              </div>
              <!-- 连接点 -->
              <div class="connection-points">
                <div class="point point-top"></div>
                <div class="point point-right"></div>
                <div class="point point-bottom"></div>
                <div class="point point-left"></div>
              </div>
            </div>

            <!-- 连接线 -->
            <svg class="connections-layer" :style="{ width: '100%', height: '100%' }">
              <defs>
                <marker
                  id="arrowhead"
                  markerWidth="10"
                  markerHeight="7"
                  refX="9"
                  refY="3.5"
                  orient="auto"
                  markerUnits="strokeWidth"
                >
                  <polygon points="0 0, 10 3.5, 0 7" fill="#409eff" />
                </marker>
              </defs>
              <path
                v-for="connection in connections"
                :key="`${connection.from}-${connection.to}`"
                :d="getConnectionPath(connection)"
                stroke="#409eff"
                stroke-width="2"
                fill="none"
                marker-end="url(#arrowhead)"
              />
            </svg>
          </div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="right-panel">
        <div class="panel-header">
          <el-icon><Setting /></el-icon>
          <span>属性面板</span>
        </div>

        <div v-if="selectedNode" class="property-content">
          <el-card shadow="never" class="property-card">
            <template #header>
              <div class="card-header">
                <el-icon>
                  <component :is="getNodeIcon(selectedNode.type)" />
                </el-icon>
                <span>{{ getNodeTypeLabel(selectedNode.type) }}</span>
              </div>
            </template>

            <el-form label-width="80px" size="small">
              <el-form-item label="节点名称">
                <el-input
                  v-model="selectedNode.label"
                  placeholder="请输入节点名称"
                  @input="updateNodeLabel"
                />
              </el-form-item>

              <el-form-item label="节点ID">
                <el-input v-model="selectedNode.id" disabled />
              </el-form-item>

              <el-form-item label="节点类型">
                <el-tag :type="getNodeTagType(selectedNode.type)">
                  {{ getNodeTypeLabel(selectedNode.type) }}
                </el-tag>
              </el-form-item>

              <el-form-item
                label="审批人"
                v-if="selectedNode.type === 'task'"
              >
                <el-select
                  v-model="selectedNode.assignee"
                  placeholder="请选择审批人"
                  filterable
                  clearable
                  style="width: 100%"
                >
                  <el-option-group label="用户">
                    <el-option
                      v-for="user in users"
                      :key="`user-${user.userId}`"
                      :label="user.nickName"
                      :value="`user:${user.userId}`"
                    >
                      <div class="option-item">
                        <el-avatar :size="20" :src="user.avatar">
                          <el-icon><User /></el-icon>
                        </el-avatar>
                        <span>{{ user.nickName }}</span>
                      </div>
                    </el-option>
                  </el-option-group>
                  <el-option-group label="部门">
                    <el-option
                      v-for="dept in departments"
                      :key="`dept-${dept.deptId}`"
                      :label="dept.deptName"
                      :value="`dept:${dept.deptId}`"
                    >
                      <div class="option-item">
                        <el-icon><OfficeBuilding /></el-icon>
                        <span>{{ dept.deptName }}</span>
                      </div>
                    </el-option>
                  </el-option-group>
                  <el-option-group label="角色">
                    <el-option
                      v-for="role in roles"
                      :key="`role-${role.roleId}`"
                      :label="role.roleName"
                      :value="`role:${role.roleId}`"
                    >
                      <div class="option-item">
                        <el-icon><UserFilled /></el-icon>
                        <span>{{ role.roleName }}</span>
                      </div>
                    </el-option>
                  </el-option-group>
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button-group style="width: 100%">
                  <el-button @click="duplicateNode" style="flex: 1">
                    <el-icon><CopyDocument /></el-icon>
                    复制
                  </el-button>
                  <el-button type="danger" @click="deleteNode" style="flex: 1">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </el-button-group>
              </el-form-item>
            </el-form>
          </el-card>
        </div>

        <div v-else class="no-selection">
          <el-empty
            description="请选择一个节点来编辑属性"
            :image-size="80"
          >
            <template #image>
              <el-icon size="80" color="#c0c4cc">
                <Select />
              </el-icon>
            </template>
          </el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Edit, Document, Delete, Check, FolderOpened, Upload,
  Grid, Connection, User, OfficeBuilding, UserFilled, Refresh,
  VideoPlay, Share, CircleClose, Brush, ZoomOut, ZoomIn, FullScreen,
  Setting, CopyDocument, Select
} from '@element-plus/icons-vue'
import {
  saveModelJson,
  deployModel,
  createModel
} from '@/api/workflow/designer'
import { listUser } from '@/api/system/user'
import { listDept } from '@/api/system/dept'
import { listRole } from '@/api/system/role'

// 接口定义
interface FlowNode {
  id: string
  type: 'start' | 'task' | 'gateway' | 'end' | 'user' | 'dept' | 'role'
  label: string
  x: number
  y: number
  assignee?: string
  assigneeData?: any
}

interface Connection {
  from: string
  to: string
}

// 响应式数据
const processName = ref('新建流程')
const nodes = ref<FlowNode[]>([])
const connections = ref<Connection[]>([])
const selectedNode = ref<FlowNode | null>(null)
const canvasRef = ref<HTMLElement>()
const zoomLevel = ref(1)

// 数据加载状态
const loadingUsers = ref(false)
const loadingDepts = ref(false)
const loadingRoles = ref(false)

// 真实数据
const users = ref<any[]>([])
const departments = ref<any[]>([])
const roles = ref<any[]>([])

// 拖拽数据
let dragData = {
  type: '',
  data: null as any
}

// 获取真实数据
const loadUsers = async () => {
  loadingUsers.value = true
  try {
    console.log('🔄 开始加载用户数据...')
    const response = await listUser({
      pageNum: 1,
      pageSize: 100
    })
    console.log('👥 用户数据响应:', response)

    // 检查响应数据格式
    if (Array.isArray(response)) {
      // 直接返回数组（axios拦截器已处理）
      users.value = response
      console.log('✅ 用户数据加载成功:', users.value.length, '个用户')
    } else if (response && (response.code === 0 || response.code === 200)) {
      // 包装在对象中的响应
      users.value = response.rows || []
      console.log('✅ 用户数据加载成功:', users.value.length, '个用户')
    } else {
      console.error('❌ 用户数据格式异常:', response)
      users.value = []
    }

    if (users.value.length === 0) {
      ElMessage.warning('系统中暂无用户数据')
    }
  } catch (error) {
    console.error('❌ 获取用户列表异常:', error)
    // 不显示错误消息，避免首次加载时过多提示
    // ElMessage.error(`获取用户列表失败: ${error.message || '网络错误'}`)
  } finally {
    loadingUsers.value = false
  }
}

const loadDepartments = async () => {
  loadingDepts.value = true
  try {
    console.log('🔄 开始加载部门数据...')
    const response = await listDept({})
    console.log('🏢 部门数据响应:', response)
    console.log('🏢 部门数据类型:', typeof response)
    console.log('🏢 是否为数组:', Array.isArray(response))

    // 检查响应数据格式
    if (Array.isArray(response)) {
      // 直接返回数组（axios拦截器已处理）
      departments.value = response
      console.log('✅ 部门数据加载成功:', departments.value.length, '个部门')
    } else if (response && (response.code === 0 || response.code === 200)) {
      // 包装在对象中的响应
      departments.value = response.data || []
      console.log('✅ 部门数据加载成功:', departments.value.length, '个部门')
    } else {
      console.error('❌ 部门数据格式异常:', response)
      departments.value = []
    }

    if (departments.value.length === 0) {
      ElMessage.warning('系统中暂无部门数据')
    }
  } catch (error) {
    console.error('❌ 获取部门列表异常:', error)
    departments.value = []
    // 不显示错误消息，避免首次加载时过多提示
    // ElMessage.error(`获取部门列表失败: ${error.message || '网络错误'}`)
  } finally {
    loadingDepts.value = false
  }
}

const loadRoles = async () => {
  loadingRoles.value = true
  try {
    console.log('🔄 开始加载角色数据...')
    const response = await listRole({
      pageNum: 1,
      pageSize: 100
    })
    console.log('👔 角色数据响应:', response)

    // 检查响应数据格式
    if (Array.isArray(response)) {
      // 直接返回数组（axios拦截器已处理）
      roles.value = response
      console.log('✅ 角色数据加载成功:', roles.value.length, '个角色')
    } else if (response && (response.code === 0 || response.code === 200)) {
      // 包装在对象中的响应
      roles.value = response.rows || []
      console.log('✅ 角色数据加载成功:', roles.value.length, '个角色')
    } else {
      console.error('❌ 角色数据格式异常:', response)
      roles.value = []
    }

    if (roles.value.length === 0) {
      ElMessage.warning('系统中暂无角色数据')
    }
  } catch (error) {
    console.error('❌ 获取角色列表异常:', error)
    // 不显示错误消息，避免首次加载时过多提示
    // ElMessage.error(`获取角色列表失败: ${error.message || '网络错误'}`)
  } finally {
    loadingRoles.value = false
  }
}

// 刷新数据
const refreshUsers = () => loadUsers()
const refreshDepts = () => loadDepartments()
const refreshRoles = () => loadRoles()

// 开始拖拽
const startDrag = (type: string, event: DragEvent, data: any = null) => {
  dragData.type = type
  dragData.data = data
  event.dataTransfer!.effectAllowed = 'copy'
}

// 允许放置
const allowDrop = (event: DragEvent) => {
  event.preventDefault()
}

// 处理放置
const handleDrop = (event: DragEvent) => {
  event.preventDefault()

  const rect = canvasRef.value!.getBoundingClientRect()
  const x = (event.clientX - rect.left) / zoomLevel.value - 60
  const y = (event.clientY - rect.top) / zoomLevel.value - 30

  const nodeType = dragData.type
  const data = dragData.data

  let newNode: FlowNode

  if (data && (nodeType === 'user' || nodeType === 'dept' || nodeType === 'role')) {
    // 创建带有具体审批人的任务节点
    newNode = {
      id: generateNodeId(),
      type: 'task',
      label: getAssigneeLabel(nodeType, data),
      x,
      y,
      assignee: `${nodeType}:${getAssigneeId(nodeType, data)}`,
      assigneeData: data
    }
  } else {
    // 创建基础流程节点
    newNode = {
      id: generateNodeId(),
      type: nodeType as any,
      label: getDefaultLabel(nodeType),
      x,
      y
    }
  }

  nodes.value.push(newNode)
  ElMessage.success(`已添加${newNode.label}`)
}

// 生成节点ID
const generateNodeId = () => {
  return 'node_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// 获取审批人ID
const getAssigneeId = (type: string, data: any) => {
  switch (type) {
    case 'user': return data.userId
    case 'dept': return data.deptId
    case 'role': return data.roleId
    default: return ''
  }
}

// 获取审批人标签
const getAssigneeLabel = (type: string, data: any) => {
  switch (type) {
    case 'user': return `${data.nickName}审批`
    case 'dept': return `${data.deptName}审批`
    case 'role': return `${data.roleName}审批`
    default: return '审批任务'
  }
}

// 获取默认标签
const getDefaultLabel = (type: string) => {
  const labels = {
    start: '开始',
    task: '审批任务',
    end: '结束',
    user: '用户审批',
    dept: '部门审批',
    role: '角色审批'
  }
  return labels[type] || '未知节点'
}

// 获取节点图标
const getNodeIcon = (type: string) => {
  const icons = {
    start: VideoPlay,
    task: Document,
    gateway: Share,
    end: CircleClose,
    user: User,
    dept: OfficeBuilding,
    role: UserFilled
  }
  return icons[type] || Document
}

// 获取节点类型标签
const getNodeTypeLabel = (type: string) => {
  const labels = {
    start: '开始节点',
    task: '用户任务',
    gateway: '网关节点',
    end: '结束节点',
    user: '用户审批',
    dept: '部门审批',
    role: '角色审批'
  }
  return labels[type] || '未知节点'
}

// 获取节点标签类型
const getNodeTagType = (type: string) => {
  const types = {
    start: 'success',
    task: 'primary',
    gateway: 'warning',
    end: 'danger'
  }
  return types[type] || 'info'
}

// 获取审批人显示名称
const getAssigneeDisplay = (assignee: string) => {
  if (!assignee) return ''

  const [type, id] = assignee.split(':')

  switch (type) {
    case 'user':
      const user = users.value.find(u => u.userId == id)
      return user ? user.nickName : '未知用户'
    case 'dept':
      const dept = departments.value.find(d => d.deptId == id)
      return dept ? dept.deptName : '未知部门'
    case 'role':
      const role = roles.value.find(r => r.roleId == id)
      return role ? role.roleName : '未知角色'
    default:
      return assignee
  }
}

// 选择节点
const selectNode = (node: FlowNode) => {
  selectedNode.value = node
}

// 清除选择
const clearSelection = () => {
  selectedNode.value = null
}

// 删除节点
const deleteNode = async () => {
  if (!selectedNode.value) return

  try {
    await ElMessageBox.confirm('确定要删除此节点吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const nodeId = selectedNode.value.id
    const index = nodes.value.findIndex(n => n.id === nodeId)

    if (index > -1) {
      nodes.value.splice(index, 1)
      // 删除相关连接线
      connections.value = connections.value.filter(
        c => c.from !== nodeId && c.to !== nodeId
      )
      selectedNode.value = null
      ElMessage.success('节点已删除')
    }
  } catch {
    // 用户取消删除
  }
}

// 复制节点
const duplicateNode = () => {
  if (!selectedNode.value) return

  const originalNode = selectedNode.value
  const newNode: FlowNode = {
    ...originalNode,
    id: generateNodeId(),
    x: originalNode.x + 20,
    y: originalNode.y + 20,
    label: originalNode.label + '_副本'
  }

  nodes.value.push(newNode)
  selectedNode.value = newNode
  ElMessage.success('节点已复制')
}

// 更新节点标签
const updateNodeLabel = () => {
  // 实时更新，无需额外处理
}

// 清空画布
const clearCanvas = async () => {
  if (nodes.value.length === 0) {
    ElMessage.info('画布已经是空的')
    return
  }

  try {
    await ElMessageBox.confirm('确定要清空画布吗？此操作不可恢复。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    nodes.value = []
    connections.value = []
    selectedNode.value = null
    ElMessage.success('画布已清空')
  } catch {
    // 用户取消
  }
}

// 验证流程
const validateProcess = () => {
  if (nodes.value.length === 0) {
    ElMessage.warning('请先添加流程节点')
    return
  }

  const startNodes = nodes.value.filter(n => n.type === 'start')
  const endNodes = nodes.value.filter(n => n.type === 'end')

  if (startNodes.length === 0) {
    ElMessage.error('流程必须包含开始节点')
    return
  }

  if (startNodes.length > 1) {
    ElMessage.error('流程只能包含一个开始节点')
    return
  }

  if (endNodes.length === 0) {
    ElMessage.error('流程必须包含结束节点')
    return
  }

  ElMessage.success('流程验证通过')
}

// 保存流程
const saveProcess = async () => {
  if (nodes.value.length === 0) {
    ElMessage.warning('请先添加流程节点')
    return
  }

  if (!processName.value.trim()) {
    ElMessage.warning('请输入流程名称')
    return
  }

  try {
    const processData = {
      name: processName.value,
      nodes: nodes.value,
      connections: connections.value,
      createTime: new Date().toISOString()
    }

    console.log('保存流程数据:', processData)

    // 这里可以调用后端API保存
    // await saveModelJson(modelId, JSON.stringify(processData))

    ElMessage.success('流程已保存')
  } catch (error) {
    console.error('保存流程失败:', error)
    ElMessage.error('保存流程失败')
  }
}

// 部署流程
const deployProcess = async () => {
  if (nodes.value.length === 0) {
    ElMessage.warning('请先添加流程节点')
    return
  }

  // 先验证流程
  validateProcess()

  try {
    await ElMessageBox.confirm('确定要部署此流程吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })

    // 这里可以调用后端API部署
    // await deployModel(modelId)

    ElMessage.success('流程部署成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('部署流程失败:', error)
      ElMessage.error('部署流程失败')
    }
  }
}

// 缩放功能
const zoomIn = () => {
  if (zoomLevel.value < 2) {
    zoomLevel.value = Math.min(2, zoomLevel.value + 0.1)
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.1)
  }
}

const fitToScreen = () => {
  zoomLevel.value = 1
}

// 获取连接线路径
const getConnectionPath = (connection: Connection) => {
  const fromNode = nodes.value.find(n => n.id === connection.from)
  const toNode = nodes.value.find(n => n.id === connection.to)

  if (!fromNode || !toNode) return ''

  const fromX = fromNode.x + 60
  const fromY = fromNode.y + 30
  const toX = toNode.x + 60
  const toY = toNode.y + 30

  // 简单的直线连接
  return `M ${fromX} ${fromY} L ${toX} ${toY}`
}

// 节点拖拽移动
const startNodeDrag = (node: FlowNode, event: MouseEvent) => {
  event.preventDefault()

  const startX = event.clientX
  const startY = event.clientY
  const nodeStartX = node.x
  const nodeStartY = node.y

  const handleMouseMove = (e: MouseEvent) => {
    const deltaX = (e.clientX - startX) / zoomLevel.value
    const deltaY = (e.clientY - startY) / zoomLevel.value

    node.x = nodeStartX + deltaX
    node.y = nodeStartY + deltaY
  }

  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 组件挂载时加载数据
onMounted(async () => {
  console.log('🎨 流程设计器初始化...')

  // 延迟一下，确保组件完全挂载
  await new Promise(resolve => setTimeout(resolve, 100))

  try {
    // 串行加载数据，避免并发请求问题
    console.log('📡 开始加载基础数据...')

    await loadUsers()
    await loadDepartments()
    await loadRoles()

    // 统计加载结果
    const totalData = users.value.length + departments.value.length + roles.value.length

    console.log('📈 最终数据统计:')
    console.log(`  👥 用户: ${users.value.length}个`)
    console.log(`  🏢 部门: ${departments.value.length}个`)
    console.log(`  👔 角色: ${roles.value.length}个`)
    console.log(`  📊 总计: ${totalData}条数据`)

    if (totalData > 0) {
      ElMessage.success(`设计器加载完成！共加载 ${totalData} 条数据`)
    } else {
      ElMessage.warning('数据加载完成，但暂无可用数据')
    }

  } catch (error) {
    console.error('❌ 设计器初始化失败:', error)
    ElMessage.error('设计器初始化失败，请刷新页面重试')
  }

  console.log('✅ 流程设计器初始化完成')
})
</script>

<style scoped>
.workflow-designer {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 工具栏样式 */
.designer-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.title-icon {
  margin-right: 8px;
  color: #409eff;
}

.toolbar-center {
  flex: 1;
  max-width: 300px;
  margin: 0 20px;
}

.process-name-input {
  font-size: 16px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

/* 主布局 */
.designer-layout {
  display: flex;
  flex: 1;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
}

/* 左侧面板 */
.left-panel {
  width: 280px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.panel-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px 12px 0 0;
}

.panel-header .el-icon {
  margin-right: 8px;
  color: #409eff;
}

/* 组件区域 */
.component-section {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.section-header .el-icon {
  margin-right: 6px;
}

/* 组件网格 */
.component-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.component-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: grab;
  transition: all 0.3s ease;
  user-select: none;
}

.component-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.component-card:active {
  cursor: grabbing;
  transform: translateY(0);
}

.component-card.node-start {
  border-color: #67c23a;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
}

.component-card.node-task {
  border-color: #409eff;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
}

.component-card.node-gateway {
  border-color: #e6a23c;
  background: linear-gradient(135deg, #fffbf0 0%, #fff7e6 100%);
}

.component-card.node-end {
  border-color: #f56c6c;
  background: linear-gradient(135deg, #fff0f0 0%, #ffe6e6 100%);
}

.card-icon {
  font-size: 20px;
  margin-bottom: 4px;
  color: #409eff;
}

.card-label {
  font-size: 12px;
  color: #606266;
  text-align: center;
}

/* 画布区域 */
.canvas-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.canvas-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #f0f0f0;
}

.canvas-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.canvas-title .el-icon {
  margin-right: 8px;
  color: #409eff;
}

.canvas-tools {
  display: flex;
  align-items: center;
  gap: 8px;
}

.canvas-wrapper {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.canvas {
  width: 100%;
  height: 100%;
  background: #fafafa;
  position: relative;
  overflow: hidden;
  transition: transform 0.2s ease;
}

.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(to right, #e0e0e0 1px, transparent 1px),
    linear-gradient(to bottom, #e0e0e0 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.5;
}

/* 流程节点 */
.flow-node {
  position: absolute;
  width: 120px;
  height: 60px;
  background: #fff;
  border: 2px solid #409eff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flow-node:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.flow-node.selected {
  border-color: #67c23a;
  box-shadow: 0 0 0 3px rgba(103, 194, 58, 0.2);
}

.flow-node.node-start {
  border-color: #67c23a;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
}

.flow-node.node-task {
  border-color: #409eff;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
}

.flow-node.node-gateway {
  border-color: #e6a23c;
  background: linear-gradient(135deg, #fffbf0 0%, #fff7e6 100%);
}

.flow-node.node-end {
  border-color: #f56c6c;
  background: linear-gradient(135deg, #fff0f0 0%, #ffe6e6 100%);
}

.node-content {
  text-align: center;
  padding: 4px;
}

.node-icon {
  font-size: 18px;
  margin-bottom: 4px;
  color: #409eff;
}

.node-label {
  font-size: 12px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
}

.node-assignee {
  font-size: 10px;
  color: #909399;
  margin-top: 2px;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 连接点 */
.connection-points {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.point {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #409eff;
  border: 2px solid #fff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.flow-node:hover .point {
  opacity: 1;
}

.point-top {
  top: 0;
  left: 50%;
}

.point-right {
  top: 50%;
  right: 0;
  transform: translate(50%, -50%);
}

.point-bottom {
  bottom: 0;
  left: 50%;
  transform: translate(-50%, 50%);
}

.point-left {
  top: 50%;
  left: 0;
}

/* 连接线 */
.connections-layer {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
}

/* 右侧属性面板 */
.right-panel {
  width: 300px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
}

.property-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.property-card {
  border: none;
  box-shadow: none;
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.card-header .el-icon {
  margin-right: 8px;
  color: #409eff;
}

.no-selection {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 空数据提示 */
.empty-tip {
  padding: 20px;
  text-align: center;
}

.empty-tip .el-empty {
  padding: 10px;
}
</style>
