import request from '@/utils/request'

// 获取用户工作台数据
export function getWorkspaceData() {
  return request({
    url: '/workspace/data',
    method: 'get'
  })
}

// 获取用户个人信息
export function getUserInfo() {
  return request({
    url: '/workspace/userInfo',
    method: 'get'
  })
}

// 获取用户待办统计
export function getTodoStats() {
  return request({
    url: '/workspace/todoStats',
    method: 'get'
  })
}

// 获取用户公告统计
export function getNoticeStats() {
  return request({
    url: '/workspace/noticeStats',
    method: 'get'
  })
}

// 获取用户消息统计
export function getMessageStats() {
  return request({
    url: '/workspace/messageStats',
    method: 'get'
  })
}

// 获取用户快捷应用
export function getQuickApps() {
  return request({
    url: '/workspace/quickApps',
    method: 'get'
  })
}

// 保存用户快捷应用配置
export function saveQuickApps(appCodes: string[]) {
  return request({
    url: '/workspace/quickApps',
    method: 'post',
    data: appCodes
  })
}

// 获取可用的快捷应用
export function getAvailableApps() {
  return request({
    url: '/workspace/availableApps',
    method: 'get'
  })
}

// 获取用户布局配置
export function getLayoutConfig() {
  return request({
    url: '/workspace/layoutConfig',
    method: 'get'
  })
}

// 保存用户布局配置
export function saveLayoutConfig(layoutConfig: any) {
  return request({
    url: '/workspace/layoutConfig',
    method: 'post',
    data: layoutConfig
  })
}

// 获取用户主题配置
export function getThemeConfig() {
  return request({
    url: '/workspace/themeConfig',
    method: 'get'
  })
}

// 保存用户主题配置
export function saveThemeConfig(themeConfig: any) {
  return request({
    url: '/workspace/themeConfig',
    method: 'post',
    data: themeConfig
  })
}

// 获取工作台统计数据
export function getStatistics() {
  return request({
    url: '/workspace/statistics',
    method: 'get'
  })
}

// 重置工作台配置
export function resetWorkspace() {
  return request({
    url: '/workspace/reset',
    method: 'post'
  })
}

// 保存用户配置
export function saveUserConfig(configType: string, configKey: string, configValue: string, sortOrder?: number) {
  return request({
    url: '/workspace/config',
    method: 'post',
    params: {
      configType,
      configKey,
      configValue,
      sortOrder
    }
  })
}

// 删除用户配置
export function deleteUserConfig(configType: string, configKey: string) {
  return request({
    url: '/workspace/config',
    method: 'delete',
    params: {
      configType,
      configKey
    }
  })
}
