<template>
  <el-card class="message-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span class="card-title">站内消息</span>
        <div class="header-actions">
          <el-badge :value="messageStats.unreadMessages" :hidden="!messageStats.unreadMessages" type="danger">
            <el-button type="text" @click="$emit('refresh')">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </el-badge>
          <el-button type="text" @click="goToMessageList">
            <el-icon><More /></el-icon>
          </el-button>
        </div>
      </div>
    </template>
    
    <div class="message-content">
      <!-- 消息统计 -->
      <div class="message-stats">
        <div class="stat-item">
          <div class="stat-number unread">{{ messageStats.unreadMessages || 0 }}</div>
          <div class="stat-label">未读</div>
        </div>
        <div class="stat-item">
          <div class="stat-number total">{{ messageStats.totalMessages || 0 }}</div>
          <div class="stat-label">总计</div>
        </div>
        <div class="stat-item">
          <div class="stat-number sent">{{ messageStats.sentMessages || 0 }}</div>
          <div class="stat-label">已发送</div>
        </div>
      </div>
      
      <!-- 最新消息 -->
      <div class="latest-messages" v-if="messageStats.latestMessages && messageStats.latestMessages.length > 0">
        <h4 class="section-title">最新消息</h4>
        <div class="message-list">
          <div 
            v-for="message in messageStats.latestMessages" 
            :key="message.messageId"
            class="message-item"
            :class="{ 'unread': message.isRead === '0' }"
            @click="viewMessage(message.messageId)"
          >
            <div class="message-info">
              <div class="message-title">
                <span class="message-type-badge" :class="getMessageTypeClass(message.messageType)">
                  {{ getMessageTypeText(message.messageType) }}
                </span>
                {{ message.title }}
              </div>
              <div class="message-meta">
                <span class="message-sender">{{ message.senderName }}</span>
                <span class="message-time">{{ formatDate(message.createTime) }}</span>
              </div>
              <div class="message-preview" v-if="message.content">
                {{ truncateText(message.content, 50) }}
              </div>
            </div>
            <div class="message-status">
              <el-tag 
                v-if="message.priority === '3' || message.priority === '4'" 
                :type="message.priority === '4' ? 'danger' : 'warning'"
                size="small"
              >
                {{ message.priority === '4' ? '紧急' : '重要' }}
              </el-tag>
              <el-icon v-if="message.isRead === '0'" class="unread-dot"><CircleFilled /></el-icon>
              <el-icon class="arrow-icon"><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 快速操作 -->
      <div class="quick-actions">
        <el-button type="primary" size="small" @click="composeMessage">
          <el-icon><EditPen /></el-icon>
          写消息
        </el-button>
        <el-button size="small" @click="markAllAsRead" v-if="messageStats.unreadMessages > 0">
          全部已读
        </el-button>
      </div>
      
      <!-- 空状态 -->
      <div v-if="!messageStats.latestMessages || messageStats.latestMessages.length === 0" class="empty-state">
        <el-icon class="empty-icon"><ChatDotRound /></el-icon>
        <p>暂无消息</p>
        <el-button type="primary" size="small" @click="composeMessage">发送消息</el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Refresh, More, CircleFilled, ArrowRight, EditPen, ChatDotRound } from '@element-plus/icons-vue'
import { markAllMessageAsRead } from '@/api/system/message'

// Props
interface Props {
  messageStats: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits(['refresh'])

const router = useRouter()

// 获取消息类型样式类
const getMessageTypeClass = (type: string) => {
  const classes: Record<string, string> = {
    '1': 'system',      // 系统消息
    '2': 'notification', // 通知消息
    '3': 'private',     // 私信消息
    '4': 'reminder'     // 提醒消息
  }
  return classes[type] || 'default'
}

// 获取消息类型文本
const getMessageTypeText = (type: string) => {
  const texts: Record<string, string> = {
    '1': '系统',
    '2': '通知',
    '3': '私信',
    '4': '提醒'
  }
  return texts[type] || '消息'
}

// 截断文本
const truncateText = (text: string, length: number) => {
  if (!text) return ''
  return text.length > length ? text.substring(0, length) + '...' : text
}

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return ''
  const d = new Date(date)
  const now = new Date()
  const diff = now.getTime() - d.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  return d.toLocaleDateString('zh-CN')
}

// 查看消息详情
const viewMessage = (messageId: number) => {
  router.push(`/system/message/detail/${messageId}`)
}

// 跳转到消息列表
const goToMessageList = () => {
  router.push('/system/message')
}

// 写消息
const composeMessage = () => {
  router.push('/system/message/compose')
}

// 全部标记为已读
const markAllAsRead = async () => {
  try {
    await markAllMessageAsRead()
    ElMessage.success('已全部标记为已读')
    emit('refresh')
  } catch (error) {
    console.error('标记已读失败:', error)
    ElMessage.error('标记已读失败')
  }
}
</script>

<style scoped>
.message-card {
  height: 100%;
  min-height: 300px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 5px;
}

.message-content {
  height: 100%;
}

.message-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 5px;
}

.stat-number.unread { color: #f56c6c; }
.stat-number.total { color: #303133; }
.stat-number.sent { color: #67c23a; }

.stat-label {
  font-size: 12px;
  color: #909399;
}

.section-title {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.message-list {
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 15px;
}

.message-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.3s;
}

.message-item:hover {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding-left: 8px;
  padding-right: 8px;
}

.message-item.unread {
  background-color: #fafcff;
}

.message-item:last-child {
  border-bottom: none;
}

.message-info {
  flex: 1;
}

.message-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 6px;
}

.message-type-badge {
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
  color: white;
}

.message-type-badge.system { background-color: #909399; }
.message-type-badge.notification { background-color: #409eff; }
.message-type-badge.private { background-color: #67c23a; }
.message-type-badge.reminder { background-color: #e6a23c; }

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.message-sender, .message-time {
  font-size: 12px;
  color: #909399;
}

.message-preview {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.message-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  margin-left: 10px;
}

.unread-dot {
  color: #f56c6c;
  font-size: 8px;
}

.arrow-icon {
  color: #c0c4cc;
  font-size: 12px;
}

.quick-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

@media (max-width: 768px) {
  .message-stats {
    flex-wrap: wrap;
  }
  
  .stat-item {
    flex: 0 0 33.33%;
    margin-bottom: 10px;
  }
  
  .message-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .message-status {
    margin-left: 0;
    margin-top: 5px;
    flex-direction: row;
    align-self: flex-end;
  }
  
  .quick-actions {
    flex-direction: column;
  }
}
</style>
