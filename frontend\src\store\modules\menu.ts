import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { RouteRecordRaw } from 'vue-router'
import router from '@/router'

export interface MenuItem {
  menuId?: number
  menuName?: string
  parentId?: number
  path?: string
  component?: string
  query?: string
  menuType?: string
  visible?: string
  isFrame?: string
  isCache?: string
  perms?: string
  icon?: string
  children?: MenuItem[]
}

export const useMenuStore = defineStore('menu', () => {
  // 状态
  const menus = ref<MenuItem[]>([])
  const routes = ref<RouteRecordRaw[]>([])

  // 设置菜单数据
  const setMenus = (menuData: MenuItem[]) => {
    console.log('设置菜单数据:', menuData)
    menus.value = menuData
    const dynamicRoutes = generateRoutes(menuData)
    routes.value = dynamicRoutes

    console.log('生成的动态路由:', dynamicRoutes)

    // 动态添加路由到Vue Router
    dynamicRoutes.forEach(route => {
      try {
        router.addRoute(route)
        console.log('成功添加路由:', route.name, route.path)
      } catch (error) {
        console.error('添加路由失败:', route.name, route.path, error)
      }
    })
  }

  // 生成路由
  const generateRoutes = (menuData: MenuItem[]): RouteRecordRaw[] => {
    const routes: RouteRecordRaw[] = []

    menuData.forEach(menu => {
      if (menu.visible === '0') { // 只处理可见菜单
        const route = generateRoute(menu, true) // 顶级菜单
        if (route) {
          routes.push(route)
        }
      }
    })

    return routes
  }

  // 生成单个路由
  const generateRoute = (menu: MenuItem, isRoot = false): RouteRecordRaw | null => {
    if (!menu.path || !menu.menuId) return null

    // 处理路径：顶级菜单需要以/开头，子菜单使用相对路径
    let routePath = menu.path
    if (isRoot && !routePath.startsWith('/')) {
      routePath = `/${routePath}`
    }

    // 生成唯一的路由名称，避免冲突
    const routeName = `Menu_${menu.menuId}_${menu.path.replace(/[^a-zA-Z0-9]/g, '_')}`

    const route: RouteRecordRaw = {
      path: routePath,
      name: routeName,
      meta: {
        title: menu.menuName,
        icon: menu.icon,
        hidden: menu.visible === '1'
      }
    }

    // 设置组件
    if (menu.component) {
      if (menu.component === 'Layout') {
        route.component = () => import('@/layout/index.vue')
      } else if (menu.component === 'ParentView') {
        route.component = () => import('@/components/ParentView/index.vue')
      } else if (menu.component === 'InnerLink') {
        route.component = () => import('@/components/InnerLink/index.vue')
      } else {
        // 使用 @vite-ignore 注释来避免 Vite 警告
        route.component = () => import(/* @vite-ignore */ `@/views/${menu.component}.vue`)
      }
    } else {
      // 如果没有组件，根据菜单类型和层级设置默认组件
      if (menu.menuType === 'M') {
        if (isRoot) {
          // 顶级目录使用Layout组件
          route.component = () => import('@/layout/index.vue')
        } else {
          // 子级目录使用ParentView组件
          route.component = () => import('@/components/ParentView/index.vue')
        }
      } else {
        // 其他类型使用ParentView组件
        route.component = () => import('@/components/ParentView/index.vue')
      }
    }

    // 处理子菜单
    if (menu.children && menu.children.length > 0) {
      route.children = []
      menu.children.forEach(child => {
        const childRoute = generateRoute(child, false) // 子菜单
        if (childRoute) {
          route.children!.push(childRoute)
        }
      })

      // 如果是目录类型且有子菜单，设置重定向到第一个子菜单
      if (menu.menuType === 'M' && route.children.length > 0) {
        const firstChild = route.children[0]
        if (firstChild.path) {
          // 确保路径正确拼接
          const childPath = firstChild.path.startsWith('/') ? firstChild.path : firstChild.path
          route.redirect = `${route.path}/${childPath}`
        }
      }
    }

    return route
  }

  // 清空菜单数据
  const clearMenus = () => {
    // 移除动态添加的路由
    routes.value.forEach(route => {
      if (route.name) {
        try {
          router.removeRoute(route.name as string)
        } catch (error) {
          console.warn('移除路由失败:', route.name, error)
        }
      }
    })

    menus.value = []
    routes.value = []
  }

  return {
    // 状态
    menus,
    routes,
    
    // 方法
    setMenus,
    generateRoutes,
    clearMenus
  }
})
