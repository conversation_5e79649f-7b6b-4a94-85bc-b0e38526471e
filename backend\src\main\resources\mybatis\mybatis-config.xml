<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
	
	<settings>
		<!-- 使用jdbc的getGeneratedKeys获取数据库自增主键值 -->
		<setting name="useGeneratedKeys" value="true" />
		
		<!-- 使用列别名替换列名 默认:true -->
		<setting name="useColumnLabel" value="true" />
		
		<!-- 开启驼峰命名转换:Table{create_time} -> Entity{createTime} -->
		<setting name="mapUnderscoreToCamelCase" value="true" />
		
		<!-- 二级缓存的全局开关 -->
		<setting name="cacheEnabled" value="false" />
		
		<!-- 延迟加载的全局开关 -->
		<setting name="lazyLoadingEnabled" value="false" />
		
		<!-- 开启时，任何方法的调用都会加载该对象的所有属性 -->
		<setting name="aggressiveLazyLoading" value="false" />
		
		<!-- 对于未知的SQL查询，允许返回不同的结果集以达到通用的效果 -->
		<setting name="multipleResultSetsEnabled" value="true" />
		
		<!-- 允许使用列标签代替列名 -->
		<setting name="useColumnLabel" value="true" />
		
		<!-- 不允许使用自定义的主键值(比如由程序生成的UUID 32位编码作为键值)，数据表的PK生成策略将被覆盖 -->
		<setting name="useGeneratedKeys" value="false" />
		
		<!-- 给予被嵌套的resultMap以字段-属性的映射支持 FULL,PARTIAL -->
		<setting name="autoMappingBehavior" value="PARTIAL" />
		
		<!-- 对于批量更新操作缓存SQL以提高性能 BATCH,SIMPLE -->
		<!-- <setting name="defaultExecutorType" value="BATCH" /> -->
		
		<!-- 数据库超过25000秒仍未响应则超时 -->
		<!-- <setting name="defaultStatementTimeout" value="25000" /> -->
	</settings>

</configuration>
