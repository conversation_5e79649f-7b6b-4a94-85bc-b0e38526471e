<template>
  <div class="user-auth-role">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>用户分配角色</span>
          <el-button type="primary" @click="goBack">返回</el-button>
        </div>
      </template>
      
      <el-form :model="userInfo" label-width="100px">
        <el-form-item label="用户名称">
          <el-input v-model="userInfo.userName" disabled />
        </el-form-item>
        <el-form-item label="用户昵称">
          <el-input v-model="userInfo.nickName" disabled />
        </el-form-item>
      </el-form>
      
      <el-divider content-position="left">角色分配</el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="never">
            <template #header>
              <span>未分配角色</span>
            </template>
            
            <el-table
              v-loading="unassignedLoading"
              :data="unassignedRoles"
              @selection-change="handleUnassignedSelectionChange"
              height="300"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="roleName" label="角色名称" />
              <el-table-column prop="roleKey" label="角色权限" />
              <el-table-column prop="remark" label="备注" show-overflow-tooltip />
            </el-table>
            
            <div class="table-footer">
              <el-button
                type="primary"
                :disabled="unassignedSelection.length === 0"
                @click="assignRoles"
              >
                分配角色
              </el-button>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card shadow="never">
            <template #header>
              <span>已分配角色</span>
            </template>
            
            <el-table
              v-loading="assignedLoading"
              :data="assignedRoles"
              @selection-change="handleAssignedSelectionChange"
              height="300"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="roleName" label="角色名称" />
              <el-table-column prop="roleKey" label="角色权限" />
              <el-table-column prop="remark" label="备注" show-overflow-tooltip />
            </el-table>
            
            <div class="table-footer">
              <el-button
                type="danger"
                :disabled="assignedSelection.length === 0"
                @click="unassignRoles"
              >
                取消分配
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <div class="form-footer">
        <el-button type="primary" @click="submitAuth">保存</el-button>
        <el-button @click="goBack">取消</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAuthRole, updateAuthRole } from '@/api/system/user'

const route = useRoute()
const router = useRouter()

const userId = ref('')
const userInfo = ref({
  userName: '',
  nickName: '',
  email: ''
})

const unassignedRoles = ref([])
const assignedRoles = ref([])
const unassignedSelection = ref([])
const assignedSelection = ref([])
const unassignedLoading = ref(false)
const assignedLoading = ref(false)

const handleUnassignedSelectionChange = (selection: any[]) => {
  unassignedSelection.value = selection
}

const handleAssignedSelectionChange = (selection: any[]) => {
  assignedSelection.value = selection
}

const getUserRoleData = () => {
  unassignedLoading.value = true
  assignedLoading.value = true
  getAuthRole(userId.value).then(response => {
    const { user, roles, roleIds } = response
    userInfo.value = user
    unassignedRoles.value = roles.filter(role => !roleIds.includes(role.roleId))
    assignedRoles.value = roles.filter(role => roleIds.includes(role.roleId))
    unassignedLoading.value = false
    assignedLoading.value = false
  }).catch(() => {
    unassignedLoading.value = false
    assignedLoading.value = false
  })
}

const getUnassignedRoles = getUserRoleData
const getAssignedRoles = () => {}

const assignRoles = () => {
  if (unassignedSelection.value.length === 0) {
    ElMessage.warning('请选择要分配的角色')
    return
  }
  
  // 移动选中的角色到已分配列表
  const selectedRoles = [...unassignedSelection.value]
  assignedRoles.value.push(...selectedRoles)
  
  // 从未分配列表中移除
  selectedRoles.forEach(role => {
    const index = unassignedRoles.value.findIndex(item => item.roleId === role.roleId)
    if (index > -1) {
      unassignedRoles.value.splice(index, 1)
    }
  })
  
  unassignedSelection.value = []
  ElMessage.success('角色分配成功')
}

const unassignRoles = () => {
  if (assignedSelection.value.length === 0) {
    ElMessage.warning('请选择要取消分配的角色')
    return
  }
  
  // 移动选中的角色到未分配列表
  const selectedRoles = [...assignedSelection.value]
  unassignedRoles.value.push(...selectedRoles)
  
  // 从已分配列表中移除
  selectedRoles.forEach(role => {
    const index = assignedRoles.value.findIndex(item => item.roleId === role.roleId)
    if (index > -1) {
      assignedRoles.value.splice(index, 1)
    }
  })
  
  assignedSelection.value = []
  ElMessage.success('取消分配成功')
}

const submitAuth = async () => {
  try {
    await ElMessageBox.confirm('确认要保存角色分配吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const roleIds = assignedRoles.value.map(role => role.roleId)
    await updateAuthRole({ userId: userId.value, roleIds })
    ElMessage.success('保存成功')
    router.back()
  } catch {
    // 用户取消操作
  }
}

const goBack = () => {
  router.back()
}

onMounted(() => {
  userId.value = route.params.userId as string
  if (userId.value) {
    getUserRoleData()
  }
})
</script>

<style scoped>
.user-auth-role {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-footer {
  margin-top: 20px;
  text-align: center;
}

.form-footer {
  margin-top: 30px;
  text-align: center;
}
</style>
