<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目编号" prop="projectNo">
        <el-input
          v-model="queryParams.projectNo"
          placeholder="请输入项目编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目类型" prop="projectType">
        <el-select v-model="queryParams.projectType" placeholder="请选择项目类型" clearable>
          <el-option label="技术开发" value="技术开发" />
          <el-option label="技术服务" value="技术服务" />
          <el-option label="技术咨询" value="技术咨询" />
          <el-option label="技术转让" value="技术转让" />
        </el-select>
      </el-form-item>
      <el-form-item label="项目状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择项目状态" clearable>
          <el-option label="申请中" value="0" />
          <el-option label="立项" value="1" />
          <el-option label="执行中" value="2" />
          <el-option label="变更中" value="3" />
          <el-option label="结项中" value="4" />
          <el-option label="已结项" value="5" />
          <el-option label="已撤销" value="6" />
        </el-select>
      </el-form-item>
      <el-form-item label="负责人" prop="principalName">
        <el-input
          v-model="queryParams.principalName"
          placeholder="请输入负责人姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['project:horizontal:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['project:horizontal:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:horizontal:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['project:horizontal:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="TrendCharts"
          @click="handleStatistics"
          v-hasPermi="['project:horizontal:statistics']"
        >统计</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="projectList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="项目编号" align="center" prop="projectNo" width="120" />
      <el-table-column label="项目名称" align="center" prop="projectName" :show-overflow-tooltip="true" />
      <el-table-column label="项目类型" align="center" prop="projectType" width="100" />
      <el-table-column label="合作单位" align="center" prop="partnerName" :show-overflow-tooltip="true" />
      <el-table-column label="项目经费" align="center" prop="totalFund" width="120">
        <template #default="scope">
          <span>{{ formatMoney(scope.row.totalFund) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="到账经费" align="center" prop="receivedFund" width="120">
        <template #default="scope">
          <span>{{ formatMoney(scope.row.receivedFund) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="到账率" align="center" width="100">
        <template #default="scope">
          <el-tag :type="getFundRateType(scope.row.fundReceiveRate)">
            {{ scope.row.fundReceiveRate ? scope.row.fundReceiveRate.toFixed(1) + '%' : '0%' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="项目状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="statusOptions" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="负责人" align="center" prop="principalName" width="100" />
      <el-table-column label="所属部门" align="center" prop="deptName" width="120" />
      <el-table-column label="项目进度" align="center" width="120">
        <template #default="scope">
          <el-progress 
            :percentage="scope.row.progressPercentage || 0" 
            :color="getProgressColor(scope.row.progressPercentage)"
            :stroke-width="6"
          />
        </template>
      </el-table-column>
      <el-table-column label="剩余天数" align="center" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.remainingDays !== null" :type="getRemainingDaysType(scope.row.remainingDays)">
            {{ scope.row.remainingDays > 0 ? scope.row.remainingDays + '天' : '已逾期' }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['project:horizontal:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['project:horizontal:edit']">修改</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['project:horizontal:manage']">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="approve" v-if="scope.row.status === 0">立项</el-dropdown-item>
                <el-dropdown-item command="execute" v-if="scope.row.status === 1">执行</el-dropdown-item>
                <el-dropdown-item command="suspend" v-if="scope.row.status === 2">挂起</el-dropdown-item>
                <el-dropdown-item command="resume" v-if="scope.row.status === 3">恢复</el-dropdown-item>
                <el-dropdown-item command="complete" v-if="[1,2,3].includes(scope.row.status)">结项</el-dropdown-item>
                <el-dropdown-item command="cancel" v-if="[0,1,2,3].includes(scope.row.status)">撤销</el-dropdown-item>
                <el-dropdown-item command="fund" v-if="[1,2,3].includes(scope.row.status)">更新经费</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['project:horizontal:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改横向项目对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="projectRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目编号" prop="projectNo">
              <el-input v-model="form.projectNo" placeholder="请输入项目编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="form.projectName" placeholder="请输入项目名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目类型" prop="projectType">
              <el-select v-model="form.projectType" placeholder="请选择项目类型">
                <el-option label="技术开发" value="技术开发" />
                <el-option label="技术服务" value="技术服务" />
                <el-option label="技术咨询" value="技术咨询" />
                <el-option label="技术转让" value="技术转让" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学科分类" prop="subjectCategory">
              <el-input v-model="form.subjectCategory" placeholder="请输入学科分类" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目负责人" prop="principalName">
              <el-input v-model="form.principalName" placeholder="请输入项目负责人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属部门" prop="deptName">
              <el-input v-model="form.deptName" placeholder="请输入所属部门" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目开始时间" prop="startDate">
              <el-date-picker
                v-model="form.startDate"
                type="date"
                placeholder="选择开始时间"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目结束时间" prop="endDate">
              <el-date-picker
                v-model="form.endDate"
                type="date"
                placeholder="选择结束时间"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目总经费" prop="totalFund">
              <el-input-number v-model="form.totalFund" :min="0" :precision="2" placeholder="请输入项目总经费" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="已到账经费" prop="receivedFund">
              <el-input-number v-model="form.receivedFund" :min="0" :precision="2" placeholder="请输入已到账经费" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="关键词" prop="keywords">
          <el-input v-model="form.keywords" placeholder="请输入关键词，多个关键词用分号分隔" />
        </el-form-item>
        <el-form-item label="项目摘要" prop="projectSummary">
          <el-input v-model="form.projectSummary" type="textarea" :rows="3" placeholder="请输入项目摘要" />
        </el-form-item>
        <el-form-item label="研究内容" prop="researchContent">
          <el-input v-model="form.researchContent" type="textarea" :rows="4" placeholder="请输入研究内容" />
        </el-form-item>
        <el-form-item label="预期成果" prop="expectedResults">
          <el-input v-model="form.expectedResults" type="textarea" :rows="3" placeholder="请输入预期成果" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 项目详情对话框 -->
    <project-detail v-model="detailOpen" :project-id="currentProjectId" />

    <!-- 统计分析对话框 -->
    <project-statistics v-model="statisticsOpen" />
  </div>
</template>

<script setup name="HorizontalProject">
import { 
  listHorizontalProject, 
  getHorizontalProject, 
  delHorizontalProject, 
  delHorizontalProjects,
  addHorizontalProject, 
  updateHorizontalProject,
  exportHorizontalProject,
  approveProject,
  executeProject,
  suspendProject,
  resumeProject,
  completeProject,
  cancelProject,
  updateProjectFund,
  generateProjectNo
} from "@/api/project/horizontal"
import ProjectDetail from './components/ProjectDetail.vue'
import ProjectStatistics from './components/ProjectStatistics.vue'

const { proxy } = getCurrentInstance()

const projectList = ref([])
const open = ref(false)
const detailOpen = ref(false)
const statisticsOpen = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const currentProjectId = ref(null)

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectNo: null,
    projectName: null,
    projectType: null,
    status: null,
    principalName: null,
    deptName: null
  },
  rules: {
    projectNo: [
      { required: true, message: "项目编号不能为空", trigger: "blur" }
    ],
    projectName: [
      { required: true, message: "项目名称不能为空", trigger: "blur" }
    ],
    projectType: [
      { required: true, message: "项目类型不能为空", trigger: "change" }
    ],
    principalName: [
      { required: true, message: "项目负责人不能为空", trigger: "blur" }
    ],
    deptName: [
      { required: true, message: "所属部门不能为空", trigger: "blur" }
    ],
    startDate: [
      { required: true, message: "项目开始时间不能为空", trigger: "blur" }
    ],
    endDate: [
      { required: true, message: "项目结束时间不能为空", trigger: "blur" }
    ],
    totalFund: [
      { required: true, message: "项目总经费不能为空", trigger: "blur" }
    ]
  }
})

const { queryParams, form, rules } = toRefs(data)

// 状态选项
const statusOptions = ref([
  { label: "申请中", value: "0" },
  { label: "立项", value: "1" },
  { label: "执行中", value: "2" },
  { label: "变更中", value: "3" },
  { label: "结项中", value: "4" },
  { label: "已结项", value: "5" },
  { label: "已撤销", value: "6" }
])

/** 查询横向项目列表 */
function getList() {
  loading.value = true
  listHorizontalProject(queryParams.value).then(response => {
    projectList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    projectNo: null,
    projectName: null,
    projectType: null,
    subjectCategory: null,
    principalName: null,
    deptName: null,
    startDate: null,
    endDate: null,
    totalFund: null,
    receivedFund: 0,
    keywords: null,
    projectSummary: null,
    researchContent: null,
    expectedResults: null,
    remark: null
  }
  proxy.resetForm("projectRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
async function handleAdd() {
  reset()
  // 生成项目编号
  try {
    const response = await generateProjectNo()
    form.value.projectNo = response.data
  } catch (error) {
    console.error('生成项目编号失败:', error)
  }
  open.value = true
  title.value = "添加横向项目"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value
  getHorizontalProject(id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改横向项目"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["projectRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateHorizontalProject(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addHorizontalProject(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const projectIds = row.id || ids.value
  proxy.$modal.confirm('是否确认删除横向项目编号为"' + projectIds + '"的数据项？').then(function() {
    return delHorizontalProjects(projectIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('project/horizontal/export', {
    ...queryParams.value
  }, `horizontal_project_${new Date().getTime()}.xlsx`)
}

/** 详情按钮操作 */
function handleDetail(row) {
  currentProjectId.value = row.id
  detailOpen.value = true
}

/** 统计按钮操作 */
function handleStatistics() {
  statisticsOpen.value = true
}

/** 更多操作命令处理 */
function handleCommand(command, row) {
  switch (command) {
    case 'approve':
      handleApprove(row)
      break
    case 'execute':
      handleExecute(row)
      break
    case 'suspend':
      handleSuspend(row)
      break
    case 'resume':
      handleResume(row)
      break
    case 'complete':
      handleComplete(row)
      break
    case 'cancel':
      handleCancel(row)
      break
    case 'fund':
      handleUpdateFund(row)
      break
  }
}

/** 项目立项 */
function handleApprove(row) {
  proxy.$modal.confirm('确认立项该项目？').then(() => {
    return approveProject(row.id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("立项成功")
  })
}

/** 项目执行 */
function handleExecute(row) {
  proxy.$modal.confirm('确认执行该项目？').then(() => {
    return executeProject(row.id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("项目已开始执行")
  })
}

/** 项目挂起 */
function handleSuspend(row) {
  proxy.$prompt('请输入挂起原因', '项目挂起', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(({ value }) => {
    return suspendProject(row.id, value)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("项目已挂起")
  })
}

/** 项目恢复 */
function handleResume(row) {
  proxy.$modal.confirm('确认恢复该项目？').then(() => {
    return resumeProject(row.id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("项目已恢复")
  })
}

/** 项目结项 */
function handleComplete(row) {
  proxy.$modal.confirm('确认结项该项目？').then(() => {
    return completeProject(row.id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("项目已结项")
  })
}

/** 项目撤销 */
function handleCancel(row) {
  proxy.$prompt('请输入撤销原因', '项目撤销', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(({ value }) => {
    return cancelProject(row.id, value)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("项目已撤销")
  })
}

/** 更新项目经费 */
function handleUpdateFund(row) {
  proxy.$prompt('请输入已到账经费', '更新经费', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^\d+(\.\d{1,2})?$/,
    inputErrorMessage: '请输入正确的金额格式'
  }).then(({ value }) => {
    return updateProjectFund(row.id, parseFloat(value))
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("经费更新成功")
  })
}

// 格式化金额
function formatMoney(money) {
  if (!money) return '0.00'
  return parseFloat(money).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 获取经费到账率类型
function getFundRateType(rate) {
  if (!rate) return 'info'
  if (rate >= 80) return 'success'
  if (rate >= 50) return 'warning'
  return 'danger'
}

// 获取进度颜色
function getProgressColor(percentage) {
  if (!percentage) return '#909399'
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 50) return '#e6a23c'
  return '#f56c6c'
}

// 获取剩余天数类型
function getRemainingDaysType(days) {
  if (days < 0) return 'danger'
  if (days <= 7) return 'danger'
  if (days <= 30) return 'warning'
  return 'success'
}

onMounted(() => {
  getList()
})
</script>
