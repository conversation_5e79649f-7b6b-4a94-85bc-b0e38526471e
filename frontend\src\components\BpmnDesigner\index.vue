<template>
  <div class="bpmn-designer">
    <div class="designer-toolbar">
      <el-button-group>
        <el-button type="primary" icon="FolderOpened" @click="openFile">打开文件</el-button>
        <el-button type="success" icon="Download" @click="downloadXml">下载XML</el-button>
        <el-button type="info" icon="Picture" @click="downloadSvg">下载SVG</el-button>
        <el-button type="warning" icon="Refresh" @click="resetDiagram">重置</el-button>
      </el-button-group>
      
      <div class="toolbar-right">
        <el-button type="primary" @click="saveDiagram" :loading="saving">
          <el-icon><Check /></el-icon>
          保存
        </el-button>
      </div>
    </div>

    <div class="designer-container">
      <div class="designer-canvas" ref="canvasRef"></div>
      <div class="designer-panel" ref="panelRef"></div>
    </div>

    <!-- 文件上传对话框 -->
    <el-dialog title="打开BPMN文件" v-model="uploadDialog.visible" width="500px">
      <el-upload
        ref="uploadRef"
        :auto-upload="false"
        :show-file-list="false"
        accept=".bpmn,.xml"
        :on-change="handleFileChange"
      >
        <template #trigger>
          <el-button type="primary">选择文件</el-button>
        </template>
        <template #tip>
          <div class="el-upload__tip">
            只能上传 .bpmn 或 .xml 格式的文件
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="uploadDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="loadFile">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, defineEmits, defineProps } from 'vue'
import BpmnModeler from 'bpmn-js/lib/Modeler'
// import propertiesPanelModule from 'bpmn-js-properties-panel'
// import propertiesProviderModule from 'bpmn-js-properties-panel/lib/provider/camunda'
// import camundaModdleDescriptor from 'camunda-bpmn-moddle/resources/camunda.json'
import { ElMessage } from 'element-plus'

// 引入BPMN.js样式
import 'bpmn-js/dist/assets/diagram-js.css'
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css'
// import 'bpmn-js-properties-panel/dist/assets/properties-panel.css'

interface Props {
  modelValue?: string
  readonly?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'save', xml: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  readonly: false
})

const emit = defineEmits<Emits>()

const canvasRef = ref<HTMLElement>()
const panelRef = ref<HTMLElement>()
const uploadRef = ref()
const saving = ref(false)
const uploadDialog = ref({
  visible: false,
  file: null as File | null
})

let bpmnModeler: BpmnModeler | null = null

// 默认的BPMN XML模板
const defaultXml = `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" 
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI" 
                  id="Definitions_1" 
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1"/>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="79" width="36" height="36"/>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`

/** 初始化BPMN设计器 */
const initBpmnModeler = () => {
  if (!canvasRef.value || !panelRef.value) return

  bpmnModeler = new BpmnModeler({
    container: canvasRef.value,
    propertiesPanel: {
      parent: panelRef.value
    },
    // additionalModules: [
    //   propertiesPanelModule
    //   // propertiesProviderModule
    // ],
    // moddleExtensions: {
    //   camunda: camundaModdleDescriptor
    // },
    keyboard: {
      bindTo: document
    }
  })

  // 加载初始XML
  const initialXml = props.modelValue || defaultXml
  loadXml(initialXml)

  // 监听变化事件
  bpmnModeler.on('commandStack.changed', () => {
    if (!props.readonly) {
      saveToModel()
    }
  })
}

/** 加载XML到设计器 */
const loadXml = async (xml: string) => {
  if (!bpmnModeler) return

  try {
    await bpmnModeler.importXML(xml)
    
    // 自适应画布大小
    const canvas = bpmnModeler.get('canvas')
    canvas.zoom('fit-viewport')
    
    ElMessage.success('流程图加载成功')
  } catch (error) {
    console.error('加载XML失败:', error)
    ElMessage.error('流程图加载失败')
  }
}

/** 保存到模型 */
const saveToModel = async () => {
  if (!bpmnModeler) return

  try {
    const { xml } = await bpmnModeler.saveXML({ format: true })
    emit('update:modelValue', xml)
  } catch (error) {
    console.error('保存XML失败:', error)
  }
}

/** 打开文件 */
const openFile = () => {
  uploadDialog.value.visible = true
}

/** 处理文件选择 */
const handleFileChange = (file: any) => {
  uploadDialog.value.file = file.raw
}

/** 加载文件 */
const loadFile = () => {
  const file = uploadDialog.value.file
  if (!file) {
    ElMessage.warning('请选择文件')
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    const xml = e.target?.result as string
    loadXml(xml)
    uploadDialog.value.visible = false
    uploadDialog.value.file = null
  }
  reader.readAsText(file)
}

/** 下载XML */
const downloadXml = async () => {
  if (!bpmnModeler) return

  try {
    const { xml } = await bpmnModeler.saveXML({ format: true })
    downloadFile(xml, 'process.bpmn', 'application/xml')
    ElMessage.success('XML下载成功')
  } catch (error) {
    console.error('下载XML失败:', error)
    ElMessage.error('XML下载失败')
  }
}

/** 下载SVG */
const downloadSvg = async () => {
  if (!bpmnModeler) return

  try {
    const { svg } = await bpmnModeler.saveSVG()
    downloadFile(svg, 'process.svg', 'image/svg+xml')
    ElMessage.success('SVG下载成功')
  } catch (error) {
    console.error('下载SVG失败:', error)
    ElMessage.error('SVG下载失败')
  }
}

/** 重置流程图 */
const resetDiagram = () => {
  loadXml(defaultXml)
  ElMessage.success('流程图已重置')
}

/** 保存流程图 */
const saveDiagram = async () => {
  if (!bpmnModeler) return

  saving.value = true
  try {
    const { xml } = await bpmnModeler.saveXML({ format: true })
    emit('save', xml)
    ElMessage.success('保存成功')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

/** 下载文件工具函数 */
const downloadFile = (content: string, filename: string, mimeType: string) => {
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

/** 设置只读模式 */
const setReadonly = (readonly: boolean) => {
  if (!bpmnModeler) return

  const modeling = bpmnModeler.get('modeling')
  const eventBus = bpmnModeler.get('eventBus')
  
  if (readonly) {
    // 禁用编辑功能
    eventBus.on('element.click', 1500, (event: any) => {
      event.stopPropagation()
    })
  }
}

// 监听props变化
watch(() => props.modelValue, (newXml) => {
  if (newXml && bpmnModeler) {
    loadXml(newXml)
  }
})

watch(() => props.readonly, (readonly) => {
  setReadonly(readonly)
})

onMounted(() => {
  initBpmnModeler()
})

onUnmounted(() => {
  if (bpmnModeler) {
    bpmnModeler.destroy()
  }
})

// 暴露方法给父组件
defineExpose({
  loadXml,
  downloadXml,
  downloadSvg,
  resetDiagram,
  saveDiagram
})
</script>

<style scoped>
.bpmn-designer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.designer-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #e4e7ed;
  background: #f5f7fa;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.designer-container {
  flex: 1;
  display: flex;
  position: relative;
}

.designer-canvas {
  flex: 1;
  height: 100%;
  background: #ffffff;
}

.designer-panel {
  width: 300px;
  height: 100%;
  border-left: 1px solid #e4e7ed;
  background: #fafafa;
  overflow: auto;
}

/* BPMN.js样式覆盖 */
:deep(.djs-palette) {
  left: 20px;
  top: 20px;
}

:deep(.bpp-properties-panel) {
  background: #fafafa;
}

:deep(.bpp-properties-panel .bpp-properties-header) {
  background: #f0f2f5;
  border-bottom: 1px solid #e4e7ed;
}
</style>
