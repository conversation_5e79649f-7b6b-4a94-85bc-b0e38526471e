# 科研管理系统 - 前端项目

基于 Vue 3 + TypeScript + Element Plus 的现代化前端项目。

## 技术栈

- **框架**: Vue 3.3+
- **语言**: TypeScript
- **构建工具**: Vite 4.5+
- **UI框架**: Element Plus 2.3+
- **状态管理**: Pinia 2.1+
- **路由**: Vue Router 4.2+
- **HTTP客户端**: Axios 1.4+
- **样式**: SCSS
- **代码规范**: ESLint + Prettier

## 项目结构

```
src/
├── api/                    # API接口
│   └── login.ts           # 登录相关接口
├── assets/                # 静态资源
│   └── images/            # 图片资源
├── components/            # 公共组件
│   ├── Breadcrumb/        # 面包屑导航
│   ├── Hamburger/         # 汉堡菜单
│   ├── HeaderSearch/      # 头部搜索
│   ├── RightPanel/        # 右侧面板
│   ├── Screenfull/        # 全屏组件
│   ├── SizeSelect/        # 尺寸选择
│   └── SvgIcon/           # SVG图标
├── layout/                # 布局组件
│   ├── components/        # 布局子组件
│   │   ├── AppMain.vue    # 主内容区
│   │   ├── Navbar.vue     # 顶部导航
│   │   ├── Settings.vue   # 设置面板
│   │   ├── Sidebar/       # 侧边栏
│   │   └── TagsView/      # 标签页
│   └── index.vue          # 主布局
├── router/                # 路由配置
│   └── index.ts           # 路由定义
├── store/                 # 状态管理
│   ├── modules/           # 状态模块
│   │   ├── app.ts         # 应用状态
│   │   ├── settings.ts    # 设置状态
│   │   └── user.ts        # 用户状态
│   └── index.ts           # 状态入口
├── styles/                # 样式文件
│   ├── element-ui.scss    # Element Plus样式覆盖
│   ├── index.scss         # 全局样式
│   ├── mixin.scss         # 样式混入
│   ├── sidebar.scss       # 侧边栏样式
│   └── variables.scss     # 样式变量
├── utils/                 # 工具函数
│   ├── auth.ts            # 认证工具
│   ├── index.ts           # 通用工具
│   ├── jsencrypt.ts       # 加密工具
│   ├── request.ts         # HTTP请求封装
│   └── validate.ts        # 验证工具
├── views/                 # 页面视图
│   ├── dashboard/         # 首页
│   ├── error/             # 错误页面
│   ├── login/             # 登录页面
│   └── redirect/          # 重定向页面
├── App.vue                # 根组件
├── main.ts                # 入口文件
└── permission.ts          # 权限控制
```

## 开发指南

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 代码检查

```bash
npm run lint
```

### 类型检查

```bash
npm run type-check
```

## 功能特性

### 已实现功能

- ✅ 项目基础架构搭建
- ✅ Vue 3 + TypeScript 配置
- ✅ Element Plus UI框架集成
- ✅ Vite 构建工具配置
- ✅ 路由配置和权限控制
- ✅ Pinia 状态管理
- ✅ Axios HTTP请求封装
- ✅ 登录页面和认证流程
- ✅ 主布局和导航组件
- ✅ 响应式设计支持
- ✅ 全局样式和主题配置

### 待实现功能

- ⏳ 用户管理页面
- ⏳ 角色权限管理
- ⏳ 部门管理
- ⏳ 菜单管理
- ⏳ 系统设置
- ⏳ 数据统计图表
- ⏳ 文件上传组件
- ⏳ 表格组件封装
- ⏳ 表单组件封装

## 环境配置

### 开发环境

- Node.js >= 16.0.0
- npm >= 7.0.0

### 环境变量

项目支持多环境配置：

- `.env.development` - 开发环境
- `.env.production` - 生产环境

### 代理配置

开发环境下，API请求会代理到后端服务：

```typescript
proxy: {
  '/dev-api': {
    target: 'http://localhost:8080',
    changeOrigin: true,
    rewrite: (p) => p.replace(/^\/dev-api/, '')
  }
}
```

## 注意事项

1. 确保后端服务已启动并运行在 `http://localhost:8080`
2. 登录默认账号：`admin` / `admin123`
3. 项目使用 TypeScript，请注意类型定义
4. 遵循 ESLint 和 Prettier 代码规范
5. 组件开发请遵循 Vue 3 Composition API 规范

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request

## 许可证

MIT License
