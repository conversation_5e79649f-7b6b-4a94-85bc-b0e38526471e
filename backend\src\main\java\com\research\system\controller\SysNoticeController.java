package com.research.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.research.common.core.controller.BaseController;
import com.research.common.core.domain.AjaxResult;
import com.research.common.core.page.TableDataInfo;
import com.research.common.utils.SecurityUtils;
import com.research.common.utils.PageUtils;
import com.research.common.utils.MybatisPlusPageUtils;
import com.research.system.domain.SysNotice;
import com.research.system.service.ISysNoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 通知公告控制器
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@RestController
@RequestMapping("/system/notice")
public class SysNoticeController extends BaseController {

    @Autowired
    private ISysNoticeService noticeService;

    /**
     * 查询通知公告列表
     */
    @PreAuthorize("@ss.hasPermi('system:notice:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysNotice notice) {
        try {
            Page<SysNotice> page = MybatisPlusPageUtils.createSafePage();
            Long userId = SecurityUtils.getUserId();
            IPage<SysNotice> result = noticeService.selectNoticeList(page, notice, userId);
            return MybatisPlusPageUtils.convertToTableDataInfo(result);
        } catch (Exception e) {
            System.err.println("查询通知公告列表失败: " + e.getMessage());
            return MybatisPlusPageUtils.createEmptyTableDataInfo();
        }
    }

    /**
     * 查询用户可见的通知公告列表
     */
    @GetMapping("/userList")
    public TableDataInfo userList(SysNotice notice) {
        try {
            Page<SysNotice> page = MybatisPlusPageUtils.createSafePage();
            Long userId = SecurityUtils.getUserId();
            IPage<SysNotice> result = noticeService.selectUserNoticeList(page, notice, userId);
            return MybatisPlusPageUtils.convertToTableDataInfo(result);
        } catch (Exception e) {
            System.err.println("查询用户通知公告列表失败: " + e.getMessage());
            return MybatisPlusPageUtils.createEmptyTableDataInfo();
        }
    }

    /**
     * 根据通知公告编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:notice:query')")
    @GetMapping(value = "/{noticeId}")
    public AjaxResult getInfo(@PathVariable Long noticeId) {
        Long userId = SecurityUtils.getUserId();
        SysNotice notice = noticeService.selectNoticeDetail(noticeId, userId);
        return success(notice);
    }

    /**
     * 查看通知公告详情（用户端）
     */
    @GetMapping(value = "/view/{noticeId}")
    public AjaxResult viewNotice(@PathVariable Long noticeId,
                               @RequestParam(required = false) Integer readDuration) {
        Long userId = SecurityUtils.getUserId();
        SysNotice notice = noticeService.selectNoticeDetail(noticeId, userId);
        
        if (notice != null) {
            // 记录阅读
            noticeService.recordNoticeRead(noticeId, userId, readDuration);
        }
        
        return success(notice);
    }

    /**
     * 新增通知公告
     */
    @PreAuthorize("@ss.hasPermi('system:notice:add')")
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysNotice notice) {
        return toAjax(noticeService.insertNotice(notice));
    }

    /**
     * 修改通知公告
     */
    @PreAuthorize("@ss.hasPermi('system:notice:edit')")
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysNotice notice) {
        return toAjax(noticeService.updateNotice(notice));
    }

    /**
     * 删除通知公告
     */
    @PreAuthorize("@ss.hasPermi('system:notice:remove')")
    @DeleteMapping("/{noticeIds}")
    public AjaxResult remove(@PathVariable Long[] noticeIds) {
        return toAjax(noticeService.deleteNoticeByIds(noticeIds));
    }

    /**
     * 发布通知公告
     */
    @PreAuthorize("@ss.hasPermi('system:notice:publish')")
    @PostMapping("/publish/{noticeId}")
    public AjaxResult publish(@PathVariable Long noticeId) {
        String publishBy = SecurityUtils.getUsername();
        return toAjax(noticeService.publishNotice(noticeId, publishBy));
    }

    /**
     * 撤回通知公告
     */
    @PreAuthorize("@ss.hasPermi('system:notice:withdraw')")
    @PostMapping("/withdraw/{noticeId}")
    public AjaxResult withdraw(@PathVariable Long noticeId) {
        return toAjax(noticeService.withdrawNotice(noticeId));
    }

    /**
     * 置顶/取消置顶通知公告
     */
    @PreAuthorize("@ss.hasPermi('system:notice:top')")
    @PostMapping("/top/{noticeId}")
    public AjaxResult setTop(@PathVariable Long noticeId, @RequestParam String isTop) {
        return toAjax(noticeService.setNoticeTop(noticeId, isTop));
    }

    /**
     * 查询最新公告列表（用于工作台展示）
     */
    @GetMapping("/latest")
    public AjaxResult getLatestNotices(@RequestParam(defaultValue = "5") Integer limit) {
        Long userId = SecurityUtils.getUserId();
        List<SysNotice> notices = noticeService.selectLatestNotices(userId, limit);
        return success(notices);
    }

    /**
     * 查询用户未读公告数量
     */
    @GetMapping("/unreadCount")
    public AjaxResult getUnreadCount() {
        Long userId = SecurityUtils.getUserId();
        Long count = noticeService.selectUnreadNoticeCount(userId);
        return success(count);
    }

    /**
     * 查询公告阅读统计
     */
    @PreAuthorize("@ss.hasPermi('system:notice:stats')")
    @GetMapping("/readStats/{noticeId}")
    public AjaxResult getReadStats(@PathVariable Long noticeId) {
        Map<String, Object> stats = noticeService.selectNoticeReadStats(noticeId);
        return success(stats);
    }

    /**
     * 全文搜索公告
     */
    @GetMapping("/search")
    public TableDataInfo searchNotices(@RequestParam String keyword) {
        startPage();
        Page<SysNotice> page = new Page<>(PageUtils.getPageNum(), PageUtils.getPageSize());
        Long userId = SecurityUtils.getUserId();
        IPage<SysNotice> list = noticeService.searchNotices(page, keyword, userId);
        return getDataTable(list);
    }

    /**
     * 导出通知公告数据
     */
    @PreAuthorize("@ss.hasPermi('system:notice:export')")
    @PostMapping("/export")
    public void export(SysNotice notice) {
        List<SysNotice> list = noticeService.exportNoticeList(notice);
        // TODO: 实现导出功能
    }

    /**
     * 批量导入通知公告
     */
    @PreAuthorize("@ss.hasPermi('system:notice:import')")
    @PostMapping("/importData")
    public AjaxResult importData(@RequestBody List<SysNotice> noticeList, 
                               @RequestParam(defaultValue = "false") Boolean isUpdateSupport) {
        String operName = SecurityUtils.getUsername();
        String message = noticeService.importNotice(noticeList, isUpdateSupport, operName);
        return success(message);
    }
}
