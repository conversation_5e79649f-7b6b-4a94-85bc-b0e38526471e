package com.research.common.utils.redis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Redis分布式锁工具类
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnBean(RedisTemplate.class)
public class RedisLock {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String LOCK_PREFIX = "redis_lock:";
    private static final String UNLOCK_LUA_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "return redis.call('del', KEYS[1]) " +
        "else " +
        "return 0 " +
        "end";

    /**
     * 尝试获取分布式锁
     *
     * @param lockKey 锁的key
     * @param requestId 请求标识
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     * @return 是否获取成功
     */
    public boolean tryLock(String lockKey, String requestId, long expireTime, TimeUnit timeUnit) {
        String key = LOCK_PREFIX + lockKey;
        Boolean result = redisTemplate.opsForValue().setIfAbsent(key, requestId, expireTime, timeUnit);
        return result != null && result;
    }

    /**
     * 尝试获取分布式锁（默认30秒过期）
     *
     * @param lockKey 锁的key
     * @param requestId 请求标识
     * @return 是否获取成功
     */
    public boolean tryLock(String lockKey, String requestId) {
        return tryLock(lockKey, requestId, 30, TimeUnit.SECONDS);
    }

    /**
     * 尝试获取分布式锁（自动生成requestId）
     *
     * @param lockKey 锁的key
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     * @return 请求标识（用于释放锁）
     */
    public String tryLock(String lockKey, long expireTime, TimeUnit timeUnit) {
        String requestId = UUID.randomUUID().toString();
        boolean success = tryLock(lockKey, requestId, expireTime, timeUnit);
        return success ? requestId : null;
    }

    /**
     * 尝试获取分布式锁（自动生成requestId，默认30秒过期）
     *
     * @param lockKey 锁的key
     * @return 请求标识（用于释放锁）
     */
    public String tryLock(String lockKey) {
        return tryLock(lockKey, 30, TimeUnit.SECONDS);
    }

    /**
     * 释放分布式锁
     *
     * @param lockKey 锁的key
     * @param requestId 请求标识
     * @return 是否释放成功
     */
    public boolean releaseLock(String lockKey, String requestId) {
        String key = LOCK_PREFIX + lockKey;
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptText(UNLOCK_LUA_SCRIPT);
        redisScript.setResultType(Long.class);
        
        Long result = redisTemplate.execute(redisScript, Collections.singletonList(key), requestId);
        return result != null && result == 1L;
    }

    /**
     * 检查锁是否存在
     *
     * @param lockKey 锁的key
     * @return 是否存在
     */
    public boolean isLocked(String lockKey) {
        String key = LOCK_PREFIX + lockKey;
        return redisTemplate.hasKey(key);
    }

    /**
     * 获取锁的剩余时间
     *
     * @param lockKey 锁的key
     * @return 剩余时间（秒）
     */
    public long getLockExpire(String lockKey) {
        String key = LOCK_PREFIX + lockKey;
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 强制释放锁（慎用）
     *
     * @param lockKey 锁的key
     * @return 是否释放成功
     */
    public boolean forceReleaseLock(String lockKey) {
        String key = LOCK_PREFIX + lockKey;
        return redisTemplate.delete(key);
    }
}
