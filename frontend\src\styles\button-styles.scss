/* ===== 全局按钮样式 ===== */

/* 顶部操作按钮样式 */
.top-action-btn,
.action-btn {
  margin-right: 8px !important;
  padding: 8px 16px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  border-radius: 4px !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 4px !important;
  white-space: nowrap !important;
  
  &:last-child {
    margin-right: 0 !important;
  }
  
  /* 确保文字显示 */
  span {
    display: inline !important;
    font-size: inherit !important;
    color: inherit !important;
    font-weight: inherit !important;
  }
}

/* 表格操作列按钮容器 */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
  justify-content: flex-start;
}

/* 表格操作按钮基础样式 */
.table-action-btn {
  margin: 0 !important;
  padding: 4px 8px !important;
  font-size: 12px !important;
  border-radius: 3px !important;
  min-width: 60px !important;
  white-space: nowrap !important;
  font-weight: 500 !important;
  text-align: center !important;
  color: #fff !important;
  
  /* 确保文字显示 */
  span {
    display: inline !important;
    font-size: inherit !important;
    color: inherit !important;
    font-weight: inherit !important;
  }
}

/* 表格操作按钮颜色样式 */
.table-action-btn.el-button--primary {
  background-color: var(--el-color-primary) !important;
  border-color: var(--el-color-primary) !important;
}

.table-action-btn.el-button--success {
  background-color: var(--el-color-success) !important;
  border-color: var(--el-color-success) !important;
}

.table-action-btn.el-button--warning {
  background-color: var(--el-color-warning) !important;
  border-color: var(--el-color-warning) !important;
}

.table-action-btn.el-button--info {
  background-color: var(--el-color-info) !important;
  border-color: var(--el-color-info) !important;
}

.table-action-btn.el-button--danger {
  background-color: var(--el-color-danger) !important;
  border-color: var(--el-color-danger) !important;
}

/* 链接按钮样式修复 */
.el-button--text,
.el-button.is-link {
  padding: 4px 8px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  
  span {
    display: inline !important;
    color: inherit !important;
  }
}

/* 搜索表单按钮 */
.search-btn {
  margin-left: 8px !important;
  padding: 8px 16px !important;
  font-size: 14px !important;
  
  &.el-button--primary {
    background-color: var(--el-color-primary) !important;
    border-color: var(--el-color-primary) !important;
  }
}

/* 对话框按钮 */
.dialog-btn {
  padding: 8px 20px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  margin-left: 8px !important;
  
  &:first-child {
    margin-left: 0 !important;
  }
}

/* 按钮组样式 */
.el-button-group {
  .el-button {
    margin: 0 !important;
    
    &:not(:first-child) {
      margin-left: -1px !important;
    }
  }
}

/* 图标按钮 */
.icon-btn {
  padding: 8px !important;
  width: 32px !important;
  height: 32px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  
  .el-icon {
    font-size: 16px !important;
  }
}

/* 小尺寸按钮 */
.el-button--small {
  padding: 6px 12px !important;
  font-size: 12px !important;
  
  &.table-action-btn {
    padding: 3px 6px !important;
    min-width: 50px !important;
  }
}

/* 大尺寸按钮 */
.el-button--large {
  padding: 10px 20px !important;
  font-size: 16px !important;
}

/* 禁用状态 */
.el-button.is-disabled,
.el-button:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

/* 加载状态 */
.el-button.is-loading {
  pointer-events: none !important;
  
  .el-icon {
    animation: rotating 2s linear infinite;
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式按钮样式 */
@media (max-width: 1200px) {
  .top-action-btn,
  .action-btn {
    padding: 6px 12px !important;
    font-size: 13px !important;
  }
  
  .table-action-btn {
    padding: 3px 6px !important;
    font-size: 11px !important;
    min-width: 50px !important;
  }
}

@media (max-width: 768px) {
  .action-buttons {
    gap: 4px;
    flex-direction: column;
    align-items: stretch;
  }
  
  .table-action-btn {
    margin-bottom: 2px !important;
    width: 100% !important;
    min-width: auto !important;
  }
  
  .top-action-btn,
  .action-btn {
    width: 100% !important;
    justify-content: center !important;
    margin-right: 0 !important;
    margin-bottom: 8px !important;
  }
}

/* 特殊场景按钮 */
.floating-btn {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 56px;
  height: 56px;
  border-radius: 50% !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  
  .el-icon {
    font-size: 24px !important;
  }
}

/* 工具栏按钮 */
.toolbar-btn {
  padding: 6px 12px !important;
  font-size: 13px !important;
  margin-right: 4px !important;
  
  &:last-child {
    margin-right: 0 !important;
  }
}
