package com.research.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.research.common.utils.SecurityUtils;
import com.research.system.domain.SysNotice;
import com.research.system.domain.SysNoticeRead;
import com.research.system.domain.SysUser;
import com.research.system.mapper.SysNoticeMapper;
import com.research.system.mapper.SysNoticeReadMapper;
import com.research.system.mapper.SysUserMapper;
import com.research.system.service.ISysNoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 通知公告Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Service
public class SysNoticeServiceImpl extends ServiceImpl<SysNoticeMapper, SysNotice> implements ISysNoticeService {

    @Autowired
    private SysNoticeMapper noticeMapper;

    @Autowired
    private SysNoticeReadMapper noticeReadMapper;

    @Autowired
    private SysUserMapper userMapper;

    /**
     * 查询通知公告列表
     */
    @Override
    public IPage<SysNotice> selectNoticeList(Page<SysNotice> page, SysNotice notice, Long userId) {
        try {
            // 使用QueryWrapper避免Java模块系统问题
            com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<SysNotice> queryWrapper =
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();

            // 默认只查询正常状态的公告，避免全表查询
            queryWrapper.eq("status", "0"); // 0-正常，1-关闭

            // 添加用户权限过滤（如果需要）
            if (userId != null) {
                // 可以根据用户权限添加更多过滤条件
                // 例如：只显示用户可见的公告
            }

            // 添加其他查询条件（如果有）
            if (notice != null) {
                if (notice.getNoticeTitle() != null && !notice.getNoticeTitle().trim().isEmpty()) {
                    queryWrapper.like("notice_title", notice.getNoticeTitle().trim());
                }
                if (notice.getNoticeType() != null && !notice.getNoticeType().trim().isEmpty()) {
                    queryWrapper.eq("notice_type", notice.getNoticeType().trim());
                }
                if (notice.getStatus() != null && !notice.getStatus().trim().isEmpty()) {
                    // 如果指定了状态，则覆盖默认的状态条件
                    queryWrapper.and(wrapper -> wrapper.eq("status", notice.getStatus().trim()));
                }
                if (notice.getNoticeType() != null && !notice.getNoticeType().trim().isEmpty()) {
                    queryWrapper.eq("notice_type", notice.getNoticeType().trim());
                }
                if (notice.getImportance() != null && !notice.getImportance().trim().isEmpty()) {
                    queryWrapper.eq("importance", notice.getImportance().trim());
                }
                if (notice.getIsTop() != null && !notice.getIsTop().trim().isEmpty()) {
                    queryWrapper.eq("is_top", notice.getIsTop().trim());
                }
                // 创建时间范围查询
                if (notice.getCreateTime() != null) {
                    queryWrapper.ge("create_time", notice.getCreateTime());
                }
            }

            // 安全检查：限制分页大小
            if (page.getSize() > 100) {
                page.setSize(100); // 最大每页100条
            }
            if (page.getSize() <= 0) {
                page.setSize(10); // 默认每页10条
            }

            // 多字段排序：置顶优先，重要程度优先，创建时间倒序
            queryWrapper.orderByDesc("is_top", "importance", "create_time");

            // 直接使用分页查询，让MyBatis-Plus自动处理COUNT
            IPage<SysNotice> result = this.page(page, queryWrapper);

            return result;
        } catch (Exception e) {
            // 如果还是有问题，返回空的分页结果
            System.err.println("查询通知公告列表出错: " + e.getMessage());
            e.printStackTrace();

            // 创建空的分页结果
            IPage<SysNotice> emptyPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(page.getCurrent(), page.getSize());
            emptyPage.setRecords(new java.util.ArrayList<>());
            emptyPage.setTotal(0);
            return emptyPage;
        }
    }

    /**
     * 查询用户可见的通知公告列表
     */
    @Override
    public IPage<SysNotice> selectUserNoticeList(Page<SysNotice> page, SysNotice notice, Long userId) {
        SysUser user = userMapper.selectById(userId);
        Long deptId = user != null ? user.getDeptId() : null;
        return noticeMapper.selectUserNoticeList(page, notice, userId, deptId);
    }

    /**
     * 查询通知公告详情
     */
    @Override
    public SysNotice selectNoticeDetail(Long noticeId, Long userId) {
        SysNotice notice = noticeMapper.selectNoticeDetail(noticeId, userId);
        if (notice != null) {
            // 增加阅读次数
            noticeMapper.incrementReadCount(noticeId);
        }
        return notice;
    }

    /**
     * 新增通知公告
     */
    @Override
    @Transactional
    public boolean insertNotice(SysNotice notice) {
        notice.setCreateBy(SecurityUtils.getUsername());
        notice.setCreateTime(LocalDateTime.now());
        return save(notice);
    }

    /**
     * 修改通知公告
     */
    @Override
    @Transactional
    public boolean updateNotice(SysNotice notice) {
        notice.setUpdateBy(SecurityUtils.getUsername());
        notice.setUpdateTime(LocalDateTime.now());
        return updateById(notice);
    }

    /**
     * 删除通知公告
     */
    @Override
    @Transactional
    public boolean deleteNoticeByIds(Long[] noticeIds) {
        for (Long noticeId : noticeIds) {
            // 删除公告阅读记录
            noticeReadMapper.deleteByNoticeId(noticeId);
        }
        return removeByIds(Arrays.asList(noticeIds));
    }

    /**
     * 发布通知公告
     */
    @Override
    @Transactional
    public boolean publishNotice(Long noticeId, String publishBy) {
        SysNotice notice = new SysNotice();
        notice.setNoticeId(noticeId);
        notice.setStatus("0"); // 正常状态
        notice.setPublishBy(publishBy);
        notice.setPublishTime(LocalDateTime.now());
        notice.setUpdateBy(SecurityUtils.getUsername());
        notice.setUpdateTime(LocalDateTime.now());
        return updateById(notice);
    }

    /**
     * 撤回通知公告
     */
    @Override
    @Transactional
    public boolean withdrawNotice(Long noticeId) {
        SysNotice notice = new SysNotice();
        notice.setNoticeId(noticeId);
        notice.setStatus("1"); // 关闭状态
        notice.setUpdateBy(SecurityUtils.getUsername());
        notice.setUpdateTime(LocalDateTime.now());
        return updateById(notice);
    }

    /**
     * 置顶/取消置顶通知公告
     */
    @Override
    @Transactional
    public boolean setNoticeTop(Long noticeId, String isTop) {
        SysNotice notice = new SysNotice();
        notice.setNoticeId(noticeId);
        notice.setIsTop(isTop);
        notice.setUpdateBy(SecurityUtils.getUsername());
        notice.setUpdateTime(LocalDateTime.now());
        return updateById(notice);
    }

    /**
     * 记录公告阅读
     */
    @Override
    @Transactional
    public boolean recordNoticeRead(Long noticeId, Long userId, Integer readDuration) {
        // 检查是否已经阅读过
        SysNoticeRead existRead = noticeReadMapper.selectByNoticeAndUser(noticeId, userId);
        if (existRead != null) {
            return true; // 已经阅读过，不重复记录
        }

        // 获取用户信息
        SysUser user = userMapper.selectById(userId);
        String userName = user != null ? user.getUserName() : "";

        // 创建阅读记录
        SysNoticeRead noticeRead = new SysNoticeRead();
        noticeRead.setNoticeId(noticeId);
        noticeRead.setUserId(userId);
        noticeRead.setUserName(userName);
        noticeRead.setReadTime(LocalDateTime.now());
        noticeRead.setReadDuration(readDuration != null ? readDuration : 0);

        return noticeReadMapper.insertNoticeRead(noticeRead) > 0;
    }

    /**
     * 查询最新公告列表（用于工作台展示）
     */
    @Override
    public List<SysNotice> selectLatestNotices(Long userId, Integer limit) {
        SysUser user = userMapper.selectById(userId);
        Long deptId = user != null ? user.getDeptId() : null;
        return noticeMapper.selectLatestNotices(userId, deptId, limit != null ? limit : 5);
    }

    /**
     * 查询用户未读公告数量
     */
    @Override
    public Long selectUnreadNoticeCount(Long userId) {
        SysUser user = userMapper.selectById(userId);
        Long deptId = user != null ? user.getDeptId() : null;
        return noticeMapper.selectUnreadNoticeCount(userId, deptId);
    }

    /**
     * 查询公告阅读统计
     */
    @Override
    public Map<String, Object> selectNoticeReadStats(Long noticeId) {
        return noticeReadMapper.selectReadStatsByNotice(noticeId);
    }

    /**
     * 全文搜索公告
     */
    @Override
    public IPage<SysNotice> searchNotices(Page<SysNotice> page, String keyword, Long userId) {
        SysUser user = userMapper.selectById(userId);
        Long deptId = user != null ? user.getDeptId() : null;
        return noticeMapper.searchNotices(page, keyword, userId, deptId);
    }

    /**
     * 导出公告数据
     */
    @Override
    public List<SysNotice> exportNoticeList(SysNotice notice) {
        // 这里可以根据需要实现导出逻辑
        return list();
    }

    /**
     * 批量导入公告
     */
    @Override
    @Transactional
    public String importNotice(List<SysNotice> noticeList, Boolean isUpdateSupport, String operName) {
        if (noticeList == null || noticeList.isEmpty()) {
            throw new RuntimeException("导入公告数据不能为空！");
        }
        
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        for (SysNotice notice : noticeList) {
            try {
                notice.setCreateBy(operName);
                notice.setCreateTime(LocalDateTime.now());
                save(notice);
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、公告 ").append(notice.getNoticeTitle()).append(" 导入成功");
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、公告 " + notice.getNoticeTitle() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
            }
        }
        
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        
        return successMsg.toString();
    }
}
