<template>
  <el-card class="notice-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span class="card-title">通知公告</span>
        <div class="header-actions">
          <el-badge :value="noticeStats.unreadCount" :hidden="!noticeStats.unreadCount" type="danger">
            <el-button type="text" @click="$emit('refresh')">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </el-badge>
          <el-button type="text" @click="goToNoticeList">
            <el-icon><More /></el-icon>
          </el-button>
        </div>
      </div>
    </template>
    
    <div class="notice-content">
      <!-- 未读统计 -->
      <div class="unread-stats" v-if="noticeStats.unreadCount > 0">
        <div class="unread-info">
          <el-icon class="bell-icon"><Bell /></el-icon>
          <span class="unread-text">您有 <strong>{{ noticeStats.unreadCount }}</strong> 条未读公告</span>
        </div>
        <el-button type="primary" size="small" @click="goToNoticeList">查看</el-button>
      </div>
      
      <!-- 最新公告 -->
      <div class="latest-notices" v-if="noticeStats.latestNotices && noticeStats.latestNotices.length > 0">
        <h4 class="section-title">最新公告</h4>
        <div class="notice-list">
          <div 
            v-for="notice in noticeStats.latestNotices" 
            :key="notice.noticeId"
            class="notice-item"
            :class="{ 'unread': !notice.isRead }"
            @click="viewNotice(notice.noticeId)"
          >
            <div class="notice-info">
              <div class="notice-title">
                <span v-if="notice.isTop === '1'" class="top-badge">置顶</span>
                <span v-if="notice.importance === '3'" class="urgent-badge">紧急</span>
                <span v-else-if="notice.importance === '2'" class="important-badge">重要</span>
                {{ notice.noticeTitle }}
              </div>
              <div class="notice-meta">
                <el-tag 
                  :type="notice.noticeType === '1' ? 'primary' : 'success'" 
                  size="small"
                >
                  {{ notice.noticeType === '1' ? '通知' : '公告' }}
                </el-tag>
                <span class="notice-time">{{ formatDate(notice.publishTime) }}</span>
                <span class="notice-author">{{ notice.publishBy }}</span>
              </div>
            </div>
            <div class="notice-status">
              <el-icon v-if="!notice.isRead" class="unread-dot"><CircleFilled /></el-icon>
              <el-icon class="arrow-icon"><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="!noticeStats.latestNotices || noticeStats.latestNotices.length === 0" class="empty-state">
        <el-icon class="empty-icon"><Bell /></el-icon>
        <p>暂无公告信息</p>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { Refresh, More, Bell, CircleFilled, ArrowRight } from '@element-plus/icons-vue'

// Props
interface Props {
  noticeStats: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits(['refresh'])

const router = useRouter()

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return ''
  const d = new Date(date)
  const now = new Date()
  const diff = now.getTime() - d.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  return d.toLocaleDateString('zh-CN')
}

// 查看公告详情
const viewNotice = (noticeId: number) => {
  router.push(`/system/notice/detail/${noticeId}`)
}

// 跳转到公告列表
const goToNoticeList = () => {
  router.push('/system/notice')
}
</script>

<style scoped>
.notice-card {
  height: 100%;
  min-height: 300px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 5px;
}

.notice-content {
  height: 100%;
}

.unread-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  margin-bottom: 15px;
}

.unread-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bell-icon {
  color: #409eff;
  font-size: 16px;
}

.unread-text {
  font-size: 14px;
  color: #303133;
}

.section-title {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.notice-list {
  max-height: 250px;
  overflow-y: auto;
}

.notice-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.3s;
}

.notice-item:hover {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding-left: 8px;
  padding-right: 8px;
}

.notice-item.unread {
  background-color: #fafcff;
}

.notice-item:last-child {
  border-bottom: none;
}

.notice-info {
  flex: 1;
}

.notice-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 6px;
}

.top-badge {
  background-color: #f56c6c;
  color: white;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
}

.urgent-badge {
  background-color: #f56c6c;
  color: white;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
}

.important-badge {
  background-color: #e6a23c;
  color: white;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
}

.notice-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.notice-time, .notice-author {
  font-size: 12px;
  color: #909399;
}

.notice-status {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-left: 10px;
}

.unread-dot {
  color: #f56c6c;
  font-size: 8px;
}

.arrow-icon {
  color: #c0c4cc;
  font-size: 12px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

@media (max-width: 768px) {
  .unread-stats {
    flex-direction: column;
    gap: 10px;
  }
  
  .notice-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .notice-status {
    margin-left: 0;
    margin-top: 5px;
    align-self: flex-end;
  }
  
  .notice-meta {
    flex-wrap: wrap;
  }
}
</style>
