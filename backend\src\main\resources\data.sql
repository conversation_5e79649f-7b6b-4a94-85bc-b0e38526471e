-- H2数据库初始化数据

-- 插入部门数据
INSERT INTO sys_dept (dept_id, dept_name, parent_id, ancestors, order_num, leader, phone, email, status, del_flag, create_by, create_time) VALUES
(100, '龙湖大学', 0, '0', 0, '校长', '15888888888', '<EMAIL>', '0', '0', 'admin', CURRENT_TIMESTAMP),
(101, '科研处', 100, '0,100', 1, '科研处长', '15888888889', '<EMAIL>', '0', '0', 'admin', CURRENT_TIMESTAMP),
(102, '教务处', 100, '0,100', 2, '教务处长', '15888888890', '<EMAIL>', '0', '0', 'admin', CURRENT_TIMESTAMP),
(103, '计算机学院', 100, '0,100', 3, '院长', '15888888891', '<EMAIL>', '0', '0', 'admin', CURRENT_TIMESTAMP),
(104, '数学学院', 100, '0,100', 4, '院长', '15888888892', '<EMAIL>', '0', '0', 'admin', CURRENT_TIMESTAMP);

-- 插入角色数据
INSERT INTO sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) VALUES
(1, '超级管理员', 'admin', 1, '1', TRUE, TRUE, '0', '0', 'admin', CURRENT_TIMESTAMP, '超级管理员'),
(2, '科研管理员', 'research_admin', 2, '2', TRUE, TRUE, '0', '0', 'admin', CURRENT_TIMESTAMP, '科研管理员'),
(3, '普通用户', 'common', 3, '3', TRUE, TRUE, '0', '0', 'admin', CURRENT_TIMESTAMP, '普通用户'),
(4, '评审专家', 'expert', 4, '4', TRUE, TRUE, '0', '0', 'admin', CURRENT_TIMESTAMP, '评审专家'),
(5, '项目负责人', 'project_leader', 5, '4', TRUE, TRUE, '0', '0', 'admin', CURRENT_TIMESTAMP, '项目负责人');

-- 插入管理员用户（密码：admin123）
INSERT INTO sys_user (user_id, dept_id, user_name, nick_name, user_type, email, phonenumber, sex, avatar, password, status, del_flag, login_ip, login_date, create_by, create_time, update_by, update_time, remark) VALUES
(1, 101, 'admin', '管理员', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', '127.0.0.1', CURRENT_TIMESTAMP, 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '管理员');

-- 插入测试用户（密码：admin123）
INSERT INTO sys_user (user_id, user_name, dept_id, nick_name, user_type, email, phonenumber, sex, password, status, del_flag, create_by, create_time, remark) VALUES
(2, 'researcher1', 103, '研究员张三', '00', '<EMAIL>', '13800138001', '1', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', 'admin', CURRENT_TIMESTAMP, '科研人员'),
(3, 'researcher2', 103, '研究员李四', '00', '<EMAIL>', '13800138002', '0', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', 'admin', CURRENT_TIMESTAMP, '科研人员'),
(4, 'teacher1', 104, '教师王五', '00', '<EMAIL>', '13800138003', '1', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', 'admin', CURRENT_TIMESTAMP, '教学人员'),
(5, 'expert1', 101, '专家赵六', '00', '<EMAIL>', '13800138004', '1', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', 'admin', CURRENT_TIMESTAMP, '评审专家'),
(6, 'student1', 103, '学生小明', '00', '<EMAIL>', '13800138005', '1', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', 'admin', CURRENT_TIMESTAMP, '研究生');

-- 插入用户角色关联数据
INSERT INTO sys_user_role (user_id, role_id) VALUES
(1, 1),  -- admin -> 超级管理员
(2, 3),  -- researcher1 -> 普通用户
(3, 3),  -- researcher2 -> 普通用户
(4, 3),  -- teacher1 -> 普通用户
(5, 4),  -- expert1 -> 评审专家
(6, 3);  -- student1 -> 普通用户

-- 插入菜单数据
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
(1, '个人工作台', 0, 0, 'workspace', 'workspace/index', 1, 0, 'C', '0', '0', '', 'monitor', 'admin', CURRENT_TIMESTAMP, '个人工作台'),
(2, '系统管理', 0, 1, 'system', NULL, 1, 0, 'M', '0', '0', '', 'system', 'admin', CURRENT_TIMESTAMP, '系统管理目录'),
(3, '用户管理', 2, 1, 'user', 'system/user/index', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', CURRENT_TIMESTAMP, '用户管理菜单'),
(4, '角色管理', 2, 2, 'role', 'system/role/index', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', CURRENT_TIMESTAMP, '角色管理菜单'),
(5, '部门管理', 2, 3, 'dept', 'system/dept/index', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', CURRENT_TIMESTAMP, '部门管理菜单'),
(6, '通知公告', 2, 4, 'notice', 'system/notice/index', 1, 0, 'C', '0', '0', 'system:notice:list', 'bell', 'admin', CURRENT_TIMESTAMP, '通知公告菜单'),
(7, '待办事项', 2, 5, 'todo', 'system/todo/index', 1, 0, 'C', '0', '0', 'system:todo:list', 'tickets', 'admin', CURRENT_TIMESTAMP, '待办事项菜单'),
(8, '站内消息', 2, 6, 'message', 'system/message/index', 1, 0, 'C', '0', '0', 'system:message:list', 'message', 'admin', CURRENT_TIMESTAMP, '站内消息菜单');

-- 插入角色菜单关联数据
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6), (1, 7), (1, 8);  -- 超级管理员拥有所有菜单权限

-- 插入快捷应用数据
INSERT INTO sys_quick_app (app_id, app_name, app_code, app_icon, app_url, app_desc, app_type, is_enabled, sort_order, create_by, create_time) VALUES
(1, '用户管理', 'user_manage', 'user', '/system/user', '系统用户管理', '1', '1', 1, 'admin', CURRENT_TIMESTAMP),
(2, '角色管理', 'role_manage', 'peoples', '/system/role', '系统角色管理', '1', '1', 2, 'admin', CURRENT_TIMESTAMP),
(3, '部门管理', 'dept_manage', 'tree', '/system/dept', '组织部门管理', '1', '1', 3, 'admin', CURRENT_TIMESTAMP),
(4, '通知公告', 'notice_manage', 'bell', '/system/notice', '通知公告管理', '1', '1', 4, 'admin', CURRENT_TIMESTAMP),
(5, '待办事项', 'todo_manage', 'tickets', '/system/todo', '待办事项管理', '1', '1', 5, 'admin', CURRENT_TIMESTAMP),
(6, '站内消息', 'message_manage', 'message', '/system/message', '站内消息管理', '1', '1', 6, 'admin', CURRENT_TIMESTAMP),
(7, '系统监控', 'system_monitor', 'monitor', '/monitor', '系统监控', '1', '1', 7, 'admin', CURRENT_TIMESTAMP),
(8, '在线文档', 'online_doc', 'document', 'https://doc.research.com', '在线帮助文档', '2', '1', 8, 'admin', CURRENT_TIMESTAMP);

-- 插入示例通知公告
INSERT INTO sys_notice (notice_id, notice_title, notice_type, notice_content, status, importance, is_top, publish_by, publish_time, create_by, create_time) VALUES
(1, '系统升级通知', '1', '<p>尊敬的用户：</p><p>为了提供更好的服务，系统将于本周末进行升级维护，预计维护时间为2小时。维护期间系统将暂停服务，请合理安排工作时间。</p><p>感谢您的理解与支持！</p>', '0', '2', '1', 'admin', CURRENT_TIMESTAMP, 'admin', CURRENT_TIMESTAMP),
(2, '新功能发布公告', '2', '<p>各位用户：</p><p>系统新增了个人工作台功能，包括：</p><ul><li>个人信息展示</li><li>待办事项统计</li><li>通知公告提醒</li><li>快捷应用配置</li></ul><p>欢迎大家体验使用！</p>', '0', '1', '0', 'admin', CURRENT_TIMESTAMP, 'admin', CURRENT_TIMESTAMP);

-- 插入示例待办事项
INSERT INTO sys_todo (todo_id, title, content, priority, status, assignee_id, assignee_name, creator_id, creator_name, due_time, create_by, create_time) VALUES
(1, '完成系统测试报告', '需要完成第二阶段功能的系统测试报告，包括功能测试、性能测试和安全测试。', '3', '0', 2, '研究员张三', 1, '管理员', DATE_ADD(CURRENT_TIMESTAMP, INTERVAL 3 DAY), 'admin', CURRENT_TIMESTAMP),
(2, '准备项目演示材料', '为下周的项目评审会议准备演示材料，包括PPT和演示视频。', '2', '0', 3, '研究员李四', 1, '管理员', DATE_ADD(CURRENT_TIMESTAMP, INTERVAL 5 DAY), 'admin', CURRENT_TIMESTAMP),
(3, '更新用户手册', '根据新功能更新系统用户手册，确保文档的准确性和完整性。', '2', '1', 4, '教师王五', 1, '管理员', DATE_ADD(CURRENT_TIMESTAMP, INTERVAL 7 DAY), 'admin', CURRENT_TIMESTAMP);

-- 插入示例站内消息
INSERT INTO sys_message (message_id, title, content, message_type, priority, sender_id, sender_name, receiver_id, receiver_name, create_by, create_time) VALUES
(1, '欢迎使用系统', '欢迎您使用科研管理系统！如有任何问题，请随时联系管理员。', '1', '2', 1, '管理员', 2, '研究员张三', 'admin', CURRENT_TIMESTAMP),
(2, '任务分配通知', '您有新的待办事项需要处理，请及时查看并完成。', '2', '3', 1, '管理员', 2, '研究员张三', 'admin', CURRENT_TIMESTAMP),
(3, '系统维护提醒', '系统将于本周末进行维护，请提前保存好您的工作。', '4', '2', 1, '管理员', 3, '研究员李四', 'admin', CURRENT_TIMESTAMP);

-- ----------------------------
-- 操作日志表
-- ----------------------------
CREATE TABLE IF NOT EXISTS sys_oper_log (
  oper_id           BIGINT          NOT NULL AUTO_INCREMENT    COMMENT '日志主键',
  title             VARCHAR(50)     DEFAULT ''                 COMMENT '模块标题',
  business_type     INT             DEFAULT 0                  COMMENT '业务类型（0其他 1新增 2修改 3删除）',
  method            VARCHAR(100)    DEFAULT ''                 COMMENT '方法名称',
  request_method    VARCHAR(10)     DEFAULT ''                 COMMENT '请求方式',
  operator_type     INT             DEFAULT 0                  COMMENT '操作类别（0其他 1后台用户 2手机端用户）',
  oper_name         VARCHAR(50)     DEFAULT ''                 COMMENT '操作人员',
  dept_name         VARCHAR(50)     DEFAULT ''                 COMMENT '部门名称',
  oper_url          VARCHAR(255)    DEFAULT ''                 COMMENT '请求URL',
  oper_ip           VARCHAR(128)    DEFAULT ''                 COMMENT '主机地址',
  oper_location     VARCHAR(255)    DEFAULT ''                 COMMENT '操作地点',
  oper_param        VARCHAR(2000)   DEFAULT ''                 COMMENT '请求参数',
  json_result       VARCHAR(2000)   DEFAULT ''                 COMMENT '返回参数',
  status            INT             DEFAULT 0                  COMMENT '操作状态（0正常 1异常）',
  error_msg         VARCHAR(2000)   DEFAULT ''                 COMMENT '错误消息',
  oper_time         TIMESTAMP       DEFAULT CURRENT_TIMESTAMP  COMMENT '操作时间',
  cost_time         BIGINT          DEFAULT 0                  COMMENT '消耗时间',
  user_agent        VARCHAR(500)    DEFAULT ''                 COMMENT '用户代理',
  PRIMARY KEY (oper_id)
);
