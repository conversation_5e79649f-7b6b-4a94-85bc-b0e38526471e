package com.research.system.controller;

import com.research.common.core.domain.AjaxResult;
import com.research.common.utils.JwtUtils;
import com.research.common.utils.StringUtils;
import com.research.common.core.domain.model.LoginUser;
import com.research.framework.security.UserDetailsServiceImpl;
import com.research.system.domain.SysMenu;
import com.research.system.domain.SysUser;
import com.research.system.service.ISysMenuService;
import com.research.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 登录验证
 * 
 * <AUTHOR>
 */
@RestController
public class SysLoginController {
    
    @Autowired
    private AuthenticationManager authenticationManager;
    
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysMenuService menuService;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    /**
     * 登录方法
     * 
     * @param username 用户名
     * @param password 密码
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody Map<String, String> loginBody) {
        String username = loginBody.get("username");
        String password = loginBody.get("password");
        
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            return AjaxResult.error("用户名或密码不能为空");
        }
        
        try {
            // 用户验证
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(username, password)
            );
            SecurityContextHolder.getContext().setAuthentication(authentication);
            
            // 生成token
            LoginUser loginUser = (LoginUser) authentication.getPrincipal();
            String token = JwtUtils.createTokenStatic(loginUser.getUsername());
            
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("用户名或密码错误");
        }
    }

    /**
     * 获取用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !(authentication.getPrincipal() instanceof LoginUser)) {
            return AjaxResult.error("获取用户信息失败");
        }
        
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        SysUser user = loginUser.getUser();
        
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("userId", user.getUserId());
        userInfo.put("userName", user.getUserName());
        userInfo.put("nickName", user.getNickName());
        userInfo.put("email", user.getEmail());
        userInfo.put("phonenumber", user.getPhonenumber());
        userInfo.put("sex", user.getSex());
        userInfo.put("avatar", user.getAvatar());
        userInfo.put("deptId", user.getDeptId());
        userInfo.put("deptName", ""); // TODO: 获取部门名称
        
        // 获取用户菜单
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(user.getUserId());
        List<SysMenu> routerMenus = menuService.buildMenus(menus);

        // 获取用户权限
        Set<String> permissions = menuService.selectMenuPermsByUserId(user.getUserId());

        result.put("user", userInfo);
        result.put("roles", new String[]{"admin"}); // TODO: 获取实际角色
        result.put("permissions", permissions.toArray(new String[0]));
        result.put("menus", routerMenus);

        return AjaxResult.success(result);
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public AjaxResult logout() {
        SecurityContextHolder.clearContext();
        return AjaxResult.success("退出成功");
    }

    /**
     * 获取验证码
     */
    @GetMapping("/captchaImage")
    public AjaxResult getCode() {
        Map<String, Object> result = new HashMap<>();
        result.put("captchaEnabled", false); // 暂时禁用验证码
        result.put("uuid", "");
        result.put("img", "");
        return AjaxResult.success(result);
    }
}
