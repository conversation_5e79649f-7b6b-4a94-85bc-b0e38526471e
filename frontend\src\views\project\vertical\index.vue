<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目类型" prop="projectType">
        <el-select v-model="queryParams.projectType" placeholder="请选择项目类型" clearable>
          <el-option label="国家级" value="国家级" />
          <el-option label="省部级" value="省部级" />
          <el-option label="市厅级" value="市厅级" />
          <el-option label="校级" value="校级" />
        </el-select>
      </el-form-item>
      <el-form-item label="项目状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择项目状态" clearable>
          <el-option label="申请中" value="0" />
          <el-option label="立项" value="1" />
          <el-option label="执行中" value="2" />
          <el-option label="变更中" value="3" />
          <el-option label="结项中" value="4" />
          <el-option label="已结项" value="5" />
          <el-option label="已撤销" value="6" />
        </el-select>
      </el-form-item>
      <el-form-item label="负责人" prop="principalName">
        <el-input
          v-model="queryParams.principalName"
          placeholder="请输入负责人姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['project:vertical:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['project:vertical:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:vertical:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['project:vertical:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="TrendCharts"
          @click="handleStatistics"
        >统计分析</el-button>
      </el-col>
      <RightToolbar v-model:showSearch="showSearch" @queryTable="getList"></RightToolbar>
    </el-row>

    <el-table v-loading="loading" :data="projectList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="项目编号" align="center" prop="projectNo" width="120" />
      <el-table-column label="项目名称" align="center" prop="projectName" :show-overflow-tooltip="true" />
      <el-table-column label="项目类型" align="center" prop="projectType" width="100" />
      <el-table-column label="项目级别" align="center" prop="projectLevel" width="100" />
      <el-table-column label="负责人" align="center" prop="principalName" width="100" />
      <el-table-column label="所属部门" align="center" prop="deptName" width="120" />
      <el-table-column label="项目状态" align="center" prop="status" width="100">
        <template #default="scope">
          <DictTag :options="statusOptions" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="项目预算" align="center" prop="budget" width="120">
        <template #default="scope">
          <span>{{ formatCurrency(scope.row.budget) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="预算使用率" align="center" width="120">
        <template #default="scope">
          <el-progress 
            :percentage="getBudgetUsageRate(scope.row)" 
            :color="getBudgetUsageColor(scope.row)"
            :stroke-width="8"
          />
        </template>
      </el-table-column>
      <el-table-column label="项目周期" align="center" width="180">
        <template #default="scope">
          <span>{{ formatDateRange(scope.row.startDate, scope.row.endDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['project:vertical:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['project:vertical:edit']">修改</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['project:vertical:edit']">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="approve" v-if="scope.row.status == 0">立项</el-dropdown-item>
                <el-dropdown-item command="execute" v-if="scope.row.status == 1">执行</el-dropdown-item>
                <el-dropdown-item command="change" v-if="scope.row.status == 2">变更</el-dropdown-item>
                <el-dropdown-item command="complete" v-if="scope.row.status == 2">结项</el-dropdown-item>
                <el-dropdown-item command="suspend" v-if="scope.row.status == 2">暂停</el-dropdown-item>
                <el-dropdown-item command="cancel" v-if="scope.row.status < 5">撤销</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <Pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改纵向项目对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="projectRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目编号" prop="projectNo">
              <el-input v-model="form.projectNo" placeholder="请输入项目编号" readonly>
                <template #append>
                  <el-button @click="generateNo">生成</el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="form.projectName" placeholder="请输入项目名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目类型" prop="projectType">
              <el-select v-model="form.projectType" placeholder="请选择项目类型">
                <el-option label="国家级" value="国家级" />
                <el-option label="省部级" value="省部级" />
                <el-option label="市厅级" value="市厅级" />
                <el-option label="校级" value="校级" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目级别" prop="projectLevel">
              <el-select v-model="form.projectLevel" placeholder="请选择项目级别">
                <el-option label="重点项目" value="重点项目" />
                <el-option label="一般项目" value="一般项目" />
                <el-option label="青年项目" value="青年项目" />
                <el-option label="自筹项目" value="自筹项目" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目负责人" prop="principalName">
              <el-input v-model="form.principalName" placeholder="请输入负责人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属部门" prop="deptName">
              <el-input v-model="form.deptName" placeholder="请输入所属部门" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="资助机构" prop="fundingAgency">
              <el-input v-model="form.fundingAgency" placeholder="请输入资助机构" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目来源" prop="projectSource">
              <el-input v-model="form.projectSource" placeholder="请输入项目来源" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目预算" prop="budget">
              <el-input-number v-model="form.budget" :min="0" :precision="2" placeholder="请输入项目预算" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学科分类" prop="subjectCategory">
              <el-input v-model="form.subjectCategory" placeholder="请输入学科分类" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始日期" prop="startDate">
              <el-date-picker
                v-model="form.startDate"
                type="date"
                placeholder="选择开始日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束日期" prop="endDate">
              <el-date-picker
                v-model="form.endDate"
                type="date"
                placeholder="选择结束日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="关键词" prop="keywords">
          <el-input v-model="form.keywords" placeholder="请输入关键词，多个关键词用分号分隔" />
        </el-form-item>
        <el-form-item label="项目简介" prop="projectSummary">
          <el-input v-model="form.projectSummary" type="textarea" :rows="3" placeholder="请输入项目简介" />
        </el-form-item>
        <el-form-item label="研究内容" prop="researchContent">
          <el-input v-model="form.researchContent" type="textarea" :rows="4" placeholder="请输入研究内容" />
        </el-form-item>
        <el-form-item label="预期成果" prop="expectedResults">
          <el-input v-model="form.expectedResults" type="textarea" :rows="3" placeholder="请输入预期成果" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 项目详情对话框 -->
    <project-detail v-model="detailOpen" :project-id="currentProjectId" />

    <!-- 统计分析对话框 -->
    <project-statistics v-model="statisticsOpen" />
  </div>
</template>

<script setup name="VerticalProject">
import { 
  listVerticalProject, 
  getVerticalProject, 
  delVerticalProject, 
  addVerticalProject, 
  updateVerticalProject,
  generateProjectNo,
  approveProject,
  executeProject,
  closeProject,
  cancelProject
} from "@/api/project/vertical"
import ProjectDetail from './components/ProjectDetail.vue'
import ProjectStatistics from './components/ProjectStatistics.vue'
import Pagination from '@/components/Pagination/index.vue'
import RightToolbar from '@/components/RightToolbar/index.vue'
import DictTag from '@/components/DictTag/index.vue'

const { proxy } = getCurrentInstance()

const projectList = ref([])
const open = ref(false)
const detailOpen = ref(false)
const statisticsOpen = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const currentProjectId = ref(null)

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectName: null,
    projectType: null,
    status: null,
    principalName: null
  },
  rules: {
    projectNo: [
      { required: true, message: "项目编号不能为空", trigger: "blur" }
    ],
    projectName: [
      { required: true, message: "项目名称不能为空", trigger: "blur" }
    ],
    projectType: [
      { required: true, message: "项目类型不能为空", trigger: "change" }
    ],
    principalName: [
      { required: true, message: "项目负责人不能为空", trigger: "blur" }
    ],
    budget: [
      { required: true, message: "项目预算不能为空", trigger: "blur" }
    ]
  }
})

const { queryParams, form, rules } = toRefs(data)

// 状态选项
const statusOptions = ref([
  { label: "申请中", value: "0" },
  { label: "立项", value: "1" },
  { label: "执行中", value: "2" },
  { label: "变更中", value: "3" },
  { label: "结项中", value: "4" },
  { label: "已结项", value: "5" },
  { label: "已撤销", value: "6" }
])

/** 查询纵向项目列表 */
function getList() {
  loading.value = true

  // 模拟数据，实际应该调用API
  setTimeout(() => {
    projectList.value = [
      {
        id: 1,
        projectNo: 'ZX2024001',
        projectName: '基于人工智能的教学质量评估系统研究',
        projectType: '省部级',
        projectLevel: '重点项目',
        principalName: '张教授',
        deptName: '计算机系',
        status: '2',
        budget: 500000,
        usedBudget: 150000,
        startDate: '2024-01-01',
        endDate: '2025-12-31',
        fundingAgency: '河南省教育厅',
        projectSource: '河南省高等学校重点科研项目',
        keywords: '人工智能;教学质量;评估系统',
        subjectCategory: '计算机科学与技术'
      },
      {
        id: 2,
        projectNo: 'ZX2024002',
        projectName: '检察业务智能化管理平台关键技术研究',
        projectType: '国家级',
        projectLevel: '一般项目',
        principalName: '李教授',
        deptName: '法学系',
        status: '1',
        budget: 800000,
        usedBudget: 0,
        startDate: '2024-03-01',
        endDate: '2026-02-28',
        fundingAgency: '国家自然科学基金委',
        projectSource: '国家自然科学基金',
        keywords: '检察业务;智能化;管理平台',
        subjectCategory: '法学'
      },
      {
        id: 3,
        projectNo: 'ZX2024003',
        projectName: '大数据环境下的学生行为分析与预警系统',
        projectType: '市厅级',
        projectLevel: '青年项目',
        principalName: '王博士',
        deptName: '数据科学系',
        status: '2',
        budget: 200000,
        usedBudget: 80000,
        startDate: '2024-02-01',
        endDate: '2025-01-31',
        fundingAgency: '郑州市科技局',
        projectSource: '郑州市科技计划项目',
        keywords: '大数据;行为分析;预警系统',
        subjectCategory: '数据科学与大数据技术'
      }
    ]
    total.value = 3
    loading.value = false
  }, 500)

  // 实际API调用（暂时注释）
  // listVerticalProject(queryParams.value).then(response => {
  //   projectList.value = response.rows
  //   total.value = response.total
  //   loading.value = false
  // })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    projectNo: null,
    projectName: null,
    projectType: null,
    projectLevel: null,
    status: 0,
    startDate: null,
    endDate: null,
    budget: null,
    usedBudget: 0,
    principalId: null,
    principalName: null,
    deptId: null,
    deptName: null,
    fundingAgency: null,
    projectSource: null,
    projectSummary: null,
    researchContent: null,
    expectedResults: null,
    keywords: null,
    subjectCategory: null
  }
  proxy.resetForm("projectRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加纵向项目"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value

  // 模拟获取项目详情
  const project = projectList.value.find(p => p.id === id)
  if (project) {
    form.value = { ...project }
    open.value = true
    title.value = "修改纵向项目"
  }

  // 实际API调用（暂时注释）
  // getVerticalProject(id).then(response => {
  //   form.value = response.data
  //   open.value = true
  //   title.value = "修改纵向项目"
  // })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["projectRef"].validate(valid => {
    if (valid) {
      // 模拟提交处理
      setTimeout(() => {
        if (form.value.id != null) {
          // 更新现有项目
          const index = projectList.value.findIndex(p => p.id === form.value.id)
          if (index !== -1) {
            projectList.value[index] = { ...form.value }
          }
          proxy.$modal.msgSuccess("修改成功")
        } else {
          // 添加新项目
          const newProject = {
            ...form.value,
            id: Date.now(), // 使用时间戳作为临时ID
            status: '0' // 默认状态为申请中
          }
          projectList.value.unshift(newProject)
          total.value++
          proxy.$modal.msgSuccess("新增成功")
        }
        open.value = false
      }, 500)

      // 实际API调用（暂时注释）
      // if (form.value.id != null) {
      //   updateVerticalProject(form.value).then(response => {
      //     proxy.$modal.msgSuccess("修改成功")
      //     open.value = false
      //     getList()
      //   })
      // } else {
      //   addVerticalProject(form.value).then(response => {
      //     proxy.$modal.msgSuccess("新增成功")
      //     open.value = false
      //     getList()
      //   })
      // }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const projectIds = row.id || ids.value
  proxy.$modal.confirm('是否确认删除纵向项目编号为"' + projectIds + '"的数据项？').then(function() {
    // 模拟删除处理
    if (Array.isArray(projectIds)) {
      // 批量删除
      projectIds.forEach(id => {
        const index = projectList.value.findIndex(p => p.id === id)
        if (index !== -1) {
          projectList.value.splice(index, 1)
          total.value--
        }
      })
    } else {
      // 单个删除
      const index = projectList.value.findIndex(p => p.id === projectIds)
      if (index !== -1) {
        projectList.value.splice(index, 1)
        total.value--
      }
    }
    proxy.$modal.msgSuccess("删除成功")

    // 实际API调用（暂时注释）
    // return delVerticalProject(projectIds)
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('project/vertical/export', {
    ...queryParams.value
  }, `vertical_project_${new Date().getTime()}.xlsx`)
}

/** 详情按钮操作 */
function handleDetail(row) {
  currentProjectId.value = row.id
  detailOpen.value = true
}

/** 统计分析按钮操作 */
function handleStatistics() {
  statisticsOpen.value = true
}

/** 生成项目编号 */
function generateNo() {
  // 模拟生成项目编号
  const typePrefix = {
    '国家级': 'GJ',
    '省部级': 'SB',
    '市厅级': 'ST',
    '校级': 'XJ'
  }
  const prefix = typePrefix[form.value.projectType] || 'ZX'
  const year = new Date().getFullYear()
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  form.value.projectNo = `${prefix}${year}${random}`

  // 实际API调用（暂时注释）
  // generateProjectNo(form.value.projectType).then(response => {
  //   form.value.projectNo = response.data
  // })
}

/** 更多操作命令处理 */
function handleCommand(command, row) {
  switch (command) {
    case 'approve':
      handleApprove(row)
      break
    case 'execute':
      handleExecute(row)
      break
    case 'change':
      handleChange(row)
      break
    case 'complete':
      handleComplete(row)
      break
    case 'suspend':
      handleSuspend(row)
      break
    case 'cancel':
      handleCancel(row)
      break
  }
}

/** 项目立项 */
function handleApprove(row) {
  proxy.$modal.confirm('确认立项该项目？').then(() => {
    // 模拟立项处理
    const project = projectList.value.find(p => p.id === row.id)
    if (project) {
      project.status = '1' // 立项状态
    }
    proxy.$modal.msgSuccess("立项成功")

    // 实际API调用（暂时注释）
    // return approveProject(row.id)
  })
}

/** 项目执行 */
function handleExecute(row) {
  proxy.$modal.confirm('确认开始执行该项目？').then(() => {
    // 模拟执行处理
    const project = projectList.value.find(p => p.id === row.id)
    if (project) {
      project.status = '2' // 执行中状态
    }
    proxy.$modal.msgSuccess("项目已开始执行")

    // 实际API调用（暂时注释）
    // return executeProject(row.id)
  })
}

/** 项目变更 */
function handleChange(row) {
  proxy.$modal.msgInfo("项目变更功能开发中...")
}

/** 项目结项 */
function handleComplete(row) {
  proxy.$modal.confirm('确认结项该项目？').then(() => {
    // 模拟结项处理
    const project = projectList.value.find(p => p.id === row.id)
    if (project) {
      project.status = '5' // 已结项状态
    }
    proxy.$modal.msgSuccess("结项成功")

    // 实际API调用（暂时注释）
    // return closeProject(row.id)
  })
}

/** 项目暂停 */
function handleSuspend(row) {
  proxy.$modal.msgInfo("项目暂停功能开发中...")
}

/** 项目撤销 */
function handleCancel(row) {
  proxy.$modal.confirm('确认撤销该项目？').then(() => {
    // 模拟撤销处理
    const project = projectList.value.find(p => p.id === row.id)
    if (project) {
      project.status = '6' // 已撤销状态
    }
    proxy.$modal.msgSuccess("撤销成功")

    // 实际API调用（暂时注释）
    // return cancelProject(row.id, '用户手动撤销')
  })
}

// 工具函数
function formatCurrency(amount) {
  if (!amount) return '¥0.00'
  return '¥' + Number(amount).toLocaleString('zh-CN', { minimumFractionDigits: 2 })
}

function getBudgetUsageRate(row) {
  if (!row.budget || row.budget === 0) return 0
  return Math.round((row.usedBudget || 0) / row.budget * 100)
}

function getBudgetUsageColor(row) {
  const rate = getBudgetUsageRate(row)
  if (rate >= 90) return '#f56c6c'
  if (rate >= 70) return '#e6a23c'
  return '#67c23a'
}

function formatDateRange(startDate, endDate) {
  if (!startDate || !endDate) return '-'
  return `${startDate} 至 ${endDate}`
}

onMounted(() => {
  getList()
})
</script>
