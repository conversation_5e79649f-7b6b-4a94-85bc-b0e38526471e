package com.research.workflow.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.research.common.core.controller.BaseController;
import com.research.common.core.domain.AjaxResult;
import com.research.common.core.page.TableDataInfo;
import com.research.common.utils.MybatisPlusPageUtils;
import com.research.common.utils.SecurityUtils;
import com.research.workflow.domain.ProcessDefinition;
import com.research.workflow.domain.ProcessInstance;
import com.research.workflow.service.IWorkflowProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作流流程管理Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/workflow/process")
public class WorkflowProcessController extends BaseController {

    @Autowired
    private IWorkflowProcessService processService;

    /**
     * 部署示例流程
     */
    @PostMapping("/deploy/sample")
    public AjaxResult deploySampleProcess() {
        try {
            // 简单审批流程的BPMN XML
            String bpmnXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<bpmn2:definitions xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" \n" +
                "                   xmlns:bpmn2=\"http://www.omg.org/spec/BPMN/20100524/MODEL\" \n" +
                "                   xmlns:activiti=\"http://activiti.org/bpmn\" \n" +
                "                   id=\"Definitions_1\" \n" +
                "                   targetNamespace=\"http://research.com/workflow\">\n" +
                "  <bpmn2:process id=\"simpleApproval\" name=\"简单审批流程\" isExecutable=\"true\">\n" +
                "    <bpmn2:startEvent id=\"startEvent\" name=\"开始\">\n" +
                "      <bpmn2:outgoing>flow1</bpmn2:outgoing>\n" +
                "    </bpmn2:startEvent>\n" +
                "    <bpmn2:userTask id=\"applyTask\" name=\"提交申请\" activiti:assignee=\"${applicant}\">\n" +
                "      <bpmn2:incoming>flow1</bpmn2:incoming>\n" +
                "      <bpmn2:outgoing>flow2</bpmn2:outgoing>\n" +
                "    </bpmn2:userTask>\n" +
                "    <bpmn2:userTask id=\"approveTask\" name=\"领导审批\" activiti:assignee=\"${approver}\">\n" +
                "      <bpmn2:incoming>flow2</bpmn2:incoming>\n" +
                "      <bpmn2:outgoing>flow3</bpmn2:outgoing>\n" +
                "    </bpmn2:userTask>\n" +
                "    <bpmn2:endEvent id=\"endEvent\" name=\"结束\">\n" +
                "      <bpmn2:incoming>flow3</bpmn2:incoming>\n" +
                "    </bpmn2:endEvent>\n" +
                "    <bpmn2:sequenceFlow id=\"flow1\" sourceRef=\"startEvent\" targetRef=\"applyTask\"/>\n" +
                "    <bpmn2:sequenceFlow id=\"flow2\" sourceRef=\"applyTask\" targetRef=\"approveTask\"/>\n" +
                "    <bpmn2:sequenceFlow id=\"flow3\" sourceRef=\"approveTask\" targetRef=\"endEvent\"/>\n" +
                "  </bpmn2:process>\n" +
                "</bpmn2:definitions>";

            String deploymentId = processService.deployProcess("简单审批流程", bpmnXml);
            return AjaxResult.success("流程部署成功", deploymentId);
        } catch (Exception e) {
            logger.error("部署示例流程失败", e);
            return AjaxResult.error("部署失败: " + e.getMessage());
        }
    }

    // ==================== 流程定义相关接口 ====================

    /**
     * 查询流程定义列表
     */
    @PreAuthorize("@ss.hasPermi('workflow:process:list')")
    @GetMapping("/definitions")
    public TableDataInfo listDefinitions(ProcessDefinition processDefinition) {
        try {
            Page<ProcessDefinition> page = MybatisPlusPageUtils.createSafePage();
            return MybatisPlusPageUtils.convertToTableDataInfo(
                processService.selectDefinitionList(page, processDefinition)
            );
        } catch (Exception e) {
            logger.error("查询流程定义列表失败", e);
            return MybatisPlusPageUtils.createEmptyTableDataInfo();
        }
    }

    /**
     * 获取流程定义详情
     */
    @PreAuthorize("@ss.hasPermi('workflow:process:query')")
    @GetMapping("/definition/{id}")
    public AjaxResult getDefinition(@PathVariable String id) {
        try {
            ProcessDefinition definition = processService.selectDefinitionById(id);
            return AjaxResult.success(definition);
        } catch (Exception e) {
            logger.error("获取流程定义详情失败", e);
            return AjaxResult.error("获取失败：" + e.getMessage());
        }
    }

    /**
     * 启动流程实例
     */
    @PreAuthorize("@ss.hasPermi('workflow:process:start')")
    @PostMapping("/definition/{id}/start")
    public AjaxResult startProcess(@PathVariable String id, @RequestBody Map<String, Object> variables) {
        try {
            ProcessInstance instance = processService.startProcess(id, variables);
            return AjaxResult.success("启动成功", instance);
        } catch (Exception e) {
            logger.error("启动流程失败", e);
            return AjaxResult.error("启动失败：" + e.getMessage());
        }
    }

    /**
     * 部署科研项目申请流程
     */
    @PostMapping("/deploy/research")
    public AjaxResult deployResearchProcess() {
        try {
            // 从resources目录读取BPMN文件
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream("processes/research-project-approval.bpmn");
            if (inputStream == null) {
                return AjaxResult.error("找不到科研项目申请流程文件");
            }

            // 读取文件内容
            StringBuilder bpmnXml = new StringBuilder();
            try (java.util.Scanner scanner = new java.util.Scanner(inputStream, "UTF-8")) {
                while (scanner.hasNextLine()) {
                    bpmnXml.append(scanner.nextLine()).append("\n");
                }
            }

            String deploymentId = processService.deployProcess("科研项目申请流程", bpmnXml.toString());
            return AjaxResult.success("科研项目申请流程部署成功", deploymentId);
        } catch (Exception e) {
            logger.error("部署科研项目申请流程失败", e);
            return AjaxResult.error("部署失败: " + e.getMessage());
        }
    }

    /**
     * 启动简单审批流程（测试用）
     */
    @PostMapping("/start/simple")
    public AjaxResult startSimpleProcess(@RequestBody Map<String, Object> params) {
        try {
            // 设置流程变量
            Map<String, Object> variables = new HashMap<>();
            variables.put("applicant", SecurityUtils.getUserId().toString());
            variables.put("approver", "2"); // 假设用户ID为2的是审批人
            variables.put("title", params.getOrDefault("title", "测试申请"));
            variables.put("content", params.getOrDefault("content", "这是一个测试申请"));

            // 通过流程Key启动流程
            String processInstanceId = processService.startProcess("simpleApproval", variables).getProcessInstanceId();

            Map<String, Object> result = new HashMap<>();
            result.put("processInstanceId", processInstanceId);
            result.put("message", "简单审批流程启动成功");

            return AjaxResult.success("简单审批流程启动成功", result);
        } catch (Exception e) {
            logger.error("启动简单审批流程失败", e);
            return AjaxResult.error("启动失败: " + e.getMessage());
        }
    }

    /**
     * 挂起流程定义
     */
    @PreAuthorize("@ss.hasPermi('workflow:process:suspend')")
    @PostMapping("/definition/{id}/suspend")
    public AjaxResult suspendDefinition(@PathVariable String id) {
        try {
            processService.suspendDefinition(id);
            return AjaxResult.success("挂起成功");
        } catch (Exception e) {
            logger.error("挂起流程定义失败", e);
            return AjaxResult.error("挂起失败：" + e.getMessage());
        }
    }

    /**
     * 激活流程定义
     */
    @PreAuthorize("@ss.hasPermi('workflow:process:activate')")
    @PostMapping("/definition/{id}/activate")
    public AjaxResult activateDefinition(@PathVariable String id) {
        try {
            processService.activateDefinition(id);
            return AjaxResult.success("激活成功");
        } catch (Exception e) {
            logger.error("激活流程定义失败", e);
            return AjaxResult.error("激活失败：" + e.getMessage());
        }
    }

    /**
     * 删除流程定义
     */
    @PreAuthorize("@ss.hasPermi('workflow:process:delete')")
    @DeleteMapping("/definition/{id}")
    public AjaxResult deleteDefinition(@PathVariable String id) {
        try {
            processService.deleteDefinition(id);
            return AjaxResult.success("删除成功");
        } catch (Exception e) {
            logger.error("删除流程定义失败", e);
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }

    // ==================== 流程实例相关接口 ====================

    /**
     * 查询流程实例列表
     */
    @PreAuthorize("@ss.hasPermi('workflow:process:instance')")
    @GetMapping("/instances")
    public TableDataInfo listInstances(ProcessInstance processInstance) {
        try {
            Page<ProcessInstance> page = MybatisPlusPageUtils.createSafePage();
            return MybatisPlusPageUtils.convertToTableDataInfo(
                processService.selectInstanceList(page, processInstance)
            );
        } catch (Exception e) {
            logger.error("查询流程实例列表失败", e);
            return MybatisPlusPageUtils.createEmptyTableDataInfo();
        }
    }

    /**
     * 获取流程实例详情
     */
    @PreAuthorize("@ss.hasPermi('workflow:process:query')")
    @GetMapping("/instance/{id}")
    public AjaxResult getInstance(@PathVariable String id) {
        try {
            ProcessInstance instance = processService.selectInstanceById(id);
            return AjaxResult.success(instance);
        } catch (Exception e) {
            logger.error("获取流程实例详情失败", e);
            return AjaxResult.error("获取失败：" + e.getMessage());
        }
    }

    /**
     * 挂起流程实例
     */
    @PreAuthorize("@ss.hasPermi('workflow:process:suspend')")
    @PostMapping("/instance/{id}/suspend")
    public AjaxResult suspendInstance(@PathVariable String id) {
        try {
            processService.suspendInstance(id);
            return AjaxResult.success("挂起成功");
        } catch (Exception e) {
            logger.error("挂起流程实例失败", e);
            return AjaxResult.error("挂起失败：" + e.getMessage());
        }
    }

    /**
     * 激活流程实例
     */
    @PreAuthorize("@ss.hasPermi('workflow:process:activate')")
    @PostMapping("/instance/{id}/activate")
    public AjaxResult activateInstance(@PathVariable String id) {
        try {
            processService.activateInstance(id);
            return AjaxResult.success("激活成功");
        } catch (Exception e) {
            logger.error("激活流程实例失败", e);
            return AjaxResult.error("激活失败：" + e.getMessage());
        }
    }

    /**
     * 终止流程实例
     */
    @PreAuthorize("@ss.hasPermi('workflow:process:terminate')")
    @PostMapping("/instance/{id}/terminate")
    public AjaxResult terminateInstance(@PathVariable String id, @RequestParam(required = false) String reason) {
        try {
            processService.terminateInstance(id, reason);
            return AjaxResult.success("终止成功");
        } catch (Exception e) {
            logger.error("终止流程实例失败", e);
            return AjaxResult.error("终止失败：" + e.getMessage());
        }
    }

    /**
     * 批量终止流程实例
     */
    @PreAuthorize("@ss.hasPermi('workflow:process:terminate')")
    @PostMapping("/instances/terminate")
    public AjaxResult batchTerminateInstances(@RequestBody List<String> instanceIds) {
        try {
            processService.batchTerminateInstances(instanceIds);
            return AjaxResult.success("批量终止成功");
        } catch (Exception e) {
            logger.error("批量终止流程实例失败", e);
            return AjaxResult.error("批量终止失败：" + e.getMessage());
        }
    }

    /**
     * 查看流程图
     */
    @PreAuthorize("@ss.hasPermi('workflow:process:view')")
    @GetMapping("/instance/{id}/diagram")
    public AjaxResult getProcessDiagram(@PathVariable String id) {
        try {
            String diagram = processService.getProcessDiagram(id);
            return AjaxResult.success("获取成功", diagram);
        } catch (Exception e) {
            logger.error("获取流程图失败", e);
            return AjaxResult.error("获取失败：" + e.getMessage());
        }
    }

    /**
     * 查看流程历史
     */
    @PreAuthorize("@ss.hasPermi('workflow:process:history')")
    @GetMapping("/instance/{id}/history")
    public AjaxResult getProcessHistory(@PathVariable String id) {
        try {
            List<Map<String, Object>> history = processService.getProcessHistory(id);
            return AjaxResult.success("获取成功", history);
        } catch (Exception e) {
            logger.error("获取流程历史失败", e);
            return AjaxResult.error("获取失败：" + e.getMessage());
        }
    }

    // ==================== 流程统计相关接口 ====================

    /**
     * 获取流程统计数据
     */
    @PreAuthorize("@ss.hasPermi('workflow:process:statistics')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        try {
            Map<String, Object> statistics = processService.getProcessStatistics();
            return AjaxResult.success("获取成功", statistics);
        } catch (Exception e) {
            logger.error("获取流程统计数据失败", e);
            return AjaxResult.error("获取失败：" + e.getMessage());
        }
    }

    /**
     * 获取流程趋势数据
     */
    @PreAuthorize("@ss.hasPermi('workflow:process:statistics')")
    @GetMapping("/statistics/trend")
    public AjaxResult getTrendData(@RequestParam(defaultValue = "7") int days) {
        try {
            List<Map<String, Object>> trendData = processService.getProcessTrendData(days);
            return AjaxResult.success("获取成功", trendData);
        } catch (Exception e) {
            logger.error("获取流程趋势数据失败", e);
            return AjaxResult.error("获取失败：" + e.getMessage());
        }
    }

    /**
     * 获取流程详细统计
     */
    @PreAuthorize("@ss.hasPermi('workflow:process:statistics')")
    @GetMapping("/statistics/detail")
    public AjaxResult getDetailStatistics() {
        try {
            List<Map<String, Object>> detailStats = processService.getProcessDetailStatistics();
            return AjaxResult.success("获取成功", detailStats);
        } catch (Exception e) {
            logger.error("获取流程详细统计失败", e);
            return AjaxResult.error("获取失败：" + e.getMessage());
        }
    }

    /**
     * 导出统计数据
     */
    @PreAuthorize("@ss.hasPermi('workflow:process:export')")
    @GetMapping("/statistics/export")
    public AjaxResult exportStatistics() {
        try {
            // TODO: 实现导出功能
            return AjaxResult.success("导出功能开发中...");
        } catch (Exception e) {
            logger.error("导出统计数据失败", e);
            return AjaxResult.error("导出失败：" + e.getMessage());
        }
    }
}
