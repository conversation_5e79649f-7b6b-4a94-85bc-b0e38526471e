import request from '@/utils/request'

// ==================== 流程定义相关API ====================

// 查询流程定义列表
export function listDefinitions(query?: any) {
  return request({
    url: '/workflow/process/definitions',
    method: 'get',
    params: query
  })
}

// 获取流程定义列表（新接口）
export function getProcessDefinitionList(query?: any) {
  return request({
    url: '/workflow/process/definitions',
    method: 'get',
    params: query
  })
}

// 部署简单审批流程
export function deploySampleProcess() {
  return request({
    url: '/workflow/process/deploy/sample',
    method: 'post'
  })
}

// 部署科研项目申请流程
export function deployResearchProcess() {
  return request({
    url: '/workflow/process/deploy/research',
    method: 'post'
  })
}

// 启动简单审批流程
export function startSimpleProcess(data: any) {
  return request({
    url: '/workflow/process/start/simple',
    method: 'post',
    data
  })
}

// 获取流程定义详情
export function getDefinition(id: string) {
  return request({
    url: '/workflow/process/definition/' + id,
    method: 'get'
  })
}

// 启动流程实例
export function startProcess(id: string, variables: any) {
  return request({
    url: '/workflow/process/definition/' + id + '/start',
    method: 'post',
    data: variables
  })
}

// 挂起流程定义
export function suspendDefinition(id: string) {
  return request({
    url: '/workflow/process/definition/' + id + '/suspend',
    method: 'post'
  })
}

// 激活流程定义
export function activateDefinition(id: string) {
  return request({
    url: '/workflow/process/definition/' + id + '/activate',
    method: 'post'
  })
}

// 挂起流程定义（新接口）
export function suspendProcessDefinition(id: string) {
  return request({
    url: '/workflow/process/definition/' + id + '/suspend',
    method: 'post'
  })
}

// 激活流程定义（新接口）
export function activateProcessDefinition(id: string) {
  return request({
    url: '/workflow/process/definition/' + id + '/activate',
    method: 'post'
  })
}

// 删除流程定义
export function deleteDefinition(id: string) {
  return request({
    url: '/workflow/process/definition/' + id,
    method: 'delete'
  })
}

// ==================== 流程实例相关API ====================

// 查询流程实例列表
export function listInstances(query?: any) {
  return request({
    url: '/workflow/process/instances',
    method: 'get',
    params: query
  })
}

// 获取流程实例详情
export function getInstance(id: string) {
  return request({
    url: '/workflow/process/instance/' + id,
    method: 'get'
  })
}

// 挂起流程实例
export function suspendInstance(id: string) {
  return request({
    url: '/workflow/process/instance/' + id + '/suspend',
    method: 'post'
  })
}

// 激活流程实例
export function activateInstance(id: string) {
  return request({
    url: '/workflow/process/instance/' + id + '/activate',
    method: 'post'
  })
}

// 终止流程实例
export function terminateInstance(id: string, reason?: string) {
  return request({
    url: '/workflow/process/instance/' + id + '/terminate',
    method: 'post',
    params: { reason }
  })
}

// 批量终止流程实例
export function batchTerminateInstances(instanceIds: string[]) {
  return request({
    url: '/workflow/process/instances/terminate',
    method: 'post',
    data: instanceIds
  })
}

// 查看流程图
export function getProcessDiagram(id: string) {
  return request({
    url: '/workflow/process/instance/' + id + '/diagram',
    method: 'get'
  })
}

// 查看流程历史
export function getProcessHistory(id: string) {
  return request({
    url: '/workflow/process/instance/' + id + '/history',
    method: 'get'
  })
}

// ==================== 流程统计相关API ====================

// 获取流程统计数据
export function getStatistics() {
  return request({
    url: '/workflow/process/statistics',
    method: 'get'
  })
}

// 获取流程趋势数据
export function getTrendData(days: number = 7) {
  return request({
    url: '/workflow/process/statistics/trend',
    method: 'get',
    params: { days }
  })
}

// 获取流程详细统计
export function getDetailStatistics() {
  return request({
    url: '/workflow/process/statistics/detail',
    method: 'get'
  })
}

// 导出统计数据
export function exportStatistics() {
  return request({
    url: '/workflow/process/statistics/export',
    method: 'get'
  })
}
