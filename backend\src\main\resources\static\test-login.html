<!DOCTYPE html>
<html>
<head>
    <title>登录接口测试</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>登录接口测试</h1>
    <div>
        <label>用户名: <input type="text" id="username" value="admin"></label><br><br>
        <label>密码: <input type="password" id="password" value="admin123"></label><br><br>
        <button onclick="testLogin()">测试登录</button>
    </div>
    <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>

    <script>
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                document.getElementById('result').innerHTML = 
                    '<h3>响应结果:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                    
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<h3>错误:</h3><pre>' + error.message + '</pre>';
            }
        }
    </script>
</body>
</html>
