<template>
  <div class="role-auth-user">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>角色分配用户</span>
          <el-button type="primary" @click="goBack">返回</el-button>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="never">
            <template #header>
              <span>未分配用户</span>
            </template>
            
            <div class="search-box">
              <el-input
                v-model="unassignedQuery.userName"
                placeholder="请输入用户名"
                clearable
                @keyup.enter="getUnassignedUsers"
              >
                <template #append>
                  <el-button @click="getUnassignedUsers">搜索</el-button>
                </template>
              </el-input>
            </div>
            
            <el-table
              v-loading="unassignedLoading"
              :data="unassignedUsers"
              @selection-change="handleUnassignedSelectionChange"
              height="400"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="userName" label="用户名" />
              <el-table-column prop="nickName" label="用户昵称" />
              <el-table-column prop="email" label="邮箱" />
            </el-table>
            
            <div class="table-footer">
              <el-button
                type="primary"
                @click="openSelectUser"
              >
                选择用户
              </el-button>
              <el-button
                type="success"
                :disabled="unassignedSelection.length === 0"
                @click="assignUsers"
              >
                分配用户
              </el-button>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card shadow="never">
            <template #header>
              <span>已分配用户</span>
            </template>
            
            <div class="search-box">
              <el-input
                v-model="assignedQuery.userName"
                placeholder="请输入用户名"
                clearable
                @keyup.enter="getAssignedUsers"
              >
                <template #append>
                  <el-button @click="getAssignedUsers">搜索</el-button>
                </template>
              </el-input>
            </div>
            
            <el-table
              v-loading="assignedLoading"
              :data="assignedUsers"
              @selection-change="handleAssignedSelectionChange"
              height="400"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="userName" label="用户名" />
              <el-table-column prop="nickName" label="用户昵称" />
              <el-table-column prop="email" label="邮箱" />
            </el-table>
            
            <div class="table-footer">
              <el-button
                type="danger"
                :disabled="assignedSelection.length === 0"
                @click="unassignUsers"
              >
                取消分配
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 选择用户对话框 -->
    <select-user ref="selectUserRef" @ok="handleSelectUserOk" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { allocatedUserList, unallocatedUserList, authUserCancel, authUserCancelAll, authUserSelectAll } from '@/api/system/role'
import SelectUser from './selectUser.vue'

const route = useRoute()
const router = useRouter()

const roleId = ref('')
const roleName = ref('')

const unassignedUsers = ref([])
const assignedUsers = ref([])
const unassignedSelection = ref([])
const assignedSelection = ref([])
const unassignedLoading = ref(false)
const assignedLoading = ref(false)

const unassignedQuery = ref({
  userName: ''
})

const assignedQuery = ref({
  userName: ''
})

const selectUserRef = ref()

const handleUnassignedSelectionChange = (selection: any[]) => {
  unassignedSelection.value = selection
}

const handleAssignedSelectionChange = (selection: any[]) => {
  assignedSelection.value = selection
}

const getUnassignedUsers = () => {
  unassignedLoading.value = true
  const params = {
    roleId: roleId.value,
    ...unassignedQuery.value
  }
  unallocatedUserList(params).then(response => {
    unassignedUsers.value = response.rows
    unassignedLoading.value = false
  }).catch(() => {
    unassignedLoading.value = false
  })
}

const getAssignedUsers = () => {
  assignedLoading.value = true
  const params = {
    roleId: roleId.value,
    ...assignedQuery.value
  }
  allocatedUserList(params).then(response => {
    assignedUsers.value = response.rows
    assignedLoading.value = false
  }).catch(() => {
    assignedLoading.value = false
  })
}

const assignUsers = async () => {
  if (unassignedSelection.value.length === 0) {
    ElMessage.warning('请选择要分配的用户')
    return
  }

  try {
    await ElMessageBox.confirm('确认要分配选中的用户吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const userIds = unassignedSelection.value.map(user => user.userId).join(',')
    await authUserSelectAll({ roleId: roleId.value, userIds })
    ElMessage.success('分配成功')
    getUnassignedUsers()
    getAssignedUsers()
  } catch {
    // 用户取消操作
  }
}

const unassignUsers = async () => {
  if (assignedSelection.value.length === 0) {
    ElMessage.warning('请选择要取消分配的用户')
    return
  }

  try {
    await ElMessageBox.confirm('确认要取消分配选中的用户吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const userIds = assignedSelection.value.map(user => user.userId).join(',')
    await authUserCancelAll({ roleId: roleId.value, userIds })
    ElMessage.success('取消分配成功')
    getUnassignedUsers()
    getAssignedUsers()
  } catch {
    // 用户取消操作
  }
}

const goBack = () => {
  router.back()
}

const openSelectUser = () => {
  selectUserRef.value.show(roleId.value)
}

const handleSelectUserOk = () => {
  getUnassignedUsers()
  getAssignedUsers()
}

onMounted(() => {
  roleId.value = route.params.roleId as string
  if (roleId.value) {
    getUnassignedUsers()
    getAssignedUsers()
  }
})
</script>

<style scoped>
.role-auth-user {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-box {
  margin-bottom: 20px;
}

.table-footer {
  margin-top: 20px;
  text-align: center;
}
</style>
