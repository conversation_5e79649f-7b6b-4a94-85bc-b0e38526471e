package com.research.common.utils.ip;

import com.research.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 获取地址类
 * 
 * <AUTHOR>
 */
public class AddressUtils {
    
    private static final Logger log = LoggerFactory.getLogger(AddressUtils.class);

    // IP地址查询
    public static final String IP_URL = "http://whois.pconline.com.cn/ipJson.jsp";

    // 未知地址
    public static final String UNKNOWN = "XX XX";

    public static String getRealAddressByIP(String ip) {
        // 内网不查询
        if (IpUtils.internalIp(ip)) {
            return "内网IP";
        }
        try {
            // 简化实现，实际应该调用第三方IP地址查询服务
            return "XX XX";
        } catch (Exception e) {
            log.error("获取地理位置异常 {}", ip);
            return UNKNOWN;
        }
    }
}
