package com.research.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 待办事项实体类
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_todo")
public class SysTodo implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 待办ID */
    @TableId(value = "todo_id", type = IdType.AUTO)
    private Long todoId;

    /** 待办标题 */
    @TableField("title")
    private String title;

    /** 待办内容 */
    @TableField("content")
    private String content;

    /** 待办类型（1个人待办 2工作流待办 3系统待办） */
    @TableField("todo_type")
    private String todoType;

    /** 优先级（1低 2中 3高 4紧急） */
    @TableField("priority")
    private String priority;

    /** 状态（0待处理 1处理中 2已完成 3已取消） */
    @TableField("status")
    private String status;

    /** 负责人ID */
    @TableField("assignee_id")
    private Long assigneeId;

    /** 负责人姓名 */
    @TableField("assignee_name")
    private String assigneeName;

    /** 截止时间 */
    @TableField("due_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dueTime;

    /** 完成时间 */
    @TableField("complete_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTime;

    /** 关联业务ID */
    @TableField("business_id")
    private String businessId;

    /** 关联业务类型 */
    @TableField("business_type")
    private String businessType;

    /** 关联业务标题 */
    @TableField("business_title")
    private String businessTitle;

    /** 处理结果 */
    @TableField("result")
    private String result;

    /** 是否已读（0未读 1已读） */
    @TableField("is_read")
    private String isRead;

    /** 提醒时间 */
    @TableField("remind_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime remindTime;

    /** 是否已提醒（0未提醒 1已提醒） */
    @TableField("is_reminded")
    private String isReminded;

    /** 标签 */
    @TableField("tags")
    private String tags;

    /** 附件路径 */
    @TableField("attachment_path")
    private String attachmentPath;

    /** 创建者 */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 更新者 */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /** 备注 */
    @TableField("remark")
    private String remark;

    // 非数据库字段
    /** 分类名称 */
    @TableField(exist = false)
    private String categoryName;

    /** 创建人姓名 */
    @TableField(exist = false)
    private String createByName;

    /** 是否逾期 */
    @TableField(exist = false)
    private Boolean isOverdue;

    /** 剩余天数 */
    @TableField(exist = false)
    private Integer remainingDays;

    // 手动添加getter和setter方法以解决Lombok编译问题
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(LocalDateTime completeTime) {
        this.completeTime = completeTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Long getTodoId() {
        return todoId;
    }

    public void setTodoId(Long todoId) {
        this.todoId = todoId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTodoType() {
        return todoType;
    }

    public void setTodoType(String todoType) {
        this.todoType = todoType;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public Long getAssigneeId() {
        return assigneeId;
    }

    public void setAssigneeId(Long assigneeId) {
        this.assigneeId = assigneeId;
    }

    public String getAssigneeName() {
        return assigneeName;
    }

    public void setAssigneeName(String assigneeName) {
        this.assigneeName = assigneeName;
    }

    public LocalDateTime getDueTime() {
        return dueTime;
    }

    public void setDueTime(LocalDateTime dueTime) {
        this.dueTime = dueTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }
}
