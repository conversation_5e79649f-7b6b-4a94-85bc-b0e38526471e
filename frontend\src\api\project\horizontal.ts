import request from '@/utils/request'

// 查询横向项目列表
export function listHorizontalProject(query: any) {
  return request({
    url: '/project/horizontal/list',
    method: 'get',
    params: query
  })
}

// 查询横向项目详细
export function getHorizontalProject(id: number) {
  return request({
    url: '/project/horizontal/' + id,
    method: 'get'
  })
}

// 根据项目编号查询项目
export function getHorizontalProjectByNo(projectNo: string) {
  return request({
    url: '/project/horizontal/no/' + projectNo,
    method: 'get'
  })
}

// 新增横向项目
export function addHorizontalProject(data: any) {
  return request({
    url: '/project/horizontal',
    method: 'post',
    data: data
  })
}

// 修改横向项目
export function updateHorizontalProject(data: any) {
  return request({
    url: '/project/horizontal',
    method: 'put',
    data: data
  })
}

// 删除横向项目
export function delHorizontalProject(id: number) {
  return request({
    url: '/project/horizontal/' + id,
    method: 'delete'
  })
}

// 批量删除横向项目
export function delHorizontalProjects(ids: number[]) {
  return request({
    url: '/project/horizontal/' + ids.join(','),
    method: 'delete'
  })
}

// 导出横向项目
export function exportHorizontalProject(query: any) {
  return request({
    url: '/project/horizontal/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 提交项目申请
export function submitProjectApplication(data: any) {
  return request({
    url: '/project/horizontal/submit',
    method: 'post',
    data: data
  })
}

// 项目立项
export function approveProject(id: number, approvalFilePath?: string) {
  return request({
    url: '/project/horizontal/approve/' + id,
    method: 'put',
    params: { approvalFilePath }
  })
}

// 项目执行
export function executeProject(id: number) {
  return request({
    url: '/project/horizontal/execute/' + id,
    method: 'put'
  })
}

// 项目挂起
export function suspendProject(id: number, reason: string) {
  return request({
    url: '/project/horizontal/suspend/' + id,
    method: 'put',
    params: { reason }
  })
}

// 项目恢复
export function resumeProject(id: number) {
  return request({
    url: '/project/horizontal/resume/' + id,
    method: 'put'
  })
}

// 项目结项
export function completeProject(id: number) {
  return request({
    url: '/project/horizontal/complete/' + id,
    method: 'put'
  })
}

// 项目撤销
export function cancelProject(id: number, reason: string) {
  return request({
    url: '/project/horizontal/cancel/' + id,
    method: 'put',
    params: { reason }
  })
}

// 更新项目经费
export function updateProjectFund(id: number, receivedFund: number) {
  return request({
    url: '/project/horizontal/fund/' + id,
    method: 'put',
    params: { receivedFund }
  })
}

// 查询我负责的项目
export function getMyProjects(principalId: number) {
  return request({
    url: '/project/horizontal/my/' + principalId,
    method: 'get'
  })
}

// 查询部门项目
export function getDeptProjects(deptId: number) {
  return request({
    url: '/project/horizontal/dept/' + deptId,
    method: 'get'
  })
}

// 根据状态查询项目
export function getProjectsByStatus(status: number) {
  return request({
    url: '/project/horizontal/status/' + status,
    method: 'get'
  })
}

// 查询即将到期的项目
export function getExpiringProjects(days: number = 30) {
  return request({
    url: '/project/horizontal/expiring',
    method: 'get',
    params: { days }
  })
}

// 查询已逾期的项目
export function getOverdueProjects() {
  return request({
    url: '/project/horizontal/overdue',
    method: 'get'
  })
}

// 查询经费到账率低的项目
export function getLowFundReceiveRateProjects(threshold: number = 50) {
  return request({
    url: '/project/horizontal/low-fund-rate',
    method: 'get',
    params: { threshold }
  })
}

// 根据合作单位查询项目
export function getProjectsByPartner(partnerId: number) {
  return request({
    url: '/project/horizontal/partner/' + partnerId,
    method: 'get'
  })
}

// 根据合同查询项目
export function getProjectsByContract(contractId: number) {
  return request({
    url: '/project/horizontal/contract/' + contractId,
    method: 'get'
  })
}

// 搜索项目
export function searchProjects(keyword: string) {
  return request({
    url: '/project/horizontal/search',
    method: 'get',
    params: { keyword }
  })
}

// 查询项目统计信息
export function getProjectStatistics() {
  return request({
    url: '/project/horizontal/statistics',
    method: 'get'
  })
}

// 查询部门项目统计
export function getDeptStatistics() {
  return request({
    url: '/project/horizontal/statistics/dept',
    method: 'get'
  })
}

// 查询项目类型统计
export function getTypeStatistics() {
  return request({
    url: '/project/horizontal/statistics/type',
    method: 'get'
  })
}

// 查询项目状态统计
export function getStatusStatistics() {
  return request({
    url: '/project/horizontal/statistics/status',
    method: 'get'
  })
}

// 查询年度项目统计
export function getYearlyStatistics(year: number) {
  return request({
    url: '/project/horizontal/statistics/yearly',
    method: 'get',
    params: { year }
  })
}

// 查询月度项目统计
export function getMonthlyStatistics(year: number, month: number) {
  return request({
    url: '/project/horizontal/statistics/monthly',
    method: 'get',
    params: { year, month }
  })
}

// 查询合作单位项目统计
export function getPartnerStatistics() {
  return request({
    url: '/project/horizontal/statistics/partner',
    method: 'get'
  })
}

// 查询项目经费统计
export function getFundStatistics() {
  return request({
    url: '/project/horizontal/statistics/fund',
    method: 'get'
  })
}

// 查询项目进度统计
export function getProgressStatistics() {
  return request({
    url: '/project/horizontal/statistics/progress',
    method: 'get'
  })
}

// 生成项目编号
export function generateProjectNo(projectType?: string) {
  return request({
    url: '/project/horizontal/generate-no',
    method: 'get',
    params: { projectType }
  })
}

// 检查项目编号是否存在
export function checkProjectNo(projectNo: string, id?: number) {
  return request({
    url: '/project/horizontal/check-no',
    method: 'get',
    params: { projectNo, id }
  })
}
