<template>
  <div class="navbar">
    <div class="left-section">
      <span class="page-title">个人工作台</span>
    </div>

    <div class="center-section">
      <el-input
        v-model="searchText"
        placeholder="站内搜索"
        class="search-input"
        clearable
      >
        <template #append>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
        </template>
      </el-input>
    </div>

    <div class="right-menu">
      <template v-if="device !== 'mobile'">
        <el-tooltip content="消息通知" effect="dark" placement="bottom">
          <div class="right-menu-item hover-effect notification-item">
            <el-icon><Bell /></el-icon>
            <span class="notification-badge">3</span>
          </div>
        </el-tooltip>

        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>
      </template>

      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <img :src="userStore.userInfo.avatar || defaultAvatar" class="user-avatar">
          <span class="username">{{ userStore.userInfo.userName || 'admin' }}</span>
          <el-icon class="el-icon-caret-bottom">
            <caret-bottom />
          </el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <router-link to="/user/profile">
              <el-dropdown-item>个人中心</el-dropdown-item>
            </router-link>
            <el-dropdown-item divided @click="logout">
              <span style="display:block;">退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import { CaretBottom, Search, Bell } from '@element-plus/icons-vue'
import { useAppStore } from '@/store/modules/app'
import { useUserStore } from '@/store/modules/user'
import Screenfull from '@/components/Screenfull/index.vue'
import SizeSelect from '@/components/SizeSelect/index.vue'

const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()

const searchText = ref('')

const sidebar = computed(() => appStore.sidebar)
const device = computed(() => appStore.device)
const defaultAvatar = '/src/assets/images/profile.jpg'

const handleSearch = () => {
  if (searchText.value.trim()) {
    ElMessage.info(`搜索功能开发中: ${searchText.value}`)
  } else {
    ElMessage.warning('请输入搜索内容')
  }
}

const logout = async () => {
  try {
    await ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await userStore.logout()
    router.push('/login')
  } catch (error) {
    console.log('取消退出')
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 60px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  padding: 0 20px;

  .left-section {
    flex: 0 0 auto;

    .page-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .center-section {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 400px;
    margin: 0 auto;

    .search-input {
      width: 100%;

      :deep(.el-input-group__append) {
        background: #4f7cff;
        border-color: #4f7cff;

        .el-button {
          background: transparent;
          border: none;
          color: white;

          &:hover {
            background: rgba(255, 255, 255, 0.1);
          }
        }
      }
    }
  }

  .right-menu {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    height: 100%;

    .right-menu-item {
      display: flex;
      align-items: center;
      padding: 0 12px;
      height: 40px;
      font-size: 18px;
      color: #5a5e66;
      border-radius: 6px;
      margin-left: 8px;

      &.hover-effect {
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          background: #f5f7fa;
          color: #4f7cff;
        }
      }

      &.notification-item {
        position: relative;

        .notification-badge {
          position: absolute;
          top: -2px;
          right: 6px;
          background: #f56c6c;
          color: white;
          font-size: 10px;
          padding: 1px 4px;
          border-radius: 8px;
          min-width: 16px;
          text-align: center;
          line-height: 14px;
        }
      }
    }

    .avatar-container {
      margin-left: 16px;

      .avatar-wrapper {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        border-radius: 6px;
        transition: all 0.3s;
        cursor: pointer;

        &:hover {
          background: #f5f7fa;
        }

        .user-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          margin-right: 8px;
        }

        .username {
          font-size: 14px;
          color: #303133;
          margin-right: 8px;
        }

        .el-icon-caret-bottom {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .navbar {
    padding: 0 10px;

    .center-section {
      display: none;
    }

    .left-section .page-title {
      font-size: 16px;
    }

    .right-menu .avatar-wrapper .username {
      display: none;
    }
  }
}
</style>
