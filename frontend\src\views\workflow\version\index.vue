<template>
  <div class="app-container">
    <!-- 流程选择 -->
    <el-card class="mb-4">
      <div class="process-selector">
        <el-form :inline="true">
          <el-form-item label="选择流程">
            <el-select 
              v-model="selectedProcessKey" 
              placeholder="请选择流程定义"
              @change="handleProcessChange"
              style="width: 300px"
            >
              <el-option
                v-for="process in processList"
                :key="process.key"
                :label="process.name"
                :value="process.key"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="refreshVersions" :disabled="!selectedProcessKey">
              <el-icon><Refresh /></el-icon>
              刷新版本
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 版本统计 -->
    <el-row :gutter="20" class="mb-4" v-if="selectedProcessKey">
      <el-col :span="6">
        <el-card>
          <div class="statistic-item">
            <div class="statistic-value">{{ statistics.totalVersions || 0 }}</div>
            <div class="statistic-label">总版本数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="statistic-item">
            <div class="statistic-value">{{ statistics.publishedVersions || 0 }}</div>
            <div class="statistic-label">已发布版本</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="statistic-item">
            <div class="statistic-value">{{ statistics.totalRunningInstances || 0 }}</div>
            <div class="statistic-label">运行中实例</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="statistic-item">
            <div class="statistic-value">{{ statistics.totalCompletedInstances || 0 }}</div>
            <div class="statistic-label">已完成实例</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 版本管理 -->
    <el-card v-if="selectedProcessKey">
      <template #header>
        <div class="card-header">
          <span>版本管理 - {{ selectedProcessName }}</span>
          <div>
            <el-button type="primary" @click="handleCreateVersion">
              <el-icon><Plus /></el-icon>
              创建版本
            </el-button>
            <el-button @click="handleRouteTest">
              <el-icon><Connection /></el-icon>
              路由测试
            </el-button>
          </div>
        </div>
      </template>

      <el-table v-loading="loading" :data="versionList">
        <el-table-column label="版本标签" prop="versionTag" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.isDefault ? 'success' : 'info'">
              {{ scope.row.versionTag }}
              <el-icon v-if="scope.row.isDefault" class="ml-1"><Star /></el-icon>
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="版本名称" prop="versionName" :show-overflow-tooltip="true" />
        <el-table-column label="版本描述" prop="versionDescription" :show-overflow-tooltip="true" />
        <el-table-column label="发布状态" prop="publishStatus" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.publishStatus)">
              {{ getStatusText(scope.row.publishStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="发布策略" prop="publishStrategy" width="120">
          <template #default="scope">
            <span>{{ getStrategyText(scope.row.publishStrategy) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="流量比例" prop="trafficRatio" width="100">
          <template #default="scope">
            <span v-if="scope.row.publishStrategy === 1">{{ scope.row.trafficRatio }}%</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="运行实例" prop="runningInstanceCount" width="100" />
        <el-table-column label="完成实例" prop="completedInstanceCount" width="100" />
        <el-table-column label="发布时间" prop="publishTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.publishTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300" fixed="right">
          <template #default="scope">
            <el-button 
              v-if="scope.row.publishStatus === 0" 
              link 
              type="success" 
              @click="handlePublish(scope.row)"
            >
              <el-icon><Upload /></el-icon>发布
            </el-button>
            <el-button 
              v-if="scope.row.publishStatus === 1" 
              link 
              type="warning" 
              @click="handleDeprecate(scope.row)"
            >
              <el-icon><Close /></el-icon>停用
            </el-button>
            <el-button 
              v-if="scope.row.publishStatus === 2" 
              link 
              type="primary" 
              @click="handleRollback(scope.row)"
            >
              <el-icon><RefreshLeft /></el-icon>回滚
            </el-button>
            <el-button link type="primary" @click="handleConfigRoute(scope.row)">
              <el-icon><Setting /></el-icon>路由配置
            </el-button>
            <el-button link type="info" @click="handleViewDetails(scope.row)">
              <el-icon><View /></el-icon>详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建版本对话框 -->
    <el-dialog title="创建新版本" v-model="createDialog.visible" width="500px">
      <el-form ref="createFormRef" :model="createDialog.form" :rules="createDialog.rules" label-width="100px">
        <el-form-item label="版本标签" prop="versionTag">
          <el-input v-model="createDialog.form.versionTag" placeholder="如：v2.0" />
        </el-form-item>
        <el-form-item label="版本名称" prop="versionName">
          <el-input v-model="createDialog.form.versionName" placeholder="请输入版本名称" />
        </el-form-item>
        <el-form-item label="版本描述">
          <el-input 
            v-model="createDialog.form.versionDescription" 
            type="textarea" 
            :rows="3"
            placeholder="请输入版本描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="createDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitCreateForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 发布版本对话框 -->
    <el-dialog title="发布版本" v-model="publishDialog.visible" width="600px">
      <el-form ref="publishFormRef" :model="publishDialog.form" :rules="publishDialog.rules" label-width="100px">
        <el-form-item label="发布策略" prop="publishStrategy">
          <el-radio-group v-model="publishDialog.form.publishStrategy">
            <el-radio :label="0">全量发布</el-radio>
            <el-radio :label="1">灰度发布</el-radio>
            <el-radio :label="2">A/B测试</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item 
          v-if="publishDialog.form.publishStrategy === 1" 
          label="流量比例" 
          prop="trafficRatio"
        >
          <el-slider 
            v-model="publishDialog.form.trafficRatio" 
            :min="1" 
            :max="100" 
            show-input
            :format-tooltip="(val) => val + '%'"
          />
        </el-form-item>
        <el-form-item 
          v-if="publishDialog.form.publishStrategy === 2" 
          label="目标用户" 
          prop="targetUsers"
        >
          <el-input 
            v-model="publishDialog.form.targetUsers" 
            type="textarea" 
            :rows="3"
            placeholder="请输入目标用户ID，多个用逗号分隔"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="publishDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitPublishForm">确定发布</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 路由测试对话框 -->
    <el-dialog title="版本路由测试" v-model="routeTestDialog.visible" width="700px">
      <el-form :model="routeTestDialog.form" label-width="100px">
        <el-form-item label="用户ID">
          <el-input v-model="routeTestDialog.form.userId" placeholder="请输入测试用户ID" />
        </el-form-item>
        <el-form-item label="用户组">
          <el-input v-model="routeTestDialog.form.userGroups" placeholder="请输入用户组，多个用逗号分隔" />
        </el-form-item>
        <el-form-item label="部门">
          <el-input v-model="routeTestDialog.form.department" placeholder="请输入部门" />
        </el-form-item>
        <el-form-item label="测试结果">
          <el-input
            v-model="routeTestDialog.result"
            type="textarea"
            :rows="4"
            readonly
            placeholder="点击测试按钮查看路由结果"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="routeTestDialog.visible = false">关闭</el-button>
          <el-button type="primary" @click="executeRouteTest">执行测试</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 版本比较对话框 -->
    <el-dialog title="版本比较" v-model="compareDialog.visible" width="900px">
      <div class="version-compare">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card>
              <template #header>
                <span>源版本: {{ compareDialog.sourceVersion?.versionTag }}</span>
              </template>
              <div class="version-info">
                <p><strong>版本名称:</strong> {{ compareDialog.sourceVersion?.versionName }}</p>
                <p><strong>发布时间:</strong> {{ parseTime(compareDialog.sourceVersion?.publishTime) }}</p>
                <p><strong>运行实例:</strong> {{ compareDialog.sourceVersion?.runningInstanceCount }}</p>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card>
              <template #header>
                <span>目标版本: {{ compareDialog.targetVersion?.versionTag }}</span>
              </template>
              <div class="version-info">
                <p><strong>版本名称:</strong> {{ compareDialog.targetVersion?.versionName }}</p>
                <p><strong>发布时间:</strong> {{ parseTime(compareDialog.targetVersion?.publishTime) }}</p>
                <p><strong>运行实例:</strong> {{ compareDialog.targetVersion?.runningInstanceCount }}</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-divider>差异对比</el-divider>
        <el-table :data="compareDialog.differences" style="width: 100%">
          <el-table-column prop="field" label="字段" width="150" />
          <el-table-column prop="sourceValue" label="源版本值" />
          <el-table-column prop="targetValue" label="目标版本值" />
          <el-table-column prop="changeType" label="变更类型" width="100">
            <template #default="scope">
              <el-tag :type="getChangeTypeColor(scope.row.changeType)">
                {{ scope.row.changeType }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="compareDialog.visible = false">关闭</el-button>
          <el-button type="primary" @click="exportComparison">导出对比报告</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 版本详情对话框 -->
    <el-dialog title="版本详情" v-model="detailDialog.visible" width="800px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="版本ID">{{ detailDialog.version?.versionId }}</el-descriptions-item>
        <el-descriptions-item label="版本标签">{{ detailDialog.version?.versionTag }}</el-descriptions-item>
        <el-descriptions-item label="版本名称">{{ detailDialog.version?.versionName }}</el-descriptions-item>
        <el-descriptions-item label="流程定义Key">{{ detailDialog.version?.processDefinitionKey }}</el-descriptions-item>
        <el-descriptions-item label="发布状态">
          <el-tag :type="getStatusType(detailDialog.version?.publishStatus)">
            {{ getStatusText(detailDialog.version?.publishStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="发布策略">{{ getStrategyText(detailDialog.version?.publishStrategy) }}</el-descriptions-item>
        <el-descriptions-item label="运行实例">{{ detailDialog.version?.runningInstanceCount || 0 }}</el-descriptions-item>
        <el-descriptions-item label="完成实例">{{ detailDialog.version?.completedInstanceCount || 0 }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ detailDialog.version?.createBy }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailDialog.version?.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="发布时间">{{ parseTime(detailDialog.version?.publishTime) }}</el-descriptions-item>
        <el-descriptions-item label="版本描述" :span="2">{{ detailDialog.version?.versionDescription || '无' }}</el-descriptions-item>
      </el-descriptions>

      <el-divider>路由配置</el-divider>
      <el-table :data="detailDialog.routeRules" style="width: 100%">
        <el-table-column prop="ruleType" label="规则类型" width="120" />
        <el-table-column prop="condition" label="条件" />
        <el-table-column prop="target" label="目标" width="150" />
        <el-table-column prop="priority" label="优先级" width="80" />
      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialog.visible = false">关闭</el-button>
          <el-button type="primary" @click="downloadVersionConfig">下载配置</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { 
  listProcessVersion,
  createVersion,
  publishVersion,
  deprecateVersion,
  rollbackVersion,
  getVersionStatistics,
  testVersionRoute
} from '@/api/workflow/version'
import { listProcessDefinition } from '@/api/workflow/processDefinition'

const { proxy } = getCurrentInstance() as any
const { parseTime } = proxy

const loading = ref(false)
const selectedProcessKey = ref('')
const selectedProcessName = ref('')
const processList = ref([])
const versionList = ref([])
const statistics = ref({})

// 创建版本对话框
const createDialog = reactive({
  visible: false,
  form: {
    versionTag: '',
    versionName: '',
    versionDescription: ''
  },
  rules: {
    versionTag: [
      { required: true, message: '版本标签不能为空', trigger: 'blur' }
    ],
    versionName: [
      { required: true, message: '版本名称不能为空', trigger: 'blur' }
    ]
  }
})

// 发布版本对话框
const publishDialog = reactive({
  visible: false,
  versionId: null,
  form: {
    publishStrategy: 0,
    trafficRatio: 10,
    targetUsers: ''
  },
  rules: {
    publishStrategy: [
      { required: true, message: '请选择发布策略', trigger: 'change' }
    ]
  }
})

// 路由测试对话框
const routeTestDialog = reactive({
  visible: false,
  form: {
    userId: '',
    userGroups: '',
    department: ''
  },
  result: ''
})

// 版本比较对话框
const compareDialog = reactive({
  visible: false,
  sourceVersion: null,
  targetVersion: null,
  differences: []
})

// 版本详情对话框
const detailDialog = reactive({
  visible: false,
  version: null,
  routeRules: []
})

/** 初始化 */
onMounted(() => {
  getProcessList()
})

/** 获取流程列表 */
const getProcessList = () => {
  listProcessDefinition({}).then(response => {
    processList.value = response.rows || []
  })
}

/** 流程变更处理 */
const handleProcessChange = () => {
  const selectedProcess = processList.value.find(p => p.key === selectedProcessKey.value)
  selectedProcessName.value = selectedProcess?.name || ''
  getVersionList()
  getStatistics()
}

/** 获取版本列表 */
const getVersionList = () => {
  if (!selectedProcessKey.value) return
  
  loading.value = true
  listProcessVersion(selectedProcessKey.value).then(response => {
    versionList.value = response.rows || []
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}

/** 获取统计信息 */
const getStatistics = () => {
  if (!selectedProcessKey.value) return
  
  getVersionStatistics(selectedProcessKey.value).then(response => {
    statistics.value = response.data || {}
  })
}

/** 刷新版本 */
const refreshVersions = () => {
  getVersionList()
  getStatistics()
}

/** 创建版本 */
const handleCreateVersion = () => {
  createDialog.visible = true
}

/** 提交创建表单 */
const submitCreateForm = () => {
  proxy.$refs.createFormRef.validate((valid: boolean) => {
    if (valid) {
      const data = {
        processDefinitionKey: selectedProcessKey.value,
        ...createDialog.form
      }
      createVersion(data).then(() => {
        proxy.$modal.msgSuccess('创建成功')
        createDialog.visible = false
        getVersionList()
      })
    }
  })
}

/** 发布版本 */
const handlePublish = (row: any) => {
  publishDialog.versionId = row.versionId
  publishDialog.visible = true
}

/** 提交发布表单 */
const submitPublishForm = () => {
  publishVersion(publishDialog.versionId, publishDialog.form).then(() => {
    proxy.$modal.msgSuccess('发布成功')
    publishDialog.visible = false
    getVersionList()
    getStatistics()
  })
}

/** 停用版本 */
const handleDeprecate = (row: any) => {
  proxy.$modal.confirm('确认要停用版本"' + row.versionTag + '"吗？').then(() => {
    deprecateVersion(row.versionId).then(() => {
      proxy.$modal.msgSuccess('停用成功')
      getVersionList()
      getStatistics()
    })
  })
}

/** 版本回滚 */
const handleRollback = (row: any) => {
  proxy.$modal.confirm('确认要回滚到版本"' + row.versionTag + '"吗？').then(() => {
    const data = {
      processDefinitionKey: selectedProcessKey.value,
      targetVersionId: row.versionId
    }
    rollbackVersion(data).then(() => {
      proxy.$modal.msgSuccess('回滚成功')
      getVersionList()
      getStatistics()
    })
  })
}

/** 配置路由 */
const handleConfigRoute = (row: any) => {
  proxy.$modal.msgSuccess('路由配置功能开发中...')
}

/** 查看详情 */
const handleViewDetails = (row: any) => {
  detailDialog.version = row
  detailDialog.routeRules = JSON.parse(row.routeRules || '[]')
  detailDialog.visible = true
}

/** 路由测试 */
const handleRouteTest = () => {
  routeTestDialog.visible = true
  routeTestDialog.result = ''
}

/** 执行路由测试 */
const executeRouteTest = () => {
  const data = {
    processDefinitionKey: selectedProcessKey.value,
    userId: routeTestDialog.form.userId,
    userGroups: routeTestDialog.form.userGroups ? routeTestDialog.form.userGroups.split(',') : [],
    department: routeTestDialog.form.department,
    context: {}
  }
  
  testVersionRoute(data).then(response => {
    routeTestDialog.result = `路由结果：${response.data || '默认版本'}`
  }).catch(error => {
    routeTestDialog.result = `路由失败：${error.message}`
  })
}

/** 获取状态类型 */
const getStatusType = (status: number) => {
  const typeMap = { 0: 'info', 1: 'success', 2: 'warning', 3: 'danger' }
  return typeMap[status] || 'info'
}

/** 获取状态文本 */
const getStatusText = (status: number) => {
  const textMap = { 0: '草稿', 1: '已发布', 2: '已停用', 3: '已归档' }
  return textMap[status] || '未知'
}

/** 获取策略文本 */
const getStrategyText = (strategy: number) => {
  const textMap = { 0: '全量发布', 1: '灰度发布', 2: 'A/B测试' }
  return textMap[strategy] || '未知'
}

/** 版本比较 */
const handleCompareVersions = () => {
  if (versionList.value.length < 2) {
    proxy.$modal.msgWarning('至少需要两个版本才能进行比较')
    return
  }

  // 选择最新的两个版本进行比较
  const sortedVersions = [...versionList.value].sort((a, b) =>
    new Date(b.createTime).getTime() - new Date(a.createTime).getTime()
  )

  compareDialog.sourceVersion = sortedVersions[1]
  compareDialog.targetVersion = sortedVersions[0]

  // 模拟差异数据
  compareDialog.differences = [
    {
      field: '发布状态',
      sourceValue: getStatusText(compareDialog.sourceVersion.publishStatus),
      targetValue: getStatusText(compareDialog.targetVersion.publishStatus),
      changeType: '修改'
    },
    {
      field: '运行实例数',
      sourceValue: compareDialog.sourceVersion.runningInstanceCount || 0,
      targetValue: compareDialog.targetVersion.runningInstanceCount || 0,
      changeType: '修改'
    }
  ]

  compareDialog.visible = true
}

/** 获取变更类型颜色 */
const getChangeTypeColor = (changeType: string) => {
  const colorMap = { '新增': 'success', '修改': 'warning', '删除': 'danger' }
  return colorMap[changeType] || 'info'
}

/** 导出对比报告 */
const exportComparison = () => {
  proxy.$modal.msgSuccess('对比报告导出功能开发中...')
}

/** 下载版本配置 */
const downloadVersionConfig = () => {
  const config = {
    version: detailDialog.version,
    routeRules: detailDialog.routeRules
  }

  const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `version-${detailDialog.version.versionTag}-config.json`
  a.click()
  URL.revokeObjectURL(url)
}

/** 批量操作 */
const handleBatchOperation = (operation: string) => {
  proxy.$modal.msgSuccess(`批量${operation}功能开发中...`)
}

/** 版本回滚确认 */
const confirmRollback = (row: any) => {
  proxy.$modal.confirm(
    `确认要回滚到版本"${row.versionTag}"吗？此操作将影响正在运行的流程实例。`,
    '版本回滚确认',
    {
      confirmButtonText: '确认回滚',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    handleRollback(row)
  })
}

/** 版本克隆 */
const handleCloneVersion = (row: any) => {
  createDialog.form.versionTag = `${row.versionTag}-copy`
  createDialog.form.versionName = `${row.versionName} (副本)`
  createDialog.form.versionDescription = `基于 ${row.versionTag} 创建的副本`
  createDialog.visible = true
}
</script>

<style scoped>
.process-selector {
  display: flex;
  align-items: center;
}

.statistic-item {
  text-align: center;
}

.statistic-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.statistic-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-4 {
  margin-bottom: 16px;
}

.ml-1 {
  margin-left: 4px;
}
</style>
