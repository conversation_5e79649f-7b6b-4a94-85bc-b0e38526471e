-- 正确的菜单权限配置SQL
-- 基于现有前端路由和页面结构创建
-- 执行前请先清空现有菜单数据

-- 清空现有数据（可选，谨慎使用）
DELETE FROM sys_role_menu WHERE role_id > 0;
DELETE FROM sys_menu WHERE menu_id > 0;

-- 重置自增ID
ALTER TABLE sys_menu AUTO_INCREMENT = 1;

-- 插入系统菜单数据
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES

-- 一级菜单
(1, '个人工作台', 0, 1, 'workspace', 'workspace/index', '', '1', '0', 'C', '0', '0', 'workspace:view', 'monitor', 'admin', NOW(), '', NULL, '个人工作台菜单'),

-- 系统管理
(100, '系统管理', 0, 2, 'system', NULL, '', '1', '0', 'M', '0', '0', '', 'system', 'admin', NOW(), '', NULL, '系统管理目录'),
(101, '用户管理', 100, 1, 'user', 'system/user/index', '', '1', '0', 'C', '0', '0', 'system:user:list', 'user', 'admin', NOW(), '', NULL, '用户管理菜单'),
(102, '角色管理', 100, 2, 'role', 'system/role/index', '', '1', '0', 'C', '0', '0', 'system:role:list', 'peoples', 'admin', NOW(), '', NULL, '角色管理菜单'),
(103, '菜单管理', 100, 3, 'menu', 'system/menu/index', '', '1', '0', 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', NOW(), '', NULL, '菜单管理菜单'),
(104, '部门管理', 100, 4, 'dept', 'system/dept/index', '', '1', '0', 'C', '0', '0', 'system:dept:list', 'tree', 'admin', NOW(), '', NULL, '部门管理菜单'),
(105, '岗位管理', 100, 5, 'post', 'system/post/index', '', '1', '0', 'C', '0', '0', 'system:post:list', 'post', 'admin', NOW(), '', NULL, '岗位管理菜单'),
(106, '通知公告', 100, 6, 'notice', 'system/notice/index', '', '1', '0', 'C', '0', '0', 'system:notice:list', 'bell', 'admin', NOW(), '', NULL, '通知公告菜单'),
(107, '待办事项', 100, 7, 'todo', 'system/todo/index', '', '1', '0', 'C', '0', '0', 'system:todo:list', 'tickets', 'admin', NOW(), '', NULL, '待办事项菜单'),
(108, '站内消息', 100, 8, 'message', 'system/message/index', '', '1', '0', 'C', '0', '0', 'system:message:list', 'message', 'admin', NOW(), '', NULL, '站内消息菜单'),

-- 工作流管理
(200, '工作流管理', 0, 3, 'workflow', NULL, '', '1', '0', 'M', '0', '0', '', 'workflow', 'admin', NOW(), '', NULL, '工作流管理目录'),
(201, '流程定义', 200, 1, 'definition', 'workflow/definition/index', '', '1', '0', 'C', '0', '0', 'workflow:definition:list', 'tree-table', 'admin', NOW(), '', NULL, '流程定义菜单'),
(202, '流程实例', 200, 2, 'instance', 'workflow/instance/index', '', '1', '0', 'C', '0', '0', 'workflow:instance:list', 'list', 'admin', NOW(), '', NULL, '流程实例菜单'),
(203, '任务管理', 200, 3, 'task', 'workflow/task/index', '', '1', '0', 'C', '0', '0', 'workflow:task:list', 'skill', 'admin', NOW(), '', NULL, '任务管理菜单'),
(204, '流程设计器', 200, 4, 'designer', 'workflow/designer/index', '', '1', '0', 'C', '0', '0', 'workflow:designer:view', 'build', 'admin', NOW(), '', NULL, '流程设计器菜单'),
(205, '可视化设计器', 200, 5, 'simple-designer', 'workflow/designer/simple', '', '1', '0', 'C', '0', '0', 'workflow:simple:view', 'magic-stick', 'admin', NOW(), '', NULL, '可视化设计器菜单'),
(206, '版本管理', 200, 6, 'version', 'workflow/version/index', '', '1', '0', 'C', '0', '0', 'workflow:version:list', 'collection-tag', 'admin', NOW(), '', NULL, '版本管理菜单'),
(207, '监控大屏', 200, 7, 'monitor', 'workflow/monitor/dashboard', '', '1', '0', 'C', '0', '0', 'workflow:monitor:view', 'monitor', 'admin', NOW(), '', NULL, '监控大屏菜单'),

-- 项目管理（动态路由）
(300, '项目管理', 0, 4, 'project', NULL, '', '1', '0', 'M', '0', '0', '', 'project', 'admin', NOW(), '', NULL, '项目管理目录'),
(301, '横向项目', 300, 1, 'horizontal', 'project/horizontal/index', '', '1', '0', 'C', '0', '0', 'project:horizontal:list', 'horizontal-project', 'admin', NOW(), '', NULL, '横向项目菜单'),
(302, '合同管理', 300, 2, 'contract', 'project/contract/index', '', '1', '0', 'C', '0', '0', 'project:contract:list', 'contract', 'admin', NOW(), '', NULL, '合同管理菜单'),
(303, '合作单位', 300, 3, 'partner', 'project/partner/index', '', '1', '0', 'C', '0', '0', 'project:partner:list', 'partner', 'admin', NOW(), '', NULL, '合作单位菜单'),

-- 用户管理按钮权限
(1001, '用户查询', 101, 1, '', '', '', '1', '0', 'F', '0', '0', 'system:user:query', '#', 'admin', NOW(), '', NULL, ''),
(1002, '用户新增', 101, 2, '', '', '', '1', '0', 'F', '0', '0', 'system:user:add', '#', 'admin', NOW(), '', NULL, ''),
(1003, '用户修改', 101, 3, '', '', '', '1', '0', 'F', '0', '0', 'system:user:edit', '#', 'admin', NOW(), '', NULL, ''),
(1004, '用户删除', 101, 4, '', '', '', '1', '0', 'F', '0', '0', 'system:user:remove', '#', 'admin', NOW(), '', NULL, ''),
(1005, '用户导出', 101, 5, '', '', '', '1', '0', 'F', '0', '0', 'system:user:export', '#', 'admin', NOW(), '', NULL, ''),
(1006, '用户导入', 101, 6, '', '', '', '1', '0', 'F', '0', '0', 'system:user:import', '#', 'admin', NOW(), '', NULL, ''),
(1007, '重置密码', 101, 7, '', '', '', '1', '0', 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', NOW(), '', NULL, ''),

-- 角色管理按钮权限
(1008, '角色查询', 102, 1, '', '', '', '1', '0', 'F', '0', '0', 'system:role:query', '#', 'admin', NOW(), '', NULL, ''),
(1009, '角色新增', 102, 2, '', '', '', '1', '0', 'F', '0', '0', 'system:role:add', '#', 'admin', NOW(), '', NULL, ''),
(1010, '角色修改', 102, 3, '', '', '', '1', '0', 'F', '0', '0', 'system:role:edit', '#', 'admin', NOW(), '', NULL, ''),
(1011, '角色删除', 102, 4, '', '', '', '1', '0', 'F', '0', '0', 'system:role:remove', '#', 'admin', NOW(), '', NULL, ''),
(1012, '角色导出', 102, 5, '', '', '', '1', '0', 'F', '0', '0', 'system:role:export', '#', 'admin', NOW(), '', NULL, ''),

-- 菜单管理按钮权限
(1013, '菜单查询', 103, 1, '', '', '', '1', '0', 'F', '0', '0', 'system:menu:query', '#', 'admin', NOW(), '', NULL, ''),
(1014, '菜单新增', 103, 2, '', '', '', '1', '0', 'F', '0', '0', 'system:menu:add', '#', 'admin', NOW(), '', NULL, ''),
(1015, '菜单修改', 103, 3, '', '', '', '1', '0', 'F', '0', '0', 'system:menu:edit', '#', 'admin', NOW(), '', NULL, ''),
(1016, '菜单删除', 103, 4, '', '', '', '1', '0', 'F', '0', '0', 'system:menu:remove', '#', 'admin', NOW(), '', NULL, ''),

-- 部门管理按钮权限
(1017, '部门查询', 104, 1, '', '', '', '1', '0', 'F', '0', '0', 'system:dept:query', '#', 'admin', NOW(), '', NULL, ''),
(1018, '部门新增', 104, 2, '', '', '', '1', '0', 'F', '0', '0', 'system:dept:add', '#', 'admin', NOW(), '', NULL, ''),
(1019, '部门修改', 104, 3, '', '', '', '1', '0', 'F', '0', '0', 'system:dept:edit', '#', 'admin', NOW(), '', NULL, ''),
(1020, '部门删除', 104, 4, '', '', '', '1', '0', 'F', '0', '0', 'system:dept:remove', '#', 'admin', NOW(), '', NULL, '');

-- 插入角色数据
INSERT INTO sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES
(1, '超级管理员', 'admin', 1, '1', '1', '1', '0', '0', 'admin', NOW(), '', NULL, '超级管理员'),
(2, '科研管理员', 'research_admin', 2, '2', '1', '1', '0', '0', 'admin', NOW(), '', NULL, '科研管理员'),
(3, '项目经理', 'project_manager', 3, '3', '1', '1', '0', '0', 'admin', NOW(), '', NULL, '项目经理'),
(4, '普通用户', 'common_user', 4, '4', '1', '1', '0', '0', 'admin', NOW(), '', NULL, '普通用户');

-- 为超级管理员角色分配所有菜单权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
-- 一级菜单
(1, 1),   -- 个人工作台
(1, 100), -- 系统管理
(1, 200), -- 工作流管理
(1, 300), -- 项目管理

-- 系统管理子菜单
(1, 101), (1, 102), (1, 103), (1, 104), (1, 105), (1, 106), (1, 107), (1, 108),

-- 工作流管理子菜单
(1, 201), (1, 202), (1, 203), (1, 204), (1, 205), (1, 206), (1, 207),

-- 项目管理子菜单
(1, 301), (1, 302), (1, 303),

-- 用户管理权限
(1, 1001), (1, 1002), (1, 1003), (1, 1004), (1, 1005), (1, 1006), (1, 1007),

-- 角色管理权限
(1, 1008), (1, 1009), (1, 1010), (1, 1011), (1, 1012),

-- 菜单管理权限
(1, 1013), (1, 1014), (1, 1015), (1, 1016),

-- 部门管理权限
(1, 1017), (1, 1018), (1, 1019), (1, 1020);

-- 为科研管理员角色分配部分菜单权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(2, 1),   -- 个人工作台
(2, 100), -- 系统管理
(2, 200), -- 工作流管理
(2, 101), -- 用户管理
(2, 104), -- 部门管理
(2, 106), -- 通知公告
(2, 107), -- 待办事项
(2, 108), -- 站内消息
(2, 201), -- 流程定义
(2, 202), -- 流程实例
(2, 203), -- 任务管理
(2, 1001), (2, 1002), (2, 1003), -- 用户管理部分权限
(2, 1017), (2, 1018), (2, 1019); -- 部门管理部分权限

-- 为项目经理角色分配项目相关权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(3, 1),   -- 个人工作台
(3, 300), -- 项目管理
(3, 200), -- 工作流管理
(3, 301), -- 横向项目
(3, 302), -- 合同管理
(3, 303), -- 合作单位
(3, 201), -- 流程定义
(3, 202), -- 流程实例
(3, 203); -- 任务管理

-- 为普通用户角色分配基础权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(4, 1),   -- 个人工作台
(4, 106), -- 通知公告
(4, 107), -- 待办事项
(4, 108); -- 站内消息

-- 插入部门数据
INSERT INTO sys_dept (dept_id, parent_id, ancestors, dept_name, order_num, leader, phone, email, status, del_flag, create_by, create_time, update_by, update_time) VALUES
(100, 0, '0', '科研院', 0, '院长', '15888888888', '<EMAIL>', '0', '0', 'admin', NOW(), '', NULL),
(101, 100, '0,100', '研发部', 1, '研发经理', '15888888888', '<EMAIL>', '0', '0', 'admin', NOW(), '', NULL),
(102, 100, '0,100', '管理部', 2, '管理经理', '15888888888', '<EMAIL>', '0', '0', 'admin', NOW(), '', NULL),
(103, 100, '0,100', '项目部', 3, '项目经理', '15888888888', '<EMAIL>', '0', '0', 'admin', NOW(), '', NULL),
(104, 101, '0,100,101', '前端组', 1, '前端组长', '15888888888', '<EMAIL>', '0', '0', 'admin', NOW(), '', NULL),
(105, 101, '0,100,101', '后端组', 2, '后端组长', '15888888888', '<EMAIL>', '0', '0', 'admin', NOW(), '', NULL);

-- 插入用户数据（密码为123456，已加密）
INSERT INTO sys_user (user_id, dept_id, user_name, nick_name, user_type, email, phonenumber, sex, avatar, password, status, del_flag, login_ip, login_date, create_by, create_time, update_by, update_time, remark) VALUES
(1, 100, 'admin', '管理员', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$7JB720yubVSOfvam/l0rFOaHHgWw.TjeyWLjs5wqHWPacMp/iRjLa', '0', '0', '127.0.0.1', NOW(), 'admin', NOW(), '', NULL, '管理员'),
(2, 101, 'research', '科研员', '00', '<EMAIL>', '15666666666', '1', '', '$2a$10$7JB720yubVSOfvam/l0rFOaHHgWw.TjeyWLjs5wqHWPacMp/iRjLa', '0', '0', '127.0.0.1', NOW(), 'admin', NOW(), '', NULL, '科研员'),
(3, 103, 'project', '项目经理', '00', '<EMAIL>', '15777777777', '1', '', '$2a$10$7JB720yubVSOfvam/l0rFOaHHgWw.TjeyWLjs5wqHWPacMp/iRjLa', '0', '0', '127.0.0.1', NOW(), 'admin', NOW(), '', NULL, '项目经理'),
(4, 104, 'user', '普通用户', '00', '<EMAIL>', '15999999999', '1', '', '$2a$10$7JB720yubVSOfvam/l0rFOaHHgWw.TjeyWLjs5wqHWPacMp/iRjLa', '0', '0', '127.0.0.1', NOW(), 'admin', NOW(), '', NULL, '普通用户');

-- 为用户分配角色
INSERT INTO sys_user_role (user_id, role_id) VALUES
(1, 1), -- admin用户分配超级管理员角色
(2, 2), -- research用户分配科研管理员角色
(3, 3), -- project用户分配项目经理角色
(4, 4); -- user用户分配普通用户角色

-- 提交事务
COMMIT;
