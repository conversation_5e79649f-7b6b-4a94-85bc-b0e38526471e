package com.research.workflow.domain;

import com.research.common.annotation.Excel;
import com.research.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 流程实例对象 wf_process_instance
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProcessInstance extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 流程实例ID */
    @Excel(name = "流程实例ID")
    private String processInstanceId;

    /** 流程定义ID */
    @Excel(name = "流程定义ID")
    private String processDefinitionId;

    /** 流程定义Key */
    @Excel(name = "流程定义Key")
    private String processDefinitionKey;

    /** 流程定义名称 */
    @Excel(name = "流程定义名称")
    private String processDefinitionName;

    /** 流程定义版本 */
    @Excel(name = "流程定义版本")
    private Integer processDefinitionVersion;

    /** 业务Key */
    @Excel(name = "业务Key")
    private String businessKey;

    /** 流程实例名称 */
    @Excel(name = "流程实例名称")
    private String processInstanceName;

    /** 启动用户ID */
    @Excel(name = "启动用户ID")
    private String startUserId;

    /** 启动用户名称 */
    @Excel(name = "启动用户名称")
    private String startUserName;

    /** 启动时间 */
    @Excel(name = "启动时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date startTime;

    /** 结束时间 */
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date endTime;

    /** 流程状态 */
    @Excel(name = "流程状态", readConverterExp = "0=草稿,1=运行中,2=已完成,3=已挂起,4=已终止")
    private Integer status;

    /** 是否挂起 */
    @Excel(name = "是否挂起", readConverterExp = "0=否,1=是")
    private Boolean suspended;

    /** 是否已结束 */
    @Excel(name = "是否已结束", readConverterExp = "0=否,1=是")
    private Boolean ended;

    /** 当前节点ID */
    @Excel(name = "当前节点ID")
    private String currentActivityId;

    /** 当前节点名称 */
    @Excel(name = "当前节点名称")
    private String currentActivityName;

    /** 当前处理人ID */
    @Excel(name = "当前处理人ID")
    private String currentAssignee;

    /** 当前处理人名称 */
    @Excel(name = "当前处理人名称")
    private String currentAssigneeName;

    /** 流程变量JSON */
    private String variablesJson;

    /** 流程描述 */
    @Excel(name = "流程描述")
    private String description;

    /** 删除原因 */
    @Excel(name = "删除原因")
    private String deleteReason;

    /** 租户ID */
    @Excel(name = "租户ID")
    private String tenantId;

    /** 部署ID */
    @Excel(name = "部署ID")
    private String deploymentId;

    /** 父流程实例ID */
    @Excel(name = "父流程实例ID")
    private String superProcessInstanceId;

    /** 根流程实例ID */
    @Excel(name = "根流程实例ID")
    private String rootProcessInstanceId;

    /** 流程持续时间(毫秒) */
    @Excel(name = "流程持续时间")
    private Long duration;

    /** 流程优先级 */
    @Excel(name = "流程优先级")
    private Integer priority;

    /** 流程分类 */
    @Excel(name = "流程分类")
    private String category;

    /** 流程标签 */
    @Excel(name = "流程标签")
    private String tags;

    /** 业务状态 */
    @Excel(name = "业务状态")
    private String businessStatus;

    /** 业务类型 */
    @Excel(name = "业务类型")
    private String businessType;

    /** 申请人ID */
    @Excel(name = "申请人ID")
    private String applicantId;

    /** 申请人姓名 */
    @Excel(name = "申请人姓名")
    private String applicantName;

    /** 申请部门ID */
    @Excel(name = "申请部门ID")
    private String applicantDeptId;

    /** 申请部门名称 */
    @Excel(name = "申请部门名称")
    private String applicantDeptName;

    /** 申请时间 */
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date applicationTime;

    /** 预期完成时间 */
    @Excel(name = "预期完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date expectedEndTime;

    /** 实际完成时间 */
    @Excel(name = "实际完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date actualEndTime;

    /** 是否超时 */
    @Excel(name = "是否超时", readConverterExp = "0=否,1=是")
    private Boolean overtime;

    /** 超时时长(分钟) */
    @Excel(name = "超时时长")
    private Long overtimeMinutes;

    /** 流程图片 */
    private String processImage;

    /** 表单数据JSON */
    private String formDataJson;

    /** 附件信息JSON */
    private String attachmentJson;

    /** 审批意见 */
    @Excel(name = "审批意见")
    private String approvalComment;

    /** 流程结果 */
    @Excel(name = "流程结果")
    private String processResult;

    /** 流程评分 */
    @Excel(name = "流程评分")
    private Integer rating;

    /** 流程评价 */
    @Excel(name = "流程评价")
    private String evaluation;

    // 手动添加getter和setter方法以解决Lombok编译问题
    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public String getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(String processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }

    public String getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    public void setProcessDefinitionKey(String processDefinitionKey) {
        this.processDefinitionKey = processDefinitionKey;
    }

    public String getProcessDefinitionName() {
        return processDefinitionName;
    }

    public void setProcessDefinitionName(String processDefinitionName) {
        this.processDefinitionName = processDefinitionName;
    }

    public Integer getProcessDefinitionVersion() {
        return processDefinitionVersion;
    }

    public void setProcessDefinitionVersion(Integer processDefinitionVersion) {
        this.processDefinitionVersion = processDefinitionVersion;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getStartUserId() {
        return startUserId;
    }

    public void setStartUserId(String startUserId) {
        this.startUserId = startUserId;
    }

    public String getStartUserName() {
        return startUserName;
    }

    public void setStartUserName(String startUserName) {
        this.startUserName = startUserName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Boolean getSuspended() {
        return suspended;
    }

    public void setSuspended(Boolean suspended) {
        this.suspended = suspended;
    }

    public Boolean getEnded() {
        return ended;
    }

    public void setEnded(Boolean ended) {
        this.ended = ended;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public java.util.Date getStartTime() {
        return startTime;
    }

    public void setStartTime(java.util.Date startTime) {
        this.startTime = startTime;
    }

    public java.util.Date getEndTime() {
        return endTime;
    }

    public void setEndTime(java.util.Date endTime) {
        this.endTime = endTime;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public String getDeleteReason() {
        return deleteReason;
    }

    public void setDeleteReason(String deleteReason) {
        this.deleteReason = deleteReason;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
