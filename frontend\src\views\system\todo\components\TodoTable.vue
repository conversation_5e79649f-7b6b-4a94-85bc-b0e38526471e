<template>
  <el-table v-loading="loading" :data="data" @selection-change="handleSelectionChange">
    <el-table-column type="selection" width="55" align="center" />
    <el-table-column label="序号" type="index" width="80" align="center" />
    <el-table-column label="待办标题" prop="title" :show-overflow-tooltip="true">
      <template #default="scope">
        <div class="todo-title">
          <el-tag v-if="scope.row.priority === '4'" type="danger" size="small">紧急</el-tag>
          <el-tag v-else-if="scope.row.priority === '3'" type="warning" size="small">高</el-tag>
          <span class="title-text" @click="$emit('view', scope.row)">{{ scope.row.title }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="优先级" align="center" prop="priority" width="100">
      <template #default="scope">
        <el-tag :type="getPriorityType(scope.row.priority)" size="small">
          {{ getPriorityText(scope.row.priority) }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column label="状态" align="center" prop="status" width="100">
      <template #default="scope">
        <el-tag :type="getStatusType(scope.row.status)" size="small">
          {{ getStatusText(scope.row.status) }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column label="负责人" align="center" prop="assigneeName" width="120" />
    <el-table-column label="创建人" align="center" prop="creatorName" width="120" />
    <el-table-column label="截止时间" align="center" prop="dueTime" width="180">
      <template #default="scope">
        <span :class="{ 'overdue': isOverdue(scope.row.dueTime) }">
          {{ formatTime(scope.row.dueTime) }}
        </span>
      </template>
    </el-table-column>
    <el-table-column label="创建时间" align="center" prop="createTime" width="180">
      <template #default="scope">
        <span>{{ formatTime(scope.row.createTime) }}</span>
      </template>
    </el-table-column>
    <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
      <template #default="scope">
        <el-button link type="primary" @click="$emit('view', scope.row)">
          <el-icon><View /></el-icon>查看
        </el-button>
        <el-button link type="primary" @click="$emit('edit', scope.row)">
          <el-icon><Edit /></el-icon>编辑
        </el-button>
        <el-button 
          v-if="scope.row.status === '0'" 
          link 
          type="success" 
          @click="$emit('assign', scope.row)"
        >
          <el-icon><User /></el-icon>分配
        </el-button>
        <el-button 
          v-if="scope.row.status === '0' || scope.row.status === '1'" 
          link 
          type="warning" 
          @click="$emit('process', scope.row)"
        >
          <el-icon><Operation /></el-icon>处理
        </el-button>
        <el-button 
          v-if="scope.row.status === '0' || scope.row.status === '1'" 
          link 
          type="success" 
          @click="$emit('complete', scope.row)"
        >
          <el-icon><Check /></el-icon>完成
        </el-button>
        <el-button 
          v-if="scope.row.status === '0' || scope.row.status === '1'" 
          link 
          type="info" 
          @click="$emit('cancel', scope.row)"
        >
          <el-icon><Close /></el-icon>取消
        </el-button>
        <el-button 
          v-if="scope.row.status === '2' || scope.row.status === '3'" 
          link 
          type="primary" 
          @click="$emit('reopen', scope.row)"
        >
          <el-icon><RefreshRight /></el-icon>重开
        </el-button>
        <el-button link type="danger" @click="$emit('delete', scope.row)">
          <el-icon><Delete /></el-icon>删除
        </el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import { 
  View, Edit, User, Operation, Check, Close, RefreshRight, Delete 
} from '@element-plus/icons-vue'

// Props
interface Props {
  data: any[]
  loading: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits([
  'selection-change', 'view', 'edit', 'delete', 'assign', 
  'process', 'complete', 'cancel', 'reopen'
])

// 多选框选中数据
const handleSelectionChange = (selection: any) => {
  emit('selection-change', selection)
}

// 获取优先级类型
const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    '1': 'info',    // 低
    '2': '',        // 中
    '3': 'warning', // 高
    '4': 'danger'   // 紧急
  }
  return types[priority] || ''
}

// 获取优先级文本
const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    '1': '低',
    '2': '中',
    '3': '高',
    '4': '紧急'
  }
  return texts[priority] || '未知'
}

// 获取状态类型
const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    '0': 'warning',  // 待处理
    '1': 'primary',  // 处理中
    '2': 'success',  // 已完成
    '3': 'info'      // 已取消
  }
  return types[status] || ''
}

// 获取状态文本
const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    '0': '待处理',
    '1': '处理中',
    '2': '已完成',
    '3': '已取消'
  }
  return texts[status] || '未知'
}

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}

// 判断是否逾期
const isOverdue = (dueTime: string) => {
  if (!dueTime) return false
  return new Date(dueTime) < new Date()
}
</script>

<style scoped>
.todo-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-text {
  cursor: pointer;
  color: #409eff;
}

.title-text:hover {
  text-decoration: underline;
}

.overdue {
  color: #f56c6c;
  font-weight: bold;
}
</style>
