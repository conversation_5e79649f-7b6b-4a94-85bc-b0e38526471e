package com.research.workflow.service;

import com.research.workflow.domain.ProcessDefinition;
import java.util.List;

/**
 * 流程定义Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
public interface ProcessDefinitionService {
    
    /**
     * 查询流程定义
     * 
     * @param id 流程定义主键
     * @return 流程定义
     */
    ProcessDefinition selectProcessDefinitionById(Long id);

    /**
     * 查询流程定义列表
     * 
     * @param processDefinition 流程定义
     * @return 流程定义集合
     */
    List<ProcessDefinition> selectProcessDefinitionList(ProcessDefinition processDefinition);

    /**
     * 新增流程定义
     * 
     * @param processDefinition 流程定义
     * @return 结果
     */
    int insertProcessDefinition(ProcessDefinition processDefinition);

    /**
     * 修改流程定义
     * 
     * @param processDefinition 流程定义
     * @return 结果
     */
    int updateProcessDefinition(ProcessDefinition processDefinition);

    /**
     * 批量删除流程定义
     * 
     * @param ids 需要删除的流程定义主键集合
     * @return 结果
     */
    int deleteProcessDefinitionByIds(Long[] ids);

    /**
     * 删除流程定义信息
     * 
     * @param id 流程定义主键
     * @return 结果
     */
    int deleteProcessDefinitionById(Long id);

    /**
     * 部署流程定义
     * 
     * @param id 流程定义ID
     * @return 部署ID
     */
    String deployProcessDefinition(Long id);

    /**
     * 改变流程定义状态
     * 
     * @param id 流程定义ID
     * @param status 状态
     */
    void changeProcessDefinitionStatus(Long id, String status);

    /**
     * 获取流程定义XML
     * 
     * @param id 流程定义ID
     * @return XML内容
     */
    String getProcessDefinitionXml(Long id);

    /**
     * 获取流程定义图片
     * 
     * @param id 流程定义ID
     * @return 图片Base64
     */
    String getProcessDefinitionImage(Long id);

    /**
     * 根据流程定义Key查询最新版本
     * 
     * @param processKey 流程定义Key
     * @return 流程定义
     */
    ProcessDefinition selectLatestProcessDefinitionByKey(String processKey);

    /**
     * 查询已部署的流程定义列表
     * 
     * @return 流程定义集合
     */
    List<ProcessDefinition> selectDeployedProcessDefinitionList();
}
