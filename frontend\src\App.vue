<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { getToken } from '@/utils/auth'

onMounted(async () => {
  // 如果有token但没有用户信息，预加载用户信息和菜单数据
  const userStore = useUserStore()
  const hasToken = getToken()

  if (hasToken && !userStore.userInfo.userId) {
    try {
      console.log('应用启动时预加载用户信息...')
      await userStore.getUserInfo()
      console.log('用户信息预加载完成')
    } catch (error) {
      console.error('预加载用户信息失败:', error)
    }
  }
})
</script>

<style lang="scss">
#app {
  height: 100vh;
  width: 100vw;
}
</style>
