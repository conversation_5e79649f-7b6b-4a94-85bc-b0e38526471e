package com.research.common.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 * 
 * <AUTHOR>
 */
@Component
public class JwtUtils {

    private static JwtUtils instance;

    /**
     * 令牌自定义标识
     */
    @Value("${token.header:Authorization}")
    private String header;

    /**
     * 令牌密钥
     */
    @Value("${token.secret:abcdefghijklmnopqrstuvwxyz}")
    private String secret;

    /**
     * 令牌有效期（默认30分钟）
     */
    @Value("${token.expireTime:30}")
    private int expireTime;

    public JwtUtils() {
        instance = this;
    }

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    private static final Long MILLIS_MINUTE_TEN = 20 * 60 * 1000L;

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public String getUserKey(Claims claims) {
        return getValue(claims, "user_key");
    }

    /**
     * 获取用户ID
     *
     * @return 用户ID
     */
    public String getUserId(Claims claims) {
        return getValue(claims, "user_id");
    }

    /**
     * 获取用户名
     *
     * @return 用户名
     */
    public String getUserName(Claims claims) {
        return getValue(claims, "user_name");
    }

    /**
     * 获取用户部门
     *
     * @return 用户部门
     */
    public String getDeptId(Claims claims) {
        return getValue(claims, "dept_id");
    }

    /**
     * 获取请求token
     *
     * @param token 令牌
     * @return token
     */
    private String getToken(String token) {
        if (StringUtils.isNotEmpty(token) && token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        return token;
    }

    /**
     * 从数据声明生成令牌
     *
     * @param claims 数据声明
     * @return 令牌
     */
    private String createToken(Map<String, Object> claims) {
        String token = Jwts.builder()
                .setClaims(claims)
                .setExpiration(new Date(System.currentTimeMillis() + expireTime * MILLIS_MINUTE))
                .signWith(SignatureAlgorithm.HS512, secret).compact();
        return token;
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    private Claims parseToken(String token) {
        return Jwts.parser()
                .setSigningKey(secret)
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 从令牌中获取用户名
     *
     * @param token 令牌
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        Claims claims = parseToken(token);
        return getUserName(claims);
    }

    /**
     * 获取请求token
     *
     * @param request
     * @return token
     */
    public String getToken(javax.servlet.http.HttpServletRequest request) {
        String token = request.getHeader(header);
        return getToken(token);
    }

    /**
     * 生成令牌
     *
     * @param userKey 用户标识
     * @param userId 用户ID
     * @param username 用户名
     * @param deptId 部门ID
     * @return 令牌
     */
    public String createToken(String userKey, String userId, String username, String deptId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("user_key", userKey);
        claims.put("user_id", userId);
        claims.put("user_name", username);
        claims.put("dept_id", deptId);
        return createToken(claims);
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param request
     * @return 数据声明
     */
    public Claims getClaimsFromToken(javax.servlet.http.HttpServletRequest request) {
        String token = getToken(request);
        return parseToken(token);
    }

    /**
     * 验证令牌有效期，相差不足20分钟，自动刷新缓存
     *
     * @param token 令牌
     * @return 令牌
     */
    public void verifyToken(String userKey) {
        // 这里可以添加token刷新逻辑
    }

    /**
     * 刷新令牌有效期
     *
     * @param userKey 用户标识
     */
    public void refreshToken(String userKey) {
        // 刷新缓存的用户信息
    }

    /**
     * 设置用户身份信息
     */
    public void setUserAgent(Map<String, Object> claims, String userAgent) {
        claims.put("user_agent", userAgent);
    }

    /**
     * 获取用户代理
     *
     * @param claims 身份信息
     * @return 用户代理
     */
    public String getUserAgent(Claims claims) {
        return (String) claims.get("user_agent");
    }

    /**
     * 获取请求token
     */
    public String getValue(Claims claims, String key) {
        return (String) claims.get(key);
    }

    /**
     * 检查token是否过期
     */
    public boolean isTokenExpired(String token) {
        try {
            Claims claims = parseToken(token);
            return claims.getExpiration().before(new Date());
        } catch (Exception e) {
            return true;
        }
    }

    // 静态方法
    public static String getTokenStatic(javax.servlet.http.HttpServletRequest request) {
        if (instance == null) {
            return null;
        }
        return instance.getToken(request);
    }

    public static String createTokenStatic(String username) {
        if (instance == null) {
            return null;
        }
        Map<String, Object> claims = new HashMap<>();
        claims.put("user_name", username);
        return instance.createToken(claims);
    }

    public static String getUsernameFromTokenStatic(String token) {
        if (instance == null) {
            return null;
        }
        Claims claims = instance.parseToken(token);
        return claims != null ? (String) claims.get("user_name") : null;
    }

    public static boolean validateTokenStatic(String token) {
        if (instance == null) {
            return false;
        }
        try {
            Claims claims = instance.parseToken(token);
            return claims != null && !instance.isTokenExpired(token);
        } catch (Exception e) {
            return false;
        }
    }
}
