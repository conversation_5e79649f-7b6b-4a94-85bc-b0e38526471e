<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                   xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                   xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
                   xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" 
                   xmlns:di="http://www.omg.org/spec/DD/20100524/DI" 
                   xmlns:activiti="http://activiti.org/bpmn" 
                   id="Definitions_1" 
                   targetNamespace="http://research.com/workflow">

  <bpmn2:process id="simpleApproval" name="简单审批流程" isExecutable="true">
    
    <!-- 开始事件 -->
    <bpmn2:startEvent id="startEvent" name="开始">
      <bpmn2:outgoing>flow1</bpmn2:outgoing>
    </bpmn2:startEvent>
    
    <!-- 申请任务 -->
    <bpmn2:userTask id="applyTask" name="提交申请" activiti:assignee="${applicant}">
      <bpmn2:documentation>用户提交申请</bpmn2:documentation>
      <bpmn2:incoming>flow1</bpmn2:incoming>
      <bpmn2:outgoing>flow2</bpmn2:outgoing>
    </bpmn2:userTask>
    
    <!-- 审批任务 -->
    <bpmn2:userTask id="approveTask" name="领导审批" activiti:assignee="${approver}">
      <bpmn2:documentation>领导审批申请</bpmn2:documentation>
      <bpmn2:incoming>flow2</bpmn2:incoming>
      <bpmn2:outgoing>flow3</bpmn2:outgoing>
    </bpmn2:userTask>
    
    <!-- 排他网关 -->
    <bpmn2:exclusiveGateway id="gateway" name="审批结果">
      <bpmn2:incoming>flow3</bpmn2:incoming>
      <bpmn2:outgoing>approvedFlow</bpmn2:outgoing>
      <bpmn2:outgoing>rejectedFlow</bpmn2:outgoing>
    </bpmn2:exclusiveGateway>
    
    <!-- 通过结束事件 -->
    <bpmn2:endEvent id="approvedEnd" name="审批通过">
      <bpmn2:incoming>approvedFlow</bpmn2:incoming>
    </bpmn2:endEvent>
    
    <!-- 拒绝结束事件 -->
    <bpmn2:endEvent id="rejectedEnd" name="审批拒绝">
      <bpmn2:incoming>rejectedFlow</bpmn2:incoming>
    </bpmn2:endEvent>
    
    <!-- 连接线 -->
    <bpmn2:sequenceFlow id="flow1" sourceRef="startEvent" targetRef="applyTask"/>
    <bpmn2:sequenceFlow id="flow2" sourceRef="applyTask" targetRef="approveTask"/>
    <bpmn2:sequenceFlow id="flow3" sourceRef="approveTask" targetRef="gateway"/>
    <bpmn2:sequenceFlow id="approvedFlow" name="通过" sourceRef="gateway" targetRef="approvedEnd">
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${approved == true}</bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    <bpmn2:sequenceFlow id="rejectedFlow" name="拒绝" sourceRef="gateway" targetRef="rejectedEnd">
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${approved == false}</bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    
  </bpmn2:process>

  <!-- 流程图布局信息 -->
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="simpleApproval">
      
      <bpmndi:BPMNShape id="BPMNShape_startEvent" bpmnElement="startEvent">
        <dc:Bounds x="100" y="100" width="36" height="36"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="108" y="140" width="22" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="BPMNShape_applyTask" bpmnElement="applyTask">
        <dc:Bounds x="200" y="78" width="100" height="80"/>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="BPMNShape_approveTask" bpmnElement="approveTask">
        <dc:Bounds x="350" y="78" width="100" height="80"/>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="BPMNShape_gateway" bpmnElement="gateway" isMarkerVisible="true">
        <dc:Bounds x="500" y="98" width="50" height="50"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="502" y="152" width="44" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="BPMNShape_approvedEnd" bpmnElement="approvedEnd">
        <dc:Bounds x="600" y="50" width="36" height="36"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="592" y="90" width="52" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="BPMNShape_rejectedEnd" bpmnElement="rejectedEnd">
        <dc:Bounds x="600" y="150" width="36" height="36"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="592" y="190" width="52" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      
      <!-- 连接线 -->
      <bpmndi:BPMNEdge id="BPMNEdge_flow1" bpmnElement="flow1">
        <di:waypoint x="136" y="118"/>
        <di:waypoint x="200" y="118"/>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="BPMNEdge_flow2" bpmnElement="flow2">
        <di:waypoint x="300" y="118"/>
        <di:waypoint x="350" y="118"/>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="BPMNEdge_flow3" bpmnElement="flow3">
        <di:waypoint x="450" y="118"/>
        <di:waypoint x="500" y="123"/>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="BPMNEdge_approvedFlow" bpmnElement="approvedFlow">
        <di:waypoint x="525" y="98"/>
        <di:waypoint x="525" y="68"/>
        <di:waypoint x="600" y="68"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="533" y="81" width="22" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="BPMNEdge_rejectedFlow" bpmnElement="rejectedFlow">
        <di:waypoint x="525" y="148"/>
        <di:waypoint x="525" y="168"/>
        <di:waypoint x="600" y="168"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="533" y="156" width="22" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
  
</bpmn2:definitions>
