<template>
  <el-dialog
    title="合同详情"
    v-model="visible"
    width="1200px"
    append-to-body
    @close="handleClose"
  >
    <div v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="合同编号">
          {{ contractInfo.contractNo }}
        </el-descriptions-item>
        <el-descriptions-item label="合同名称">
          {{ contractInfo.contractName }}
        </el-descriptions-item>
        <el-descriptions-item label="合同类型">
          {{ contractInfo.contractType }}
        </el-descriptions-item>
        <el-descriptions-item label="合作单位">
          {{ contractInfo.partnerName }}
        </el-descriptions-item>
        <el-descriptions-item label="合同负责人">
          {{ contractInfo.principalName }}
        </el-descriptions-item>
        <el-descriptions-item label="所属部门">
          {{ contractInfo.deptName }}
        </el-descriptions-item>
        <el-descriptions-item label="合同状态">
          <el-tag :type="getStatusType(contractInfo.status)">
            {{ contractInfo.statusName }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="审批状态">
          {{ contractInfo.approvalStatus || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="合同金额">
          <span class="money">{{ formatMoney(contractInfo.contractAmount) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="签署日期">
          {{ parseTime(contractInfo.signingDate, '{y}-{m}-{d}') }}
        </el-descriptions-item>
        <el-descriptions-item label="合同开始时间">
          {{ parseTime(contractInfo.startDate, '{y}-{m}-{d}') }}
        </el-descriptions-item>
        <el-descriptions-item label="合同结束时间">
          {{ parseTime(contractInfo.endDate, '{y}-{m}-{d}') }}
        </el-descriptions-item>
        <el-descriptions-item label="执行进度">
          <el-progress 
            :percentage="contractInfo.progressPercentage || 0" 
            :color="getProgressColor(contractInfo.progressPercentage)"
            :stroke-width="8"
          />
        </el-descriptions-item>
        <el-descriptions-item label="剩余天数">
          <el-tag v-if="contractInfo.remainingDays !== null" :type="getRemainingDaysType(contractInfo.remainingDays)">
            {{ contractInfo.remainingDays > 0 ? contractInfo.remainingDays + '天' : '已逾期' }}
          </el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="即将到期">
          <el-tag :type="contractInfo.isExpiringSoon ? 'warning' : 'success'">
            {{ contractInfo.isExpiringSoon ? '是' : '否' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="当前节点">
          {{ contractInfo.currentNode || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="合同内容" :span="2">
          <div class="text-content">{{ contractInfo.contractContent }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="付款条款" :span="2">
          <div class="text-content">{{ contractInfo.paymentTerms }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="交付条款" :span="2">
          <div class="text-content">{{ contractInfo.deliveryTerms }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ parseTime(contractInfo.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ parseTime(contractInfo.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="创建人">
          {{ contractInfo.createBy }}
        </el-descriptions-item>
        <el-descriptions-item label="更新人">
          {{ contractInfo.updateBy }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2" v-if="contractInfo.remark">
          <div class="text-content">{{ contractInfo.remark }}</div>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 合同文件 -->
      <el-divider content-position="left">合同文件</el-divider>
      <el-row :gutter="20">
        <el-col :span="12" v-if="contractInfo.contractFilePath">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>合同文件</span>
                <el-button type="text" @click="downloadFile(contractInfo.contractFilePath)">
                  <el-icon><Download /></el-icon>
                </el-button>
              </div>
            </template>
            <p>{{ getFileName(contractInfo.contractFilePath) }}</p>
          </el-card>
        </el-col>
        <el-col :span="12" v-if="contractInfo.backupFilePath">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>备案文件</span>
                <el-button type="text" @click="downloadFile(contractInfo.backupFilePath)">
                  <el-icon><Download /></el-icon>
                </el-button>
              </div>
            </template>
            <p>{{ getFileName(contractInfo.backupFilePath) }}</p>
          </el-card>
        </el-col>
      </el-row>

      <!-- 合同执行统计 -->
      <el-divider content-position="left">执行统计</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>执行进度</span>
            </template>
            <div ref="progressChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>时间进度</span>
            </template>
            <div ref="timeChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 合同变更记录 -->
      <el-divider content-position="left">变更记录</el-divider>
      <el-table :data="changeRecords" style="width: 100%">
        <el-table-column prop="changeDate" label="变更日期" width="120">
          <template #default="scope">
            {{ parseTime(scope.row.changeDate, '{y}-{m}-{d}') }}
          </template>
        </el-table-column>
        <el-table-column prop="changeType" label="变更类型" width="100" />
        <el-table-column prop="changeContent" label="变更内容" />
        <el-table-column prop="changeReason" label="变更原因" />
        <el-table-column prop="approver" label="审批人" width="100" />
        <el-table-column prop="changeStatus" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.changeStatus === '已批准' ? 'success' : 'warning'">
              {{ scope.row.changeStatus }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getContract } from "@/api/project/contract"
import * as echarts from 'echarts'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  contractId: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const loading = ref(false)
const contractInfo = ref({})
const changeRecords = ref([])
const progressChartRef = ref()
const timeChartRef = ref()

// 监听合同ID变化
watch(() => props.contractId, (newVal) => {
  if (newVal && visible.value) {
    getContractDetail()
  }
}, { immediate: true })

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal && props.contractId) {
    getContractDetail()
  }
})

/** 获取合同详情 */
function getContractDetail() {
  if (!props.contractId) return
  
  loading.value = true
  getContract(props.contractId).then(response => {
    contractInfo.value = response.data
    // 模拟变更记录数据
    changeRecords.value = [
      {
        changeDate: '2024-06-15',
        changeType: '金额变更',
        changeContent: '合同金额从100万调整为120万',
        changeReason: '项目需求增加',
        approver: '张三',
        changeStatus: '已批准'
      },
      {
        changeDate: '2024-07-01',
        changeType: '时间变更',
        changeContent: '合同结束时间延期1个月',
        changeReason: '技术难度超预期',
        approver: '李四',
        changeStatus: '审批中'
      }
    ]
    loading.value = false
    
    // 渲染图表
    nextTick(() => {
      renderProgressChart()
      renderTimeChart()
    })
  }).catch(() => {
    loading.value = false
  })
}

/** 渲染执行进度图表 */
function renderProgressChart() {
  if (!progressChartRef.value) return
  
  const chart = echarts.init(progressChartRef.value)
  const progress = contractInfo.value.progressPercentage || 0
  
  const option = {
    title: {
      text: '合同执行进度',
      left: 'center'
    },
    series: [
      {
        type: 'gauge',
        startAngle: 180,
        endAngle: 0,
        center: ['50%', '75%'],
        radius: '90%',
        min: 0,
        max: 100,
        splitNumber: 8,
        axisLine: {
          lineStyle: {
            width: 6,
            color: [
              [0.25, '#FF6E76'],
              [0.5, '#FDDD60'],
              [0.75, '#58D9F9'],
              [1, '#7CFFB2']
            ]
          }
        },
        pointer: {
          icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
          length: '12%',
          width: 20,
          offsetCenter: [0, '-60%'],
          itemStyle: {
            color: 'auto'
          }
        },
        axisTick: {
          length: 12,
          lineStyle: {
            color: 'auto',
            width: 2
          }
        },
        splitLine: {
          length: 20,
          lineStyle: {
            color: 'auto',
            width: 5
          }
        },
        axisLabel: {
          color: '#464646',
          fontSize: 20,
          distance: -60,
          formatter: function (value) {
            if (value === 100) {
              return '完成'
            } else if (value === 75) {
              return '良好'
            } else if (value === 50) {
              return '一般'
            } else if (value === 25) {
              return '较差'
            }
            return ''
          }
        },
        title: {
          offsetCenter: [0, '-20%'],
          fontSize: 20
        },
        detail: {
          fontSize: 30,
          offsetCenter: [0, '0%'],
          valueAnimation: true,
          formatter: function (value) {
            return Math.round(value) + '%'
          },
          color: 'auto'
        },
        data: [
          {
            value: progress,
            name: '执行进度'
          }
        ]
      }
    ]
  }
  
  chart.setOption(option)
}

/** 渲染时间进度图表 */
function renderTimeChart() {
  if (!timeChartRef.value) return
  
  const chart = echarts.init(timeChartRef.value)
  
  // 计算时间进度
  const startDate = new Date(contractInfo.value.startDate)
  const endDate = new Date(contractInfo.value.endDate)
  const currentDate = new Date()
  
  const totalDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24))
  const passedDays = Math.ceil((currentDate - startDate) / (1000 * 60 * 60 * 24))
  const timeProgress = Math.min(100, Math.max(0, (passedDays / totalDays) * 100))
  
  const option = {
    title: {
      text: '时间进度',
      left: 'center'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '60%'],
        data: [
          { value: passedDays, name: '已用时间', itemStyle: { color: '#409eff' } },
          { value: Math.max(0, totalDays - passedDays), name: '剩余时间', itemStyle: { color: '#e4e7ed' } }
        ],
        label: {
          formatter: '{b}: {c}天\n({d}%)'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
}

/** 关闭对话框 */
function handleClose() {
  visible.value = false
}

/** 获取状态类型 */
function getStatusType(status) {
  const statusMap = {
    0: 'info',     // 草稿
    1: 'warning',  // 审核中
    2: 'success',  // 已签署
    3: 'primary',  // 执行中
    4: 'success',  // 已完成
    5: 'danger'    // 已终止
  }
  return statusMap[status] || 'info'
}

/** 获取进度颜色 */
function getProgressColor(percentage) {
  if (!percentage) return '#909399'
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 50) return '#e6a23c'
  return '#f56c6c'
}

/** 获取剩余天数类型 */
function getRemainingDaysType(days) {
  if (days < 0) return 'danger'
  if (days <= 7) return 'danger'
  if (days <= 30) return 'warning'
  return 'success'
}

/** 格式化金额 */
function formatMoney(money) {
  if (!money) return '0.00'
  return parseFloat(money).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

/** 获取文件名 */
function getFileName(filePath) {
  if (!filePath) return ''
  return filePath.split('/').pop()
}

/** 下载文件 */
function downloadFile(filePath) {
  // 这里应该调用文件下载接口
  console.log('下载文件:', filePath)
}

/** 解析时间 */
function parseTime(time, pattern) {
  if (!time) return ''
  // 这里应该使用实际的时间解析函数
  return new Date(time).toLocaleString()
}
</script>

<style scoped>
.money {
  font-weight: bold;
  color: #409eff;
}

.text-content {
  line-height: 1.6;
  white-space: pre-wrap;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
