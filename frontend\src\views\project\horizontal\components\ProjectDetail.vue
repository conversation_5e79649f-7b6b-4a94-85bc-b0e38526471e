<template>
  <el-dialog
    title="项目详情"
    v-model="visible"
    width="1200px"
    append-to-body
    @close="handleClose"
  >
    <div v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="项目编号">
          {{ projectInfo.projectNo }}
        </el-descriptions-item>
        <el-descriptions-item label="项目名称">
          {{ projectInfo.projectName }}
        </el-descriptions-item>
        <el-descriptions-item label="项目类型">
          {{ projectInfo.projectType }}
        </el-descriptions-item>
        <el-descriptions-item label="学科分类">
          {{ projectInfo.subjectCategory }}
        </el-descriptions-item>
        <el-descriptions-item label="项目负责人">
          {{ projectInfo.principalName }}
        </el-descriptions-item>
        <el-descriptions-item label="所属部门">
          {{ projectInfo.deptName }}
        </el-descriptions-item>
        <el-descriptions-item label="合作单位">
          {{ projectInfo.partnerName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="项目状态">
          <el-tag :type="getStatusType(projectInfo.status)">
            {{ projectInfo.statusName }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="项目开始时间">
          {{ parseTime(projectInfo.startDate, '{y}-{m}-{d}') }}
        </el-descriptions-item>
        <el-descriptions-item label="项目结束时间">
          {{ parseTime(projectInfo.endDate, '{y}-{m}-{d}') }}
        </el-descriptions-item>
        <el-descriptions-item label="项目总经费">
          <span class="money">{{ formatMoney(projectInfo.totalFund) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="已到账经费">
          <span class="money">{{ formatMoney(projectInfo.receivedFund) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="经费到账率">
          <el-progress 
            :percentage="projectInfo.fundReceiveRate || 0" 
            :color="getProgressColor(projectInfo.fundReceiveRate)"
            :stroke-width="8"
          />
        </el-descriptions-item>
        <el-descriptions-item label="项目进度">
          <el-progress 
            :percentage="projectInfo.progressPercentage || 0" 
            :color="getProgressColor(projectInfo.progressPercentage)"
            :stroke-width="8"
          />
        </el-descriptions-item>
        <el-descriptions-item label="剩余天数">
          <el-tag v-if="projectInfo.remainingDays !== null" :type="getRemainingDaysType(projectInfo.remainingDays)">
            {{ projectInfo.remainingDays > 0 ? projectInfo.remainingDays + '天' : '已逾期' }}
          </el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="即将到期">
          <el-tag :type="projectInfo.isExpiringSoon ? 'warning' : 'success'">
            {{ projectInfo.isExpiringSoon ? '是' : '否' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="关键词" :span="2">
          <el-tag v-for="keyword in getKeywords(projectInfo.keywords)" :key="keyword" class="mr-2">
            {{ keyword }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="项目摘要" :span="2">
          <div class="text-content">{{ projectInfo.projectSummary }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="研究内容" :span="2">
          <div class="text-content">{{ projectInfo.researchContent }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="预期成果" :span="2">
          <div class="text-content">{{ projectInfo.expectedResults }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="审批状态">
          {{ projectInfo.approvalStatus || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="当前节点">
          {{ projectInfo.currentNode || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ parseTime(projectInfo.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ parseTime(projectInfo.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="创建人">
          {{ projectInfo.createBy }}
        </el-descriptions-item>
        <el-descriptions-item label="更新人">
          {{ projectInfo.updateBy }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2" v-if="projectInfo.remark">
          <div class="text-content">{{ projectInfo.remark }}</div>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 项目文件 -->
      <el-divider content-position="left">项目文件</el-divider>
      <el-row :gutter="20">
        <el-col :span="12" v-if="projectInfo.applicationFilePath">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>申请文件</span>
                <el-button type="text" @click="downloadFile(projectInfo.applicationFilePath)">
                  <el-icon><Download /></el-icon>
                </el-button>
              </div>
            </template>
            <p>{{ getFileName(projectInfo.applicationFilePath) }}</p>
          </el-card>
        </el-col>
        <el-col :span="12" v-if="projectInfo.contractFilePath">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>合同文件</span>
                <el-button type="text" @click="downloadFile(projectInfo.contractFilePath)">
                  <el-icon><Download /></el-icon>
                </el-button>
              </div>
            </template>
            <p>{{ getFileName(projectInfo.contractFilePath) }}</p>
          </el-card>
        </el-col>
      </el-row>

      <!-- 项目统计图表 -->
      <el-divider content-position="left">项目统计</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>经费使用情况</span>
            </template>
            <div ref="fundChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>项目进度</span>
            </template>
            <div ref="progressChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getHorizontalProject } from "@/api/project/horizontal"
import * as echarts from 'echarts'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  projectId: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const loading = ref(false)
const projectInfo = ref({})
const fundChartRef = ref()
const progressChartRef = ref()

// 监听项目ID变化
watch(() => props.projectId, (newVal) => {
  if (newVal && visible.value) {
    getProjectDetail()
  }
}, { immediate: true })

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal && props.projectId) {
    getProjectDetail()
  }
})

/** 获取项目详情 */
function getProjectDetail() {
  if (!props.projectId) return
  
  loading.value = true
  getHorizontalProject(props.projectId).then(response => {
    projectInfo.value = response.data
    loading.value = false
    
    // 渲染图表
    nextTick(() => {
      renderFundChart()
      renderProgressChart()
    })
  }).catch(() => {
    loading.value = false
  })
}

/** 渲染经费图表 */
function renderFundChart() {
  if (!fundChartRef.value) return
  
  const chart = echarts.init(fundChartRef.value)
  const totalFund = projectInfo.value.totalFund || 0
  const receivedFund = projectInfo.value.receivedFund || 0
  const remainingFund = totalFund - receivedFund
  
  const option = {
    title: {
      text: '经费使用情况',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '经费',
        type: 'pie',
        radius: '50%',
        data: [
          { value: receivedFund, name: '已到账经费' },
          { value: remainingFund, name: '未到账经费' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
}

/** 渲染进度图表 */
function renderProgressChart() {
  if (!progressChartRef.value) return
  
  const chart = echarts.init(progressChartRef.value)
  const progress = projectInfo.value.progressPercentage || 0
  
  const option = {
    title: {
      text: '项目进度',
      left: 'center'
    },
    series: [
      {
        type: 'gauge',
        startAngle: 180,
        endAngle: 0,
        center: ['50%', '75%'],
        radius: '90%',
        min: 0,
        max: 100,
        splitNumber: 8,
        axisLine: {
          lineStyle: {
            width: 6,
            color: [
              [0.25, '#FF6E76'],
              [0.5, '#FDDD60'],
              [0.75, '#58D9F9'],
              [1, '#7CFFB2']
            ]
          }
        },
        pointer: {
          icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
          length: '12%',
          width: 20,
          offsetCenter: [0, '-60%'],
          itemStyle: {
            color: 'auto'
          }
        },
        axisTick: {
          length: 12,
          lineStyle: {
            color: 'auto',
            width: 2
          }
        },
        splitLine: {
          length: 20,
          lineStyle: {
            color: 'auto',
            width: 5
          }
        },
        axisLabel: {
          color: '#464646',
          fontSize: 20,
          distance: -60,
          formatter: function (value) {
            if (value === 100) {
              return '完成'
            } else if (value === 75) {
              return '良好'
            } else if (value === 50) {
              return '一般'
            } else if (value === 25) {
              return '较差'
            }
            return ''
          }
        },
        title: {
          offsetCenter: [0, '-20%'],
          fontSize: 20
        },
        detail: {
          fontSize: 30,
          offsetCenter: [0, '0%'],
          valueAnimation: true,
          formatter: function (value) {
            return Math.round(value) + '%'
          },
          color: 'auto'
        },
        data: [
          {
            value: progress,
            name: '完成度'
          }
        ]
      }
    ]
  }
  
  chart.setOption(option)
}

/** 关闭对话框 */
function handleClose() {
  visible.value = false
}

/** 获取状态类型 */
function getStatusType(status) {
  const statusMap = {
    0: 'info',     // 申请中
    1: 'success',  // 立项
    2: 'primary',  // 执行中
    3: 'warning',  // 变更中
    4: 'warning',  // 结项中
    5: 'success',  // 已结项
    6: 'danger'    // 已撤销
  }
  return statusMap[status] || 'info'
}

/** 获取进度颜色 */
function getProgressColor(percentage) {
  if (!percentage) return '#909399'
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 50) return '#e6a23c'
  return '#f56c6c'
}

/** 获取剩余天数类型 */
function getRemainingDaysType(days) {
  if (days < 0) return 'danger'
  if (days <= 7) return 'danger'
  if (days <= 30) return 'warning'
  return 'success'
}

/** 格式化金额 */
function formatMoney(money) {
  if (!money) return '0.00'
  return parseFloat(money).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

/** 获取关键词数组 */
function getKeywords(keywords) {
  if (!keywords) return []
  return keywords.split(';').filter(k => k.trim())
}

/** 获取文件名 */
function getFileName(filePath) {
  if (!filePath) return ''
  return filePath.split('/').pop()
}

/** 下载文件 */
function downloadFile(filePath) {
  // 这里应该调用文件下载接口
  console.log('下载文件:', filePath)
}

/** 解析时间 */
function parseTime(time, pattern) {
  if (!time) return ''
  // 这里应该使用实际的时间解析函数
  return new Date(time).toLocaleString()
}
</script>

<style scoped>
.money {
  font-weight: bold;
  color: #409eff;
}

.text-content {
  line-height: 1.6;
  white-space: pre-wrap;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mr-2 {
  margin-right: 8px;
}
</style>
