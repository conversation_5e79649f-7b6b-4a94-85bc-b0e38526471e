<template>
  <div class="top-right-btn" :style="style">
    <el-row>
      <el-col :span="24">
        <el-tooltip class="item" effect="dark" content="搜索" placement="top" v-if="search">
          <el-button size="small" circle icon="Search" @click="toggleSearch" />
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="刷新" placement="top">
          <el-button size="small" circle icon="Refresh" @click="refresh" />
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="显隐列" placement="top" v-if="columns">
          <el-button size="small" circle icon="Menu" @click="showColumn" />
        </el-tooltip>
      </el-col>
    </el-row>
    <el-popover placement="bottom" :width="120" v-model:visible="open" trigger="click" v-if="columns">
      <template #reference>
        <span></span>
      </template>
      <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">
        列展示
      </el-checkbox>
      <el-divider style="margin: 12px 0" />
      <el-checkbox-group v-model="checkedColumns" @change="handleCheckedColumnsChange">
        <el-checkbox v-for="item in columns" :key="item.key" :label="item.key">
          {{ item.label }}
        </el-checkbox>
      </el-checkbox-group>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Props {
  showSearch?: boolean
  columns?: any[]
  search?: boolean
  gutter?: number
}

const props = withDefaults(defineProps<Props>(), {
  showSearch: true,
  search: true,
  gutter: 10
})

const emit = defineEmits<{
  'update:showSearch': [value: boolean]
  queryTable: []
}>()

// 显隐数据
const open = ref(false)
const checkAll = ref(true)
const isIndeterminate = ref(false)
const checkedColumns = ref<number[]>([])

const style = computed(() => {
  const ret: any = {}
  if (props.gutter) {
    ret.marginRight = props.gutter / 2 + 'px'
  }
  return ret
})

// 搜索
function toggleSearch() {
  emit('update:showSearch', !props.showSearch)
}

// 刷新
function refresh() {
  emit('queryTable')
}

// 右侧列表
function showColumn() {
  open.value = true
}

// 全选
function handleCheckAllChange(val: boolean) {
  if (val) {
    checkedColumns.value = props.columns?.map(item => item.key) || []
  } else {
    checkedColumns.value = []
  }
  isIndeterminate.value = false
  setColumnsVisible()
}

// 选择列
function handleCheckedColumnsChange(value: number[]) {
  const checkedCount = value.length
  const allCount = props.columns?.length || 0
  checkAll.value = checkedCount === allCount
  isIndeterminate.value = checkedCount > 0 && checkedCount < allCount
  setColumnsVisible()
}

// 设置列显隐
function setColumnsVisible() {
  if (props.columns) {
    props.columns.forEach(item => {
      item.visible = checkedColumns.value.includes(item.key)
    })
  }
}

// 初始化列显隐
watch(() => props.columns, (newVal) => {
  if (newVal) {
    checkedColumns.value = newVal.filter(item => item.visible).map(item => item.key)
    checkAll.value = checkedColumns.value.length === newVal.length
    isIndeterminate.value = checkedColumns.value.length > 0 && checkedColumns.value.length < newVal.length
  }
}, { immediate: true, deep: true })
</script>

<style lang="scss" scoped>
.top-right-btn {
  margin-left: auto;
}
</style>
