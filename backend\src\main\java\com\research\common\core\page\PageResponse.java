package com.research.common.core.page;



import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 分页响应类
 * 
 * <AUTHOR>
 */
public class PageResponse<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 当前页码 */
    private long pageNum;

    /** 每页显示记录数 */
    private long pageSize;

    /** 总记录数 */
    private long total;

    /** 总页数 */
    private long pages;

    /** 数据列表 */
    private List<T> records;

    /** 是否有上一页 */
    private boolean hasPrevious;

    /** 是否有下一页 */
    private boolean hasNext;

    /** 是否为第一页 */
    private boolean isFirst;

    /** 是否为最后一页 */
    private boolean isLast;

    public PageResponse() {
        this.records = Collections.emptyList();
    }

    public PageResponse(long pageNum, long pageSize, long total, List<T> records) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.records = records != null ? records : Collections.emptyList();
        this.pages = calculatePages(total, pageSize);
        this.hasPrevious = pageNum > 1;
        this.hasNext = pageNum < pages;
        this.isFirst = pageNum == 1;
        this.isLast = pageNum == pages || pages == 0;
    }

    /**
     * 计算总页数
     */
    private long calculatePages(long total, long pageSize) {
        if (pageSize == 0) {
            return 0;
        }
        return (total + pageSize - 1) / pageSize;
    }

    /**
     * 创建空的分页响应
     */
    public static <T> PageResponse<T> empty() {
        return new PageResponse<>(1, 10, 0, Collections.emptyList());
    }

    /**
     * 创建空的分页响应（指定页码和页大小）
     */
    public static <T> PageResponse<T> empty(long pageNum, long pageSize) {
        return new PageResponse<>(pageNum, pageSize, 0, Collections.emptyList());
    }

    /**
     * 创建分页响应
     */
    public static <T> PageResponse<T> of(long pageNum, long pageSize, long total, List<T> records) {
        return new PageResponse<>(pageNum, pageSize, total, records);
    }

    /**
     * 从PageRequest创建分页响应
     */
    public static <T> PageResponse<T> of(PageRequest pageRequest, long total, List<T> records) {
        return new PageResponse<>(pageRequest.getPageNum(), pageRequest.getPageSize(), total, records);
    }

    /**
     * 获取开始记录数（用于显示）
     */
    public long getStartRecord() {
        if (total == 0) {
            return 0;
        }
        return (pageNum - 1) * pageSize + 1;
    }

    /**
     * 获取结束记录数（用于显示）
     */
    public long getEndRecord() {
        if (total == 0) {
            return 0;
        }
        long end = pageNum * pageSize;
        return Math.min(end, total);
    }

    /**
     * 获取分页信息描述
     */
    public String getPageInfo() {
        if (total == 0) {
            return "暂无数据";
        }
        return String.format("第 %d-%d 条，共 %d 条记录，第 %d/%d 页", 
                getStartRecord(), getEndRecord(), total, pageNum, pages);
    }

    // Getters and Setters
    public long getPageNum() {
        return pageNum;
    }

    public void setPageNum(long pageNum) {
        this.pageNum = pageNum;
    }

    public long getPageSize() {
        return pageSize;
    }

    public void setPageSize(long pageSize) {
        this.pageSize = pageSize;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
        this.pages = calculatePages(total, pageSize);
    }

    public long getPages() {
        return pages;
    }

    public void setPages(long pages) {
        this.pages = pages;
    }

    public List<T> getRecords() {
        return records;
    }

    public void setRecords(List<T> records) {
        this.records = records != null ? records : Collections.emptyList();
    }

    public boolean isHasPrevious() {
        return hasPrevious;
    }

    public void setHasPrevious(boolean hasPrevious) {
        this.hasPrevious = hasPrevious;
    }

    public boolean isHasNext() {
        return hasNext;
    }

    public void setHasNext(boolean hasNext) {
        this.hasNext = hasNext;
    }

    public boolean isFirst() {
        return isFirst;
    }

    public void setFirst(boolean first) {
        isFirst = first;
    }

    public boolean isLast() {
        return isLast;
    }

    public void setLast(boolean last) {
        isLast = last;
    }

    @Override
    public String toString() {
        return "PageResponse{" +
                "pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", total=" + total +
                ", pages=" + pages +
                ", records=" + (records != null ? records.size() : 0) + " items" +
                '}';
    }
}
