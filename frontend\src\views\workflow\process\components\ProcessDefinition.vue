<template>
  <div class="process-definition">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="流程名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入流程名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="流程分类" prop="category">
        <el-select v-model="queryParams.category" placeholder="请选择流程分类" clearable>
          <el-option label="审批流程" value="approval" />
          <el-option label="业务流程" value="business" />
          <el-option label="系统流程" value="system" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="激活" value="active" />
          <el-option label="挂起" value="suspended" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleDeploy"
        >部署流程</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Upload"
          @click="handleImport"
        >导入流程</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="definitionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="80" align="center" />
      <el-table-column label="流程名称" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="流程标识" prop="key" :show-overflow-tooltip="true" />
      <el-table-column label="版本" prop="version" width="80" align="center">
        <template #default="scope">
          <el-tag type="info">v{{ scope.row.version }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="分类" prop="category" width="100" align="center">
        <template #default="scope">
          <el-tag :type="getCategoryType(scope.row.category)">
            {{ getCategoryText(scope.row.category) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="suspended" width="80" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.suspended ? 'danger' : 'success'">
            {{ scope.row.suspended ? '挂起' : '激活' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="部署时间" prop="deploymentTime" width="180" align="center">
        <template #default="scope">
          {{ parseTime(scope.row.deploymentTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="300" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleStart(scope.row)">
            <el-icon><VideoPlay /></el-icon>启动
          </el-button>
          <el-button link type="info" @click="handleView(scope.row)">
            <el-icon><View /></el-icon>查看
          </el-button>
          <el-button link type="warning" @click="handleDesign(scope.row)">
            <el-icon><Edit /></el-icon>设计
          </el-button>
          <el-button 
            v-if="!scope.row.suspended" 
            link 
            type="danger" 
            @click="handleSuspend(scope.row)"
          >
            <el-icon><VideoPause /></el-icon>挂起
          </el-button>
          <el-button 
            v-else 
            link 
            type="success" 
            @click="handleActivate(scope.row)"
          >
            <el-icon><VideoPlay /></el-icon>激活
          </el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)">
            <el-button link type="primary">
              更多<el-icon><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="export">导出</el-dropdown-item>
                <el-dropdown-item command="copy">复制</el-dropdown-item>
                <el-dropdown-item command="history">历史版本</el-dropdown-item>
                <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { parseTime } from '@/utils/common'
import Pagination from '@/components/Pagination/index.vue'
import RightToolbar from '@/components/RightToolbar/index.vue'
import { listDefinitions, suspendDefinition, activateDefinition, deleteDefinition, startProcess } from '@/api/workflow/process'

// 数据
const loading = ref(false)
const showSearch = ref(true)
const definitionList = ref([])
const total = ref(0)
const multiple = ref(true)
const ids = ref([])

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: '',
  category: '',
  status: ''
})

// 获取流程定义列表
const getList = () => {
  loading.value = true
  listDefinitions(queryParams).then(response => {
    definitionList.value = response.rows || []
    total.value = response.total || 0
    loading.value = false
    emitDataLoaded()
  }).catch(() => {
    loading.value = false
    ElMessage.error('获取流程定义列表失败')
    emitDataLoaded()
  })
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置
const resetQuery = () => {
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    name: '',
    category: '',
    status: ''
  })
  getList()
}

// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
  ids.value = selection.map(item => item.id)
  multiple.value = !selection.length
}

// 获取分类类型
const getCategoryType = (category: string) => {
  const types: Record<string, string> = {
    approval: 'primary',
    business: 'success', 
    system: 'warning'
  }
  return types[category] || 'info'
}

// 获取分类文本
const getCategoryText = (category: string) => {
  const texts: Record<string, string> = {
    approval: '审批流程',
    business: '业务流程',
    system: '系统流程'
  }
  return texts[category] || '未知'
}

// 部署流程
const handleDeploy = () => {
  ElMessage.info('部署流程功能开发中...')
}

// 导入流程
const handleImport = () => {
  ElMessage.info('导入流程功能开发中...')
}

// 删除流程
const handleDelete = () => {
  ElMessageBox.confirm('确认删除选中的流程定义吗？', '提示', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('删除成功')
    getList()
  })
}

// 启动流程
const handleStart = (row: any) => {
  ElMessage.info(`启动流程: ${row.name}`)
}

// 查看流程
const handleView = (row: any) => {
  ElMessage.info(`查看流程: ${row.name}`)
}

// 设计流程
const handleDesign = (row: any) => {
  ElMessage.info(`设计流程: ${row.name}`)
}

// 挂起流程
const handleSuspend = (row: any) => {
  ElMessageBox.confirm(`确认挂起流程"${row.name}"吗？`, '提示', {
    type: 'warning'
  }).then(() => {
    return suspendDefinition(row.id)
  }).then(() => {
    ElMessage.success('挂起成功')
    getList()
  }).catch(() => {
    ElMessage.error('挂起失败')
  })
}

// 激活流程
const handleActivate = (row: any) => {
  ElMessageBox.confirm(`确认激活流程"${row.name}"吗？`, '提示', {
    type: 'warning'
  }).then(() => {
    return activateDefinition(row.id)
  }).then(() => {
    ElMessage.success('激活成功')
    getList()
  }).catch(() => {
    ElMessage.error('激活失败')
  })
}

// 更多操作
const handleCommand = (command: string, row: any) => {
  switch (command) {
    case 'export':
      ElMessage.info(`导出流程: ${row.name}`)
      break
    case 'copy':
      ElMessage.info(`复制流程: ${row.name}`)
      break
    case 'history':
      ElMessage.info(`查看历史版本: ${row.name}`)
      break
    case 'delete':
      ElMessageBox.confirm(`确认删除流程"${row.name}"吗？`, '提示', {
        type: 'warning'
      }).then(() => {
        ElMessage.success('删除成功')
        getList()
      })
      break
  }
}

// 刷新数据
const refreshData = () => {
  getList()
}

// 页面挂载
onMounted(() => {
  getList()
})

// 定义emit
const emit = defineEmits(['data-loaded'])

// 暴露方法
defineExpose({
  refreshData
})

// 在获取数据后触发事件
const emitDataLoaded = () => {
  emit('data-loaded', total.value)
}
</script>

<style scoped>
.process-definition {
  height: 100%;
  background: #fafbfc;
  border-radius: 8px;
  padding: 20px;
}

.mb8 {
  margin-bottom: 16px;
}

/* 搜索表单样式 */
:deep(.el-form--inline .el-form-item) {
  margin-right: 20px;
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* 操作按钮样式 */
:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

:deep(.el-button--primary:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

:deep(.el-button--success) {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

:deep(.el-button--danger) {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

/* 表格样式 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

:deep(.el-table__header) {
  background: #f8fafc;
}

:deep(.el-table th) {
  background: #f8fafc !important;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f3f4f6;
}

:deep(.el-table__row:hover) {
  background: #f8fafc;
}

/* 标签样式 */
:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
  border: none;
  padding: 4px 12px;
}

:deep(.el-tag--primary) {
  background: rgba(59, 130, 246, 0.1);
  color: #1d4ed8;
}

:deep(.el-tag--success) {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
}

:deep(.el-tag--warning) {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
}

:deep(.el-tag--danger) {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

:deep(.el-tag--info) {
  background: rgba(107, 114, 128, 0.1);
  color: #4b5563;
}

/* 下拉菜单样式 */
:deep(.el-dropdown-menu) {
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border: 1px solid #e5e7eb;
}

:deep(.el-dropdown-menu__item) {
  padding: 8px 16px;
  transition: all 0.2s ease;
}

:deep(.el-dropdown-menu__item:hover) {
  background: #f3f4f6;
  color: #3b82f6;
}

/* 分页样式 */
:deep(.el-pagination) {
  margin-top: 20px;
  justify-content: center;
}

:deep(.el-pagination .el-pager li) {
  border-radius: 6px;
  margin: 0 2px;
}

:deep(.el-pagination .el-pager li.is-active) {
  background: #3b82f6;
  color: white;
}
</style>
