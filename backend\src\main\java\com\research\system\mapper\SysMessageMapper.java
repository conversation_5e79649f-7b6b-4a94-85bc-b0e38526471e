package com.research.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.research.system.domain.SysMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 站内消息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Mapper
public interface SysMessageMapper extends BaseMapper<SysMessage> {

    /**
     * 查询收件箱消息列表
     * 
     * @param page 分页参数
     * @param message 查询条件
     * @param userId 用户ID
     * @return 收件箱消息列表
     */
    IPage<SysMessage> selectInboxMessageList(Page<SysMessage> page, @Param("message") SysMessage message, @Param("userId") Long userId);

    /**
     * 查询收件箱消息总数
     *
     * @param message 查询条件
     * @param userId 用户ID
     * @return 消息总数
     */
    long selectInboxMessageCount(@Param("message") SysMessage message, @Param("userId") Long userId);

    /**
     * 查询发件箱消息列表
     *
     * @param page 分页参数
     * @param message 查询条件
     * @param userId 用户ID
     * @return 发件箱消息列表
     */
    IPage<SysMessage> selectOutboxMessageList(Page<SysMessage> page, @Param("message") SysMessage message, @Param("userId") Long userId);

    /**
     * 查询发件箱消息总数
     *
     * @param message 查询条件
     * @param userId 用户ID
     * @return 消息总数
     */
    long selectOutboxMessageCount(@Param("message") SysMessage message, @Param("userId") Long userId);

    /**
     * 查询消息详情
     * 
     * @param messageId 消息ID
     * @param userId 用户ID（用于权限检查）
     * @return 消息详情
     */
    SysMessage selectMessageDetail(@Param("messageId") Long messageId, @Param("userId") Long userId);

    /**
     * 查询用户未读消息数量
     * 
     * @param userId 用户ID
     * @return 未读消息数量
     */
    Long selectUnreadMessageCount(@Param("userId") Long userId);

    /**
     * 标记消息为已读
     * 
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 影响行数
     */
    int markAsRead(@Param("messageId") Long messageId, @Param("userId") Long userId);

    /**
     * 批量标记消息为已读
     * 
     * @param messageIds 消息ID列表
     * @param userId 用户ID
     * @return 影响行数
     */
    int batchMarkAsRead(@Param("messageIds") List<Long> messageIds, @Param("userId") Long userId);

    /**
     * 全部标记为已读
     * 
     * @param userId 用户ID
     * @return 影响行数
     */
    int markAllAsRead(@Param("userId") Long userId);

    /**
     * 删除消息（逻辑删除）
     * 
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteMessage(@Param("messageId") Long messageId, @Param("userId") Long userId);

    /**
     * 批量删除消息
     * 
     * @param messageIds 消息ID列表
     * @param userId 用户ID
     * @return 影响行数
     */
    int batchDeleteMessage(@Param("messageIds") List<Long> messageIds, @Param("userId") Long userId);

    /**
     * 搜索消息
     * 
     * @param page 分页参数
     * @param keyword 搜索关键词
     * @param userId 用户ID
     * @param messageType 消息类型
     * @return 搜索结果
     */
    IPage<SysMessage> searchMessages(Page<SysMessage> page, @Param("keyword") String keyword, 
                                   @Param("userId") Long userId, @Param("messageType") String messageType);

    /**
     * 查询消息统计信息
     * 
     * @param userId 用户ID
     * @return 消息统计信息
     */
    Map<String, Object> selectMessageStatistics(@Param("userId") Long userId);

    /**
     * 查询最新消息（用于工作台展示）
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最新消息列表
     */
    List<SysMessage> selectLatestMessages(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 发送消息给多个用户
     * 
     * @param message 消息内容
     * @param receiverIds 接收人ID列表
     * @return 影响行数
     */
    int sendMessageToUsers(@Param("message") SysMessage message, @Param("receiverIds") List<Long> receiverIds);

    /**
     * 发送消息给部门所有用户
     * 
     * @param message 消息内容
     * @param deptIds 部门ID列表
     * @return 影响行数
     */
    int sendMessageToDepts(@Param("message") SysMessage message, @Param("deptIds") List<Long> deptIds);
}
