package com.research.project.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 合作单位实体
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@TableName("partner")
public class Partner {

    /** 合作单位ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 单位编码 */
    private String partnerCode;

    /** 单位名称 */
    private String partnerName;

    /** 单位类型：企业、高校、科研院所、政府机构等 */
    private String partnerType;

    /** 法定代表人 */
    private String legalRepresentative;

    /** 联系人 */
    private String contactPerson;

    /** 联系电话 */
    private String contactPhone;

    /** 联系邮箱 */
    private String contactEmail;

    /** 单位地址 */
    private String address;

    /** 邮政编码 */
    private String postalCode;

    /** 官方网站 */
    private String website;

    /** 经营范围 */
    private String businessScope;

    /** 注册资本 */
    private BigDecimal registrationCapital;

    /** 成立日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date establishmentDate;

    /** 统一社会信用代码 */
    private String creditCode;

    /** 税务登记号 */
    private String taxNumber;

    /** 开户银行 */
    private String bankName;

    /** 银行账号 */
    private String bankAccount;

    /** 状态：0-禁用，1-正常，2-待审核 */
    private Integer status;

    /** 合作等级：A、B、C、D */
    private String cooperationLevel;

    /** 合作次数 */
    private Integer cooperationCount;

    /** 累计合同金额 */
    private BigDecimal totalContractAmount;

    /** 最近合作日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date lastCooperationDate;

    /** 资质文件路径 */
    private String qualificationFilePath;

    /** 创建者 */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 备注 */
    private String remark;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getPartnerType() {
        return partnerType;
    }

    public void setPartnerType(String partnerType) {
        this.partnerType = partnerType;
    }

    public String getLegalRepresentative() {
        return legalRepresentative;
    }

    public void setLegalRepresentative(String legalRepresentative) {
        this.legalRepresentative = legalRepresentative;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public BigDecimal getRegistrationCapital() {
        return registrationCapital;
    }

    public void setRegistrationCapital(BigDecimal registrationCapital) {
        this.registrationCapital = registrationCapital;
    }

    public Date getEstablishmentDate() {
        return establishmentDate;
    }

    public void setEstablishmentDate(Date establishmentDate) {
        this.establishmentDate = establishmentDate;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCooperationLevel() {
        return cooperationLevel;
    }

    public void setCooperationLevel(String cooperationLevel) {
        this.cooperationLevel = cooperationLevel;
    }

    public Integer getCooperationCount() {
        return cooperationCount;
    }

    public void setCooperationCount(Integer cooperationCount) {
        this.cooperationCount = cooperationCount;
    }

    public BigDecimal getTotalContractAmount() {
        return totalContractAmount;
    }

    public void setTotalContractAmount(BigDecimal totalContractAmount) {
        this.totalContractAmount = totalContractAmount;
    }

    public Date getLastCooperationDate() {
        return lastCooperationDate;
    }

    public void setLastCooperationDate(Date lastCooperationDate) {
        this.lastCooperationDate = lastCooperationDate;
    }

    public String getQualificationFilePath() {
        return qualificationFilePath;
    }

    public void setQualificationFilePath(String qualificationFilePath) {
        this.qualificationFilePath = qualificationFilePath;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "Partner{" +
                "id=" + id +
                ", partnerCode='" + partnerCode + '\'' +
                ", partnerName='" + partnerName + '\'' +
                ", partnerType='" + partnerType + '\'' +
                ", legalRepresentative='" + legalRepresentative + '\'' +
                ", contactPerson='" + contactPerson + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", contactEmail='" + contactEmail + '\'' +
                ", address='" + address + '\'' +
                ", status=" + status +
                ", cooperationLevel='" + cooperationLevel + '\'' +
                ", cooperationCount=" + cooperationCount +
                ", totalContractAmount=" + totalContractAmount +
                ", remark='" + remark + '\'' +
                '}';
    }
}
