package com.research.workflow.service;

import com.research.workflow.domain.WorkflowVersion;

import java.util.List;
import java.util.Map;

/**
 * 工作流版本管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IWorkflowVersionService {
    
    /**
     * 查询流程版本列表
     * 
     * @param processDefinitionKey 流程定义Key
     * @return 版本列表
     */
    List<WorkflowVersion> selectVersionList(String processDefinitionKey);

    /**
     * 根据版本ID查询版本信息
     * 
     * @param versionId 版本ID
     * @return 版本信息
     */
    WorkflowVersion selectVersionById(String versionId);

    /**
     * 创建新版本
     * 
     * @param workflowVersion 版本信息
     * @return 结果
     */
    int createVersion(WorkflowVersion workflowVersion);

    /**
     * 发布版本
     * 
     * @param versionId 版本ID
     * @param publishStrategy 发布策略
     * @param trafficRatio 流量比例
     * @param targetUsers 目标用户
     * @return 结果
     */
    int publishVersion(String versionId, Integer publishStrategy, Integer trafficRatio, String targetUsers);

    /**
     * 停用版本
     * 
     * @param versionId 版本ID
     * @return 结果
     */
    int deprecateVersion(String versionId);

    /**
     * 版本回滚
     * 
     * @param processDefinitionKey 流程定义Key
     * @param targetVersionId 目标版本ID
     * @return 结果
     */
    int rollbackVersion(String processDefinitionKey, String targetVersionId);

    /**
     * 切换版本
     * 
     * @param processDefinitionKey 流程定义Key
     * @param targetVersionId 目标版本ID
     * @return 结果
     */
    int switchVersion(String processDefinitionKey, String targetVersionId);

    /**
     * 删除版本
     * 
     * @param versionId 版本ID
     * @return 结果
     */
    int deleteVersionById(String versionId);

    /**
     * 比较版本
     * 
     * @param sourceVersionId 源版本ID
     * @param targetVersionId 目标版本ID
     * @return 比较结果
     */
    Map<String, Object> compareVersions(String sourceVersionId, String targetVersionId);

    /**
     * 获取版本统计信息
     * 
     * @param processDefinitionKey 流程定义Key
     * @return 统计信息
     */
    Map<String, Object> getVersionStatistics(String processDefinitionKey);

    /**
     * 获取路由统计信息
     * 
     * @param processDefinitionKey 流程定义Key
     * @return 路由统计
     */
    Map<String, Object> getRouteStatistics(String processDefinitionKey);

    /**
     * 创建全量发布路由
     * 
     * @param processDefinitionKey 流程定义Key
     * @param processDefinitionId 流程定义ID
     * @param versionId 版本ID
     * @return 结果
     */
    int createFullReleaseRoute(String processDefinitionKey, String processDefinitionId, String versionId);

    /**
     * 创建灰度发布路由
     * 
     * @param processDefinitionKey 流程定义Key
     * @param processDefinitionId 流程定义ID
     * @param versionId 版本ID
     * @param trafficRatio 流量比例
     * @return 结果
     */
    int createGrayReleaseRoute(String processDefinitionKey, String processDefinitionId, String versionId, Integer trafficRatio);

    /**
     * 创建A/B测试路由
     * 
     * @param processDefinitionKey 流程定义Key
     * @param processDefinitionId 流程定义ID
     * @param versionId 版本ID
     * @param targetUsers 目标用户列表
     * @return 结果
     */
    int createABTestRoute(String processDefinitionKey, String processDefinitionId, String versionId, List<String> targetUsers);

    /**
     * 创建基于部门的路由
     * 
     * @param processDefinitionKey 流程定义Key
     * @param processDefinitionId 流程定义ID
     * @param versionId 版本ID
     * @param departments 部门列表
     * @return 结果
     */
    int createDepartmentRoute(String processDefinitionKey, String processDefinitionId, String versionId, List<String> departments);

    /**
     * 测试版本路由
     * 
     * @param processDefinitionKey 流程定义Key
     * @param userId 用户ID
     * @param userGroups 用户组
     * @param department 部门
     * @param context 上下文
     * @return 路由结果
     */
    String testVersionRoute(String processDefinitionKey, String userId, String userGroups, String department, Map<String, Object> context);

    /**
     * 获取版本分支信息
     * 
     * @param processDefinitionKey 流程定义Key
     * @return 分支信息
     */
    Map<String, Object> getVersionBranches(String processDefinitionKey);

    /**
     * 创建版本分支
     * 
     * @param sourceVersionId 源版本ID
     * @param branchName 分支名称
     * @param branchType 分支类型
     * @return 结果
     */
    int createVersionBranch(String sourceVersionId, String branchName, Integer branchType);

    /**
     * 合并版本分支
     * 
     * @param sourceVersionId 源版本ID
     * @param targetVersionId 目标版本ID
     * @return 结果
     */
    int mergeVersionBranch(String sourceVersionId, String targetVersionId);

    /**
     * 获取版本变更历史
     * 
     * @param versionId 版本ID
     * @return 变更历史
     */
    List<Map<String, Object>> getVersionHistory(String versionId);

    /**
     * 版本标签管理
     * 
     * @param versionId 版本ID
     * @param tags 标签列表
     * @return 结果
     */
    int manageVersionTags(String versionId, List<String> tags);

    /**
     * 获取版本依赖关系
     * 
     * @param versionId 版本ID
     * @return 依赖关系
     */
    Map<String, Object> getVersionDependencies(String versionId);

    /**
     * 版本兼容性检查
     * 
     * @param sourceVersionId 源版本ID
     * @param targetVersionId 目标版本ID
     * @return 兼容性检查结果
     */
    Map<String, Object> checkVersionCompatibility(String sourceVersionId, String targetVersionId);
}
