# Nginx配置指南 - 科研成果多维敏捷管控中心

## 🎯 配置目标
在不影响现有项目的情况下，为科研管理系统添加Nginx配置。

## 📝 配置步骤

### 1. 备份现有配置
```bash
cp /usr/local/nginx/conf/nginx.conf /usr/local/nginx/conf/nginx.conf.backup
```

### 2. 编辑主配置文件
```bash
vi /usr/local/nginx/conf/nginx.conf
```

### 3. 在http块中添加include指令

找到http块，在其中添加一行：
```nginx
http {
    # 现有配置保持不变...
    include       mime.types;
    default_type  application/octet-stream;
    
    # 添加这一行 - 包含科研管理系统配置
    include research-management.conf;
    
    # 现有的server块保持不变...
    server {
        listen       80;
        server_name  localhost;
        # ... 现有配置
    }
    
    # 其他现有配置保持不变...
}
```

### 4. 验证配置文件位置
确认以下文件已创建：
```bash
ls -la /usr/local/nginx/conf/research-management.conf
```

### 5. 测试配置
```bash
nginx -t
```

### 6. 重新加载配置
```bash
nginx -s reload
```

## 📋 完整的配置文件内容

### research-management.conf 内容：
```nginx
# 科研成果多维敏捷管控中心配置
server {
    listen 3000;
    server_name _;
    root /var/www/research-management;
    index index.html;

    # 处理前端路由
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
}
```

## 🔍 验证部署

### 1. 检查端口监听
```bash
netstat -tlnp | grep :3000
netstat -tlnp | grep :8989
```

### 2. 检查进程状态
```bash
ps aux | grep research-management
ps aux | grep nginx
```

### 3. 测试访问
```bash
# 测试前端
curl -I http://localhost:3000

# 测试后端
curl -I http://localhost:8989/actuator/health
```

## 🚨 故障排除

### 问题1: 端口冲突
如果3000端口被占用，可以修改配置文件中的端口号：
```bash
vi /usr/local/nginx/conf/research-management.conf
# 将 listen 3000; 改为其他端口，如 listen 3001;
```

### 问题2: 权限问题
```bash
# 检查文件权限
ls -la /var/www/research-management/
# 修复权限
chown -R nginx:nginx /var/www/research-management/
chmod -R 755 /var/www/research-management/
```

### 问题3: 配置语法错误
```bash
# 测试配置
nginx -t
# 查看错误日志
tail -f /usr/local/nginx/logs/error.log
```

## 📊 监控和维护

### 查看访问日志
```bash
tail -f /usr/local/nginx/logs/access.log | grep ":3000"
```

### 查看应用日志
```bash
tail -f /opt/research-management/app.log
```

### 重启服务
```bash
# 重启后端
/opt/research-management/restart.sh

# 重新加载Nginx
nginx -s reload
```

## 🔒 安全建议

1. **定期备份配置文件**
2. **监控访问日志**
3. **定期更新系统和应用**
4. **配置防火墙规则**
5. **使用HTTPS（生产环境）**

## 📞 技术支持

如果遇到问题，请提供以下信息：
1. 错误日志内容
2. 配置文件内容
3. 系统环境信息
4. 端口占用情况
