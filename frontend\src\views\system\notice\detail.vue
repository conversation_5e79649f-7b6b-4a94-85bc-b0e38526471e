<template>
  <div class="app-container">
    <el-card v-loading="loading" class="notice-detail-card">
      <!-- 公告头部 -->
      <div class="notice-header">
        <div class="notice-title-section">
          <h1 class="notice-title">
            <el-tag v-if="notice.isTop === '1'" type="danger" size="small">置顶</el-tag>
            <el-tag v-if="notice.importance === '3'" type="danger" size="small">紧急</el-tag>
            <el-tag v-else-if="notice.importance === '2'" type="warning" size="small">重要</el-tag>
            {{ notice.noticeTitle }}
          </h1>
          <div class="notice-meta">
            <div class="meta-item">
              <el-icon><User /></el-icon>
              <span>发布人：{{ notice.publishBy }}</span>
            </div>
            <div class="meta-item">
              <el-icon><Calendar /></el-icon>
              <span>发布时间：{{ parseTime(notice.publishTime) }}</span>
            </div>
            <div class="meta-item">
              <el-icon><View /></el-icon>
              <span>阅读次数：{{ notice.readCount || 0 }}</span>
            </div>
            <div class="meta-item">
              <el-tag :type="notice.noticeType === '1' ? 'primary' : 'success'" size="small">
                {{ notice.noticeType === '1' ? '通知' : '公告' }}
              </el-tag>
            </div>
          </div>
        </div>
        
        <div class="notice-actions">
          <el-button type="primary" @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <el-button 
            v-if="hasPermission(['system:notice:edit'])" 
            type="success" 
            @click="handleEdit"
          >
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-dropdown @command="handleCommand">
            <el-button type="info">
              更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="print">
                  <el-icon><Printer /></el-icon>打印
                </el-dropdown-item>
                <el-dropdown-item command="share">
                  <el-icon><Share /></el-icon>分享
                </el-dropdown-item>
                <el-dropdown-item command="stats" v-if="hasPermission(['system:notice:stats'])">
                  <el-icon><DataAnalysis /></el-icon>阅读统计
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <!-- 公告内容 -->
      <div class="notice-content">
        <div class="content-wrapper" v-html="notice.noticeContent"></div>
      </div>
      
      <!-- 附件下载 -->
      <div v-if="notice.attachmentPath" class="notice-attachments">
        <h3>附件下载</h3>
        <div class="attachment-list">
          <div class="attachment-item">
            <el-icon><Document /></el-icon>
            <span class="attachment-name">{{ getFileName(notice.attachmentPath) }}</span>
            <el-button type="primary" size="small" @click="downloadAttachment">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 有效期提醒 -->
      <div v-if="notice.validEndTime" class="notice-validity">
        <el-alert
          :title="getValidityText()"
          :type="getValidityType()"
          :closable="false"
          show-icon
        />
      </div>
    </el-card>

    <!-- 阅读统计对话框 -->
    <el-dialog v-model="statsVisible" title="阅读统计" width="800px">
      <div class="stats-content">
        <div class="stats-summary">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-statistic title="总阅读次数" :value="readStats.totalReads || 0" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="阅读人数" :value="readStats.readUsers || 0" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="阅读率" :value="readStats.readRate || 0" suffix="%" />
            </el-col>
          </el-row>
        </div>
        
        <div class="stats-chart">
          <div ref="chartRef" style="height: 300px;"></div>
        </div>
        
        <div class="stats-table">
          <el-table :data="readStats.readList || []" height="200">
            <el-table-column prop="userName" label="用户姓名" />
            <el-table-column prop="readTime" label="阅读时间" />
            <el-table-column prop="readDuration" label="阅读时长(秒)" />
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  User, Calendar, View, ArrowLeft, Edit, Printer, Share,
  DataAnalysis, Document, Download, ArrowDown
} from '@element-plus/icons-vue'
import { viewNotice, getNoticeReadStats } from '@/api/system/notice'
import * as echarts from 'echarts'

const { proxy } = getCurrentInstance() as any

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const notice = ref<any>({})
const statsVisible = ref(false)
const readStats = ref<any>({})
const chartRef = ref<HTMLElement>()
const startTime = ref(Date.now())

// 获取公告详情
const getNoticeDetail = async () => {
  try {
    loading.value = true
    const noticeId = route.params.id as string
    const readDuration = Math.floor((Date.now() - startTime.value) / 1000)
    
    const { data } = await viewNotice(parseInt(noticeId), readDuration)
    notice.value = data
  } catch (error) {
    console.error('获取公告详情失败:', error)
    ElMessage.error('获取公告详情失败')
  } finally {
    loading.value = false
  }
}

// 获取阅读统计
const getReadStats = async () => {
  try {
    const { data } = await getNoticeReadStats(notice.value.noticeId)
    readStats.value = data
    
    // 渲染图表
    nextTick(() => {
      renderChart()
    })
  } catch (error) {
    console.error('获取阅读统计失败:', error)
    ElMessage.error('获取阅读统计失败')
  }
}

// 渲染统计图表
const renderChart = () => {
  if (!chartRef.value) return
  
  const chart = echarts.init(chartRef.value)
  const option = {
    title: {
      text: '阅读趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: readStats.value.dateList || []
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: readStats.value.readCounts || [],
      type: 'line',
      smooth: true,
      itemStyle: {
        color: '#409EFF'
      }
    }]
  }
  
  chart.setOption(option)
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 编辑公告
const handleEdit = () => {
  router.push(`/system/notice/edit/${notice.value.noticeId}`)
}

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  switch (command) {
    case 'print':
      window.print()
      break
    case 'share':
      handleShare()
      break
    case 'stats':
      handleStats()
      break
  }
}

// 分享公告
const handleShare = () => {
  const url = window.location.href
  navigator.clipboard.writeText(url).then(() => {
    ElMessage.success('链接已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

// 查看统计
const handleStats = async () => {
  statsVisible.value = true
  await getReadStats()
}

// 下载附件
const downloadAttachment = () => {
  if (notice.value.attachmentPath) {
    const link = document.createElement('a')
    link.href = notice.value.attachmentPath
    link.download = getFileName(notice.value.attachmentPath)
    link.click()
  }
}

// 获取文件名
const getFileName = (path: string) => {
  if (!path) return ''
  return path.split('/').pop() || ''
}

// 获取有效期文本
const getValidityText = () => {
  if (!notice.value.validEndTime) return ''
  
  const endTime = new Date(notice.value.validEndTime)
  const now = new Date()
  const diffDays = Math.ceil((endTime.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) {
    return '此公告已过期'
  } else if (diffDays === 0) {
    return '此公告今日到期'
  } else if (diffDays <= 7) {
    return `此公告将在 ${diffDays} 天后到期`
  } else {
    return `此公告有效期至 ${parseTime(notice.value.validEndTime, '{y}-{m}-{d}')}`
  }
}

// 获取有效期类型
const getValidityType = () => {
  if (!notice.value.validEndTime) return 'info'
  
  const endTime = new Date(notice.value.validEndTime)
  const now = new Date()
  const diffDays = Math.ceil((endTime.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return 'error'
  if (diffDays <= 3) return 'warning'
  return 'info'
}

// 记录阅读时长
const recordReadDuration = () => {
  const readDuration = Math.floor((Date.now() - startTime.value) / 1000)
  if (readDuration > 5) { // 阅读超过5秒才记录
    // 这里可以调用API记录阅读时长
  }
}

onMounted(() => {
  getNoticeDetail()
})

onUnmounted(() => {
  recordReadDuration()
})
</script>

<style scoped>
.notice-detail-card {
  margin: 20px;
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 30px;
}

.notice-title-section {
  flex: 1;
}

.notice-title {
  margin: 0 0 15px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
  display: flex;
  align-items: center;
  gap: 10px;
}

.notice-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #606266;
}

.notice-actions {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.notice-content {
  margin-bottom: 30px;
}

.content-wrapper {
  line-height: 1.8;
  font-size: 16px;
  color: #303133;
}

.content-wrapper :deep(img) {
  max-width: 100%;
  height: auto;
}

.notice-attachments {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.notice-attachments h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #303133;
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.attachment-name {
  flex: 1;
  font-size: 14px;
  color: #303133;
}

.notice-validity {
  margin-top: 20px;
}

.stats-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stats-summary {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

@media (max-width: 768px) {
  .notice-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .notice-title {
    font-size: 20px;
  }
  
  .notice-meta {
    flex-direction: column;
    gap: 10px;
  }
  
  .notice-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .content-wrapper {
    font-size: 14px;
  }
  
  .attachment-item {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media print {
  .notice-actions {
    display: none;
  }
  
  .notice-detail-card {
    margin: 0;
    box-shadow: none;
  }
}
</style>
