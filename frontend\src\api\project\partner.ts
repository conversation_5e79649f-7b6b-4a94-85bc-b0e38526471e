import request from '@/utils/request'

// 查询合作单位列表
export function listPartner(query: any) {
  return request({
    url: '/project/partner/list',
    method: 'get',
    params: query
  })
}

// 查询合作单位详细
export function getPartner(id: number) {
  return request({
    url: '/project/partner/' + id,
    method: 'get'
  })
}

// 根据单位编码查询合作单位
export function getPartnerByCode(partnerCode: string) {
  return request({
    url: '/project/partner/code/' + partnerCode,
    method: 'get'
  })
}

// 新增合作单位
export function addPartner(data: any) {
  return request({
    url: '/project/partner',
    method: 'post',
    data: data
  })
}

// 修改合作单位
export function updatePartner(data: any) {
  return request({
    url: '/project/partner',
    method: 'put',
    data: data
  })
}

// 删除合作单位
export function delPartner(id: number) {
  return request({
    url: '/project/partner/' + id,
    method: 'delete'
  })
}

// 批量删除合作单位
export function delPartners(ids: number[]) {
  return request({
    url: '/project/partner/' + ids.join(','),
    method: 'delete'
  })
}

// 导出合作单位
export function exportPartner(query: any) {
  return request({
    url: '/project/partner/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 根据单位类型查询合作单位
export function getPartnersByType(partnerType: string) {
  return request({
    url: '/project/partner/type/' + partnerType,
    method: 'get'
  })
}

// 根据合作等级查询合作单位
export function getPartnersByLevel(cooperationLevel: string) {
  return request({
    url: '/project/partner/level/' + cooperationLevel,
    method: 'get'
  })
}

// 根据状态查询合作单位
export function getPartnersByStatus(status: number) {
  return request({
    url: '/project/partner/status/' + status,
    method: 'get'
  })
}

// 搜索合作单位
export function searchPartners(keyword: string) {
  return request({
    url: '/project/partner/search',
    method: 'get',
    params: { keyword }
  })
}

// 启用合作单位
export function enablePartner(id: number) {
  return request({
    url: '/project/partner/enable/' + id,
    method: 'put'
  })
}

// 禁用合作单位
export function disablePartner(id: number) {
  return request({
    url: '/project/partner/disable/' + id,
    method: 'put'
  })
}

// 审核合作单位
export function auditPartner(id: number, approved: boolean) {
  return request({
    url: '/project/partner/audit/' + id,
    method: 'put',
    params: { approved }
  })
}

// 更新合作等级
export function updateCooperationLevel(id: number, cooperationLevel: string) {
  return request({
    url: '/project/partner/level/' + id,
    method: 'put',
    params: { cooperationLevel }
  })
}

// 更新合作统计信息
export function updateCooperationStatistics(id: number, cooperationCount: number, 
                                           totalContractAmount: number, 
                                           lastCooperationDate: Date) {
  return request({
    url: '/project/partner/statistics/' + id,
    method: 'put',
    params: { cooperationCount, totalContractAmount, lastCooperationDate }
  })
}

// 查询合作单位统计信息
export function getPartnerStatistics() {
  return request({
    url: '/project/partner/statistics',
    method: 'get'
  })
}

// 查询单位类型统计
export function getPartnerTypeStatistics() {
  return request({
    url: '/project/partner/statistics/type',
    method: 'get'
  })
}

// 查询合作等级统计
export function getPartnerLevelStatistics() {
  return request({
    url: '/project/partner/statistics/level',
    method: 'get'
  })
}

// 查询合作单位状态统计
export function getPartnerStatusStatistics() {
  return request({
    url: '/project/partner/statistics/status',
    method: 'get'
  })
}

// 查询年度合作统计
export function getPartnerYearlyStatistics(year: number) {
  return request({
    url: '/project/partner/statistics/yearly',
    method: 'get',
    params: { year }
  })
}

// 查询合作单位合同金额统计
export function getPartnerContractAmountStatistics() {
  return request({
    url: '/project/partner/statistics/amount',
    method: 'get'
  })
}

// 查询合作活跃度统计
export function getPartnerActivityStatistics() {
  return request({
    url: '/project/partner/statistics/activity',
    method: 'get'
  })
}

// 查询优质合作单位
export function getQualityPartners(minScore: number = 8.0, minCooperationCount: number = 3) {
  return request({
    url: '/project/partner/quality',
    method: 'get',
    params: { minScore, minCooperationCount }
  })
}

// 查询新增合作单位趋势
export function getNewPartnerTrend(startDate: string, endDate: string) {
  return request({
    url: '/project/partner/statistics/new-trend',
    method: 'get',
    params: { startDate, endDate }
  })
}

// 查询合作单位地域分布
export function getPartnerRegionDistribution() {
  return request({
    url: '/project/partner/statistics/region',
    method: 'get'
  })
}

// 查询即将过期资质的合作单位
export function getPartnersWithExpiringQualifications(days: number = 30) {
  return request({
    url: '/project/partner/expiring-qualifications',
    method: 'get',
    params: { days }
  })
}

// 生成单位编码
export function generatePartnerCode(partnerType?: string) {
  return request({
    url: '/project/partner/generate-code',
    method: 'get',
    params: { partnerType }
  })
}

// 检查单位编码是否存在
export function checkPartnerCode(partnerCode: string, id?: number) {
  return request({
    url: '/project/partner/check-code',
    method: 'get',
    params: { partnerCode, id }
  })
}

// 批量导入合作单位
export function importPartners(data: any) {
  return request({
    url: '/project/partner/import',
    method: 'post',
    data: data
  })
}

// 合作单位评价
export function evaluatePartner(partnerId: number, projectId?: number, contractId?: number, 
                               scores: any, content?: string) {
  return request({
    url: '/project/partner/evaluate/' + partnerId,
    method: 'post',
    data: scores,
    params: { projectId, contractId, content }
  })
}
