import request from '@/utils/request'

// 查询通知公告列表
export function listNotice(query: any) {
  return request({
    url: '/system/notice/list',
    method: 'get',
    params: query
  })
}

// 查询用户可见的通知公告列表
export function listUserNotice(query: any) {
  return request({
    url: '/system/notice/userList',
    method: 'get',
    params: query
  })
}

// 查询通知公告详细
export function getNotice(noticeId: number) {
  return request({
    url: '/system/notice/' + noticeId,
    method: 'get'
  })
}

// 查看通知公告详情（用户端）
export function viewNotice(noticeId: number, readDuration?: number) {
  return request({
    url: '/system/notice/view/' + noticeId,
    method: 'get',
    params: { readDuration }
  })
}

// 新增通知公告
export function addNotice(data: any) {
  return request({
    url: '/system/notice',
    method: 'post',
    data: data
  })
}

// 修改通知公告
export function updateNotice(data: any) {
  return request({
    url: '/system/notice',
    method: 'put',
    data: data
  })
}

// 删除通知公告
export function delNotice(noticeIds: number[]) {
  return request({
    url: '/system/notice/' + noticeIds,
    method: 'delete'
  })
}

// 发布通知公告
export function publishNotice(noticeId: number) {
  return request({
    url: '/system/notice/publish/' + noticeId,
    method: 'post'
  })
}

// 撤回通知公告
export function withdrawNotice(noticeId: number) {
  return request({
    url: '/system/notice/withdraw/' + noticeId,
    method: 'post'
  })
}

// 置顶/取消置顶通知公告
export function setNoticeTop(noticeId: number, isTop: string) {
  return request({
    url: '/system/notice/top/' + noticeId,
    method: 'post',
    params: { isTop }
  })
}

// 查询最新公告列表
export function getLatestNotices(limit: number = 5) {
  return request({
    url: '/system/notice/latest',
    method: 'get',
    params: { limit }
  })
}

// 查询用户未读公告数量
export function getUnreadNoticeCount() {
  return request({
    url: '/system/notice/unreadCount',
    method: 'get'
  })
}

// 查询公告阅读统计
export function getNoticeReadStats(noticeId: number) {
  return request({
    url: '/system/notice/readStats/' + noticeId,
    method: 'get'
  })
}

// 全文搜索公告
export function searchNotices(query: any) {
  return request({
    url: '/system/notice/search',
    method: 'get',
    params: query
  })
}

// 导出通知公告数据
export function exportNotice(query: any) {
  return request({
    url: '/system/notice/export',
    method: 'post',
    data: query
  })
}

// 批量导入通知公告
export function importNotice(data: any, isUpdateSupport: boolean = false) {
  return request({
    url: '/system/notice/importData',
    method: 'post',
    data: data,
    params: { isUpdateSupport }
  })
}
