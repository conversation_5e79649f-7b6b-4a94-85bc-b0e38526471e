<template>
  <el-dialog
    title="合同统计分析"
    v-model="visible"
    width="1400px"
    append-to-body
    @close="handleClose"
  >
    <div v-loading="loading">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="mb-4">
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.totalCount || 0 }}</div>
              <div class="stat-label">合同总数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ formatMoney(statistics.totalAmount) }}</div>
              <div class="stat-label">总金额(万元)</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ formatMoney(statistics.avgAmount) }}</div>
              <div class="stat-label">平均金额(万元)</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.expiringCount || 0 }}</div>
              <div class="stat-label">即将到期合同</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 图表区域 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>合同状态分布</span>
            </template>
            <div ref="statusChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>合同类型分布</span>
            </template>
            <div ref="typeChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="mt-4">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>部门合同统计</span>
            </template>
            <div ref="deptChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>合作单位统计</span>
            </template>
            <div ref="partnerChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="mt-4">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>合同金额分布</span>
            </template>
            <div ref="amountChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>合同执行情况</span>
            </template>
            <div ref="executionChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="mt-4">
        <el-col :span="24">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>合同签署趋势</span>
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="getSigningTrendData"
                  style="width: 240px;"
                />
              </div>
            </template>
            <div ref="trendChartRef" style="height: 400px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 详细统计表格 -->
      <el-row :gutter="20" class="mt-4">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>合同状态详情</span>
            </template>
            <el-table :data="statusData" style="width: 100%">
              <el-table-column prop="statusName" label="状态" />
              <el-table-column prop="contractCount" label="合同数量" />
              <el-table-column prop="totalAmount" label="总金额">
                <template #default="scope">
                  {{ formatMoney(scope.row.totalAmount) }}
                </template>
              </el-table-column>
              <el-table-column label="占比">
                <template #default="scope">
                  {{ ((scope.row.contractCount / statistics.totalCount) * 100).toFixed(1) }}%
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>合同类型详情</span>
            </template>
            <el-table :data="typeData" style="width: 100%">
              <el-table-column prop="contractType" label="类型" />
              <el-table-column prop="contractCount" label="合同数量" />
              <el-table-column prop="totalAmount" label="总金额">
                <template #default="scope">
                  {{ formatMoney(scope.row.totalAmount) }}
                </template>
              </el-table-column>
              <el-table-column prop="avgAmount" label="平均金额">
                <template #default="scope">
                  {{ formatMoney(scope.row.avgAmount) }}
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="exportStatistics">导出统计</el-button>
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { 
  getContractStatistics,
  getContractStatusStatistics,
  getContractTypeStatistics,
  getContractDeptStatistics,
  getContractPartnerStatistics,
  getContractAmountStatistics,
  getContractExecutionStatistics,
  getContractSigningTrend
} from "@/api/project/contract"
import * as echarts from 'echarts'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const loading = ref(false)
const statistics = ref({})
const statusData = ref([])
const typeData = ref([])
const deptData = ref([])
const partnerData = ref([])
const amountData = ref({})
const executionData = ref([])
const trendData = ref([])
const dateRange = ref([])

// 图表引用
const statusChartRef = ref()
const typeChartRef = ref()
const deptChartRef = ref()
const partnerChartRef = ref()
const amountChartRef = ref()
const executionChartRef = ref()
const trendChartRef = ref()

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal) {
    loadStatistics()
  }
})

/** 加载统计数据 */
async function loadStatistics() {
  loading.value = true
  try {
    // 设置默认日期范围（最近一年）
    const endDate = new Date()
    const startDate = new Date()
    startDate.setFullYear(endDate.getFullYear() - 1)
    dateRange.value = [startDate, endDate]
    
    // 并行加载所有统计数据
    const [
      statisticsRes,
      statusRes,
      typeRes,
      deptRes,
      partnerRes,
      amountRes,
      executionRes,
      trendRes
    ] = await Promise.all([
      getContractStatistics(),
      getContractStatusStatistics(),
      getContractTypeStatistics(),
      getContractDeptStatistics(),
      getContractPartnerStatistics(),
      getContractAmountStatistics(),
      getContractExecutionStatistics(),
      getContractSigningTrend(formatDate(startDate), formatDate(endDate))
    ])

    statistics.value = statisticsRes.data
    statusData.value = statusRes.data
    typeData.value = typeRes.data
    deptData.value = deptRes.data
    partnerData.value = partnerRes.data
    amountData.value = amountRes.data
    executionData.value = executionRes.data
    trendData.value = trendRes.data

    // 渲染图表
    nextTick(() => {
      renderStatusChart()
      renderTypeChart()
      renderDeptChart()
      renderPartnerChart()
      renderAmountChart()
      renderExecutionChart()
      renderTrendChart()
    })
  } catch (error) {
    console.error('加载统计数据失败:', error)
  } finally {
    loading.value = false
  }
}

/** 获取签署趋势数据 */
async function getSigningTrendData() {
  if (!dateRange.value || dateRange.value.length !== 2) return
  
  try {
    const response = await getContractSigningTrend(
      formatDate(dateRange.value[0]), 
      formatDate(dateRange.value[1])
    )
    trendData.value = response.data
    renderTrendChart()
  } catch (error) {
    console.error('获取签署趋势数据失败:', error)
  }
}

/** 渲染状态分布图表 */
function renderStatusChart() {
  if (!statusChartRef.value || !statusData.value.length) return
  
  const chart = echarts.init(statusChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '合同状态',
        type: 'pie',
        radius: '50%',
        data: statusData.value.map(item => ({
          value: item.contractCount,
          name: item.statusName
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 渲染类型分布图表 */
function renderTypeChart() {
  if (!typeChartRef.value || !typeData.value.length) return
  
  const chart = echarts.init(typeChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: typeData.value.map(item => item.contractType),
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '合同数量',
        type: 'bar',
        data: typeData.value.map(item => item.contractCount),
        itemStyle: {
          color: '#409eff'
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 渲染部门统计图表 */
function renderDeptChart() {
  if (!deptChartRef.value || !deptData.value.length) return
  
  const chart = echarts.init(deptChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: deptData.value.slice(0, 10).map(item => item.deptName),
      axisLabel: {
        width: 80,
        overflow: 'truncate'
      }
    },
    series: [
      {
        name: '合同数量',
        type: 'bar',
        data: deptData.value.slice(0, 10).map(item => item.contractCount),
        itemStyle: {
          color: '#67c23a'
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 渲染合作单位统计图表 */
function renderPartnerChart() {
  if (!partnerChartRef.value || !partnerData.value.length) return
  
  const chart = echarts.init(partnerChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: partnerData.value.slice(0, 10).map(item => item.partnerName),
      axisLabel: {
        width: 80,
        overflow: 'truncate'
      }
    },
    series: [
      {
        name: '合同数量',
        type: 'bar',
        data: partnerData.value.slice(0, 10).map(item => item.contractCount),
        itemStyle: {
          color: '#e6a23c'
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 渲染金额分布图表 */
function renderAmountChart() {
  if (!amountChartRef.value || !amountData.value) return
  
  const chart = echarts.init(amountChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '合同金额',
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: amountData.value.largeContractCount, name: '大额合同(>100万)' },
          { value: amountData.value.mediumContractCount, name: '中额合同(10-100万)' },
          { value: amountData.value.smallContractCount, name: '小额合同(<10万)' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 渲染执行情况图表 */
function renderExecutionChart() {
  if (!executionChartRef.value || !executionData.value.length) return
  
  const chart = echarts.init(executionChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: executionData.value.map(item => item.executionStage),
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '合同数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '占比(%)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '合同数量',
        type: 'bar',
        data: executionData.value.map(item => item.contractCount),
        itemStyle: {
          color: '#409eff'
        }
      },
      {
        name: '占比',
        type: 'line',
        yAxisIndex: 1,
        data: executionData.value.map(item => item.percentage),
        itemStyle: {
          color: '#f56c6c'
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 渲染签署趋势图表 */
function renderTrendChart() {
  if (!trendChartRef.value || !trendData.value.length) return
  
  const chart = echarts.init(trendChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['合同数量', '合同金额']
    },
    xAxis: {
      type: 'category',
      data: trendData.value.map(item => item.signingDate)
    },
    yAxis: [
      {
        type: 'value',
        name: '合同数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '合同金额(万元)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '合同数量',
        type: 'line',
        data: trendData.value.map(item => item.contractCount),
        itemStyle: {
          color: '#409eff'
        }
      },
      {
        name: '合同金额',
        type: 'bar',
        yAxisIndex: 1,
        data: trendData.value.map(item => item.totalAmount / 10000),
        itemStyle: {
          color: '#67c23a'
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 格式化金额 */
function formatMoney(money) {
  if (!money) return '0.00'
  return (parseFloat(money) / 10000).toFixed(2)
}

/** 格式化日期 */
function formatDate(date) {
  if (!date) return ''
  const d = new Date(date)
  return d.getFullYear() + '-' + 
         String(d.getMonth() + 1).padStart(2, '0') + '-' + 
         String(d.getDate()).padStart(2, '0')
}

/** 导出统计 */
function exportStatistics() {
  // 这里应该调用导出接口
  console.log('导出合同统计数据')
}

/** 关闭对话框 */
function handleClose() {
  visible.value = false
}
</script>

<style scoped>
.stat-card {
  text-align: center;
}

.stat-item {
  padding: 20px 0;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}
</style>
