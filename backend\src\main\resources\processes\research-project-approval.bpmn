<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                   xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                   xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
                   xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" 
                   xmlns:di="http://www.omg.org/spec/DD/20100524/DI" 
                   xmlns:activiti="http://activiti.org/bpmn" 
                   id="Definitions_1" 
                   targetNamespace="http://research.com/workflow">

  <bpmn2:process id="researchProjectApproval" name="科研项目申请流程" isExecutable="true">
    
    <!-- 开始事件 -->
    <bpmn2:startEvent id="startEvent" name="开始申请">
      <bpmn2:outgoing>flow1</bpmn2:outgoing>
    </bpmn2:startEvent>
    
    <!-- 填写申请表 -->
    <bpmn2:userTask id="fillApplicationTask" name="填写申请表" activiti:assignee="${applicant}">
      <bpmn2:documentation>申请人填写科研项目申请表</bpmn2:documentation>
      <bpmn2:incoming>flow1</bpmn2:incoming>
      <bpmn2:incoming>rejectToApplicantFlow</bpmn2:incoming>
      <bpmn2:outgoing>flow2</bpmn2:outgoing>
    </bpmn2:userTask>
    
    <!-- 院系审核 -->
    <bpmn2:userTask id="departmentReviewTask" name="院系审核" activiti:assignee="${departmentReviewer}">
      <bpmn2:documentation>院系负责人审核项目申请</bpmn2:documentation>
      <bpmn2:incoming>flow2</bpmn2:incoming>
      <bpmn2:outgoing>flow3</bpmn2:outgoing>
    </bpmn2:userTask>
    
    <!-- 院系审核结果网关 -->
    <bpmn2:exclusiveGateway id="departmentGateway" name="院系审核结果">
      <bpmn2:incoming>flow3</bpmn2:incoming>
      <bpmn2:outgoing>departmentApprovedFlow</bpmn2:outgoing>
      <bpmn2:outgoing>departmentRejectedFlow</bpmn2:outgoing>
    </bpmn2:exclusiveGateway>
    
    <!-- 学校审批 -->
    <bpmn2:userTask id="schoolApprovalTask" name="学校审批" activiti:assignee="${schoolApprover}">
      <bpmn2:documentation>学校科研处审批项目申请</bpmn2:documentation>
      <bpmn2:incoming>departmentApprovedFlow</bpmn2:incoming>
      <bpmn2:outgoing>flow4</bpmn2:outgoing>
    </bpmn2:userTask>
    
    <!-- 学校审批结果网关 -->
    <bpmn2:exclusiveGateway id="schoolGateway" name="学校审批结果">
      <bpmn2:incoming>flow4</bpmn2:incoming>
      <bpmn2:outgoing>schoolApprovedFlow</bpmn2:outgoing>
      <bpmn2:outgoing>schoolRejectedFlow</bpmn2:outgoing>
    </bpmn2:exclusiveGateway>
    
    <!-- 项目立项 -->
    <bpmn2:serviceTask id="projectEstablishmentTask" name="项目立项" activiti:class="com.research.workflow.delegate.ProjectEstablishmentDelegate">
      <bpmn2:documentation>自动创建项目记录</bpmn2:documentation>
      <bpmn2:incoming>schoolApprovedFlow</bpmn2:incoming>
      <bpmn2:outgoing>flow5</bpmn2:outgoing>
    </bpmn2:serviceTask>
    
    <!-- 通知申请人 -->
    <bpmn2:serviceTask id="notifyApplicantTask" name="通知申请人" activiti:class="com.research.workflow.delegate.NotificationDelegate">
      <bpmn2:documentation>通知申请人审批结果</bpmn2:documentation>
      <bpmn2:incoming>flow5</bpmn2:incoming>
      <bpmn2:incoming>schoolRejectedFlow</bpmn2:incoming>
      <bpmn2:incoming>departmentRejectedFlow</bpmn2:incoming>
      <bpmn2:outgoing>flow6</bpmn2:outgoing>
    </bpmn2:serviceTask>
    
    <!-- 结束事件 -->
    <bpmn2:endEvent id="endEvent" name="流程结束">
      <bpmn2:incoming>flow6</bpmn2:incoming>
    </bpmn2:endEvent>
    
    <!-- 连接线 -->
    <bpmn2:sequenceFlow id="flow1" sourceRef="startEvent" targetRef="fillApplicationTask"/>
    <bpmn2:sequenceFlow id="flow2" sourceRef="fillApplicationTask" targetRef="departmentReviewTask"/>
    <bpmn2:sequenceFlow id="flow3" sourceRef="departmentReviewTask" targetRef="departmentGateway"/>
    
    <bpmn2:sequenceFlow id="departmentApprovedFlow" name="院系通过" sourceRef="departmentGateway" targetRef="schoolApprovalTask">
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${departmentApproved == true}</bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    
    <bpmn2:sequenceFlow id="departmentRejectedFlow" name="院系拒绝" sourceRef="departmentGateway" targetRef="notifyApplicantTask">
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${departmentApproved == false}</bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    
    <bpmn2:sequenceFlow id="flow4" sourceRef="schoolApprovalTask" targetRef="schoolGateway"/>
    
    <bpmn2:sequenceFlow id="schoolApprovedFlow" name="学校通过" sourceRef="schoolGateway" targetRef="projectEstablishmentTask">
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${schoolApproved == true}</bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    
    <bpmn2:sequenceFlow id="schoolRejectedFlow" name="学校拒绝" sourceRef="schoolGateway" targetRef="notifyApplicantTask">
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${schoolApproved == false}</bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    
    <bpmn2:sequenceFlow id="rejectToApplicantFlow" name="退回修改" sourceRef="departmentGateway" targetRef="fillApplicationTask">
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${departmentApproved == 'reject'}</bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    
    <bpmn2:sequenceFlow id="flow5" sourceRef="projectEstablishmentTask" targetRef="notifyApplicantTask"/>
    <bpmn2:sequenceFlow id="flow6" sourceRef="notifyApplicantTask" targetRef="endEvent"/>
    
  </bpmn2:process>

  <!-- 流程图布局信息 -->
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="researchProjectApproval">
      
      <bpmndi:BPMNShape id="BPMNShape_startEvent" bpmnElement="startEvent">
        <dc:Bounds x="100" y="200" width="36" height="36"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="96" y="240" width="44" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="BPMNShape_fillApplicationTask" bpmnElement="fillApplicationTask">
        <dc:Bounds x="200" y="178" width="100" height="80"/>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="BPMNShape_departmentReviewTask" bpmnElement="departmentReviewTask">
        <dc:Bounds x="350" y="178" width="100" height="80"/>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="BPMNShape_departmentGateway" bpmnElement="departmentGateway" isMarkerVisible="true">
        <dc:Bounds x="500" y="193" width="50" height="50"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="492" y="247" width="66" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="BPMNShape_schoolApprovalTask" bpmnElement="schoolApprovalTask">
        <dc:Bounds x="600" y="100" width="100" height="80"/>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="BPMNShape_schoolGateway" bpmnElement="schoolGateway" isMarkerVisible="true">
        <dc:Bounds x="750" y="115" width="50" height="50"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="742" y="169" width="66" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="BPMNShape_projectEstablishmentTask" bpmnElement="projectEstablishmentTask">
        <dc:Bounds x="850" y="50" width="100" height="80"/>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="BPMNShape_notifyApplicantTask" bpmnElement="notifyApplicantTask">
        <dc:Bounds x="850" y="178" width="100" height="80"/>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNShape id="BPMNShape_endEvent" bpmnElement="endEvent">
        <dc:Bounds x="1000" y="200" width="36" height="36"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="996" y="240" width="44" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      
      <!-- 连接线 -->
      <bpmndi:BPMNEdge id="BPMNEdge_flow1" bpmnElement="flow1">
        <di:waypoint x="136" y="218"/>
        <di:waypoint x="200" y="218"/>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="BPMNEdge_flow2" bpmnElement="flow2">
        <di:waypoint x="300" y="218"/>
        <di:waypoint x="350" y="218"/>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="BPMNEdge_flow3" bpmnElement="flow3">
        <di:waypoint x="450" y="218"/>
        <di:waypoint x="500" y="218"/>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="BPMNEdge_departmentApprovedFlow" bpmnElement="departmentApprovedFlow">
        <di:waypoint x="525" y="193"/>
        <di:waypoint x="525" y="140"/>
        <di:waypoint x="600" y="140"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="533" y="164" width="44" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="BPMNEdge_departmentRejectedFlow" bpmnElement="departmentRejectedFlow">
        <di:waypoint x="550" y="218"/>
        <di:waypoint x="850" y="218"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="688" y="200" width="44" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="BPMNEdge_flow4" bpmnElement="flow4">
        <di:waypoint x="700" y="140"/>
        <di:waypoint x="750" y="140"/>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="BPMNEdge_schoolApprovedFlow" bpmnElement="schoolApprovedFlow">
        <di:waypoint x="775" y="115"/>
        <di:waypoint x="775" y="90"/>
        <di:waypoint x="850" y="90"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="783" y="100" width="44" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="BPMNEdge_schoolRejectedFlow" bpmnElement="schoolRejectedFlow">
        <di:waypoint x="775" y="165"/>
        <di:waypoint x="775" y="218"/>
        <di:waypoint x="850" y="218"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="783" y="189" width="44" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="BPMNEdge_rejectToApplicantFlow" bpmnElement="rejectToApplicantFlow">
        <di:waypoint x="525" y="243"/>
        <di:waypoint x="525" y="300"/>
        <di:waypoint x="250" y="300"/>
        <di:waypoint x="250" y="258"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds x="375" y="282" width="44" height="14"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="BPMNEdge_flow5" bpmnElement="flow5">
        <di:waypoint x="900" y="130"/>
        <di:waypoint x="900" y="178"/>
      </bpmndi:BPMNEdge>
      
      <bpmndi:BPMNEdge id="BPMNEdge_flow6" bpmnElement="flow6">
        <di:waypoint x="950" y="218"/>
        <di:waypoint x="1000" y="218"/>
      </bpmndi:BPMNEdge>
      
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
  
</bpmn2:definitions>
