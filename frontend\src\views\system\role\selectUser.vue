<template>
  <el-dialog
    v-model="visible"
    title="选择用户"
    width="800px"
    :before-close="cancel"
  >
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px">
      <el-form-item label="用户名称" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="phonenumber">
        <el-input
          v-model="queryParams.phonenumber"
          placeholder="请输入手机号码"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="用户名称" prop="userName" :show-overflow-tooltip="true" />
      <el-table-column label="用户昵称" prop="nickName" :show-overflow-tooltip="true" />
      <el-table-column label="邮箱" prop="email" :show-overflow-tooltip="true" />
      <el-table-column label="手机" prop="phonenumber" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSelectUser">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { unallocatedUserList, authUserSelectAll } from '@/api/system/role'
import { parseTime } from '@/utils/common'
import { useDict } from '@/utils/dict'
import DictTag from '@/components/DictTag/index.vue'
import Pagination from '@/components/Pagination/index.vue'

const emit = defineEmits(['ok'])

// 遮罩层
const loading = ref(true)
// 选中数组
const ids = ref([])
// 总条数
const total = ref(0)
// 用户表格数据
const userList = ref([])
// 是否显示弹出层
const visible = ref(false)
// 角色ID
const roleId = ref('')

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  roleId: '',
  userName: '',
  phonenumber: ''
})

// 字典
const { sys_normal_disable } = useDict('sys_normal_disable')

/** 查询未分配用户列表 */
const getList = () => {
  loading.value = true
  queryParams.roleId = roleId.value
  unallocatedUserList(queryParams).then(response => {
    userList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.pageNum = 1
  queryParams.pageSize = 10
  queryParams.userName = ''
  queryParams.phonenumber = ''
  handleQuery()
}

// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
  ids.value = selection.map(item => item.userId)
}

/** 选择用户 */
const handleSelectUser = async () => {
  if (ids.value.length === 0) {
    ElMessage.warning('请选择要分配的用户')
    return
  }
  
  const userIds = ids.value.join(',')
  await authUserSelectAll({ roleId: roleId.value, userIds })
  ElMessage.success('分配成功')
  visible.value = false
  emit('ok')
}

/** 取消按钮 */
const cancel = () => {
  visible.value = false
  reset()
}

/** 表单重置 */
const reset = () => {
  userList.value = []
  total.value = 0
  ids.value = []
  queryParams.pageNum = 1
  queryParams.pageSize = 10
  queryParams.userName = ''
  queryParams.phonenumber = ''
}

/** 显示弹框 */
const show = (id?: string) => {
  reset()
  visible.value = true
  if (id) {
    roleId.value = id
    getList()
  }
}

// 暴露方法给父组件
defineExpose({
  show
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
