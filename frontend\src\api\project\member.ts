import request from '@/utils/request'

// 查询项目成员列表
export function listProjectMember(query: any) {
  return request({
    url: '/project/member/list',
    method: 'get',
    params: query
  })
}

// 查询项目成员详细
export function getProjectMember(id: number) {
  return request({
    url: '/project/member/' + id,
    method: 'get'
  })
}

// 根据项目ID查询成员列表
export function getMembersByProjectId(projectId: number) {
  return request({
    url: '/project/member/project/' + projectId,
    method: 'get'
  })
}

// 根据用户ID查询参与的项目
export function getProjectsByUserId(userId: number) {
  return request({
    url: '/project/member/user/' + userId,
    method: 'get'
  })
}

// 新增项目成员
export function addProjectMember(data: any) {
  return request({
    url: '/project/member',
    method: 'post',
    data: data
  })
}

// 修改项目成员
export function updateProjectMember(data: any) {
  return request({
    url: '/project/member',
    method: 'put',
    data: data
  })
}

// 删除项目成员
export function delProjectMember(id: number | number[]) {
  return request({
    url: '/project/member/' + id,
    method: 'delete'
  })
}

// 批量添加项目成员
export function batchAddMembers(projectId: number, members: any[]) {
  return request({
    url: '/project/member/batch/' + projectId,
    method: 'post',
    data: members
  })
}

// 移除项目成员
export function removeMember(projectId: number, userId: number) {
  return request({
    url: '/project/member/remove/' + projectId + '/' + userId,
    method: 'delete'
  })
}

// 更改成员角色
export function changeMemberRole(projectId: number, userId: number, memberRole: number) {
  return request({
    url: '/project/member/role/' + projectId + '/' + userId,
    method: 'put',
    params: { memberRole }
  })
}

// 设置项目负责人
export function setPrincipal(projectId: number, userId: number) {
  return request({
    url: '/project/member/principal/' + projectId + '/' + userId,
    method: 'put'
  })
}

// 查询项目负责人
export function getPrincipalByProjectId(projectId: number) {
  return request({
    url: '/project/member/principal/' + projectId,
    method: 'get'
  })
}

// 查询项目主要参与人
export function getMainMembersByProjectId(projectId: number) {
  return request({
    url: '/project/member/main/' + projectId,
    method: 'get'
  })
}

// 查询外聘专家
export function getExternalMembersByProjectId(projectId: number) {
  return request({
    url: '/project/member/external/' + projectId,
    method: 'get'
  })
}

// 检查用户是否为项目成员
export function checkIsMember(projectId: number, userId: number) {
  return request({
    url: '/project/member/check/' + projectId + '/' + userId,
    method: 'get'
  })
}

// 检查用户是否为项目负责人
export function checkIsPrincipal(projectId: number, userId: number) {
  return request({
    url: '/project/member/check-principal/' + projectId + '/' + userId,
    method: 'get'
  })
}

// 统计项目成员数量
export function countMembersByProjectId(projectId: number) {
  return request({
    url: '/project/member/count/project/' + projectId,
    method: 'get'
  })
}

// 统计用户参与的项目数量
export function countProjectsByUserId(userId: number) {
  return request({
    url: '/project/member/count/user/' + userId,
    method: 'get'
  })
}

// 查询成员角色统计
export function getRoleStatistics(projectId: number) {
  return request({
    url: '/project/member/statistics/role/' + projectId,
    method: 'get'
  })
}

// 查询部门成员统计
export function getDeptStatistics(projectId: number) {
  return request({
    url: '/project/member/statistics/dept/' + projectId,
    method: 'get'
  })
}

// 查询成员工作量统计
export function getWorkloadStatistics(projectId: number) {
  return request({
    url: '/project/member/statistics/workload/' + projectId,
    method: 'get'
  })
}

// 查询活跃成员统计
export function getActiveMemberStatistics(limit: number = 10) {
  return request({
    url: '/project/member/statistics/active',
    method: 'get',
    params: { limit }
  })
}

// 查询即将离开的成员
export function getLeavingMembers(days: number = 30) {
  return request({
    url: '/project/member/leaving',
    method: 'get',
    params: { days }
  })
}

// 成员离开项目
export function memberLeave(projectId: number, userId: number, leaveDate: string, reason: string) {
  return request({
    url: '/project/member/leave/' + projectId + '/' + userId,
    method: 'put',
    data: { leaveDate, reason }
  })
}

// 导出成员数据
export function exportProjectMember(query: any) {
  return request({
    url: '/project/member/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 导入成员数据
export function importProjectMember(projectId: number, data: FormData) {
  return request({
    url: '/project/member/importData/' + projectId,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 下载成员导入模板
export function downloadMemberTemplate() {
  return request({
    url: '/project/member/importTemplate',
    method: 'post',
    responseType: 'blob'
  })
}
