package com.research.workflow.controller;

import com.research.common.annotation.Log;
import com.research.common.core.controller.BaseController;
import com.research.common.core.domain.AjaxResult;

import com.research.common.constant.BusinessType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.research.workflow.domain.WorkflowVersion;
import com.research.workflow.service.IWorkflowVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 工作流版本管理控制器
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/workflow/version")
public class WorkflowVersionController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(WorkflowVersionController.class);

    @Autowired
    private IWorkflowVersionService workflowVersionService;

    /**
     * 查询流程版本列表
     */
    // @PreAuthorize("@ss.hasPermi('workflow:version:list')")  // 临时注释，开发阶段
    @GetMapping("/list")
    public AjaxResult list(@RequestParam String processDefinitionKey) {
        logger.info("版本管理 - 查询版本列表，processDefinitionKey: {}", processDefinitionKey);
        startPage();
        List<WorkflowVersion> list = workflowVersionService.selectVersionList(processDefinitionKey);
        logger.info("版本管理 - 查询到版本数量: {}", list.size());
        return getDataTable(list);
    }

    /**
     * 获取流程版本详细信息
     */
    @PreAuthorize("@ss.hasPermi('workflow:version:query')")
    @GetMapping(value = "/{versionId}")
    public AjaxResult getInfo(@PathVariable("versionId") String versionId) {
        return success(workflowVersionService.selectVersionById(versionId));
    }

    /**
     * 创建新版本
     */
    @PreAuthorize("@ss.hasPermi('workflow:version:create')")
    @Log(title = "流程版本", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public AjaxResult create(@RequestParam String processDefinitionKey,
                           @RequestParam String versionTag,
                           @RequestParam String versionName,
                           @RequestParam(required = false) String versionDescription) {
        WorkflowVersion version = new WorkflowVersion();
        version.setProcessDefinitionKey(processDefinitionKey);
        version.setVersionTag(versionTag);
        version.setVersionName(versionName);
        version.setVersionDescription(versionDescription);
        version.setCreateBy(getUsername());
        
        return toAjax(workflowVersionService.createVersion(version));
    }

    /**
     * 发布版本
     */
    @PreAuthorize("@ss.hasPermi('workflow:version:publish')")
    @Log(title = "流程版本", businessType = BusinessType.UPDATE)
    @PutMapping("/publish/{versionId}")
    public AjaxResult publish(@PathVariable String versionId,
                            @RequestParam Integer publishStrategy,
                            @RequestParam(required = false) Integer trafficRatio,
                            @RequestParam(required = false) String targetUsers) {
        return toAjax(workflowVersionService.publishVersion(versionId, publishStrategy, trafficRatio, targetUsers));
    }

    /**
     * 停用版本
     */
    @PreAuthorize("@ss.hasPermi('workflow:version:deprecate')")
    @Log(title = "流程版本", businessType = BusinessType.UPDATE)
    @PutMapping("/deprecate/{versionId}")
    public AjaxResult deprecate(@PathVariable String versionId) {
        return toAjax(workflowVersionService.deprecateVersion(versionId));
    }

    /**
     * 版本回滚
     */
    @PreAuthorize("@ss.hasPermi('workflow:version:rollback')")
    @Log(title = "流程版本", businessType = BusinessType.UPDATE)
    @PutMapping("/rollback")
    public AjaxResult rollback(@RequestParam String processDefinitionKey,
                             @RequestParam String targetVersionId) {
        return toAjax(workflowVersionService.rollbackVersion(processDefinitionKey, targetVersionId));
    }

    /**
     * 获取版本统计信息
     */
    // @PreAuthorize("@ss.hasPermi('workflow:version:query')")  // 临时注释，开发阶段
    @GetMapping("/statistics")
    public AjaxResult getStatistics(@RequestParam String processDefinitionKey) {
        logger.info("版本管理 - 查询版本统计，processDefinitionKey: {}", processDefinitionKey);
        Map<String, Object> statistics = workflowVersionService.getVersionStatistics(processDefinitionKey);
        logger.info("版本管理 - 统计数据获取成功");
        return success(statistics);
    }

    /**
     * 获取版本路由统计
     */
    @PreAuthorize("@ss.hasPermi('workflow:version:query')")
    @GetMapping("/route/statistics")
    public AjaxResult getRouteStatistics(@RequestParam String processDefinitionKey) {
        Map<String, Object> routeStats = workflowVersionService.getRouteStatistics(processDefinitionKey);
        return success(routeStats);
    }

    /**
     * 创建全量发布路由
     */
    @PreAuthorize("@ss.hasPermi('workflow:version:route')")
    @PostMapping("/route/full")
    public AjaxResult createFullReleaseRoute(@RequestParam String processDefinitionKey,
                                           @RequestParam String processDefinitionId,
                                           @RequestParam String versionId) {
        return toAjax(workflowVersionService.createFullReleaseRoute(processDefinitionKey, processDefinitionId, versionId));
    }

    /**
     * 创建灰度发布路由
     */
    @PreAuthorize("@ss.hasPermi('workflow:version:route')")
    @PostMapping("/route/gray")
    public AjaxResult createGrayReleaseRoute(@RequestParam String processDefinitionKey,
                                           @RequestParam String processDefinitionId,
                                           @RequestParam String versionId,
                                           @RequestParam Integer trafficRatio) {
        return toAjax(workflowVersionService.createGrayReleaseRoute(processDefinitionKey, processDefinitionId, versionId, trafficRatio));
    }

    /**
     * 创建A/B测试路由
     */
    @PreAuthorize("@ss.hasPermi('workflow:version:route')")
    @PostMapping("/route/abtest")
    public AjaxResult createABTestRoute(@RequestParam String processDefinitionKey,
                                      @RequestParam String processDefinitionId,
                                      @RequestParam String versionId,
                                      @RequestBody List<String> targetUsers) {
        return toAjax(workflowVersionService.createABTestRoute(processDefinitionKey, processDefinitionId, versionId, targetUsers));
    }

    /**
     * 创建基于部门的路由
     */
    @PreAuthorize("@ss.hasPermi('workflow:version:route')")
    @PostMapping("/route/department")
    public AjaxResult createDepartmentRoute(@RequestParam String processDefinitionKey,
                                          @RequestParam String processDefinitionId,
                                          @RequestParam String versionId,
                                          @RequestBody List<String> departments) {
        return toAjax(workflowVersionService.createDepartmentRoute(processDefinitionKey, processDefinitionId, versionId, departments));
    }

    /**
     * 测试版本路由
     */
    @PreAuthorize("@ss.hasPermi('workflow:version:test')")
    @PostMapping("/route/test")
    public AjaxResult testVersionRoute(@RequestParam String processDefinitionKey,
                                     @RequestParam String userId,
                                     @RequestParam(required = false) String userGroups,
                                     @RequestParam(required = false) String department,
                                     @RequestBody Map<String, Object> context) {
        String routeResult = workflowVersionService.testVersionRoute(processDefinitionKey, userId, userGroups, department, context);
        return success(routeResult);
    }

    /**
     * 切换版本
     */
    @PreAuthorize("@ss.hasPermi('workflow:version:switch')")
    @Log(title = "流程版本", businessType = BusinessType.UPDATE)
    @PutMapping("/switch")
    public AjaxResult switchVersion(@RequestParam String processDefinitionKey,
                                  @RequestParam String targetVersionId) {
        return toAjax(workflowVersionService.switchVersion(processDefinitionKey, targetVersionId));
    }

    /**
     * 删除版本
     */
    @PreAuthorize("@ss.hasPermi('workflow:version:remove')")
    @Log(title = "流程版本", businessType = BusinessType.DELETE)
    @DeleteMapping("/{versionId}")
    public AjaxResult remove(@PathVariable String versionId) {
        return toAjax(workflowVersionService.deleteVersionById(versionId));
    }

    /**
     * 比较版本
     */
    @PreAuthorize("@ss.hasPermi('workflow:version:compare')")
    @GetMapping("/compare")
    public AjaxResult compareVersions(@RequestParam String sourceVersionId,
                                    @RequestParam String targetVersionId) {
        Map<String, Object> comparison = workflowVersionService.compareVersions(sourceVersionId, targetVersionId);
        return success(comparison);
    }
}
