-- =============================================
-- 科研成果多维敏捷管控中心 - 数据初始化脚本
-- =============================================

-- 清空现有数据（注意：这会删除所有数据，请谨慎使用）
-- DELETE FROM sys_user_role;
-- DELETE FROM sys_role_menu;
-- DELETE FROM sys_role_dept;
-- DELETE FROM sys_user_post;
-- DELETE FROM sys_menu;
-- DELETE FROM sys_role;
-- DELETE FROM sys_post;
-- DELETE FROM sys_dept;
-- DELETE FROM sys_user;

-- =============================================
-- 1. 部门数据初始化
-- =============================================
INSERT INTO sys_dept (dept_id, parent_id, ancestors, dept_name, order_num, leader, phone, email, status, del_flag, create_by, create_time) VALUES
(100, 0, '0', '科研大学', 0, '校长', '15888888888', '<EMAIL>', '0', '0', 'admin', NOW()),
(101, 100, '0,100', '科研管理部', 1, '科研处长', '15888888889', '<EMAIL>', '0', '0', 'admin', NOW()),
(102, 100, '0,100', '学术委员会', 2, '学术委员会主任', '15888888890', '<EMAIL>', '0', '0', 'admin', NOW()),
(103, 100, '0,100', '信息中心', 3, '信息中心主任', '15888888891', '<EMAIL>', '0', '0', 'admin', NOW()),
(104, 100, '0,100', '计算机学院', 4, '院长', '15888888892', '<EMAIL>', '0', '0', 'admin', NOW()),
(105, 100, '0,100', '管理学院', 5, '院长', '15888888893', '<EMAIL>', '0', '0', 'admin', NOW()),
(106, 100, '0,100', '理学院', 6, '院长', '15888888894', '<EMAIL>', '0', '0', 'admin', NOW()),
(107, 104, '0,100,104', '软件工程系', 1, '系主任', '15888888895', '<EMAIL>', '0', '0', 'admin', NOW()),
(108, 104, '0,100,104', '计算机科学系', 2, '系主任', '15888888896', '<EMAIL>', '0', '0', 'admin', NOW()),
(109, 105, '0,100,105', '工商管理系', 1, '系主任', '15888888897', '<EMAIL>', '0', '0', 'admin', NOW());

-- =============================================
-- 2. 岗位数据初始化
-- =============================================
INSERT INTO sys_post (post_id, post_code, post_name, post_sort, status, create_by, create_time, remark) VALUES
(1, 'president', '校长', 1, '0', 'admin', NOW(), '学校最高行政负责人'),
(2, 'dean', '院长', 2, '0', 'admin', NOW(), '学院行政负责人'),
(3, 'director', '系主任', 3, '0', 'admin', NOW(), '系部行政负责人'),
(4, 'professor', '教授', 4, '0', 'admin', NOW(), '高级教学科研人员'),
(5, 'associate_prof', '副教授', 5, '0', 'admin', NOW(), '中级教学科研人员'),
(6, 'lecturer', '讲师', 6, '0', 'admin', NOW(), '初级教学科研人员'),
(7, 'researcher', '研究员', 7, '0', 'admin', NOW(), '专职科研人员'),
(8, 'admin_staff', '行政人员', 8, '0', 'admin', NOW(), '行政管理人员'),
(9, 'tech_staff', '技术人员', 9, '0', 'admin', NOW(), '技术支持人员');

-- =============================================
-- 3. 角色数据初始化
-- =============================================
INSERT INTO sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) VALUES
(1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', NOW(), '超级管理员'),
(2, '科研管理员', 'research_admin', 2, '2', 1, 1, '0', '0', 'admin', NOW(), '科研项目和成果管理员'),
(3, '学院管理员', 'college_admin', 3, '3', 1, 1, '0', '0', 'admin', NOW(), '学院级管理员'),
(4, '系部管理员', 'dept_admin', 4, '4', 1, 1, '0', '0', 'admin', NOW(), '系部级管理员'),
(5, '教师', 'teacher', 5, '5', 1, 1, '0', '0', 'admin', NOW(), '普通教师用户'),
(6, '科研人员', 'researcher', 6, '5', 1, 1, '0', '0', 'admin', NOW(), '专职科研人员'),
(7, '学生', 'student', 7, '5', 1, 1, '0', '0', 'admin', NOW(), '学生用户'),
(8, '访客', 'guest', 8, '5', 1, 1, '0', '0', 'admin', NOW(), '访客用户');

-- =============================================
-- 4. 用户数据初始化
-- =============================================
INSERT INTO sys_user (user_id, dept_id, user_name, nick_name, user_type, email, phonenumber, sex, avatar, password, status, del_flag, login_ip, login_date, create_by, create_time, remark) VALUES
(1, 103, 'admin', '系统管理员', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', '127.0.0.1', NOW(), 'admin', NOW(), '管理员'),
(2, 101, 'research_admin', '科研管理员', '00', '<EMAIL>', '15666666666', '1', '', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', '', NULL, 'admin', NOW(), '科研管理员'),
(3, 104, 'dean_cs', '计算机学院院长', '00', '<EMAIL>', '15777777777', '1', '', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', '', NULL, 'admin', NOW(), '计算机学院院长'),
(4, 105, 'dean_mgmt', '管理学院院长', '00', '<EMAIL>', '15888888899', '0', '', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', '', NULL, 'admin', NOW(), '管理学院院长'),
(5, 107, 'prof_zhang', '张教授', '00', '<EMAIL>', '15999999999', '1', '', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', '', NULL, 'admin', NOW(), '软件工程教授'),
(6, 107, 'prof_li', '李副教授', '00', '<EMAIL>', '15888888877', '0', '', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', '', NULL, 'admin', NOW(), '软件工程副教授'),
(7, 108, 'prof_wang', '王教授', '00', '<EMAIL>', '15777777788', '1', '', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', '', NULL, 'admin', NOW(), '计算机科学教授'),
(8, 109, 'prof_chen', '陈教授', '00', '<EMAIL>', '15666666677', '0', '', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', '', NULL, 'admin', NOW(), '工商管理教授'),
(9, 107, 'teacher_liu', '刘讲师', '00', '<EMAIL>', '15555555566', '1', '', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', '', NULL, 'admin', NOW(), '软件工程讲师'),
(10, 102, 'researcher_zhou', '周研究员', '00', '<EMAIL>', '15444444455', '0', '', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', '', NULL, 'admin', NOW(), '专职研究员');

-- =============================================
-- 5. 用户角色关联
-- =============================================
INSERT INTO sys_user_role (user_id, role_id) VALUES
(1, 1),  -- admin -> 超级管理员
(2, 2),  -- research_admin -> 科研管理员
(3, 3),  -- dean_cs -> 学院管理员
(4, 3),  -- dean_mgmt -> 学院管理员
(5, 5),  -- prof_zhang -> 教师
(6, 5),  -- prof_li -> 教师
(7, 5),  -- prof_wang -> 教师
(8, 5),  -- prof_chen -> 教师
(9, 5),  -- teacher_liu -> 教师
(10, 6); -- researcher_zhou -> 科研人员

-- =============================================
-- 6. 用户岗位关联
-- =============================================
INSERT INTO sys_user_post (user_id, post_id) VALUES
(1, 8),  -- admin -> 行政人员
(2, 8),  -- research_admin -> 行政人员
(3, 2),  -- dean_cs -> 院长
(4, 2),  -- dean_mgmt -> 院长
(5, 4),  -- prof_zhang -> 教授
(6, 5),  -- prof_li -> 副教授
(7, 4),  -- prof_wang -> 教授
(8, 4),  -- prof_chen -> 教授
(9, 6),  -- teacher_liu -> 讲师
(10, 7); -- researcher_zhou -> 研究员

-- =============================================
-- 7. 通知公告初始化数据
-- =============================================
INSERT INTO sys_notice (notice_id, notice_title, notice_type, notice_content, status, create_by, create_time, remark) VALUES
(1, '科研成果多维敏捷管控中心正式上线', '1', '各位老师和同学：\n\n科研成果多维敏捷管控中心已正式上线，该系统将为我校科研管理提供全方位的信息化支持。\n\n主要功能包括：\n1. 科研项目全生命周期管理\n2. 科研成果统计与分析\n3. 学术资源共享平台\n4. 科研团队协作工具\n\n请各位用户及时登录系统，熟悉相关功能。如有问题，请联系信息中心。', '0', 'admin', NOW(), '系统上线通知'),
(2, '2024年度科研项目申报通知', '2', '各学院、各部门：\n\n现启动2024年度校级科研项目申报工作，具体安排如下：\n\n申报时间：即日起至2024年3月31日\n申报方式：通过科研管控中心在线申报\n项目类别：基础研究、应用研究、产学研合作\n\n请各单位认真组织，确保申报质量。', '0', 'research_admin', NOW(), '项目申报通知'),
(3, '学术会议信息发布', '1', '第十届国际计算机科学与技术会议将于2024年6月在我校举办，欢迎广大师生积极参与。\n\n会议主题：人工智能与大数据\n会议时间：2024年6月15-17日\n会议地点：学术交流中心\n\n征稿截止：2024年4月30日', '0', 'dean_cs', NOW(), '学术会议通知');

-- =============================================
-- 8. 待办事项初始化数据
-- =============================================
INSERT INTO sys_todo (todo_id, title, content, priority, status, due_time, assignee_id, assignee_name, create_by, create_time, remark) VALUES
(1, '完善科研项目管理模块', '需要完善科研项目的立项、执行、结题等全流程管理功能', '3', '0', '2024-02-29 23:59:59', 2, 'research_admin', 'admin', NOW(), '系统开发任务'),
(2, '准备年度科研工作总结', '整理2023年度科研工作数据，准备年度总结报告', '2', '1', '2024-01-31 23:59:59', 2, 'research_admin', 'admin', NOW(), '年度总结任务'),
(3, '组织学术交流活动', '策划并组织本学期的学术交流活动，邀请知名专家学者', '2', '0', '2024-03-15 23:59:59', 3, 'dean_cs', 'admin', NOW(), '学术活动组织'),
(4, '更新课程教学大纲', '根据最新的专业要求，更新软件工程专业课程大纲', '1', '0', '2024-02-15 23:59:59', 5, 'prof_zhang', 'admin', NOW(), '教学任务'),
(5, '申报国家自然科学基金', '准备2024年国家自然科学基金项目申报材料', '3', '1', '2024-03-20 23:59:59', 7, 'prof_wang', 'admin', NOW(), '基金申报');

-- =============================================
-- 9. 系统消息初始化数据
-- =============================================
INSERT INTO sys_message (message_id, title, content, message_type, sender_id, sender_name, receiver_id, receiver_name, is_read, read_time, create_by, create_time) VALUES
(1, '欢迎使用科研管控中心', '欢迎您使用科研成果多维敏捷管控中心！系统为您提供全方位的科研管理服务。', '1', 1, 'admin', 2, 'research_admin', '0', NULL, 'admin', NOW()),
(2, '项目申报提醒', '2024年度科研项目申报即将截止，请及时提交申报材料。', '4', 2, 'research_admin', 5, 'prof_zhang', '0', NULL, 'admin', NOW()),
(3, '会议通知', '学院将于下周召开科研工作会议，请准时参加。', '2', 3, 'dean_cs', 5, 'prof_zhang', '0', NULL, 'admin', NOW()),
(4, '系统维护通知', '系统将于本周末进行例行维护，维护期间可能影响正常使用。', '1', 1, 'admin', 2, 'research_admin', '1', NOW(), 'admin', NOW()),
(5, '成果统计提醒', '请及时更新您的科研成果信息，以便进行年度统计。', '4', 2, 'research_admin', 7, 'prof_wang', '0', NULL, 'admin', NOW());

-- =============================================
-- 10. 菜单权限初始化数据
-- =============================================
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
-- 主菜单
(1, '系统管理', 0, 1, 'system', NULL, '', 1, 0, 'M', '0', '0', '', 'system', 'admin', NOW(), '系统管理目录'),
(2, '科研管理', 0, 2, 'research', NULL, '', 1, 0, 'M', '0', '0', '', 'research', 'admin', NOW(), '科研管理目录'),
(3, '学术资源', 0, 3, 'academic', NULL, '', 1, 0, 'M', '0', '0', '', 'academic', 'admin', NOW(), '学术资源目录'),
(4, '统计分析', 0, 4, 'statistics', NULL, '', 1, 0, 'M', '0', '0', '', 'chart', 'admin', NOW(), '统计分析目录'),

-- 系统管理子菜单
(100, '用户管理', 1, 1, 'user', 'system/user/index', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', NOW(), '用户管理菜单'),
(101, '角色管理', 1, 2, 'role', 'system/role/index', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', NOW(), '角色管理菜单'),
(102, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', NOW(), '菜单管理菜单'),
(103, '部门管理', 1, 4, 'dept', 'system/dept/index', '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', NOW(), '部门管理菜单'),
(104, '岗位管理', 1, 5, 'post', 'system/post/index', '', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 'admin', NOW(), '岗位管理菜单'),
(105, '通知公告', 1, 6, 'notice', 'system/notice/index', '', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 'admin', NOW(), '通知公告菜单'),

-- 科研管理子菜单
(200, '项目管理', 2, 1, 'project', 'research/project/index', '', 1, 0, 'C', '0', '0', 'research:project:list', 'project', 'admin', NOW(), '科研项目管理'),
(201, '成果管理', 2, 2, 'achievement', 'research/achievement/index', '', 1, 0, 'C', '0', '0', 'research:achievement:list', 'achievement', 'admin', NOW(), '科研成果管理'),
(202, '团队管理', 2, 3, 'team', 'research/team/index', '', 1, 0, 'C', '0', '0', 'research:team:list', 'team', 'admin', NOW(), '科研团队管理'),
(203, '经费管理', 2, 4, 'funding', 'research/funding/index', '', 1, 0, 'C', '0', '0', 'research:funding:list', 'money', 'admin', NOW(), '科研经费管理'),

-- 学术资源子菜单
(300, '文献管理', 3, 1, 'literature', 'academic/literature/index', '', 1, 0, 'C', '0', '0', 'academic:literature:list', 'documentation', 'admin', NOW(), '学术文献管理'),
(301, '会议管理', 3, 2, 'conference', 'academic/conference/index', '', 1, 0, 'C', '0', '0', 'academic:conference:list', 'conference', 'admin', NOW(), '学术会议管理'),
(302, '期刊管理', 3, 3, 'journal', 'academic/journal/index', '', 1, 0, 'C', '0', '0', 'academic:journal:list', 'journal', 'admin', NOW(), '学术期刊管理'),

-- 统计分析子菜单
(400, '项目统计', 4, 1, 'project-stats', 'statistics/project/index', '', 1, 0, 'C', '0', '0', 'statistics:project:view', 'chart', 'admin', NOW(), '项目统计分析'),
(401, '成果统计', 4, 2, 'achievement-stats', 'statistics/achievement/index', '', 1, 0, 'C', '0', '0', 'statistics:achievement:view', 'chart', 'admin', NOW(), '成果统计分析'),
(402, '人员统计', 4, 3, 'personnel-stats', 'statistics/personnel/index', '', 1, 0, 'C', '0', '0', 'statistics:personnel:view', 'chart', 'admin', NOW(), '人员统计分析');

-- =============================================
-- 11. 角色菜单权限关联
-- =============================================
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
-- 超级管理员拥有所有权限
(1, 1), (1, 2), (1, 3), (1, 4),
(1, 100), (1, 101), (1, 102), (1, 103), (1, 104), (1, 105),
(1, 200), (1, 201), (1, 202), (1, 203),
(1, 300), (1, 301), (1, 302),
(1, 400), (1, 401), (1, 402),

-- 科研管理员权限
(2, 2), (2, 3), (2, 4),
(2, 200), (2, 201), (2, 202), (2, 203),
(2, 300), (2, 301), (2, 302),
(2, 400), (2, 401), (2, 402),

-- 学院管理员权限
(3, 2), (3, 3), (3, 4),
(3, 200), (3, 201), (3, 202),
(3, 300), (3, 301), (3, 302),
(3, 400), (3, 401), (3, 402),

-- 系部管理员权限
(4, 2), (4, 3),
(4, 200), (4, 201), (4, 202),
(4, 300), (4, 301),
(4, 400), (4, 401),

-- 教师权限
(5, 2), (5, 3),
(5, 200), (5, 201),
(5, 300), (5, 301), (5, 302),

-- 科研人员权限
(6, 2), (6, 3), (6, 4),
(6, 200), (6, 201), (6, 202),
(6, 300), (6, 301), (6, 302),
(6, 400), (6, 401);

-- =============================================
-- 12. 角色部门权限关联
-- =============================================
INSERT INTO sys_role_dept (role_id, dept_id) VALUES
-- 超级管理员可以管理所有部门
(1, 100), (1, 101), (1, 102), (1, 103), (1, 104), (1, 105), (1, 106), (1, 107), (1, 108), (1, 109),

-- 科研管理员可以管理科研相关部门
(2, 101), (2, 102), (2, 104), (2, 105), (2, 106),

-- 学院管理员只能管理自己的学院
(3, 104), (3, 107), (3, 108),  -- 计算机学院
(3, 105), (3, 109);            -- 管理学院

-- 重置自增ID
ALTER TABLE sys_dept AUTO_INCREMENT = 110;
ALTER TABLE sys_post AUTO_INCREMENT = 10;
ALTER TABLE sys_role AUTO_INCREMENT = 9;
ALTER TABLE sys_user AUTO_INCREMENT = 11;
ALTER TABLE sys_notice AUTO_INCREMENT = 4;
ALTER TABLE sys_todo AUTO_INCREMENT = 6;
ALTER TABLE sys_message AUTO_INCREMENT = 6;
ALTER TABLE sys_menu AUTO_INCREMENT = 500;

-- =============================================
-- 13. 科研项目示例数据（暂时注释，需要先创建相关表）
-- =============================================
-- INSERT INTO research_project (project_id, project_name, project_code, project_type, project_level, start_date, end_date, total_funding, leader_id, dept_id, status, description, create_by, create_time) VALUES
-- (1, '基于深度学习的图像识别技术研究', 'PROJ2024001', 'BASIC_RESEARCH', 'NATIONAL', '2024-01-01', '2026-12-31', 500000.00, 5, 107, 'APPROVED', '研究深度学习在图像识别领域的应用，提升识别准确率和效率。', 'admin', NOW()),
-- (2, '智能制造系统优化算法研究', 'PROJ2024002', 'APPLIED_RESEARCH', 'PROVINCIAL', '2024-03-01', '2025-12-31', 300000.00, 7, 108, 'IN_PROGRESS', '针对智能制造系统的优化问题，研究高效的算法解决方案。', 'research_admin', NOW()),
-- (3, '企业数字化转型策略研究', 'PROJ2024003', 'APPLIED_RESEARCH', 'UNIVERSITY', '2024-02-01', '2025-06-30', 150000.00, 8, 109, 'IN_PROGRESS', '研究企业在数字化转型过程中的策略选择和实施路径。', 'research_admin', NOW()),
-- (4, '区块链技术在供应链管理中的应用', 'PROJ2024004', 'INDUSTRY_COOPERATION', 'ENTERPRISE', '2024-04-01', '2025-03-31', 800000.00, 6, 107, 'APPROVED', '与企业合作，研究区块链技术在供应链管理中的具体应用。', 'dean_cs', NOW()),
-- (5, '人工智能伦理与法律问题研究', 'PROJ2024005', 'BASIC_RESEARCH', 'NATIONAL', '2024-01-15', '2027-01-14', 600000.00, 10, 102, 'APPROVED', '研究人工智能发展过程中的伦理和法律问题，提出规范建议。', 'admin', NOW());

-- =============================================
-- 14. 科研成果示例数据（暂时注释，需要先创建相关表）
-- =============================================
-- INSERT INTO research_achievement (achievement_id, title, achievement_type, publication_date, journal_name, impact_factor, author_ids, project_id, dept_id, status, description, create_by, create_time) VALUES
-- (1, '深度卷积神经网络在医学图像分析中的应用', 'JOURNAL_PAPER', '2024-01-15', 'IEEE Transactions on Medical Imaging', 8.5, '5,6', 1, 107, 'PUBLISHED', '提出了一种新的深度学习方法用于医学图像分析，显著提升了诊断准确率。', 'prof_zhang', NOW()),
-- (2, '智能制造系统多目标优化算法', 'CONFERENCE_PAPER', '2024-03-20', 'International Conference on Intelligent Manufacturing', 0.0, '7,9', 2, 108, 'PUBLISHED', '在国际智能制造会议上发表的优化算法研究成果。', 'prof_wang', NOW()),
-- (3, '企业数字化转型成熟度评估模型', 'JOURNAL_PAPER', '2024-02-28', '管理科学学报', 3.2, '8', 3, 109, 'PUBLISHED', '构建了企业数字化转型成熟度的评估模型和指标体系。', 'prof_chen', NOW()),
-- (4, '基于区块链的供应链溯源系统', 'PATENT', '2024-04-10', '', 0.0, '6,5', 4, 107, 'APPLIED', '申请了基于区块链技术的供应链溯源系统专利。', 'prof_li', NOW()),
-- (5, '人工智能算法优化软件V1.0', 'SOFTWARE_COPYRIGHT', '2024-03-05', '', 0.0, '5,7,9', 1, 107, 'APPROVED', '获得了人工智能算法优化软件的软件著作权。', 'prof_zhang', NOW());

-- =============================================
-- 15. 科研团队示例数据（暂时注释，需要先创建相关表）
-- =============================================
-- INSERT INTO research_team (team_id, team_name, leader_id, dept_id, research_direction, member_count, establishment_date, status, description, create_by, create_time) VALUES
-- (1, '智能计算研究团队', 5, 107, '人工智能、机器学习、深度学习', 8, '2023-01-01', 'ACTIVE', '专注于智能计算技术的研究与应用，在图像识别、自然语言处理等领域有重要贡献。', 'dean_cs', NOW()),
-- (2, '系统优化研究团队', 7, 108, '系统优化、算法设计、性能分析', 6, '2023-03-01', 'ACTIVE', '致力于各类系统的优化问题研究，提供高效的算法解决方案。', 'dean_cs', NOW()),
-- (3, '数字化管理研究团队', 8, 109, '数字化转型、企业管理、商业模式创新', 5, '2023-02-01', 'ACTIVE', '研究企业数字化转型的理论与实践，为企业提供管理咨询服务。', 'dean_mgmt', NOW()),
-- (4, '区块链技术研究团队', 6, 107, '区块链、分布式系统、密码学', 7, '2023-06-01', 'ACTIVE', '专注于区块链技术的研究与产业应用，在供应链、金融等领域有所突破。', 'dean_cs', NOW()),
-- (5, '跨学科AI伦理研究团队', 10, 102, 'AI伦理、法律科技、社会计算', 4, '2023-09-01', 'ACTIVE', '跨学科研究人工智能的伦理和社会影响，推动AI技术的负责任发展。', 'research_admin', NOW());

-- 重置科研相关表的自增ID（暂时注释）
-- ALTER TABLE research_project AUTO_INCREMENT = 6;
-- ALTER TABLE research_achievement AUTO_INCREMENT = 6;
-- ALTER TABLE research_team AUTO_INCREMENT = 6;

-- 提交事务
COMMIT;

-- 查询验证数据
SELECT '=== 部门数据 ===' as info;
SELECT dept_id, dept_name, leader, phone FROM sys_dept ORDER BY dept_id LIMIT 10;

SELECT '=== 用户数据 ===' as info;
SELECT user_id, user_name, nick_name, email FROM sys_user ORDER BY user_id LIMIT 10;

SELECT '=== 角色分配 ===' as info;
SELECT u.user_name, r.role_name
FROM sys_user u
JOIN sys_user_role ur ON u.user_id = ur.user_id
JOIN sys_role r ON ur.role_id = r.role_id
ORDER BY u.user_id LIMIT 10;

-- SELECT '=== 科研项目 ===' as info;
-- SELECT project_id, project_name, project_type, total_funding FROM research_project ORDER BY project_id;

-- SELECT '=== 科研成果 ===' as info;
-- SELECT achievement_id, title, achievement_type, publication_date FROM research_achievement ORDER BY achievement_id;

-- SELECT '=== 科研团队 ===' as info;
-- SELECT team_id, team_name, research_direction, member_count FROM research_team ORDER BY team_id;

SELECT '数据初始化完成！共初始化了系统基础数据和科研管理示例数据。' as result;
