import request from '@/utils/request'

// 查询收件箱消息列表
export function listInboxMessage(query: any) {
  return request({
    url: '/system/message/inbox',
    method: 'get',
    params: query
  })
}

// 查询发件箱消息列表
export function listOutboxMessage(query: any) {
  return request({
    url: '/system/message/outbox',
    method: 'get',
    params: query
  })
}

// 查询消息详细
export function getMessage(messageId: number) {
  return request({
    url: '/system/message/' + messageId,
    method: 'get'
  })
}

// 发送消息
export function sendMessage(data: any) {
  return request({
    url: '/system/message/send',
    method: 'post',
    data: data
  })
}

// 发送消息给多个用户
export function sendMessageToUsers(data: any, receiverIds: number[]) {
  return request({
    url: '/system/message/sendToUsers',
    method: 'post',
    data: data,
    params: { receiverIds: receiverIds.join(',') }
  })
}

// 发送消息给部门
export function sendMessageToDepts(data: any, deptIds: number[]) {
  return request({
    url: '/system/message/sendToDepts',
    method: 'post',
    data: data,
    params: { deptIds: deptIds.join(',') }
  })
}

// 回复消息
export function replyMessage(messageId: number, replyContent: string) {
  return request({
    url: '/system/message/reply/' + messageId,
    method: 'post',
    params: { replyContent }
  })
}

// 转发消息
export function forwardMessage(messageId: number, receiverIds: number[], forwardContent?: string) {
  return request({
    url: '/system/message/forward/' + messageId,
    method: 'post',
    params: { 
      receiverIds: receiverIds.join(','),
      forwardContent 
    }
  })
}

// 标记消息为已读
export function markMessageAsRead(messageId: number) {
  return request({
    url: '/system/message/markRead/' + messageId,
    method: 'post'
  })
}

// 批量标记消息为已读
export function batchMarkMessageAsRead(messageIds: number[]) {
  return request({
    url: '/system/message/batchMarkRead',
    method: 'post',
    data: messageIds
  })
}

// 全部标记为已读
export function markAllMessageAsRead() {
  return request({
    url: '/system/message/markAllRead',
    method: 'post'
  })
}

// 删除消息
export function delMessage(messageId: number) {
  return request({
    url: '/system/message/' + messageId,
    method: 'delete'
  })
}

// 批量删除消息
export function batchDelMessage(messageIds: number[]) {
  return request({
    url: '/system/message/batch',
    method: 'delete',
    data: messageIds
  })
}

// 搜索消息
export function searchMessages(query: any) {
  return request({
    url: '/system/message/search',
    method: 'get',
    params: query
  })
}

// 查询用户未读消息数量
export function getUnreadMessageCount() {
  return request({
    url: '/system/message/unreadCount',
    method: 'get'
  })
}

// 查询消息统计信息
export function getMessageStatistics() {
  return request({
    url: '/system/message/statistics',
    method: 'get'
  })
}

// 查询最新消息
export function getLatestMessages(limit: number = 5) {
  return request({
    url: '/system/message/latest',
    method: 'get',
    params: { limit }
  })
}

// 发送系统消息
export function sendSystemMessage(title: string, content: string, receiverIds: number[]) {
  return request({
    url: '/system/message/sendSystem',
    method: 'post',
    params: { 
      title, 
      content, 
      receiverIds: receiverIds.join(',') 
    }
  })
}

// 发送通知消息
export function sendNotificationMessage(title: string, content: string, receiverIds: number[], businessId?: string, businessType?: string) {
  return request({
    url: '/system/message/sendNotification',
    method: 'post',
    params: { 
      title, 
      content, 
      receiverIds: receiverIds.join(','),
      businessId,
      businessType
    }
  })
}

// 发送提醒消息
export function sendReminderMessage(title: string, content: string, receiverId: number, businessId?: string, businessType?: string) {
  return request({
    url: '/system/message/sendReminder',
    method: 'post',
    params: { 
      title, 
      content, 
      receiverId,
      businessId,
      businessType
    }
  })
}
