package com.research.system.controller;

import com.research.common.core.controller.BaseController;
import com.research.common.core.domain.AjaxResult;
import com.research.common.utils.SecurityUtils;
import com.research.system.domain.SysQuickApp;
import com.research.system.service.ISysUserWorkspaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 个人工作台控制器
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@RestController
@RequestMapping("/workspace")
public class UserWorkspaceController extends BaseController {

    @Autowired
    private ISysUserWorkspaceService userWorkspaceService;

    /**
     * 获取用户工作台数据
     */
    @GetMapping("/data")
    public AjaxResult getWorkspaceData() {
        Long userId = SecurityUtils.getUserId();
        Map<String, Object> workspaceData = userWorkspaceService.getUserWorkspaceData(userId);
        return success(workspaceData);
    }

    /**
     * 获取用户个人信息
     */
    @GetMapping("/userInfo")
    public AjaxResult getUserInfo() {
        Long userId = SecurityUtils.getUserId();
        Map<String, Object> userInfo = userWorkspaceService.getUserInfo(userId);
        return success(userInfo);
    }

    /**
     * 获取用户待办统计
     */
    @GetMapping("/todoStats")
    public AjaxResult getTodoStats() {
        Long userId = SecurityUtils.getUserId();
        Map<String, Object> todoStats = userWorkspaceService.getUserTodoStats(userId);
        return success(todoStats);
    }

    /**
     * 获取用户公告统计
     */
    @GetMapping("/noticeStats")
    public AjaxResult getNoticeStats() {
        Long userId = SecurityUtils.getUserId();
        Map<String, Object> noticeStats = userWorkspaceService.getUserNoticeStats(userId);
        return success(noticeStats);
    }

    /**
     * 获取用户消息统计
     */
    @GetMapping("/messageStats")
    public AjaxResult getMessageStats() {
        Long userId = SecurityUtils.getUserId();
        Map<String, Object> messageStats = userWorkspaceService.getUserMessageStats(userId);
        return success(messageStats);
    }

    /**
     * 获取用户快捷应用
     */
    @GetMapping("/quickApps")
    public AjaxResult getQuickApps() {
        Long userId = SecurityUtils.getUserId();
        List<SysQuickApp> quickApps = userWorkspaceService.getUserQuickApps(userId);
        return success(quickApps);
    }

    /**
     * 保存用户快捷应用配置
     */
    @PostMapping("/quickApps")
    public AjaxResult saveQuickApps(@RequestBody List<String> appCodes) {
        Long userId = SecurityUtils.getUserId();
        boolean result = userWorkspaceService.saveUserQuickApps(userId, appCodes);
        return toAjax(result);
    }

    /**
     * 获取可用的快捷应用
     */
    @GetMapping("/availableApps")
    public AjaxResult getAvailableApps() {
        Long userId = SecurityUtils.getUserId();
        List<SysQuickApp> availableApps = userWorkspaceService.getAvailableQuickApps(userId);
        return success(availableApps);
    }

    /**
     * 获取用户布局配置
     */
    @GetMapping("/layoutConfig")
    public AjaxResult getLayoutConfig() {
        Long userId = SecurityUtils.getUserId();
        Map<String, Object> layoutConfig = userWorkspaceService.getUserLayoutConfig(userId);
        return success(layoutConfig);
    }

    /**
     * 保存用户布局配置
     */
    @PostMapping("/layoutConfig")
    public AjaxResult saveLayoutConfig(@RequestBody Map<String, Object> layoutConfig) {
        Long userId = SecurityUtils.getUserId();
        boolean result = userWorkspaceService.saveUserLayoutConfig(userId, layoutConfig);
        return toAjax(result);
    }

    /**
     * 获取用户主题配置
     */
    @GetMapping("/themeConfig")
    public AjaxResult getThemeConfig() {
        Long userId = SecurityUtils.getUserId();
        Map<String, Object> themeConfig = userWorkspaceService.getUserThemeConfig(userId);
        return success(themeConfig);
    }

    /**
     * 保存用户主题配置
     */
    @PostMapping("/themeConfig")
    public AjaxResult saveThemeConfig(@RequestBody Map<String, Object> themeConfig) {
        Long userId = SecurityUtils.getUserId();
        boolean result = userWorkspaceService.saveUserThemeConfig(userId, themeConfig);
        return toAjax(result);
    }

    /**
     * 获取工作台统计数据
     */
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        Long userId = SecurityUtils.getUserId();
        Map<String, Object> statistics = userWorkspaceService.getWorkspaceStatistics(userId);
        return success(statistics);
    }

    /**
     * 重置工作台配置
     */
    @PostMapping("/reset")
    public AjaxResult resetWorkspace() {
        Long userId = SecurityUtils.getUserId();
        boolean result = userWorkspaceService.resetUserWorkspace(userId);
        return toAjax(result);
    }

    /**
     * 保存用户配置
     */
    @PostMapping("/config")
    public AjaxResult saveUserConfig(@RequestParam String configType,
                                   @RequestParam String configKey,
                                   @RequestParam String configValue,
                                   @RequestParam(required = false) Integer sortOrder) {
        Long userId = SecurityUtils.getUserId();
        boolean result = userWorkspaceService.saveOrUpdateUserConfig(userId, configType, configKey, configValue, sortOrder);
        return toAjax(result);
    }

    /**
     * 删除用户配置
     */
    @DeleteMapping("/config")
    public AjaxResult deleteUserConfig(@RequestParam String configType,
                                     @RequestParam String configKey) {
        Long userId = SecurityUtils.getUserId();
        boolean result = userWorkspaceService.deleteUserConfig(userId, configType, configKey);
        return toAjax(result);
    }
}
