package com.research.workflow.controller;

import com.research.common.core.controller.BaseController;
import com.research.common.core.domain.AjaxResult;
import com.research.workflow.service.IWorkflowMonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 工作流监控大屏控制器
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/workflow/monitor")
public class WorkflowMonitorController extends BaseController {
    
    @Autowired
    private IWorkflowMonitorService workflowMonitorService;

    /**
     * 获取工作流总览数据
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:view')")
    @GetMapping("/overview")
    public AjaxResult getOverview() {
        Map<String, Object> overview = workflowMonitorService.getWorkflowOverview();
        return success(overview);
    }

    /**
     * 获取监控大屏综合数据
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:view')")
    @GetMapping("/dashboard")
    public AjaxResult getDashboardData() {
        Map<String, Object> dashboardData = workflowMonitorService.getDashboardData();
        return success(dashboardData);
    }

    /**
     * 获取实时指标数据
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:realtime')")
    @GetMapping("/realtime")
    public AjaxResult getRealTimeMetrics() {
        Map<String, Object> realTimeMetrics = workflowMonitorService.getRealTimeMetrics();
        return success(realTimeMetrics);
    }

    /**
     * 获取版本分布统计
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:statistics')")
    @GetMapping("/version/distribution")
    public AjaxResult getVersionDistribution() {
        Map<String, Object> versionDistribution = workflowMonitorService.getVersionDistribution();
        return success(versionDistribution);
    }

    /**
     * 获取实时流程活动数据
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:realtime')")
    @GetMapping("/activity/realtime")
    public AjaxResult getRealTimeActivity() {
        Map<String, Object> realTimeActivity = workflowMonitorService.getRealTimeActivity();
        return success(realTimeActivity);
    }

    /**
     * 获取流程性能统计
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:performance')")
    @GetMapping("/performance")
    public AjaxResult getProcessPerformance() {
        Map<String, Object> performance = workflowMonitorService.getProcessPerformance();
        return success(performance);
    }

    /**
     * 获取任务分布统计
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:statistics')")
    @GetMapping("/task/distribution")
    public AjaxResult getTaskDistribution() {
        Map<String, Object> taskDistribution = workflowMonitorService.getTaskDistribution();
        return success(taskDistribution);
    }

    /**
     * 获取历史趋势数据
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:statistics')")
    @GetMapping("/trends")
    public AjaxResult getHistoricalTrends(@RequestParam(defaultValue = "7") Integer days) {
        Map<String, Object> trends = workflowMonitorService.getHistoricalTrends(days);
        return success(trends);
    }

    /**
     * 获取版本路由统计
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:statistics')")
    @GetMapping("/route/stats")
    public AjaxResult getVersionRouteStats() {
        Map<String, Object> routeStats = workflowMonitorService.getVersionRouteStats();
        return success(routeStats);
    }

    /**
     * 获取系统健康状态
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:view')")
    @GetMapping("/health")
    public AjaxResult getSystemHealth() {
        Map<String, Object> systemHealth = workflowMonitorService.getSystemHealth();
        return success(systemHealth);
    }

    /**
     * 获取流程执行统计
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:statistics')")
    @GetMapping("/execution/stats")
    public AjaxResult getExecutionStats(@RequestParam(required = false) String processDefinitionKey,
                                      @RequestParam(defaultValue = "7") Integer days) {
        Map<String, Object> executionStats = workflowMonitorService.getExecutionStats(processDefinitionKey, days);
        return success(executionStats);
    }

    /**
     * 获取任务处理效率统计
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:performance')")
    @GetMapping("/task/efficiency")
    public AjaxResult getTaskEfficiency(@RequestParam(required = false) String assignee,
                                      @RequestParam(defaultValue = "7") Integer days) {
        Map<String, Object> taskEfficiency = workflowMonitorService.getTaskEfficiency(assignee, days);
        return success(taskEfficiency);
    }

    /**
     * 获取流程瓶颈分析
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:performance')")
    @GetMapping("/bottleneck/analysis")
    public AjaxResult getBottleneckAnalysis(@RequestParam(required = false) String processDefinitionKey) {
        Map<String, Object> bottleneckAnalysis = workflowMonitorService.getBottleneckAnalysis(processDefinitionKey);
        return success(bottleneckAnalysis);
    }

    /**
     * 获取用户活跃度统计
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:statistics')")
    @GetMapping("/user/activity")
    public AjaxResult getUserActivity(@RequestParam(defaultValue = "7") Integer days) {
        Map<String, Object> userActivity = workflowMonitorService.getUserActivity(days);
        return success(userActivity);
    }

    /**
     * 获取部门工作量统计
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:statistics')")
    @GetMapping("/department/workload")
    public AjaxResult getDepartmentWorkload(@RequestParam(defaultValue = "7") Integer days) {
        Map<String, Object> departmentWorkload = workflowMonitorService.getDepartmentWorkload(days);
        return success(departmentWorkload);
    }

    /**
     * 导出监控报表
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:export')")
    @PostMapping("/export")
    public AjaxResult exportReport(@RequestParam String reportType,
                                 @RequestParam(defaultValue = "7") Integer days,
                                 @RequestParam(required = false) String processDefinitionKey) {
        String filePath = workflowMonitorService.exportReport(reportType, days, processDefinitionKey);
        return AjaxResult.success("报表导出成功", filePath);
    }

    /**
     * 获取告警信息
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:view')")
    @GetMapping("/alerts")
    public AjaxResult getAlerts() {
        Map<String, Object> alerts = workflowMonitorService.getAlerts();
        return success(alerts);
    }

    /**
     * 获取资源使用情况
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:performance')")
    @GetMapping("/resources")
    public AjaxResult getResourceUsage() {
        Map<String, Object> resourceUsage = workflowMonitorService.getResourceUsage();
        return success(resourceUsage);
    }

    /**
     * 获取流程热力图数据
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:statistics')")
    @GetMapping("/heatmap")
    public AjaxResult getProcessHeatmap(@RequestParam String processDefinitionKey,
                                      @RequestParam(defaultValue = "7") Integer days) {
        Map<String, Object> heatmapData = workflowMonitorService.getProcessHeatmap(processDefinitionKey, days);
        return success(heatmapData);
    }

    /**
     * 获取SLA达成率统计
     */
    @PreAuthorize("@ss.hasPermi('workflow:monitor:performance')")
    @GetMapping("/sla/compliance")
    public AjaxResult getSLACompliance(@RequestParam(required = false) String processDefinitionKey,
                                     @RequestParam(defaultValue = "30") Integer days) {
        Map<String, Object> slaCompliance = workflowMonitorService.getSLACompliance(processDefinitionKey, days);
        return success(slaCompliance);
    }
}
