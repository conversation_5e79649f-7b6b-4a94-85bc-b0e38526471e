<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.research.system.mapper.SysUserPostMapper">

    <select id="countUserPostByPostId" resultType="int">
        select count(1) from sys_user_post where post_id=#{postId}
    </select>

    <delete id="deleteUserPostByUserId" parameterType="Long">
        delete from sys_user_post where user_id=#{userId}
    </delete>

    <delete id="deleteUserPostByPostId" parameterType="Long">
        delete from sys_user_post where post_id=#{postId}
    </delete>

    <delete id="deleteUserPost" parameterType="Long">
        delete from sys_user_post where user_id in
        <foreach collection="array" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <insert id="batchUserPost">
        insert into sys_user_post(user_id, post_id) values
        <foreach item="postId" collection="postIds" separator=",">
            (#{userId},#{postId})
        </foreach>
    </insert>

</mapper>
