package com.research.common.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
// import com.github.pagehelper.PageHelper;
import com.research.common.core.page.PageDomain;
import com.research.common.core.page.PageRequest;
import com.research.common.core.page.PageResponse;
import com.research.common.core.page.TableDataInfo;
import com.research.common.core.page.TableSupport;
import com.research.common.utils.sql.SqlUtil;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分页工具类
 *
 * <AUTHOR>
 */
public class PageUtils {

    /**
     * 设置请求分页数据 - 已废弃，使用MyBatis Plus分页
     */
    public static void startPage() {
        // PageDomain pageDomain = TableSupport.buildPageRequest();
        // Integer pageNum = pageDomain.getPageNum();
        // Integer pageSize = pageDomain.getPageSize();
        // String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
        // Boolean reasonable = pageDomain.getReasonable();
        // PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);

        // 现在使用MyBatis Plus分页，此方法已废弃
        System.out.println("PageUtils.startPage() 已废弃，请使用MyBatis Plus分页");
    }

    /**
     * 获取当前页码
     */
    public static Integer getPageNum() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        return pageDomain.getPageNum();
    }

    /**
     * 获取每页显示数量
     */
    public static Integer getPageSize() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        return pageDomain.getPageSize();
    }

    /**
     * 清理分页的线程变量 - 已废弃
     */
    public static void clearPage() {
        // PageHelper.clearPage();
        // MyBatis Plus不需要手动清理
    }

    /**
     * 创建MyBatis Plus分页对象
     */
    public static <T> Page<T> createPage(PageRequest pageRequest) {
        if (pageRequest == null) {
            return new Page<>(1, 10);
        }
        pageRequest.validate();
        return new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
    }

    /**
     * 创建MyBatis Plus分页对象（带排序）
     */
    public static <T> Page<T> createPageWithOrder(PageRequest pageRequest) {
        Page<T> page = createPage(pageRequest);
        if (pageRequest != null && StringUtils.isNotEmpty(pageRequest.getOrderBy())) {
            String orderBy = pageRequest.getOrderBy();
            String sortOrder = pageRequest.getSortOrder();

            // 转换为下划线格式
            orderBy = StringUtils.toUnderScoreCase(orderBy);

            if ("asc".equalsIgnoreCase(sortOrder)) {
                page.addOrder(com.baomidou.mybatisplus.core.metadata.OrderItem.asc(orderBy));
            } else {
                page.addOrder(com.baomidou.mybatisplus.core.metadata.OrderItem.desc(orderBy));
            }
        }
        return page;
    }

    /**
     * IPage转换为PageResponse
     */
    public static <T> PageResponse<T> toPageResponse(IPage<T> page) {
        if (page == null) {
            return PageResponse.empty();
        }
        return new PageResponse<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal(),
                page.getRecords()
        );
    }

    /**
     * IPage转换为PageResponse（带数据转换）
     */
    public static <T, R> PageResponse<R> toPageResponse(IPage<T> page, Function<T, R> converter) {
        if (page == null) {
            return PageResponse.empty();
        }

        List<R> convertedRecords = page.getRecords() != null
                ? page.getRecords().stream().map(converter).collect(Collectors.toList())
                : Collections.emptyList();

        return new PageResponse<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal(),
                convertedRecords
        );
    }

    /**
     * PageResponse转换为TableDataInfo
     */
    public static TableDataInfo toTableDataInfo(PageResponse<?> pageResponse) {
        if (pageResponse == null) {
            return new TableDataInfo(Collections.emptyList(), 0);
        }

        TableDataInfo tableDataInfo = new TableDataInfo(pageResponse.getRecords(), (int) pageResponse.getTotal());
        tableDataInfo.setCode(200);
        tableDataInfo.setMsg("查询成功");
        return tableDataInfo;
    }

    /**
     * 直接从IPage创建TableDataInfo
     */
    public static TableDataInfo toTableDataInfo(IPage<?> page) {
        return toTableDataInfo(toPageResponse(page));
    }

    /**
     * 列表数据转换为分页响应（内存分页）
     */
    public static <T> PageResponse<T> toPageResponse(List<T> list, PageRequest pageRequest) {
        if (list == null || list.isEmpty()) {
            return PageResponse.empty(pageRequest.getPageNum(), pageRequest.getPageSize());
        }

        pageRequest.validate();
        int total = list.size();
        int startIndex = (int) pageRequest.getOffset();
        int endIndex = Math.min(startIndex + pageRequest.getPageSize(), total);

        List<T> pageData = startIndex < total ? list.subList(startIndex, endIndex) : Collections.emptyList();

        return new PageResponse<>(
                pageRequest.getPageNum(),
                pageRequest.getPageSize(),
                total,
                pageData
        );
    }

    /**
     * 列表数据转换为TableDataInfo（内存分页）
     */
    public static TableDataInfo toTableDataInfo(List<?> list, PageRequest pageRequest) {
        PageResponse<?> pageResponse = toPageResponse(list, pageRequest);
        return toTableDataInfo(pageResponse);
    }

    /**
     * 计算总页数
     */
    public static long calculatePages(long total, long pageSize) {
        if (pageSize <= 0) {
            return 0;
        }
        return (total + pageSize - 1) / pageSize;
    }

    /**
     * 计算偏移量
     */
    public static long calculateOffset(long pageNum, long pageSize) {
        return (pageNum - 1) * pageSize;
    }

    /**
     * 验证页码是否有效
     */
    public static boolean isValidPageNum(long pageNum, long total, long pageSize) {
        if (pageNum < 1) {
            return false;
        }
        if (total == 0) {
            return pageNum == 1;
        }
        long totalPages = calculatePages(total, pageSize);
        return pageNum <= totalPages;
    }

    /**
     * 获取安全的页码（确保在有效范围内）
     */
    public static long getSafePageNum(long pageNum, long total, long pageSize) {
        if (pageNum < 1) {
            return 1;
        }
        if (total == 0) {
            return 1;
        }
        long totalPages = calculatePages(total, pageSize);
        return Math.min(pageNum, totalPages);
    }

    /**
     * 创建分页信息描述
     */
    public static String createPageInfo(long pageNum, long pageSize, long total) {
        if (total == 0) {
            return "暂无数据";
        }

        long startRecord = (pageNum - 1) * pageSize + 1;
        long endRecord = Math.min(pageNum * pageSize, total);
        long totalPages = calculatePages(total, pageSize);

        return String.format("第 %d-%d 条，共 %d 条记录，第 %d/%d 页",
                startRecord, endRecord, total, pageNum, totalPages);
    }

    /**
     * 检查是否需要查询总数
     */
    public static boolean needSearchCount(PageRequest pageRequest) {
        return pageRequest != null && Boolean.TRUE.equals(pageRequest.getSearchCount());
    }
}
