-- 横向项目管理功能数据库初始化脚本
-- 在MySQL数据库中执行此脚本来创建横向项目管理相关表

USE research_db;

-- 检查并创建横向项目表
CREATE TABLE IF NOT EXISTS horizontal_project (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    project_no VARCHAR(50) NOT NULL UNIQUE COMMENT '项目编号',
    project_name VARCHAR(255) NOT NULL COMMENT '项目名称',
    project_type VARCHAR(50) COMMENT '项目类型',
    contract_id BIGINT COMMENT '关联合同ID',
    partner_id BIGINT COMMENT '合作单位ID',
    status INT DEFAULT 0 COMMENT '项目状态：0申请中,1立项,2执行中,3变更中,4结项中,5已结项,6已撤销',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    total_fund DECIMAL(15,2) COMMENT '项目总经费',
    received_fund DECIMAL(15,2) DEFAULT 0.00 COMMENT '已到账经费',
    principal_id BIGINT COMMENT '项目负责人ID',
    principal_name VARCHAR(100) COMMENT '项目负责人姓名',
    dept_id BIGINT COMMENT '所属部门ID',
    dept_name VARCHAR(100) COMMENT '所属部门名称',
    project_summary TEXT COMMENT '项目简介',
    research_content TEXT COMMENT '研究内容',
    expected_results TEXT COMMENT '预期成果',
    keywords VARCHAR(500) COMMENT '关键词',
    subject_category VARCHAR(100) COMMENT '学科分类',
    application_file_path VARCHAR(500) COMMENT '申请书文件路径',
    contract_file_path VARCHAR(500) COMMENT '合同文件路径',
    process_instance_id VARCHAR(64) COMMENT '流程实例ID',
    current_node VARCHAR(100) COMMENT '当前节点',
    approval_status VARCHAR(50) COMMENT '审批状态',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_project_no (project_no),
    INDEX idx_status (status),
    INDEX idx_principal_id (principal_id),
    INDEX idx_dept_id (dept_id),
    INDEX idx_partner_id (partner_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='横向项目表';

-- 检查并创建合同表
CREATE TABLE IF NOT EXISTS contract (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    contract_no VARCHAR(50) NOT NULL UNIQUE COMMENT '合同编号',
    contract_name VARCHAR(255) NOT NULL COMMENT '合同名称',
    contract_type VARCHAR(50) COMMENT '合同类型',
    contract_template_id BIGINT COMMENT '合同模板ID',
    partner_id BIGINT COMMENT '合作单位ID',
    partner_name VARCHAR(200) COMMENT '合作单位名称',
    contract_amount DECIMAL(15,2) COMMENT '合同金额',
    signing_date DATE COMMENT '签署日期',
    start_date DATE COMMENT '合同开始日期',
    end_date DATE COMMENT '合同结束日期',
    status INT DEFAULT 0 COMMENT '合同状态：0草稿,1审核中,2已签署,3执行中,4已完成,5已终止',
    principal_id BIGINT COMMENT '合同负责人ID',
    principal_name VARCHAR(100) COMMENT '合同负责人姓名',
    dept_id BIGINT COMMENT '所属部门ID',
    dept_name VARCHAR(100) COMMENT '所属部门名称',
    contract_content TEXT COMMENT '合同内容',
    payment_terms TEXT COMMENT '付款条款',
    delivery_terms TEXT COMMENT '交付条款',
    contract_file_path VARCHAR(500) COMMENT '合同文件路径',
    backup_file_path VARCHAR(500) COMMENT '备案文件路径',
    process_instance_id VARCHAR(64) COMMENT '流程实例ID',
    current_node VARCHAR(100) COMMENT '当前节点',
    approval_status VARCHAR(50) COMMENT '审批状态',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_contract_no (contract_no),
    INDEX idx_status (status),
    INDEX idx_partner_id (partner_id),
    INDEX idx_principal_id (principal_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同表';

-- 检查并创建合作单位表
CREATE TABLE IF NOT EXISTS partner (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    partner_code VARCHAR(50) NOT NULL UNIQUE COMMENT '单位编码',
    partner_name VARCHAR(255) NOT NULL COMMENT '单位名称',
    partner_type VARCHAR(50) COMMENT '单位类型',
    legal_representative VARCHAR(100) COMMENT '法定代表人',
    contact_person VARCHAR(100) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    address VARCHAR(500) COMMENT '单位地址',
    postal_code VARCHAR(10) COMMENT '邮政编码',
    website VARCHAR(200) COMMENT '官方网站',
    business_scope TEXT COMMENT '经营范围',
    registration_capital DECIMAL(15,2) COMMENT '注册资本',
    establishment_date DATE COMMENT '成立日期',
    credit_code VARCHAR(50) COMMENT '统一社会信用代码',
    tax_number VARCHAR(50) COMMENT '税务登记号',
    bank_name VARCHAR(200) COMMENT '开户银行',
    bank_account VARCHAR(50) COMMENT '银行账号',
    status INT DEFAULT 1 COMMENT '状态：0禁用,1正常,2待审核',
    cooperation_level CHAR(1) DEFAULT 'C' COMMENT '合作等级：A优秀,B良好,C一般,D较差',
    cooperation_count INT DEFAULT 0 COMMENT '合作次数',
    total_contract_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '累计合同金额',
    last_cooperation_date DATE COMMENT '最近合作日期',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_partner_code (partner_code),
    INDEX idx_partner_type (partner_type),
    INDEX idx_cooperation_level (cooperation_level),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合作单位表';

-- 检查并创建合同模板表
CREATE TABLE IF NOT EXISTS contract_template (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    template_name VARCHAR(255) NOT NULL COMMENT '模板名称',
    template_type VARCHAR(50) COMMENT '模板类型',
    template_category VARCHAR(50) COMMENT '模板分类',
    template_content TEXT COMMENT '模板内容',
    version VARCHAR(20) COMMENT '版本号',
    status INT DEFAULT 1 COMMENT '状态：0禁用,1启用',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_template_type (template_type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同模板表';

-- 检查并创建横向项目成员表
CREATE TABLE IF NOT EXISTS horizontal_project_member (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    project_id BIGINT NOT NULL COMMENT '项目ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    user_name VARCHAR(100) COMMENT '用户姓名',
    user_no VARCHAR(50) COMMENT '用户编号',
    dept_id BIGINT COMMENT '部门ID',
    dept_name VARCHAR(100) COMMENT '部门名称',
    member_role INT DEFAULT 2 COMMENT '成员角色：0负责人,1主要参与人,2一般参与人',
    member_role_name VARCHAR(50) COMMENT '成员角色名称',
    workload DECIMAL(5,2) COMMENT '工作量(月)',
    position VARCHAR(100) COMMENT '职务',
    specialty VARCHAR(200) COMMENT '专业特长',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    responsibility TEXT COMMENT '职责描述',
    join_date DATE COMMENT '加入日期',
    status INT DEFAULT 0 COMMENT '状态：0正常,1退出',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_project_id (project_id),
    INDEX idx_user_id (user_id),
    INDEX idx_member_role (member_role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='横向项目成员表';

-- 检查并创建合作单位资质表
CREATE TABLE IF NOT EXISTS partner_qualification (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    partner_id BIGINT NOT NULL COMMENT '合作单位ID',
    qualification_name VARCHAR(255) NOT NULL COMMENT '资质名称',
    qualification_type VARCHAR(100) COMMENT '资质类型',
    qualification_level VARCHAR(100) COMMENT '资质等级',
    issuing_authority VARCHAR(200) COMMENT '颁发机构',
    certificate_no VARCHAR(100) COMMENT '证书编号',
    issue_date DATE COMMENT '颁发日期',
    expiry_date DATE COMMENT '到期日期',
    status INT DEFAULT 1 COMMENT '状态：0无效,1有效,2即将到期',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_partner_id (partner_id),
    INDEX idx_expiry_date (expiry_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合作单位资质表';

-- 检查并创建合作单位评价表
CREATE TABLE IF NOT EXISTS partner_evaluation (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    partner_id BIGINT NOT NULL COMMENT '合作单位ID',
    project_id BIGINT COMMENT '项目ID',
    contract_id BIGINT COMMENT '合同ID',
    evaluation_date DATE COMMENT '评价日期',
    evaluator_id BIGINT COMMENT '评价人ID',
    evaluator_name VARCHAR(100) COMMENT '评价人姓名',
    cooperation_quality_score INT COMMENT '合作质量评分(1-10)',
    delivery_quality_score INT COMMENT '交付质量评分(1-10)',
    service_attitude_score INT COMMENT '服务态度评分(1-10)',
    communication_score INT COMMENT '沟通协调评分(1-10)',
    overall_score DECIMAL(3,1) COMMENT '综合评分',
    evaluation_content TEXT COMMENT '评价内容',
    improvement_suggestions TEXT COMMENT '改进建议',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_partner_id (partner_id),
    INDEX idx_project_id (project_id),
    INDEX idx_evaluation_date (evaluation_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合作单位评价表';

-- 检查并创建合同变更记录表
CREATE TABLE IF NOT EXISTS contract_change (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    contract_id BIGINT NOT NULL COMMENT '合同ID',
    change_type VARCHAR(50) COMMENT '变更类型',
    change_reason TEXT COMMENT '变更原因',
    change_content TEXT COMMENT '变更内容',
    change_before TEXT COMMENT '变更前内容',
    change_after TEXT COMMENT '变更后内容',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '状态：pending待审核,approved已批准,rejected已拒绝',
    apply_time DATETIME COMMENT '申请时间',
    approve_time DATETIME COMMENT '审批时间',
    approver_id BIGINT COMMENT '审批人ID',
    approver_name VARCHAR(100) COMMENT '审批人姓名',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_contract_id (contract_id),
    INDEX idx_status (status),
    INDEX idx_apply_time (apply_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同变更记录表';

-- 添加外键约束（可选）
-- ALTER TABLE horizontal_project ADD CONSTRAINT fk_hp_contract FOREIGN KEY (contract_id) REFERENCES contract(id);
-- ALTER TABLE horizontal_project ADD CONSTRAINT fk_hp_partner FOREIGN KEY (partner_id) REFERENCES partner(id);
-- ALTER TABLE contract ADD CONSTRAINT fk_contract_partner FOREIGN KEY (partner_id) REFERENCES partner(id);
-- ALTER TABLE horizontal_project_member ADD CONSTRAINT fk_hpm_project FOREIGN KEY (project_id) REFERENCES horizontal_project(id);
-- ALTER TABLE partner_qualification ADD CONSTRAINT fk_pq_partner FOREIGN KEY (partner_id) REFERENCES partner(id);
-- ALTER TABLE partner_evaluation ADD CONSTRAINT fk_pe_partner FOREIGN KEY (partner_id) REFERENCES partner(id);
-- ALTER TABLE contract_change ADD CONSTRAINT fk_cc_contract FOREIGN KEY (contract_id) REFERENCES contract(id);

SELECT '横向项目管理数据库表创建完成！' as message;
