package com.research.framework.config;

import com.research.framework.security.filter.JwtAuthenticationTokenFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.filter.CorsFilter;

/**
 * Spring Security配置
 * 
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
public class SecurityConfig {

    /**
     * 安全过滤器链配置
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http, JwtAuthenticationTokenFilter jwtAuthenticationTokenFilter, CorsFilter corsFilter) throws Exception {
        http
            // 启用CORS支持
            .cors()
            .and()
            // 禁用CSRF保护
            .csrf().disable()
            // 禁用session，使用JWT
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            // 配置请求授权
            .authorizeHttpRequests()
                // 允许访问登录接口
                .antMatchers("/login", "/logout", "/captchaImage").permitAll()
                // 允许访问测试接口
                .antMatchers("/test/**").permitAll()
                // 允许访问密码工具
                .antMatchers("/password/**").permitAll()
                // 允许访问系统管理接口（暂时开放，后续会加权限控制）
                .antMatchers("/system/**").permitAll()
                // 允许访问健康检查和系统信息接口
                .antMatchers("/health", "/info").permitAll()
                // 允许访问H2控制台
                .antMatchers("/h2-console/**").permitAll()
                .antMatchers("/h2-console/login.do**").permitAll()
                .antMatchers("/h2-console/query.do**").permitAll()
                // 允许访问静态资源
                .antMatchers("/css/**", "/js/**", "/images/**").permitAll()
                // Swagger文档路径已移除
                // 其他请求需要认证
                .anyRequest().authenticated()
            .and()
            // 禁用表单登录
            .formLogin().disable()
            // 禁用HTTP Basic认证
            .httpBasic().disable()
            // 禁用默认的logout处理
            .logout().disable()
            // 允许H2控制台的iframe
            .headers()
                .frameOptions().sameOrigin()
            .and()
            // 添加CORS过滤器
            .addFilterBefore(corsFilter, UsernamePasswordAuthenticationFilter.class)
            // 添加JWT过滤器
            .addFilterBefore(jwtAuthenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    /**
     * 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 认证管理器
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }
}
