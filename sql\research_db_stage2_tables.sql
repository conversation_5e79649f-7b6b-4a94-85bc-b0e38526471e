-- 第二阶段核心业务功能相关表结构
-- 创建时间：2024-07-29

-- 公告阅读记录表
CREATE TABLE sys_notice_read (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    notice_id BIGINT NOT NULL COMMENT '公告ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    user_name VARCHAR(30) DEFAULT '' COMMENT '用户姓名',
    read_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '阅读时间',
    read_duration INT DEFAULT 0 COMMENT '阅读时长（秒）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_notice_user (notice_id, user_id),
    INDEX idx_notice_id (notice_id),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB COMMENT='公告阅读记录表';

-- 待办处理历史表
CREATE TABLE sys_todo_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    todo_id BIGINT NOT NULL COMMENT '待办ID',
    action_type CHAR(1) NOT NULL COMMENT '操作类型（1创建 2分配 3处理 4完成 5取消 6重新打开）',
    action_desc VARCHAR(200) DEFAULT '' COMMENT '操作描述',
    operator_id BIGINT NOT NULL COMMENT '操作人ID',
    operator_name VARCHAR(30) DEFAULT '' COMMENT '操作人姓名',
    old_status CHAR(1) DEFAULT NULL COMMENT '原状态',
    new_status CHAR(1) DEFAULT NULL COMMENT '新状态',
    old_assignee_id BIGINT DEFAULT NULL COMMENT '原负责人ID',
    new_assignee_id BIGINT DEFAULT NULL COMMENT '新负责人ID',
    process_result TEXT DEFAULT NULL COMMENT '处理结果',
    attachment_path VARCHAR(500) DEFAULT NULL COMMENT '附件路径',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    INDEX idx_todo_id (todo_id),
    INDEX idx_operator (operator_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB COMMENT='待办处理历史表';

-- 用户工作台配置表
CREATE TABLE sys_user_workspace (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    config_type VARCHAR(50) NOT NULL COMMENT '配置类型（quick_apps快捷应用 layout布局 theme主题）',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT DEFAULT NULL COMMENT '配置值',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_enabled CHAR(1) DEFAULT '1' COMMENT '是否启用（0否 1是）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_user_config (user_id, config_type, config_key),
    INDEX idx_user_id (user_id),
    INDEX idx_config_type (config_type)
) ENGINE=InnoDB COMMENT='用户工作台配置表';

-- 系统快捷应用表
CREATE TABLE sys_quick_app (
    app_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '应用ID',
    app_name VARCHAR(50) NOT NULL COMMENT '应用名称',
    app_code VARCHAR(50) NOT NULL COMMENT '应用编码',
    app_icon VARCHAR(100) DEFAULT NULL COMMENT '应用图标',
    app_url VARCHAR(200) DEFAULT NULL COMMENT '应用链接',
    app_desc VARCHAR(200) DEFAULT NULL COMMENT '应用描述',
    app_type CHAR(1) DEFAULT '1' COMMENT '应用类型（1内部应用 2外部链接）',
    is_enabled CHAR(1) DEFAULT '1' COMMENT '是否启用（0否 1是）',
    sort_order INT DEFAULT 0 COMMENT '排序',
    permission_code VARCHAR(100) DEFAULT NULL COMMENT '权限编码',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    UNIQUE KEY uk_app_code (app_code),
    INDEX idx_app_type (app_type),
    INDEX idx_enabled (is_enabled)
) ENGINE=InnoDB COMMENT='系统快捷应用表';

-- 公告分类表
CREATE TABLE sys_notice_category (
    category_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    category_name VARCHAR(50) NOT NULL COMMENT '分类名称',
    category_code VARCHAR(50) NOT NULL COMMENT '分类编码',
    parent_id BIGINT DEFAULT 0 COMMENT '父分类ID',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_enabled CHAR(1) DEFAULT '1' COMMENT '是否启用（0否 1是）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    UNIQUE KEY uk_category_code (category_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_enabled (is_enabled)
) ENGINE=InnoDB COMMENT='公告分类表';

-- 待办分类表
CREATE TABLE sys_todo_category (
    category_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    category_name VARCHAR(50) NOT NULL COMMENT '分类名称',
    category_code VARCHAR(50) NOT NULL COMMENT '分类编码',
    category_color VARCHAR(20) DEFAULT '#409EFF' COMMENT '分类颜色',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_enabled CHAR(1) DEFAULT '1' COMMENT '是否启用（0否 1是）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    UNIQUE KEY uk_category_code (category_code),
    INDEX idx_enabled (is_enabled)
) ENGINE=InnoDB COMMENT='待办分类表';

-- 消息附件表
CREATE TABLE sys_message_attachment (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    message_id BIGINT NOT NULL COMMENT '消息ID',
    file_name VARCHAR(200) NOT NULL COMMENT '文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT DEFAULT 0 COMMENT '文件大小（字节）',
    file_type VARCHAR(50) DEFAULT NULL COMMENT '文件类型',
    upload_by VARCHAR(64) DEFAULT '' COMMENT '上传者',
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    INDEX idx_message_id (message_id)
) ENGINE=InnoDB COMMENT='消息附件表';

-- 插入默认的快捷应用数据
INSERT INTO sys_quick_app (app_name, app_code, app_icon, app_url, app_desc, app_type, sort_order) VALUES
('个人工作台', 'workspace', 'el-icon-monitor', '/workspace', '个人工作台首页', '1', 1),
('通知公告', 'notice', 'el-icon-bell', '/notice', '查看系统通知公告', '1', 2),
('待办事项', 'todo', 'el-icon-tickets', '/todo', '查看和处理待办事项', '1', 3),
('站内消息', 'message', 'el-icon-message', '/message', '查看站内消息', '1', 4),
('用户管理', 'user', 'el-icon-user', '/system/user', '用户信息管理', '1', 5),
('角色管理', 'role', 'el-icon-s-custom', '/system/role', '角色权限管理', '1', 6),
('部门管理', 'dept', 'el-icon-office-building', '/system/dept', '部门组织管理', '1', 7);

-- 插入默认的公告分类数据
INSERT INTO sys_notice_category (category_name, category_code, sort_order) VALUES
('系统公告', 'system', 1),
('重要通知', 'important', 2),
('会议通知', 'meeting', 3),
('培训通知', 'training', 4),
('活动公告', 'activity', 5);

-- 插入默认的待办分类数据
INSERT INTO sys_todo_category (category_name, category_code, category_color, sort_order) VALUES
('日常工作', 'daily', '#409EFF', 1),
('重要任务', 'important', '#E6A23C', 2),
('紧急事务', 'urgent', '#F56C6C', 3),
('会议安排', 'meeting', '#67C23A', 4),
('项目任务', 'project', '#909399', 5);
