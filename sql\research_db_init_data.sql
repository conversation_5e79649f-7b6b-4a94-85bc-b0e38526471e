-- 科研管理系统初始化数据
-- 包含基础数据和测试数据

USE research_db;

-- ===========================================
-- 系统管理模块初始化数据
-- ===========================================

-- 初始化管理员用户
INSERT INTO sys_user (user_id, dept_id, user_name, nick_name, email, phonenumber, sex, password, status, create_by, remark) VALUES
(1, 103, 'admin', '管理员', '<EMAIL>', '15888888888', '1', '$2a$10$7JB720yubVSOfvVMe6/YqOsrtwt07CFgLpxI6/Z0BcXgVDRb5/jDO', '0', 'admin', '管理员'),
(2, 105, 'research', '科研管理员', '<EMAIL>', '15666666666', '1', '$2a$10$7JB720yubVSOfvVMe6/YqOsrtwt07CFgLpxI6/Z0BcXgVDRb5/jDO', '0', 'admin', '科研管理员'),
(3, 105, 'teacher', '教师用户', '<EMAIL>', '15777777777', '0', '$2a$10$7JB720yubVSOfvVMe6/YqOsrtwt07CFgLpxI6/Z0BcXgVDRb5/jDO', '0', 'admin', '教师用户');

-- 初始化角色信息
INSERT INTO sys_role (role_id, role_name, role_key, role_sort, data_scope, status, create_by, remark) VALUES
(1, '超级管理员', 'admin', 1, '1', '0', 'admin', '超级管理员'),
(2, '科研管理员', 'research_admin', 2, '2', '0', 'admin', '科研管理员'),
(3, '教师用户', 'teacher', 3, '4', '0', 'admin', '教师用户'),
(4, '专家用户', 'expert', 4, '4', '0', 'admin', '专家用户');

-- 初始化部门信息
INSERT INTO sys_dept (dept_id, parent_id, ancestors, dept_name, order_num, leader, phone, email, status, create_by) VALUES
(100, 0, '0', '龙湖大学', 0, '校长', '15888888888', '<EMAIL>', '0', 'admin'),
(101, 100, '0,100', '科研处', 1, '科研处长', '15888888889', '<EMAIL>', '0', 'admin'),
(102, 100, '0,100', '教务处', 2, '教务处长', '15888888890', '<EMAIL>', '0', 'admin'),
(103, 100, '0,100', '信息中心', 3, '信息中心主任', '15888888891', '<EMAIL>', '0', 'admin'),
(104, 100, '0,100', '计算机学院', 4, '院长', '15888888892', '<EMAIL>', '0', 'admin'),
(105, 100, '0,100', '管理学院', 5, '院长', '15888888893', '<EMAIL>', '0', 'admin'),
(106, 100, '0,100', '文学院', 6, '院长', '15888888894', '<EMAIL>', '0', 'admin');

-- 初始化岗位信息
INSERT INTO sys_post (post_id, post_code, post_name, post_sort, status, create_by, remark) VALUES
(1, 'ceo', '校长', 1, '0', 'admin', '校长'),
(2, 'se', '副校长', 2, '0', 'admin', '副校长'),
(3, 'hr', '处长', 3, '0', 'admin', '处长'),
(4, 'user', '院长', 4, '0', 'admin', '院长'),
(5, 'teacher', '教师', 5, '0', 'admin', '教师'),
(6, 'researcher', '科研人员', 6, '0', 'admin', '科研人员');

-- 用户和角色关联
INSERT INTO sys_user_role (user_id, role_id) VALUES
(1, 1),
(2, 2),
(3, 3);

-- 用户和岗位关联
INSERT INTO sys_user_post (user_id, post_id) VALUES
(1, 1),
(2, 3),
(3, 5);

-- 初始化菜单信息
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, remark) VALUES
(1, '系统管理', 0, 1, 'system', NULL, 1, 0, 'M', '0', '0', '', 'system', 'admin', '系统管理目录'),
(2, '项目管理', 0, 2, 'project', NULL, 1, 0, 'M', '0', '0', '', 'project', 'admin', '项目管理目录'),
(3, '成果管理', 0, 3, 'achievement', NULL, 1, 0, 'M', '0', '0', '', 'achievement', 'admin', '成果管理目录'),
(4, '申报评审', 0, 4, 'application', NULL, 1, 0, 'M', '0', '0', '', 'application', 'admin', '申报评审目录'),
(5, '龙湖讲坛', 0, 5, 'forum', NULL, 1, 0, 'M', '0', '0', '', 'forum', 'admin', '龙湖讲坛目录'),
(6, '统计分析', 0, 6, 'statistics', NULL, 1, 0, 'M', '0', '0', '', 'chart', 'admin', '统计分析目录'),

-- 系统管理子菜单
(100, '用户管理', 1, 1, 'user', 'system/user/index', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', '用户管理菜单'),
(101, '角色管理', 1, 2, 'role', 'system/role/index', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', '角色管理菜单'),
(102, '菜单管理', 1, 3, 'menu', 'system/menu/index', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', '菜单管理菜单'),
(103, '部门管理', 1, 4, 'dept', 'system/dept/index', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', '部门管理菜单'),
(104, '岗位管理', 1, 5, 'post', 'system/post/index', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 'admin', '岗位管理菜单'),
(105, '字典管理', 1, 6, 'dict', 'system/dict/index', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'admin', '字典管理菜单'),
(106, '参数设置', 1, 7, 'config', 'system/config/index', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 'admin', '参数设置菜单'),
(107, '通知公告', 1, 8, 'notice', 'system/notice/index', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 'admin', '通知公告菜单'),
(108, '日志管理', 1, 9, 'log', '', 1, 0, 'M', '0', '0', '', 'log', 'admin', '日志管理菜单'),

-- 项目管理子菜单
(200, '纵向项目', 2, 1, 'vertical', 'project/vertical/index', 1, 0, 'C', '0', '0', 'project:vertical:list', 'project', 'admin', '纵向项目菜单'),
(201, '横向项目', 2, 2, 'horizontal', 'project/horizontal/index', 1, 0, 'C', '0', '0', 'project:horizontal:list', 'project', 'admin', '横向项目菜单'),
(202, '校级项目', 2, 3, 'school', 'project/school/index', 1, 0, 'C', '0', '0', 'project:school:list', 'project', 'admin', '校级项目菜单'),
(203, '教学项目', 2, 4, 'teaching', 'project/teaching/index', 1, 0, 'C', '0', '0', 'project:teaching:list', 'education', 'admin', '教学项目菜单'),
(204, '项目变更', 2, 5, 'change', 'project/change/index', 1, 0, 'C', '0', '0', 'project:change:list', 'edit', 'admin', '项目变更菜单'),
(205, '项目结项', 2, 6, 'completion', 'project/completion/index', 1, 0, 'C', '0', '0', 'project:completion:list', 'checkbox', 'admin', '项目结项菜单'),

-- 成果管理子菜单
(300, '论文管理', 3, 1, 'paper', 'achievement/paper/index', 1, 0, 'C', '0', '0', 'achievement:paper:list', 'documentation', 'admin', '论文管理菜单'),
(301, '专利管理', 3, 2, 'patent', 'achievement/patent/index', 1, 0, 'C', '0', '0', 'achievement:patent:list', 'skill', 'admin', '专利管理菜单'),
(302, '著作管理', 3, 3, 'book', 'achievement/book/index', 1, 0, 'C', '0', '0', 'achievement:book:list', 'book', 'admin', '著作管理菜单'),
(303, '获奖管理', 3, 4, 'honor', 'achievement/honor/index', 1, 0, 'C', '0', '0', 'achievement:honor:list', 'star', 'admin', '获奖管理菜单'),
(304, '成果统计', 3, 5, 'statistics', 'achievement/statistics/index', 1, 0, 'C', '0', '0', 'achievement:statistics:list', 'chart', 'admin', '成果统计菜单'),

-- 申报评审子菜单
(400, '申报计划', 4, 1, 'plan', 'application/plan/index', 1, 0, 'C', '0', '0', 'application:plan:list', 'list', 'admin', '申报计划菜单'),
(401, '申报书模板', 4, 2, 'template', 'application/template/index', 1, 0, 'C', '0', '0', 'application:template:list', 'form', 'admin', '申报书模板菜单'),
(402, '申报信息', 4, 3, 'info', 'application/info/index', 1, 0, 'C', '0', '0', 'application:info:list', 'edit', 'admin', '申报信息菜单'),
(403, '评审方案', 4, 4, 'scheme', 'application/scheme/index', 1, 0, 'C', '0', '0', 'application:scheme:list', 'guide', 'admin', '评审方案菜单'),
(404, '专家管理', 4, 5, 'expert', 'application/expert/index', 1, 0, 'C', '0', '0', 'application:expert:list', 'peoples', 'admin', '专家管理菜单'),
(405, '专家指派', 4, 6, 'assignment', 'application/assignment/index', 1, 0, 'C', '0', '0', 'application:assignment:list', 'user', 'admin', '专家指派菜单'),
(406, '在线评审', 4, 7, 'review', 'application/review/index', 1, 0, 'C', '0', '0', 'application:review:list', 'rate', 'admin', '在线评审菜单'),
(407, '评审结果', 4, 8, 'result', 'application/result/index', 1, 0, 'C', '0', '0', 'application:result:list', 'success', 'admin', '评审结果菜单'),

-- 龙湖讲坛子菜单
(500, '讲座管理', 5, 1, 'lecture', 'forum/lecture/index', 1, 0, 'C', '0', '0', 'forum:lecture:list', 'video', 'admin', '讲座管理菜单'),
(501, '专家管理', 5, 2, 'speaker', 'forum/speaker/index', 1, 0, 'C', '0', '0', 'forum:speaker:list', 'user', 'admin', '专家管理菜单'),
(502, '音视频管理', 5, 3, 'media', 'forum/media/index', 1, 0, 'C', '0', '0', 'forum:media:list', 'video-camera', 'admin', '音视频管理菜单'),
(503, '网站管理', 5, 4, 'website', 'forum/website/index', 1, 0, 'C', '0', '0', 'forum:website:list', 'international', 'admin', '网站管理菜单');

-- 角色和菜单关联
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
-- 超级管理员拥有所有权限
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6),
(1, 100), (1, 101), (1, 102), (1, 103), (1, 104), (1, 105), (1, 106), (1, 107), (1, 108),
(1, 200), (1, 201), (1, 202), (1, 203), (1, 204), (1, 205),
(1, 300), (1, 301), (1, 302), (1, 303), (1, 304),
(1, 400), (1, 401), (1, 402), (1, 403), (1, 404), (1, 405), (1, 406), (1, 407),
(1, 500), (1, 501), (1, 502), (1, 503),
-- 科研管理员权限
(2, 2), (2, 3), (2, 4), (2, 6),
(2, 200), (2, 201), (2, 202), (2, 203), (2, 204), (2, 205),
(2, 300), (2, 301), (2, 302), (2, 303), (2, 304),
(2, 400), (2, 401), (2, 402), (2, 403), (2, 404), (2, 405), (2, 406), (2, 407),
-- 教师用户权限
(3, 2), (3, 3), (3, 4), (3, 5),
(3, 200), (3, 201), (3, 202), (3, 203),
(3, 300), (3, 301), (3, 302), (3, 303),
(3, 402), (3, 406),
(3, 500), (3, 501), (3, 502), (3, 503);

-- 初始化字典类型
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, remark) VALUES
(1, '用户性别', 'sys_user_sex', '0', 'admin', '用户性别列表'),
(2, '菜单状态', 'sys_show_hide', '0', 'admin', '菜单状态列表'),
(3, '系统开关', 'sys_normal_disable', '0', 'admin', '系统开关列表'),
(4, '任务状态', 'sys_job_status', '0', 'admin', '任务状态列表'),
(5, '任务分组', 'sys_job_group', '0', 'admin', '任务分组列表'),
(6, '系统是否', 'sys_yes_no', '0', 'admin', '系统是否列表'),
(7, '通知类型', 'sys_notice_type', '0', 'admin', '通知类型列表'),
(8, '通知状态', 'sys_notice_status', '0', 'admin', '通知状态列表'),
(9, '操作类型', 'sys_oper_type', '0', 'admin', '操作类型列表'),
(10, '系统状态', 'sys_common_status', '0', 'admin', '登录状态列表'),
(11, '项目类型', 'project_type', '0', 'admin', '项目类型列表'),
(12, '项目级别', 'project_level', '0', 'admin', '项目级别列表'),
(13, '项目状态', 'project_status', '0', 'admin', '项目状态列表'),
(14, '学科分类', 'subject_class', '0', 'admin', '学科分类列表'),
(15, '职称类型', 'title_type', '0', 'admin', '职称类型列表'),
(16, '学历类型', 'edu_level', '0', 'admin', '学历类型列表'),
(17, '学位类型', 'edu_degree', '0', 'admin', '学位类型列表'),
(18, '审核状态', 'check_status', '0', 'admin', '审核状态列表'),
(19, '论文类型', 'paper_type', '0', 'admin', '论文类型列表'),
(20, '期刊级别', 'journal_level', '0', 'admin', '期刊级别列表'),
(21, '专利类型', 'patent_type', '0', 'admin', '专利类型列表'),
(22, '专利状态', 'patent_status', '0', 'admin', '专利状态列表'),
(23, '著作类型', 'book_type', '0', 'admin', '著作类型列表'),
(24, '出版方式', 'publish_mode', '0', 'admin', '出版方式列表'),
(25, '奖项级别', 'honor_level', '0', 'admin', '奖项级别列表'),
(26, '奖项等级', 'honor_grade', '0', 'admin', '奖项等级列表'),
(27, '申报状态', 'application_status', '0', 'admin', '申报状态列表'),
(28, '评审状态', 'review_status', '0', 'admin', '评审状态列表'),
(29, '评审结果', 'review_result', '0', 'admin', '评审结果列表'),
(30, '专家级别', 'expert_level', '0', 'admin', '专家级别列表');

-- 初始化字典数据
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, remark) VALUES
-- 用户性别
(1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '性别男'),
(2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '性别女'),
(3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '性别未知'),

-- 菜单状态
(4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '显示菜单'),
(5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '隐藏菜单'),

-- 系统开关
(6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '正常状态'),
(7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '停用状态'),

-- 系统是否
(8, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '系统默认是'),
(9, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '系统默认否'),

-- 通知类型
(10, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '通知'),
(11, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '公告'),

-- 通知状态
(12, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '正常状态'),
(13, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '关闭状态'),

-- 操作类型
(14, 1, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '其他操作'),
(15, 2, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '新增操作'),
(16, 3, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '修改操作'),
(17, 4, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '删除操作'),
(18, 5, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '授权操作'),
(19, 6, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '导出操作'),
(20, 7, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '导入操作'),
(21, 8, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '强退操作'),
(22, 9, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '生成操作'),
(23, 10, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '清空操作'),

-- 系统状态
(24, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '正常状态'),
(25, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '停用状态'),

-- 项目类型
(26, 1, '国家级项目', 'national', 'project_type', '', 'primary', 'N', '0', 'admin', '国家级项目'),
(27, 2, '省部级项目', 'provincial', 'project_type', '', 'success', 'N', '0', 'admin', '省部级项目'),
(28, 3, '市厅级项目', 'municipal', 'project_type', '', 'info', 'N', '0', 'admin', '市厅级项目'),
(29, 4, '校级项目', 'school', 'project_type', '', 'warning', 'N', '0', 'admin', '校级项目'),
(30, 5, '横向项目', 'horizontal', 'project_type', '', 'secondary', 'N', '0', 'admin', '横向项目'),
(31, 6, '教学项目', 'teaching', 'project_type', '', 'primary', 'N', '0', 'admin', '教学项目'),

-- 项目级别
(32, 1, '国家级', 'national', 'project_level', '', 'primary', 'N', '0', 'admin', '国家级'),
(33, 2, '省级', 'provincial', 'project_level', '', 'success', 'N', '0', 'admin', '省级'),
(34, 3, '市级', 'municipal', 'project_level', '', 'info', 'N', '0', 'admin', '市级'),
(35, 4, '校级', 'school', 'project_level', '', 'warning', 'N', '0', 'admin', '校级'),

-- 项目状态
(36, 1, '申报中', 'applying', 'project_status', '', 'info', 'N', '0', 'admin', '申报中'),
(37, 2, '立项', 'approved', 'project_status', '', 'success', 'N', '0', 'admin', '立项'),
(38, 3, '在研', 'ongoing', 'project_status', '', 'primary', 'N', '0', 'admin', '在研'),
(39, 4, '结项', 'completed', 'project_status', '', 'secondary', 'N', '0', 'admin', '结项'),
(40, 5, '终止', 'terminated', 'project_status', '', 'danger', 'N', '0', 'admin', '终止'),

-- 审核状态
(41, 1, '待审核', 'pending', 'check_status', '', 'warning', 'N', '0', 'admin', '待审核'),
(42, 2, '审核通过', 'approved', 'check_status', '', 'success', 'N', '0', 'admin', '审核通过'),
(43, 3, '审核不通过', 'rejected', 'check_status', '', 'danger', 'N', '0', 'admin', '审核不通过'),

-- 期刊级别
(44, 1, 'SCI', 'sci', 'journal_level', '', 'primary', 'N', '0', 'admin', 'SCI期刊'),
(45, 2, 'EI', 'ei', 'journal_level', '', 'success', 'N', '0', 'admin', 'EI期刊'),
(46, 3, 'CSSCI', 'cssci', 'journal_level', '', 'info', 'N', '0', 'admin', 'CSSCI期刊'),
(47, 4, 'CSCD', 'cscd', 'journal_level', '', 'warning', 'N', '0', 'admin', 'CSCD期刊'),
(48, 5, '核心期刊', 'core', 'journal_level', '', 'secondary', 'N', '0', 'admin', '核心期刊'),
(49, 6, '普通期刊', 'normal', 'journal_level', '', 'light', 'N', '0', 'admin', '普通期刊'),

-- 专利类型
(50, 1, '发明专利', 'invention', 'patent_type', '', 'primary', 'N', '0', 'admin', '发明专利'),
(51, 2, '实用新型', 'utility', 'patent_type', '', 'success', 'N', '0', 'admin', '实用新型'),
(52, 3, '外观设计', 'design', 'patent_type', '', 'info', 'N', '0', 'admin', '外观设计'),

-- 专利状态
(53, 1, '申请中', 'applying', 'patent_status', '', 'info', 'N', '0', 'admin', '申请中'),
(54, 2, '已授权', 'authorized', 'patent_status', '', 'success', 'N', '0', 'admin', '已授权'),
(55, 3, '已公开', 'published', 'patent_status', '', 'warning', 'N', '0', 'admin', '已公开'),
(56, 4, '已驳回', 'rejected', 'patent_status', '', 'danger', 'N', '0', 'admin', '已驳回'),

-- 奖项级别
(57, 1, '国家级', 'national', 'honor_level', '', 'primary', 'N', '0', 'admin', '国家级奖项'),
(58, 2, '省部级', 'provincial', 'honor_level', '', 'success', 'N', '0', 'admin', '省部级奖项'),
(59, 3, '市厅级', 'municipal', 'honor_level', '', 'info', 'N', '0', 'admin', '市厅级奖项'),
(60, 4, '校级', 'school', 'honor_level', '', 'warning', 'N', '0', 'admin', '校级奖项'),

-- 奖项等级
(61, 1, '特等奖', 'special', 'honor_grade', '', 'primary', 'N', '0', 'admin', '特等奖'),
(62, 2, '一等奖', 'first', 'honor_grade', '', 'success', 'N', '0', 'admin', '一等奖'),
(63, 3, '二等奖', 'second', 'honor_grade', '', 'info', 'N', '0', 'admin', '二等奖'),
(64, 4, '三等奖', 'third', 'honor_grade', '', 'warning', 'N', '0', 'admin', '三等奖'),

-- 申报状态
(65, 1, '草稿', 'draft', 'application_status', '', 'secondary', 'N', '0', 'admin', '草稿状态'),
(66, 2, '已提交', 'submitted', 'application_status', '', 'info', 'N', '0', 'admin', '已提交'),
(67, 3, '评审中', 'reviewing', 'application_status', '', 'warning', 'N', '0', 'admin', '评审中'),
(68, 4, '已完成', 'completed', 'application_status', '', 'success', 'N', '0', 'admin', '已完成'),

-- 评审状态
(69, 1, '待评审', 'pending', 'review_status', '', 'warning', 'N', '0', 'admin', '待评审'),
(70, 2, '评审中', 'reviewing', 'review_status', '', 'info', 'N', '0', 'admin', '评审中'),
(71, 3, '已完成', 'completed', 'review_status', '', 'success', 'N', '0', 'admin', '已完成'),

-- 评审结果
(72, 1, '通过', 'pass', 'review_result', '', 'success', 'N', '0', 'admin', '评审通过'),
(73, 2, '不通过', 'fail', 'review_result', '', 'danger', 'N', '0', 'admin', '评审不通过'),
(74, 3, '待定', 'pending', 'review_result', '', 'warning', 'N', '0', 'admin', '评审待定'),

-- 专家级别
(75, 1, '院士', 'academician', 'expert_level', '', 'primary', 'N', '0', 'admin', '院士'),
(76, 2, '长江学者', 'changjiang', 'expert_level', '', 'success', 'N', '0', 'admin', '长江学者'),
(77, 3, '杰青', 'jieqing', 'expert_level', '', 'info', 'N', '0', 'admin', '杰出青年'),
(78, 4, '教授', 'professor', 'expert_level', '', 'warning', 'N', '0', 'admin', '教授'),
(79, 5, '副教授', 'associate_professor', 'expert_level', '', 'secondary', 'N', '0', 'admin', '副教授'),
(80, 6, '讲师', 'lecturer', 'expert_level', '', 'light', 'N', '0', 'admin', '讲师');

-- 初始化参数配置
INSERT INTO sys_config (config_id, config_name, config_key, config_value, config_type, create_by, remark) VALUES
(1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow'),
(2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '初始化密码 123456'),
(3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '深色主题theme-dark，浅色主题theme-light'),
(4, '账号自助-验证码开关', 'sys.account.captchaEnabled', 'true', 'Y', 'admin', '是否开启验证码功能（true开启，false关闭）'),
(5, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '是否开启注册用户功能（true开启，false关闭）'),
(6, '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 'admin', '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）'),
(7, '科研管理-项目申报截止提醒天数', 'research.project.remind.days', '7', 'N', 'admin', '项目申报截止前提醒天数'),
(8, '科研管理-成果统计周期', 'research.achievement.stat.period', 'year', 'N', 'admin', '成果统计周期（year年度，quarter季度，month月度）'),
(9, '科研管理-专家评审超时天数', 'research.expert.review.timeout', '15', 'N', 'admin', '专家评审超时天数'),
(10, '科研管理-文件上传大小限制', 'research.file.upload.maxSize', '100', 'N', 'admin', '文件上传大小限制（MB）');
