<template>
  <div class="simple-process-container">
    <h2>流程管理测试页面</h2>
    
    <el-card>
      <p>如果你能看到这个页面，说明路由配置正确。</p>
      <p>当前时间: {{ currentTime }}</p>
      
      <el-button type="primary" @click="testClick">测试点击</el-button>
      <el-button type="success" @click="testAPI">测试API</el-button>
    </el-card>

    <el-card class="mt-4">
      <h3>API测试结果</h3>
      <pre>{{ apiResult }}</pre>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { listDefinitions } from '@/api/workflow/process'

const currentTime = ref('')
const apiResult = ref('')

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

const testClick = () => {
  ElMessage.success('点击测试成功！')
  updateTime()
}

const testAPI = async () => {
  try {
    const response = await listDefinitions()
    apiResult.value = JSON.stringify(response, null, 2)
    ElMessage.success('API调用成功')
  } catch (error) {
    apiResult.value = 'API调用失败: ' + error
    ElMessage.error('API调用失败')
  }
}

onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
})
</script>

<style scoped>
.simple-process-container {
  padding: 20px;
}

.mt-4 {
  margin-top: 16px;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}
</style>
