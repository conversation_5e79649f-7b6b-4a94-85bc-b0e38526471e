<template>
  <div class="user-profile">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card>
          <template #header>
            <span>个人信息</span>
          </template>
          
          <div class="profile-avatar">
            <el-avatar :size="120" :src="userInfo.avatar" />
            <div class="avatar-upload">
              <el-button type="primary" size="small" @click="uploadAvatar">
                更换头像
              </el-button>
            </div>
          </div>
          
          <div class="profile-info">
            <p><strong>用户名：</strong>{{ userInfo.userName }}</p>
            <p><strong>昵称：</strong>{{ userInfo.nickName }}</p>
            <p><strong>部门：</strong>{{ userInfo.deptName }}</p>
            <p><strong>角色：</strong>{{ userInfo.roles }}</p>
            <p><strong>创建时间：</strong>{{ userInfo.createTime }}</p>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="18">
        <el-card>
          <template #header>
            <span>基本资料</span>
          </template>
          
          <el-tabs v-model="activeTab">
            <el-tab-pane label="基本信息" name="basic">
              <el-form
                ref="basicFormRef"
                :model="basicForm"
                :rules="basicRules"
                label-width="100px"
              >
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="用户昵称" prop="nickName">
                      <el-input v-model="basicForm.nickName" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="手机号码" prop="phonenumber">
                      <el-input v-model="basicForm.phonenumber" />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="邮箱" prop="email">
                      <el-input v-model="basicForm.email" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="性别">
                      <el-radio-group v-model="basicForm.sex">
                        <el-radio label="0">男</el-radio>
                        <el-radio label="1">女</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-form-item>
                  <el-button type="primary" @click="updateBasicInfo">保存</el-button>
                  <el-button @click="resetBasicForm">重置</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            
            <el-tab-pane label="修改密码" name="password">
              <el-form
                ref="passwordFormRef"
                :model="passwordForm"
                :rules="passwordRules"
                label-width="100px"
              >
                <el-form-item label="旧密码" prop="oldPassword">
                  <el-input
                    v-model="passwordForm.oldPassword"
                    type="password"
                    show-password
                  />
                </el-form-item>
                
                <el-form-item label="新密码" prop="newPassword">
                  <el-input
                    v-model="passwordForm.newPassword"
                    type="password"
                    show-password
                  />
                </el-form-item>
                
                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input
                    v-model="passwordForm.confirmPassword"
                    type="password"
                    show-password
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="updatePassword">保存</el-button>
                  <el-button @click="resetPasswordForm">重置</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

const activeTab = ref('basic')

const userInfo = ref({
  userName: '',
  nickName: '',
  deptName: '',
  roles: '',
  createTime: '',
  avatar: ''
})

const basicFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()

const basicForm = reactive({
  nickName: '',
  phonenumber: '',
  email: '',
  sex: '0'
})

const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const basicRules: FormRules = {
  nickName: [
    { required: true, message: '请输入用户昵称', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phonenumber: [
    { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

const passwordRules: FormRules = {
  oldPassword: [
    { required: true, message: '请输入旧密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

const uploadAvatar = () => {
  // TODO: 实现头像上传功能
  ElMessage.info('头像上传功能待实现')
}

const updateBasicInfo = async () => {
  if (!basicFormRef.value) return
  
  await basicFormRef.value.validate((valid) => {
    if (valid) {
      // TODO: 调用API更新基本信息
      ElMessage.success('基本信息更新成功')
    }
  })
}

const updatePassword = async () => {
  if (!passwordFormRef.value) return
  
  await passwordFormRef.value.validate((valid) => {
    if (valid) {
      // TODO: 调用API更新密码
      ElMessage.success('密码修改成功')
      resetPasswordForm()
    }
  })
}

const resetBasicForm = () => {
  basicFormRef.value?.resetFields()
}

const resetPasswordForm = () => {
  passwordFormRef.value?.resetFields()
}

onMounted(() => {
  // TODO: 调用API获取用户信息
  userInfo.value = {
    userName: 'admin',
    nickName: '管理员',
    deptName: '研发部门',
    roles: '超级管理员',
    createTime: '2024-01-01',
    avatar: ''
  }
  
  // 初始化表单数据
  Object.assign(basicForm, {
    nickName: userInfo.value.nickName,
    phonenumber: '',
    email: '',
    sex: '0'
  })
})
</script>

<style scoped>
.user-profile {
  padding: 20px;
}

.profile-avatar {
  text-align: center;
  margin-bottom: 20px;
}

.avatar-upload {
  margin-top: 10px;
}

.profile-info {
  text-align: left;
}

.profile-info p {
  margin: 10px 0;
  line-height: 1.6;
}
</style>
