-- 科研管理系统成果管理模块表结构
-- 兼容原有Oracle表结构，包含论文、专利、著作、获奖等

USE research_db;

-- ===========================================
-- 成果管理模块表结构
-- ===========================================

-- 论文表（兼容BIZ_PAPER）
CREATE TABLE biz_paper (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    paper_title VARCHAR(1024) COMMENT '论文标题',
    paper_code VARCHAR(200) COMMENT '论文编号',
    journal_name VARCHAR(500) COMMENT '期刊名称',
    journal_level_id VARCHAR(32) COMMENT '期刊级别ID',
    journal_type_id VARCHAR(32) COMMENT '期刊类型ID',
    publication_date DATE COMMENT '发表日期',
    volume_number VARCHAR(100) COMMENT '卷号',
    issue_number VARCHAR(100) COMMENT '期号',
    page_range VARCHAR(100) COMMENT '页码范围',
    doi VARCHAR(200) COMMENT 'DOI',
    issn VARCHAR(50) COMMENT 'ISSN',
    impact_factor DECIMAL(10,3) COMMENT '影响因子',
    citation_count INT COMMENT '被引次数',
    subject_class_id VARCHAR(32) COMMENT '学科分类ID',
    subject_id VARCHAR(32) COMMENT '学科ID',
    research_field VARCHAR(500) COMMENT '研究领域',
    keywords VARCHAR(1000) COMMENT '关键词',
    abstract_content TEXT COMMENT '摘要',
    first_author_id VARCHAR(32) COMMENT '第一作者ID',
    first_author_name VARCHAR(500) COMMENT '第一作者姓名',
    first_author_account VARCHAR(50) COMMENT '第一作者账号',
    corresponding_author_id VARCHAR(32) COMMENT '通讯作者ID',
    corresponding_author_name VARCHAR(500) COMMENT '通讯作者姓名',
    unit_id VARCHAR(32) COMMENT '单位ID',
    author_number INT COMMENT '作者数量',
    school_sign VARCHAR(32) COMMENT '学校标识',
    is_sci CHAR(1) DEFAULT 'N' COMMENT '是否SCI',
    is_ei CHAR(1) DEFAULT 'N' COMMENT '是否EI',
    is_cssci CHAR(1) DEFAULT 'N' COMMENT '是否CSSCI',
    is_cscd CHAR(1) DEFAULT 'N' COMMENT '是否CSCD',
    check_status VARCHAR(32) COMMENT '审核状态',
    check_date DATE COMMENT '审核日期',
    checker VARCHAR(50) COMMENT '审核人',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    note TEXT COMMENT '备注',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    last_edit_user_id VARCHAR(50) COMMENT '最后编辑用户ID',
    create_date DATE COMMENT '创建日期',
    last_edit_date DATE COMMENT '最后编辑日期',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    last_edit_user_name VARCHAR(50) COMMENT '最后编辑用户名',
    author_pids VARCHAR(500) COMMENT '作者PIDs',
    author_unit_ids VARCHAR(500) COMMENT '作者单位IDs',
    complete_data_status VARCHAR(20) COMMENT '完成数据状态',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='论文表';

-- 论文作者表（兼容BIZ_PAPER_AUTHOR）
CREATE TABLE biz_paper_author (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    paper_id VARCHAR(32) COMMENT '论文ID',
    person_id VARCHAR(32) COMMENT '人员ID',
    order_id INT COMMENT '排序ID',
    author_name VARCHAR(50) COMMENT '作者姓名',
    author_account VARCHAR(50) COMMENT '作者账号',
    author_type VARCHAR(32) COMMENT '作者类型',
    author_unit VARCHAR(500) COMMENT '作者单位',
    author_unit_id VARCHAR(32) COMMENT '作者单位ID',
    title_id VARCHAR(32) COMMENT '职称ID',
    edu_level_id VARCHAR(32) COMMENT '学历ID',
    edu_degree_id VARCHAR(32) COMMENT '学位ID',
    sex_id VARCHAR(32) COMMENT '性别ID',
    subject_id VARCHAR(32) COMMENT '学科ID',
    work_ratio DECIMAL(10,2) COMMENT '工作比例',
    is_corresponding CHAR(1) DEFAULT 'N' COMMENT '是否通讯作者',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='论文作者表';

-- 专利表（兼容BIZ_PATENT）
CREATE TABLE biz_patent (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    patent_name VARCHAR(1024) COMMENT '专利名称',
    patent_code VARCHAR(200) COMMENT '专利编号',
    application_number VARCHAR(200) COMMENT '申请号',
    authorization_number VARCHAR(200) COMMENT '授权号',
    patent_type_id VARCHAR(32) COMMENT '专利类型ID',
    patent_status VARCHAR(32) COMMENT '专利状态',
    application_date DATE COMMENT '申请日期',
    authorization_date DATE COMMENT '授权日期',
    publication_date DATE COMMENT '公开日期',
    ipc_classification VARCHAR(200) COMMENT 'IPC分类',
    subject_class_id VARCHAR(32) COMMENT '学科分类ID',
    subject_id VARCHAR(32) COMMENT '学科ID',
    technical_field VARCHAR(500) COMMENT '技术领域',
    patent_abstract TEXT COMMENT '专利摘要',
    main_claims TEXT COMMENT '主权利要求',
    inventor_id VARCHAR(32) COMMENT '发明人ID',
    inventor_name VARCHAR(500) COMMENT '发明人姓名',
    inventor_account VARCHAR(50) COMMENT '发明人账号',
    applicant_unit VARCHAR(500) COMMENT '申请单位',
    unit_id VARCHAR(32) COMMENT '单位ID',
    inventor_number INT COMMENT '发明人数量',
    agency_name VARCHAR(500) COMMENT '代理机构',
    agency_code VARCHAR(200) COMMENT '代理机构代码',
    school_sign VARCHAR(32) COMMENT '学校标识',
    check_status VARCHAR(32) COMMENT '审核状态',
    check_date DATE COMMENT '审核日期',
    checker VARCHAR(50) COMMENT '审核人',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    note TEXT COMMENT '备注',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    last_edit_user_id VARCHAR(50) COMMENT '最后编辑用户ID',
    create_date DATE COMMENT '创建日期',
    last_edit_date DATE COMMENT '最后编辑日期',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    last_edit_user_name VARCHAR(50) COMMENT '最后编辑用户名',
    author_pids VARCHAR(500) COMMENT '作者PIDs',
    author_unit_ids VARCHAR(500) COMMENT '作者单位IDs',
    complete_data_status VARCHAR(20) COMMENT '完成数据状态',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='专利表';

-- 专利发明人表（兼容BIZ_PATENT_INVENTOR）
CREATE TABLE biz_patent_inventor (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    patent_id VARCHAR(32) COMMENT '专利ID',
    person_id VARCHAR(32) COMMENT '人员ID',
    order_id INT COMMENT '排序ID',
    inventor_name VARCHAR(50) COMMENT '发明人姓名',
    inventor_account VARCHAR(50) COMMENT '发明人账号',
    inventor_type VARCHAR(32) COMMENT '发明人类型',
    inventor_unit VARCHAR(500) COMMENT '发明人单位',
    inventor_unit_id VARCHAR(32) COMMENT '发明人单位ID',
    title_id VARCHAR(32) COMMENT '职称ID',
    edu_level_id VARCHAR(32) COMMENT '学历ID',
    edu_degree_id VARCHAR(32) COMMENT '学位ID',
    sex_id VARCHAR(32) COMMENT '性别ID',
    subject_id VARCHAR(32) COMMENT '学科ID',
    work_ratio DECIMAL(10,2) COMMENT '工作比例',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='专利发明人表';

-- 著作表（兼容BIZ_BOOK）
CREATE TABLE biz_book (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    book_name VARCHAR(1024) COMMENT '著作名称',
    book_code VARCHAR(200) COMMENT '著作编号',
    isbn VARCHAR(128) COMMENT 'ISBN',
    book_type_id VARCHAR(32) COMMENT '著作类型ID',
    publish_mode_id VARCHAR(32) COMMENT '出版方式ID',
    publisher VARCHAR(255) COMMENT '出版社',
    publish_date DATE COMMENT '出版日期',
    publish_address_id VARCHAR(32) COMMENT '出版地ID',
    edition_number VARCHAR(50) COMMENT '版次',
    print_number VARCHAR(50) COMMENT '印次',
    word_count INT COMMENT '字数',
    page_count INT COMMENT '页数',
    price DECIMAL(10,2) COMMENT '定价',
    subject_class_id VARCHAR(32) COMMENT '学科分类ID',
    subject_id VARCHAR(32) COMMENT '学科ID',
    language_id VARCHAR(32) COMMENT '语言ID',
    is_translated CHAR(1) DEFAULT 'N' COMMENT '是否翻译',
    original_language VARCHAR(100) COMMENT '原文语言',
    project_source_id VARCHAR(32) COMMENT '项目来源ID',
    first_author_id VARCHAR(32) COMMENT '第一作者ID',
    first_author_name VARCHAR(500) COMMENT '第一作者姓名',
    first_author_account VARCHAR(50) COMMENT '第一作者账号',
    unit_id VARCHAR(32) COMMENT '单位ID',
    author_number INT COMMENT '作者数量',
    school_sign VARCHAR(32) COMMENT '学校标识',
    publish_level VARCHAR(128) COMMENT '出版级别',
    cip VARCHAR(32) COMMENT 'CIP',
    is_revision CHAR(1) DEFAULT 'N' COMMENT '是否再版',
    check_status VARCHAR(32) COMMENT '审核状态',
    check_date DATE COMMENT '审核日期',
    checker VARCHAR(50) COMMENT '审核人',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    note TEXT COMMENT '备注',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    last_edit_user_id VARCHAR(50) COMMENT '最后编辑用户ID',
    create_date DATE COMMENT '创建日期',
    last_edit_date DATE COMMENT '最后编辑日期',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    last_edit_user_name VARCHAR(50) COMMENT '最后编辑用户名',
    author_pids VARCHAR(500) COMMENT '作者PIDs',
    author_unit_ids VARCHAR(500) COMMENT '作者单位IDs',
    complete_data_status VARCHAR(20) COMMENT '完成数据状态',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='著作表';

-- 著作作者表（兼容BIZ_BOOK_AUTHOR）
CREATE TABLE biz_book_author (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    book_id VARCHAR(32) COMMENT '著作ID',
    person_id VARCHAR(32) COMMENT '人员ID',
    order_id INT COMMENT '排序ID',
    author_name VARCHAR(50) COMMENT '作者姓名',
    author_account VARCHAR(50) COMMENT '作者账号',
    author_type VARCHAR(32) COMMENT '作者类型',
    author_unit VARCHAR(500) COMMENT '作者单位',
    author_unit_id VARCHAR(32) COMMENT '作者单位ID',
    title_id VARCHAR(32) COMMENT '职称ID',
    edu_level_id VARCHAR(32) COMMENT '学历ID',
    edu_degree_id VARCHAR(32) COMMENT '学位ID',
    sex_id VARCHAR(32) COMMENT '性别ID',
    subject_id VARCHAR(32) COMMENT '学科ID',
    word_count INT COMMENT '字数',
    work_ratio DECIMAL(10,2) COMMENT '工作比例',
    bear_type_id VARCHAR(32) COMMENT '承担类型ID',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='著作作者表';

-- 获奖表（兼容BIZ_HONOR）
CREATE TABLE biz_honor (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    honor_name VARCHAR(500) COMMENT '奖项名称',
    product_name VARCHAR(255) COMMENT '成果名称',
    honor_number INT COMMENT '获奖序号',
    honor_unit VARCHAR(255) COMMENT '颁奖单位',
    honor_date DATE COMMENT '获奖日期',
    honor_level_id VARCHAR(32) COMMENT '奖项级别ID',
    honor_grade_id VARCHAR(32) COMMENT '奖项等级ID',
    honor_type_id VARCHAR(32) COMMENT '奖项类型ID',
    complete_unit VARCHAR(255) COMMENT '完成单位',
    total_unit_num INT COMMENT '总单位数',
    unit_order_id VARCHAR(32) COMMENT '单位排序ID',
    honor_sn VARCHAR(100) COMMENT '获奖证书号',
    subject_class_id VARCHAR(32) COMMENT '学科分类ID',
    subject_id VARCHAR(32) COMMENT '学科ID',
    project_source_id VARCHAR(32) COMMENT '项目来源ID',
    product_mode VARCHAR(100) COMMENT '成果形式',
    unit_id VARCHAR(32) COMMENT '单位ID',
    division_id VARCHAR(32) COMMENT '部门ID',
    author_number INT COMMENT '获奖人数',
    first_author_id VARCHAR(32) COMMENT '第一获奖人ID',
    first_author_name VARCHAR(500) COMMENT '第一获奖人姓名',
    first_author_account VARCHAR(50) COMMENT '第一获奖人账号',
    first_author_title_id VARCHAR(32) COMMENT '第一获奖人职称ID',
    first_author_sex_id VARCHAR(32) COMMENT '第一获奖人性别ID',
    first_author_edu_level_id VARCHAR(32) COMMENT '第一获奖人学历ID',
    first_author_edu_degree_id VARCHAR(32) COMMENT '第一获奖人学位ID',
    cooperation_type VARCHAR(255) COMMENT '合作类型',
    honor_fee VARCHAR(255) COMMENT '奖金',
    school_scale VARCHAR(255) COMMENT '学校规模',
    honor_type VARCHAR(255) COMMENT '奖项类型',
    check_status VARCHAR(32) COMMENT '审核状态',
    check_date DATE COMMENT '审核日期',
    checker VARCHAR(50) COMMENT '审核人',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    note TEXT COMMENT '备注',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    last_edit_user_id VARCHAR(50) COMMENT '最后编辑用户ID',
    create_date DATE COMMENT '创建日期',
    last_edit_date DATE COMMENT '最后编辑日期',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    last_edit_user_name VARCHAR(50) COMMENT '最后编辑用户名',
    author_pids VARCHAR(500) COMMENT '作者PIDs',
    author_unit_ids VARCHAR(500) COMMENT '作者单位IDs',
    complete_data_status VARCHAR(20) COMMENT '完成数据状态',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='获奖表';

-- 获奖人员表（兼容BIZ_HONOR_AUTHOR）
CREATE TABLE biz_honor_author (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    honor_id VARCHAR(32) COMMENT '获奖ID',
    person_id VARCHAR(32) COMMENT '人员ID',
    order_id INT COMMENT '排序ID',
    author_name VARCHAR(50) COMMENT '获奖人姓名',
    author_account VARCHAR(50) COMMENT '获奖人账号',
    author_type VARCHAR(32) COMMENT '获奖人类型',
    author_unit VARCHAR(500) COMMENT '获奖人单位',
    author_unit_id VARCHAR(32) COMMENT '获奖人单位ID',
    title_id VARCHAR(32) COMMENT '职称ID',
    edu_level_id VARCHAR(32) COMMENT '学历ID',
    edu_degree_id VARCHAR(32) COMMENT '学位ID',
    sex_id VARCHAR(32) COMMENT '性别ID',
    subject_id VARCHAR(32) COMMENT '学科ID',
    work_ratio DECIMAL(10,2) COMMENT '工作比例',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='获奖人员表';
