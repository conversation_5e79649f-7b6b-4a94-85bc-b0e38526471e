-- 工作流模型表
CREATE TABLE `workflow_model` (
  `id` varchar(64) NOT NULL COMMENT '模型ID',
  `name` varchar(100) NOT NULL COMMENT '模型名称',
  `model_key` varchar(100) NOT NULL COMMENT '模型标识',
  `category` varchar(50) DEFAULT NULL COMMENT '模型分类',
  `description` varchar(500) DEFAULT NULL COMMENT '模型描述',
  `version` int(11) DEFAULT '1' COMMENT '模型版本',
  `model_json` longtext COMMENT '模型JSON内容',
  `model_xml` longtext COMMENT '模型XML内容',
  `has_editor_source` tinyint(1) DEFAULT '1' COMMENT '是否有编辑器源码',
  `has_editor_source_extra` tinyint(1) DEFAULT '0' COMMENT '是否有编辑器扩展源码',
  `last_update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
  `deployment_id` varchar(64) DEFAULT NULL COMMENT '部署ID',
  `process_definition_id` varchar(64) DEFAULT NULL COMMENT '流程定义ID',
  `deployed` tinyint(1) DEFAULT '0' COMMENT '是否已部署',
  `deploy_time` datetime DEFAULT NULL COMMENT '部署时间',
  `status` char(1) DEFAULT '0' COMMENT '模型状态（0草稿 1已发布 2已停用）',
  `icon` varchar(100) DEFAULT NULL COMMENT '模型图标',
  `color` varchar(20) DEFAULT NULL COMMENT '模型颜色',
  `order_num` int(11) DEFAULT '0' COMMENT '排序号',
  `visible` char(1) DEFAULT '1' COMMENT '是否可见（0隐藏 1显示）',
  `tags` varchar(200) DEFAULT NULL COMMENT '模型标签',
  `form_config` longtext COMMENT '表单配置',
  `permission_config` longtext COMMENT '权限配置',
  `notification_config` longtext COMMENT '通知配置',
  `timeout_config` longtext COMMENT '超时配置',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_key` (`model_key`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_deployed` (`deployed`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_last_update_time` (`last_update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流模型表';

-- 插入示例数据
INSERT INTO `workflow_model` (`id`, `name`, `model_key`, `category`, `description`, `version`, `model_xml`, `has_editor_source`, `has_editor_source_extra`, `last_update_time`, `deployed`, `status`, `icon`, `color`, `order_num`, `visible`, `tags`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('model_001', '请假申请流程', 'leave_process', 'approval', '员工请假申请审批流程', 1, '<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn2:process id="leave_process" isExecutable="true">
    <bpmn2:startEvent id="StartEvent_1"/>
    <bpmn2:userTask id="UserTask_1" name="部门经理审批"/>
    <bpmn2:userTask id="UserTask_2" name="HR审批"/>
    <bpmn2:endEvent id="EndEvent_1"/>
    <bpmn2:sequenceFlow id="SequenceFlow_1" sourceRef="StartEvent_1" targetRef="UserTask_1"/>
    <bpmn2:sequenceFlow id="SequenceFlow_2" sourceRef="UserTask_1" targetRef="UserTask_2"/>
    <bpmn2:sequenceFlow id="SequenceFlow_3" sourceRef="UserTask_2" targetRef="EndEvent_1"/>
  </bpmn2:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="leave_process">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="173" y="102" width="36" height="36"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_UserTask_1" bpmnElement="UserTask_1">
        <dc:Bounds x="259" y="80" width="100" height="80"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_UserTask_2" bpmnElement="UserTask_2">
        <dc:Bounds x="409" y="80" width="100" height="80"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_EndEvent_1" bpmnElement="EndEvent_1">
        <dc:Bounds x="559" y="102" width="36" height="36"/>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn2:definitions>', 1, 0, NOW(), 0, '0', 'user', '#409EFF', 1, '1', '请假,审批', 'admin', NOW(), 'admin', NOW(), '请假申请流程模型'),

('model_002', '报销审批流程', 'expense_process', 'approval', '员工报销申请审批流程', 1, '<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn2:process id="expense_process" isExecutable="true">
    <bpmn2:startEvent id="StartEvent_1"/>
    <bpmn2:userTask id="UserTask_1" name="部门经理审批"/>
    <bpmn2:userTask id="UserTask_2" name="财务审批"/>
    <bpmn2:endEvent id="EndEvent_1"/>
    <bpmn2:sequenceFlow id="SequenceFlow_1" sourceRef="StartEvent_1" targetRef="UserTask_1"/>
    <bpmn2:sequenceFlow id="SequenceFlow_2" sourceRef="UserTask_1" targetRef="UserTask_2"/>
    <bpmn2:sequenceFlow id="SequenceFlow_3" sourceRef="UserTask_2" targetRef="EndEvent_1"/>
  </bpmn2:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="expense_process">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="173" y="102" width="36" height="36"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_UserTask_1" bpmnElement="UserTask_1">
        <dc:Bounds x="259" y="80" width="100" height="80"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_UserTask_2" bpmnElement="UserTask_2">
        <dc:Bounds x="409" y="80" width="100" height="80"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_EndEvent_1" bpmnElement="EndEvent_1">
        <dc:Bounds x="559" y="102" width="36" height="36"/>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn2:definitions>', 1, 0, NOW(), 0, '0', 'money', '#67C23A', 2, '1', '报销,审批', 'admin', NOW(), 'admin', NOW(), '报销审批流程模型'),

('model_003', '采购申请流程', 'purchase_process', 'business', '采购申请审批流程', 1, '<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn2:process id="purchase_process" isExecutable="true">
    <bpmn2:startEvent id="StartEvent_1"/>
    <bpmn2:userTask id="UserTask_1" name="部门经理审批"/>
    <bpmn2:userTask id="UserTask_2" name="采购部审批"/>
    <bpmn2:userTask id="UserTask_3" name="财务审批"/>
    <bpmn2:endEvent id="EndEvent_1"/>
    <bpmn2:sequenceFlow id="SequenceFlow_1" sourceRef="StartEvent_1" targetRef="UserTask_1"/>
    <bpmn2:sequenceFlow id="SequenceFlow_2" sourceRef="UserTask_1" targetRef="UserTask_2"/>
    <bpmn2:sequenceFlow id="SequenceFlow_3" sourceRef="UserTask_2" targetRef="UserTask_3"/>
    <bpmn2:sequenceFlow id="SequenceFlow_4" sourceRef="UserTask_3" targetRef="EndEvent_1"/>
  </bpmn2:process>
</bpmn2:definitions>', 1, 0, NOW(), 0, '0', 'shopping-cart', '#E6A23C', 3, '1', '采购,审批', 'admin', NOW(), 'admin', NOW(), '采购申请流程模型');

-- 创建索引以提高查询性能
CREATE INDEX idx_workflow_model_key_version ON workflow_model(model_key, version);
CREATE INDEX idx_workflow_model_category_status ON workflow_model(category, status);
CREATE INDEX idx_workflow_model_deployed_status ON workflow_model(deployed, status);
