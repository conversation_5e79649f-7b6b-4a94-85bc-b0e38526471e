package com.research.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.research.common.utils.SecurityUtils;
import com.research.system.domain.SysTodo;
import com.research.system.domain.SysTodoHistory;
import com.research.system.domain.SysUser;
import com.research.system.mapper.SysTodoMapper;
import com.research.system.mapper.SysTodoHistoryMapper;
import com.research.system.mapper.SysUserMapper;
import com.research.system.service.ISysTodoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 待办事项Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Service
public class SysTodoServiceImpl extends ServiceImpl<SysTodoMapper, SysTodo> implements ISysTodoService {

    @Autowired
    private SysTodoMapper todoMapper;

    @Autowired
    private SysTodoHistoryMapper todoHistoryMapper;

    @Autowired
    private SysUserMapper userMapper;

    /**
     * 查询待办事项列表
     */
    @Override
    public IPage<SysTodo> selectTodoList(Page<SysTodo> page, SysTodo todo) {
        return todoMapper.selectTodoList(page, todo);
    }

    /**
     * 查询我的待办事项列表
     */
    @Override
    public IPage<SysTodo> selectMyTodoList(Page<SysTodo> page, SysTodo todo, Long userId) {
        return todoMapper.selectMyTodoList(page, todo, userId);
    }

    /**
     * 查询我创建的待办事项列表
     */
    @Override
    public IPage<SysTodo> selectMyCreatedTodoList(Page<SysTodo> page, SysTodo todo, Long userId) {
        return todoMapper.selectMyCreatedTodoList(page, todo, userId);
    }

    /**
     * 查询待办事项详情
     */
    @Override
    public SysTodo selectTodoDetail(Long todoId) {
        return todoMapper.selectTodoDetail(todoId);
    }

    /**
     * 新增待办事项
     */
    @Override
    @Transactional
    public boolean insertTodo(SysTodo todo) {
        todo.setCreateBy(SecurityUtils.getUsername());
        todo.setCreateTime(LocalDateTime.now());
        boolean result = save(todo);
        
        if (result) {
            // 记录创建历史
            recordTodoHistory(todo.getTodoId(), "1", "创建待办事项", 
                SecurityUtils.getUserId(), SecurityUtils.getUsername(), 
                null, "0", null, todo.getAssigneeId(), null);
        }
        
        return result;
    }

    /**
     * 修改待办事项
     */
    @Override
    @Transactional
    public boolean updateTodo(SysTodo todo) {
        todo.setUpdateBy(SecurityUtils.getUsername());
        todo.setUpdateTime(LocalDateTime.now());
        return updateById(todo);
    }

    /**
     * 删除待办事项
     */
    @Override
    @Transactional
    public boolean deleteTodoByIds(Long[] todoIds) {
        for (Long todoId : todoIds) {
            // 删除待办处理历史
            todoHistoryMapper.deleteByTodoId(todoId);
        }
        return removeByIds(Arrays.asList(todoIds));
    }

    /**
     * 分配待办事项
     */
    @Override
    @Transactional
    public boolean assignTodo(Long todoId, Long assigneeId, String assigneeName) {
        SysTodo oldTodo = getById(todoId);
        if (oldTodo == null) {
            return false;
        }

        SysTodo todo = new SysTodo();
        todo.setTodoId(todoId);
        todo.setAssigneeId(assigneeId);
        todo.setAssigneeName(assigneeName);
        todo.setUpdateBy(SecurityUtils.getUsername());
        todo.setUpdateTime(LocalDateTime.now());
        
        boolean result = updateById(todo);
        
        if (result) {
            // 记录分配历史
            recordTodoHistory(todoId, "2", "分配待办事项给 " + assigneeName, 
                SecurityUtils.getUserId(), SecurityUtils.getUsername(), 
                oldTodo.getStatus(), oldTodo.getStatus(), 
                oldTodo.getAssigneeId(), assigneeId, null);
        }
        
        return result;
    }

    /**
     * 处理待办事项
     */
    @Override
    @Transactional
    public boolean processTodo(Long todoId, String status, String result) {
        SysTodo oldTodo = getById(todoId);
        if (oldTodo == null) {
            return false;
        }

        SysTodo todo = new SysTodo();
        todo.setTodoId(todoId);
        todo.setStatus(status);
        todo.setResult(result);
        todo.setUpdateBy(SecurityUtils.getUsername());
        todo.setUpdateTime(LocalDateTime.now());
        
        boolean updateResult = updateById(todo);
        
        if (updateResult) {
            // 记录处理历史
            recordTodoHistory(todoId, "3", "处理待办事项", 
                SecurityUtils.getUserId(), SecurityUtils.getUsername(), 
                oldTodo.getStatus(), status, null, null, result);
        }
        
        return updateResult;
    }

    /**
     * 完成待办事项
     */
    @Override
    @Transactional
    public boolean completeTodo(Long todoId, String result) {
        SysTodo oldTodo = getById(todoId);
        if (oldTodo == null) {
            return false;
        }

        SysTodo todo = new SysTodo();
        todo.setTodoId(todoId);
        todo.setStatus("2"); // 已完成
        todo.setResult(result);
        todo.setCompleteTime(LocalDateTime.now());
        todo.setUpdateBy(SecurityUtils.getUsername());
        todo.setUpdateTime(LocalDateTime.now());
        
        boolean updateResult = updateById(todo);
        
        if (updateResult) {
            // 记录完成历史
            recordTodoHistory(todoId, "4", "完成待办事项", 
                SecurityUtils.getUserId(), SecurityUtils.getUsername(), 
                oldTodo.getStatus(), "2", null, null, result);
        }
        
        return updateResult;
    }

    /**
     * 取消待办事项
     */
    @Override
    @Transactional
    public boolean cancelTodo(Long todoId, String reason) {
        SysTodo oldTodo = getById(todoId);
        if (oldTodo == null) {
            return false;
        }

        SysTodo todo = new SysTodo();
        todo.setTodoId(todoId);
        todo.setStatus("3"); // 已取消
        todo.setResult(reason);
        todo.setUpdateBy(SecurityUtils.getUsername());
        todo.setUpdateTime(LocalDateTime.now());
        
        boolean result = updateById(todo);
        
        if (result) {
            // 记录取消历史
            recordTodoHistory(todoId, "5", "取消待办事项", 
                SecurityUtils.getUserId(), SecurityUtils.getUsername(), 
                oldTodo.getStatus(), "3", null, null, reason);
        }
        
        return result;
    }

    /**
     * 重新打开待办事项
     */
    @Override
    @Transactional
    public boolean reopenTodo(Long todoId, String reason) {
        SysTodo oldTodo = getById(todoId);
        if (oldTodo == null) {
            return false;
        }

        SysTodo todo = new SysTodo();
        todo.setTodoId(todoId);
        todo.setStatus("0"); // 待处理
        todo.setCompleteTime(null);
        todo.setUpdateBy(SecurityUtils.getUsername());
        todo.setUpdateTime(LocalDateTime.now());
        
        boolean result = updateById(todo);
        
        if (result) {
            // 记录重新打开历史
            recordTodoHistory(todoId, "6", "重新打开待办事项", 
                SecurityUtils.getUserId(), SecurityUtils.getUsername(), 
                oldTodo.getStatus(), "0", null, null, reason);
        }
        
        return result;
    }

    /**
     * 标记待办为已读
     */
    @Override
    @Transactional
    public boolean markAsRead(Long todoId, Long userId) {
        return todoMapper.markAsRead(todoId, userId) > 0;
    }

    /**
     * 批量更新待办状态
     */
    @Override
    @Transactional
    public boolean batchUpdateStatus(List<Long> todoIds, String status) {
        return todoMapper.batchUpdateStatus(todoIds, status, SecurityUtils.getUsername()) > 0;
    }

    /**
     * 查询用户待办事项统计
     */
    @Override
    public Map<String, Object> selectTodoStatistics(Long userId) {
        return todoMapper.selectTodoStatistics(userId);
    }

    /**
     * 查询最近待办事项（用于工作台展示）
     */
    @Override
    public List<SysTodo> selectRecentTodos(Long userId, Integer limit) {
        return todoMapper.selectRecentTodos(userId, limit != null ? limit : 5);
    }

    /**
     * 查询即将到期的待办事项
     */
    @Override
    public List<SysTodo> selectDueSoonTodos(Long userId, Integer days) {
        return todoMapper.selectDueSoonTodos(userId, days != null ? days : 3);
    }

    /**
     * 查询逾期的待办事项
     */
    @Override
    public List<SysTodo> selectOverdueTodos(Long userId) {
        return todoMapper.selectOverdueTodos(userId);
    }

    /**
     * 查询待办处理历史
     */
    @Override
    public List<SysTodoHistory> selectTodoHistory(Long todoId) {
        return todoHistoryMapper.selectHistoryByTodoId(todoId);
    }

    /**
     * 查询待办事项按优先级统计
     */
    @Override
    public List<Map<String, Object>> selectTodoByPriorityStats(Long userId) {
        return todoMapper.selectTodoByPriorityStats(userId);
    }

    /**
     * 查询待办事项按状态统计
     */
    @Override
    public List<Map<String, Object>> selectTodoByStatusStats(Long userId) {
        return todoMapper.selectTodoByStatusStats(userId);
    }

    /**
     * 发送待办提醒
     */
    @Override
    public boolean sendTodoReminder(Long todoId) {
        // TODO: 实现提醒功能，可以发送邮件或站内消息
        return true;
    }

    /**
     * 批量发送待办提醒
     */
    @Override
    public boolean batchSendTodoReminder(List<Long> todoIds) {
        // TODO: 实现批量提醒功能
        return true;
    }

    /**
     * 记录待办处理历史
     */
    private void recordTodoHistory(Long todoId, String actionType, String actionDesc, 
                                 Long operatorId, String operatorName, String oldStatus, 
                                 String newStatus, Long oldAssigneeId, Long newAssigneeId, 
                                 String processResult) {
        SysTodoHistory history = new SysTodoHistory();
        history.setTodoId(todoId);
        history.setActionType(actionType);
        history.setActionDesc(actionDesc);
        history.setOperatorId(operatorId);
        history.setOperatorName(operatorName);
        history.setOldStatus(oldStatus);
        history.setNewStatus(newStatus);
        history.setOldAssigneeId(oldAssigneeId);
        history.setNewAssigneeId(newAssigneeId);
        history.setProcessResult(processResult);
        history.setCreateTime(LocalDateTime.now());
        
        todoHistoryMapper.insertTodoHistory(history);
    }
}
