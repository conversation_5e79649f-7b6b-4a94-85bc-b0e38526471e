import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useSettingsStore = defineStore('settings', () => {
  // 主题设置
  const theme = ref('#409EFF')
  
  // 是否显示设置面板
  const showSettings = ref(false)
  
  // 是否显示标签页
  const tagsView = ref(true)
  
  // 是否固定头部
  const fixedHeader = ref(false)
  
  // 是否显示侧边栏Logo
  const sidebarLogo = ref(true)
  
  // 动态标题
  const dynamicTitle = ref(false)
  
  // 切换设置面板显示状态
  const changeSetting = (key: string, value: any) => {
    switch (key) {
      case 'theme':
        theme.value = value
        break
      case 'showSettings':
        showSettings.value = value
        break
      case 'tagsView':
        tagsView.value = value
        break
      case 'fixedHeader':
        fixedHeader.value = value
        break
      case 'sidebarLogo':
        sidebarLogo.value = value
        break
      case 'dynamicTitle':
        dynamicTitle.value = value
        break
    }
  }
  
  return {
    theme,
    showSettings,
    tagsView,
    fixedHeader,
    sidebarLogo,
    dynamicTitle,
    changeSetting
  }
})
