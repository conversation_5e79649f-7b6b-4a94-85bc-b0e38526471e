package com.research.common.utils;

import com.research.common.exception.ServiceException;

/**
 * 异常工具类
 * 
 * <AUTHOR>
 */
public class ExceptionUtil {

    /**
     * 抛出业务异常
     * 
     * @param message 异常消息
     */
    public static void throwServiceException(String message) {
        throw new ServiceException(message);
    }

    /**
     * 抛出业务异常
     * 
     * @param message 异常消息
     * @param code 错误码
     */
    public static void throwServiceException(String message, Integer code) {
        throw new ServiceException(message, code);
    }

    /**
     * 抛出业务异常（带详细信息）
     * 
     * @param message 异常消息
     * @param detailMessage 详细信息
     */
    public static void throwServiceException(String message, String detailMessage) {
        throw new ServiceException(message).setDetailMessage(detailMessage);
    }

    /**
     * 抛出业务异常（完整参数）
     * 
     * @param message 异常消息
     * @param code 错误码
     * @param detailMessage 详细信息
     */
    public static void throwServiceException(String message, Integer code, String detailMessage) {
        throw new ServiceException(message, code).setDetailMessage(detailMessage);
    }

    /**
     * 检查条件，如果为true则抛出异常
     * 
     * @param condition 条件
     * @param message 异常消息
     */
    public static void throwIf(boolean condition, String message) {
        if (condition) {
            throw new ServiceException(message);
        }
    }

    /**
     * 检查条件，如果为true则抛出异常
     * 
     * @param condition 条件
     * @param message 异常消息
     * @param code 错误码
     */
    public static void throwIf(boolean condition, String message, Integer code) {
        if (condition) {
            throw new ServiceException(message, code);
        }
    }

    /**
     * 检查对象是否为null，如果为null则抛出异常
     * 
     * @param object 检查的对象
     * @param message 异常消息
     */
    public static void throwIfNull(Object object, String message) {
        if (object == null) {
            throw new ServiceException(message);
        }
    }

    /**
     * 检查字符串是否为空，如果为空则抛出异常
     * 
     * @param str 检查的字符串
     * @param message 异常消息
     */
    public static void throwIfEmpty(String str, String message) {
        if (StringUtils.isEmpty(str)) {
            throw new ServiceException(message);
        }
    }

    /**
     * 检查字符串是否为空白，如果为空白则抛出异常
     * 
     * @param str 检查的字符串
     * @param message 异常消息
     */
    public static void throwIfBlank(String str, String message) {
        if (StringUtils.isBlank(str)) {
            throw new ServiceException(message);
        }
    }
}
