-- 流程定义表
CREATE TABLE IF NOT EXISTS `workflow_process_definition` (
  `id` varchar(64) NOT NULL COMMENT '流程定义ID',
  `name` varchar(100) NOT NULL COMMENT '流程名称',
  `process_key` varchar(100) NOT NULL COMMENT '流程标识',
  `version` int(11) DEFAULT '1' COMMENT '版本号',
  `category` varchar(50) DEFAULT NULL COMMENT '流程分类',
  `description` varchar(500) DEFAULT NULL COMMENT '流程描述',
  `deployment_id` varchar(64) DEFAULT NULL COMMENT '部署ID',
  `deployment_time` datetime DEFAULT NULL COMMENT '部署时间',
  `suspended` tinyint(1) DEFAULT '0' COMMENT '是否挂起',
  `resource_name` varchar(200) DEFAULT NULL COMMENT '资源名称',
  `diagram_resource_name` varchar(200) DEFAULT NULL COMMENT '流程图资源名称',
  `has_start_form_key` tinyint(1) DEFAULT '0' COMMENT '是否有启动表单',
  `has_graphical_notation` tinyint(1) DEFAULT '0' COMMENT '是否有图形符号',
  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_process_key` (`process_key`),
  KEY `idx_category` (`category`),
  KEY `idx_suspended` (`suspended`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程定义表';

-- 流程实例表
CREATE TABLE IF NOT EXISTS `workflow_process_instance` (
  `instance_id` varchar(64) NOT NULL COMMENT '实例ID',
  `process_definition_id` varchar(64) NOT NULL COMMENT '流程定义ID',
  `process_definition_key` varchar(100) NOT NULL COMMENT '流程定义Key',
  `process_name` varchar(100) NOT NULL COMMENT '流程名称',
  `business_title` varchar(200) DEFAULT NULL COMMENT '业务标题',
  `business_id` varchar(64) DEFAULT NULL COMMENT '业务ID',
  `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `start_user_id` varchar(64) NOT NULL COMMENT '发起人ID',
  `start_user_name` varchar(100) NOT NULL COMMENT '发起人姓名',
  `current_task` varchar(200) DEFAULT NULL COMMENT '当前节点',
  `current_assignee_id` varchar(64) DEFAULT NULL COMMENT '当前处理人ID',
  `current_assignee_name` varchar(100) DEFAULT NULL COMMENT '当前处理人姓名',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '实例状态',
  `priority` varchar(20) DEFAULT 'NORMAL' COMMENT '优先级',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` bigint(20) DEFAULT NULL COMMENT '耗时（毫秒）',
  `variables` longtext COMMENT '流程变量JSON',
  `form_data` longtext COMMENT '表单数据JSON',
  `attachment_path` varchar(500) DEFAULT NULL COMMENT '附件路径',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志',
  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`instance_id`),
  KEY `idx_process_definition_id` (`process_definition_id`),
  KEY `idx_process_definition_key` (`process_definition_key`),
  KEY `idx_start_user_id` (`start_user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程实例表';

-- 插入流程定义示例数据
INSERT INTO `workflow_process_definition` (`id`, `name`, `process_key`, `version`, `category`, `description`, `deployment_time`, `suspended`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('def_001', '请假申请流程', 'leave_process', 1, 'approval', '员工请假申请审批流程', NOW(), 0, 'admin', NOW(), 'admin', NOW(), '请假申请流程定义'),
('def_002', '报销审批流程', 'expense_process', 2, 'approval', '员工报销申请审批流程', NOW(), 1, 'admin', NOW(), 'admin', NOW(), '报销审批流程定义'),
('def_003', '采购申请流程', 'purchase_process', 1, 'business', '采购申请审批流程', NOW(), 0, 'admin', NOW(), 'admin', NOW(), '采购申请流程定义'),
('def_004', '合同审批流程', 'contract_process', 1, 'business', '合同审批流程', NOW(), 0, 'admin', NOW(), 'admin', NOW(), '合同审批流程定义'),
('def_005', '项目申请流程', 'project_process', 1, 'business', '项目申请流程', NOW(), 0, 'admin', NOW(), 'admin', NOW(), '项目申请流程定义');

-- 插入流程实例示例数据
INSERT INTO `workflow_process_instance` (`instance_id`, `process_definition_id`, `process_definition_key`, `process_name`, `business_title`, `business_id`, `business_type`, `start_user_id`, `start_user_name`, `current_task`, `current_assignee_id`, `current_assignee_name`, `status`, `priority`, `start_time`, `end_time`, `duration`, `variables`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('PI001', 'def_001', 'leave_process', '请假申请流程', '张三的年假申请', 'BIZ001', 'leave', '1', '张三', '部门经理审批', '2', '李经理', 'ACTIVE', 'NORMAL', DATE_SUB(NOW(), INTERVAL 2 DAY), NULL, NULL, '{"leaveType":"年假","leaveDays":3,"reason":"家庭事务"}', '0', 'admin', NOW(), 'admin', NOW(), '张三的年假申请'),
('PI002', 'def_002', 'expense_process', '报销审批流程', '王五的差旅费报销', 'BIZ002', 'expense', '3', '王五', '财务审核', '4', '财务部', 'ACTIVE', 'HIGH', DATE_SUB(NOW(), INTERVAL 1 DAY), NULL, NULL, '{"expenseType":"差旅费","amount":2500.00}', '0', 'admin', NOW(), 'admin', NOW(), '王五的差旅费报销'),
('PI003', 'def_001', 'leave_process', '请假申请流程', '赵六的病假申请', 'BIZ003', 'leave', '5', '赵六', NULL, NULL, NULL, 'COMPLETED', 'URGENT', DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY), 172800000, '{"leaveType":"病假","leaveDays":2}', '0', 'admin', NOW(), 'admin', NOW(), '赵六的病假申请'),
('PI004', 'def_003', 'purchase_process', '采购申请流程', '办公用品采购申请', 'BIZ004', 'purchase', '2', '李四', '采购部审批', '6', '采购部', 'ACTIVE', 'NORMAL', DATE_SUB(NOW(), INTERVAL 3 DAY), NULL, NULL, '{"itemType":"办公用品","amount":1200.00}', '0', 'admin', NOW(), 'admin', NOW(), '办公用品采购申请'),
('PI005', 'def_004', 'contract_process', '合同审批流程', '软件采购合同审批', 'BIZ005', 'contract', '1', '张三', '法务审核', '7', '法务部', 'SUSPENDED', 'HIGH', DATE_SUB(NOW(), INTERVAL 7 DAY), NULL, NULL, '{"contractType":"软件采购","amount":50000.00}', '0', 'admin', NOW(), 'admin', NOW(), '软件采购合同审批'),
('PI006', 'def_001', 'leave_process', '请假申请流程', '钱七的事假申请', 'BIZ006', 'leave', '8', '钱七', NULL, NULL, NULL, 'TERMINATED', 'LOW', DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 8 DAY), 172800000, '{"leaveType":"事假","leaveDays":1}', '0', 'admin', NOW(), 'admin', NOW(), '钱七的事假申请');

-- 创建索引以提高查询性能
CREATE INDEX idx_process_definition_key_version ON workflow_process_definition(process_key, version);
CREATE INDEX idx_process_definition_category_suspended ON workflow_process_definition(category, suspended);
CREATE INDEX idx_process_instance_status_priority ON workflow_process_instance(status, priority);
CREATE INDEX idx_process_instance_start_time_status ON workflow_process_instance(start_time, status);
CREATE INDEX idx_process_instance_business_type_status ON workflow_process_instance(business_type, status);
