package com.research.workflow.service.impl;

import com.research.common.utils.DateUtils;
import com.research.common.utils.SecurityUtils;
import com.research.common.utils.StringUtils;
import com.research.workflow.domain.WorkflowVersion;
import com.research.workflow.mapper.WorkflowVersionMapper;
import com.research.workflow.service.IWorkflowVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 工作流版本管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class WorkflowVersionServiceImpl implements IWorkflowVersionService {
    
    @Autowired
    private WorkflowVersionMapper workflowVersionMapper;

    /**
     * 查询流程版本列表
     * 
     * @param processDefinitionKey 流程定义Key
     * @return 版本列表
     */
    @Override
    public List<WorkflowVersion> selectVersionList(String processDefinitionKey) {
        WorkflowVersion workflowVersion = new WorkflowVersion();
        workflowVersion.setProcessDefinitionKey(processDefinitionKey);
        return workflowVersionMapper.selectWorkflowVersionList(workflowVersion);
    }

    /**
     * 根据版本ID查询版本信息
     * 
     * @param versionId 版本ID
     * @return 版本信息
     */
    @Override
    public WorkflowVersion selectVersionById(String versionId) {
        return workflowVersionMapper.selectWorkflowVersionByVersionId(versionId);
    }

    /**
     * 创建新版本
     * 
     * @param workflowVersion 版本信息
     * @return 结果
     */
    @Override
    public int createVersion(WorkflowVersion workflowVersion) {
        workflowVersion.setVersionId(UUID.randomUUID().toString());
        workflowVersion.setPublishStatus(0); // 草稿状态
        workflowVersion.setIsDefault(false);
        workflowVersion.setRunningInstanceCount(0);
        workflowVersion.setCompletedInstanceCount(0);
        workflowVersion.setCreateTime(DateUtils.getNowDate());
        workflowVersion.setCreateBy(SecurityUtils.getUsername());
        return workflowVersionMapper.insertWorkflowVersion(workflowVersion);
    }

    /**
     * 发布版本
     * 
     * @param versionId 版本ID
     * @param publishStrategy 发布策略
     * @param trafficRatio 流量比例
     * @param targetUsers 目标用户
     * @return 结果
     */
    @Override
    public int publishVersion(String versionId, Integer publishStrategy, Integer trafficRatio, String targetUsers) {
        WorkflowVersion workflowVersion = new WorkflowVersion();
        workflowVersion.setVersionId(versionId);
        workflowVersion.setPublishStatus(1); // 已发布
        workflowVersion.setPublishStrategy(publishStrategy);
        workflowVersion.setTrafficRatio(trafficRatio);
        workflowVersion.setTargetUsers(targetUsers);
        workflowVersion.setPublishTime(DateUtils.getNowDate());
        workflowVersion.setUpdateBy(SecurityUtils.getUsername());
        workflowVersion.setUpdateTime(DateUtils.getNowDate());
        return workflowVersionMapper.updateWorkflowVersion(workflowVersion);
    }

    /**
     * 停用版本
     * 
     * @param versionId 版本ID
     * @return 结果
     */
    @Override
    public int deprecateVersion(String versionId) {
        WorkflowVersion workflowVersion = new WorkflowVersion();
        workflowVersion.setVersionId(versionId);
        workflowVersion.setPublishStatus(2); // 已停用
        workflowVersion.setDeprecateTime(DateUtils.getNowDate());
        workflowVersion.setUpdateBy(SecurityUtils.getUsername());
        workflowVersion.setUpdateTime(DateUtils.getNowDate());
        return workflowVersionMapper.updateWorkflowVersion(workflowVersion);
    }

    /**
     * 版本回滚
     * 
     * @param processDefinitionKey 流程定义Key
     * @param targetVersionId 目标版本ID
     * @return 结果
     */
    @Override
    public int rollbackVersion(String processDefinitionKey, String targetVersionId) {
        // 1. 将当前默认版本设为非默认
        WorkflowVersion currentDefault = new WorkflowVersion();
        currentDefault.setProcessDefinitionKey(processDefinitionKey);
        currentDefault.setIsDefault(true);
        List<WorkflowVersion> currentVersions = workflowVersionMapper.selectWorkflowVersionList(currentDefault);
        
        for (WorkflowVersion version : currentVersions) {
            version.setIsDefault(false);
            version.setUpdateBy(SecurityUtils.getUsername());
            version.setUpdateTime(DateUtils.getNowDate());
            workflowVersionMapper.updateWorkflowVersion(version);
        }
        
        // 2. 将目标版本设为默认
        WorkflowVersion targetVersion = new WorkflowVersion();
        targetVersion.setVersionId(targetVersionId);
        targetVersion.setIsDefault(true);
        targetVersion.setPublishStatus(1); // 确保是已发布状态
        targetVersion.setUpdateBy(SecurityUtils.getUsername());
        targetVersion.setUpdateTime(DateUtils.getNowDate());
        
        return workflowVersionMapper.updateWorkflowVersion(targetVersion);
    }

    /**
     * 获取版本统计信息
     * 
     * @param processDefinitionKey 流程定义Key
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getVersionStatistics(String processDefinitionKey) {
        Map<String, Object> statistics = new HashMap<>();
        
        WorkflowVersion query = new WorkflowVersion();
        query.setProcessDefinitionKey(processDefinitionKey);
        List<WorkflowVersion> versions = workflowVersionMapper.selectWorkflowVersionList(query);
        
        int totalVersions = versions.size();
        int publishedVersions = 0;
        int draftVersions = 0;
        int deprecatedVersions = 0;
        int totalRunningInstances = 0;
        int totalCompletedInstances = 0;
        
        for (WorkflowVersion version : versions) {
            switch (version.getPublishStatus()) {
                case 0: draftVersions++; break;
                case 1: publishedVersions++; break;
                case 2: deprecatedVersions++; break;
            }
            totalRunningInstances += version.getRunningInstanceCount() != null ? version.getRunningInstanceCount() : 0;
            totalCompletedInstances += version.getCompletedInstanceCount() != null ? version.getCompletedInstanceCount() : 0;
        }
        
        statistics.put("totalVersions", totalVersions);
        statistics.put("publishedVersions", publishedVersions);
        statistics.put("draftVersions", draftVersions);
        statistics.put("deprecatedVersions", deprecatedVersions);
        statistics.put("totalRunningInstances", totalRunningInstances);
        statistics.put("totalCompletedInstances", totalCompletedInstances);
        
        return statistics;
    }

    /**
     * 获取版本路由统计
     * 
     * @param processDefinitionKey 流程定义Key
     * @return 路由统计
     */
    @Override
    public Map<String, Object> getRouteStatistics(String processDefinitionKey) {
        Map<String, Object> routeStats = new HashMap<>();
        
        WorkflowVersion query = new WorkflowVersion();
        query.setProcessDefinitionKey(processDefinitionKey);
        query.setPublishStatus(1); // 只统计已发布的版本
        List<WorkflowVersion> publishedVersions = workflowVersionMapper.selectWorkflowVersionList(query);
        
        Map<String, Integer> strategyCount = new HashMap<>();
        strategyCount.put("fullRelease", 0);
        strategyCount.put("grayRelease", 0);
        strategyCount.put("abTest", 0);
        strategyCount.put("departmentRoute", 0);
        
        for (WorkflowVersion version : publishedVersions) {
            Integer strategy = version.getPublishStrategy();
            if (strategy != null) {
                switch (strategy) {
                    case 0: strategyCount.put("fullRelease", strategyCount.get("fullRelease") + 1); break;
                    case 1: strategyCount.put("grayRelease", strategyCount.get("grayRelease") + 1); break;
                    case 2: strategyCount.put("abTest", strategyCount.get("abTest") + 1); break;
                    case 3: strategyCount.put("departmentRoute", strategyCount.get("departmentRoute") + 1); break;
                }
            }
        }
        
        routeStats.put("strategyDistribution", strategyCount);
        routeStats.put("totalActiveRoutes", publishedVersions.size());
        
        return routeStats;
    }

    /**
     * 创建全量发布路由
     * 
     * @param processDefinitionKey 流程定义Key
     * @param processDefinitionId 流程定义ID
     * @param versionId 版本ID
     * @return 结果
     */
    @Override
    public int createFullReleaseRoute(String processDefinitionKey, String processDefinitionId, String versionId) {
        // 1. 将其他版本设为非默认
        WorkflowVersion query = new WorkflowVersion();
        query.setProcessDefinitionKey(processDefinitionKey);
        query.setIsDefault(true);
        List<WorkflowVersion> currentDefaults = workflowVersionMapper.selectWorkflowVersionList(query);
        
        for (WorkflowVersion version : currentDefaults) {
            version.setIsDefault(false);
            version.setUpdateBy(SecurityUtils.getUsername());
            version.setUpdateTime(DateUtils.getNowDate());
            workflowVersionMapper.updateWorkflowVersion(version);
        }
        
        // 2. 设置新版本为默认
        WorkflowVersion newDefault = new WorkflowVersion();
        newDefault.setVersionId(versionId);
        newDefault.setProcessDefinitionId(processDefinitionId);
        newDefault.setIsDefault(true);
        newDefault.setPublishStatus(1);
        newDefault.setPublishStrategy(0); // 全量发布
        newDefault.setTrafficRatio(100);
        newDefault.setPublishTime(DateUtils.getNowDate());
        newDefault.setUpdateBy(SecurityUtils.getUsername());
        newDefault.setUpdateTime(DateUtils.getNowDate());
        
        return workflowVersionMapper.updateWorkflowVersion(newDefault);
    }

    /**
     * 创建灰度发布路由
     * 
     * @param processDefinitionKey 流程定义Key
     * @param processDefinitionId 流程定义ID
     * @param versionId 版本ID
     * @param trafficRatio 流量比例
     * @return 结果
     */
    @Override
    public int createGrayReleaseRoute(String processDefinitionKey, String processDefinitionId, String versionId, Integer trafficRatio) {
        WorkflowVersion grayVersion = new WorkflowVersion();
        grayVersion.setVersionId(versionId);
        grayVersion.setProcessDefinitionId(processDefinitionId);
        grayVersion.setPublishStatus(1);
        grayVersion.setPublishStrategy(1); // 灰度发布
        grayVersion.setTrafficRatio(trafficRatio);
        grayVersion.setPublishTime(DateUtils.getNowDate());
        grayVersion.setUpdateBy(SecurityUtils.getUsername());
        grayVersion.setUpdateTime(DateUtils.getNowDate());
        
        return workflowVersionMapper.updateWorkflowVersion(grayVersion);
    }

    /**
     * 创建A/B测试路由
     * 
     * @param processDefinitionKey 流程定义Key
     * @param processDefinitionId 流程定义ID
     * @param versionId 版本ID
     * @param targetUsers 目标用户列表
     * @return 结果
     */
    @Override
    public int createABTestRoute(String processDefinitionKey, String processDefinitionId, String versionId, List<String> targetUsers) {
        WorkflowVersion abTestVersion = new WorkflowVersion();
        abTestVersion.setVersionId(versionId);
        abTestVersion.setProcessDefinitionId(processDefinitionId);
        abTestVersion.setPublishStatus(1);
        abTestVersion.setPublishStrategy(2); // A/B测试
        abTestVersion.setTargetUsers(String.join(",", targetUsers));
        abTestVersion.setPublishTime(DateUtils.getNowDate());
        abTestVersion.setUpdateBy(SecurityUtils.getUsername());
        abTestVersion.setUpdateTime(DateUtils.getNowDate());
        
        return workflowVersionMapper.updateWorkflowVersion(abTestVersion);
    }

    /**
     * 创建基于部门的路由
     * 
     * @param processDefinitionKey 流程定义Key
     * @param processDefinitionId 流程定义ID
     * @param versionId 版本ID
     * @param departments 部门列表
     * @return 结果
     */
    @Override
    public int createDepartmentRoute(String processDefinitionKey, String processDefinitionId, String versionId, List<String> departments) {
        WorkflowVersion deptVersion = new WorkflowVersion();
        deptVersion.setVersionId(versionId);
        deptVersion.setProcessDefinitionId(processDefinitionId);
        deptVersion.setPublishStatus(1);
        deptVersion.setPublishStrategy(3); // 部门路由
        deptVersion.setTargetDepartments(String.join(",", departments));
        deptVersion.setPublishTime(DateUtils.getNowDate());
        deptVersion.setUpdateBy(SecurityUtils.getUsername());
        deptVersion.setUpdateTime(DateUtils.getNowDate());
        
        return workflowVersionMapper.updateWorkflowVersion(deptVersion);
    }

    /**
     * 测试版本路由
     * 
     * @param processDefinitionKey 流程定义Key
     * @param userId 用户ID
     * @param userGroups 用户组
     * @param department 部门
     * @param context 上下文
     * @return 路由结果
     */
    @Override
    public String testVersionRoute(String processDefinitionKey, String userId, String userGroups, String department, Map<String, Object> context) {
        // 获取所有已发布的版本
        WorkflowVersion query = new WorkflowVersion();
        query.setProcessDefinitionKey(processDefinitionKey);
        query.setPublishStatus(1);
        List<WorkflowVersion> publishedVersions = workflowVersionMapper.selectWorkflowVersionList(query);
        
        // 按优先级排序：部门路由 > A/B测试 > 灰度发布 > 全量发布
        publishedVersions.sort((v1, v2) -> {
            int priority1 = getRoutePriority(v1.getPublishStrategy());
            int priority2 = getRoutePriority(v2.getPublishStrategy());
            return Integer.compare(priority1, priority2);
        });
        
        for (WorkflowVersion version : publishedVersions) {
            if (matchesRouteCondition(version, userId, userGroups, department, context)) {
                return version.getVersionId();
            }
        }
        
        // 如果没有匹配的路由，返回默认版本
        WorkflowVersion defaultQuery = new WorkflowVersion();
        defaultQuery.setProcessDefinitionKey(processDefinitionKey);
        defaultQuery.setIsDefault(true);
        List<WorkflowVersion> defaultVersions = workflowVersionMapper.selectWorkflowVersionList(defaultQuery);
        
        return defaultVersions.isEmpty() ? null : defaultVersions.get(0).getVersionId();
    }

    /**
     * 切换版本
     * 
     * @param processDefinitionKey 流程定义Key
     * @param targetVersionId 目标版本ID
     * @return 结果
     */
    @Override
    public int switchVersion(String processDefinitionKey, String targetVersionId) {
        return rollbackVersion(processDefinitionKey, targetVersionId);
    }

    /**
     * 删除版本
     * 
     * @param versionId 版本ID
     * @return 结果
     */
    @Override
    public int deleteVersionById(String versionId) {
        return workflowVersionMapper.deleteWorkflowVersionByVersionId(versionId);
    }

    /**
     * 比较版本
     * 
     * @param sourceVersionId 源版本ID
     * @param targetVersionId 目标版本ID
     * @return 比较结果
     */
    @Override
    public Map<String, Object> compareVersions(String sourceVersionId, String targetVersionId) {
        WorkflowVersion sourceVersion = workflowVersionMapper.selectWorkflowVersionByVersionId(sourceVersionId);
        WorkflowVersion targetVersion = workflowVersionMapper.selectWorkflowVersionByVersionId(targetVersionId);
        
        Map<String, Object> comparison = new HashMap<>();
        comparison.put("sourceVersion", sourceVersion);
        comparison.put("targetVersion", targetVersion);
        
        // 比较基本信息
        Map<String, Object> differences = new HashMap<>();
        differences.put("versionTag", !Objects.equals(sourceVersion.getVersionTag(), targetVersion.getVersionTag()));
        differences.put("versionName", !Objects.equals(sourceVersion.getVersionName(), targetVersion.getVersionName()));
        differences.put("publishStrategy", !Objects.equals(sourceVersion.getPublishStrategy(), targetVersion.getPublishStrategy()));
        differences.put("trafficRatio", !Objects.equals(sourceVersion.getTrafficRatio(), targetVersion.getTrafficRatio()));
        
        comparison.put("differences", differences);
        comparison.put("compareTime", DateUtils.getNowDate());
        
        return comparison;
    }

    /**
     * 获取版本依赖关系
     *
     * @param versionId 版本ID
     * @return 依赖关系
     */
    @Override
    public Map<String, Object> getVersionDependencies(String versionId) {
        Map<String, Object> dependencies = new HashMap<>();

        WorkflowVersion version = workflowVersionMapper.selectWorkflowVersionByVersionId(versionId);
        if (version == null) {
            dependencies.put("error", "版本不存在");
            return dependencies;
        }

        // 获取父版本依赖
        String parentVersionId = version.getParentVersionId();
        if (StringUtils.isNotEmpty(parentVersionId)) {
            WorkflowVersion parentVersion = workflowVersionMapper.selectWorkflowVersionByVersionId(parentVersionId);
            if (parentVersion != null) {
                Map<String, Object> parentInfo = new HashMap<>();
                parentInfo.put("versionId", parentVersion.getVersionId());
                parentInfo.put("versionTag", parentVersion.getVersionTag());
                parentInfo.put("versionName", parentVersion.getVersionName());
                parentInfo.put("publishStatus", parentVersion.getPublishStatus());
                dependencies.put("parentVersion", parentInfo);
            }
        }

        // 获取子版本依赖（分支版本）
        List<WorkflowVersion> childVersions = workflowVersionMapper.selectBranchVersions(versionId);
        if (childVersions != null && !childVersions.isEmpty()) {
            List<Map<String, Object>> childList = new ArrayList<>();
            for (WorkflowVersion childVersion : childVersions) {
                Map<String, Object> childInfo = new HashMap<>();
                childInfo.put("versionId", childVersion.getVersionId());
                childInfo.put("versionTag", childVersion.getVersionTag());
                childInfo.put("versionName", childVersion.getVersionName());
                childInfo.put("publishStatus", childVersion.getPublishStatus());
                childInfo.put("branchType", childVersion.getBranchType());
                childInfo.put("mergeStatus", childVersion.getMergeStatus());
                childList.add(childInfo);
            }
            dependencies.put("childVersions", childList);
        }

        // 获取同一流程定义下的其他版本
        WorkflowVersion query = new WorkflowVersion();
        query.setProcessDefinitionKey(version.getProcessDefinitionKey());
        List<WorkflowVersion> siblingVersions = workflowVersionMapper.selectWorkflowVersionList(query);

        if (siblingVersions != null && !siblingVersions.isEmpty()) {
            List<Map<String, Object>> siblingList = new ArrayList<>();
            for (WorkflowVersion siblingVersion : siblingVersions) {
                // 排除自己
                if (!versionId.equals(siblingVersion.getVersionId())) {
                    Map<String, Object> siblingInfo = new HashMap<>();
                    siblingInfo.put("versionId", siblingVersion.getVersionId());
                    siblingInfo.put("versionTag", siblingVersion.getVersionTag());
                    siblingInfo.put("versionName", siblingVersion.getVersionName());
                    siblingInfo.put("publishStatus", siblingVersion.getPublishStatus());
                    siblingInfo.put("isDefault", siblingVersion.getIsDefault());
                    siblingInfo.put("version", siblingVersion.getVersion());
                    siblingList.add(siblingInfo);
                }
            }
            dependencies.put("siblingVersions", siblingList);
        }

        // 获取依赖统计信息
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalSiblings", siblingVersions != null ? siblingVersions.size() - 1 : 0);
        statistics.put("totalChildren", childVersions != null ? childVersions.size() : 0);
        statistics.put("hasParent", StringUtils.isNotEmpty(parentVersionId));
        statistics.put("processDefinitionKey", version.getProcessDefinitionKey());

        dependencies.put("statistics", statistics);
        dependencies.put("queryTime", DateUtils.getNowDate());

        return dependencies;
    }

    /**
     * 检查版本兼容性
     *
     * @param sourceVersionId 源版本ID
     * @param targetVersionId 目标版本ID
     * @return 兼容性检查结果
     */
    @Override
    public Map<String, Object> checkVersionCompatibility(String sourceVersionId, String targetVersionId) {
        Map<String, Object> result = new HashMap<>();

        WorkflowVersion sourceVersion = workflowVersionMapper.selectWorkflowVersionByVersionId(sourceVersionId);
        WorkflowVersion targetVersion = workflowVersionMapper.selectWorkflowVersionByVersionId(targetVersionId);

        if (sourceVersion == null || targetVersion == null) {
            result.put("compatible", false);
            result.put("reason", "版本不存在");
            return result;
        }

        // 检查是否为同一流程定义
        if (!Objects.equals(sourceVersion.getProcessDefinitionKey(), targetVersion.getProcessDefinitionKey())) {
            result.put("compatible", false);
            result.put("reason", "不同流程定义的版本不兼容");
            return result;
        }

        // 检查版本状态
        if (sourceVersion.getPublishStatus() == null || targetVersion.getPublishStatus() == null) {
            result.put("compatible", false);
            result.put("reason", "版本状态未知");
            return result;
        }

        // 检查版本号兼容性
        Integer sourceVersionNumber = sourceVersion.getVersion();
        Integer targetVersionNumber = targetVersion.getVersion();

        boolean compatible = true;
        String reason = "版本兼容";

        if (sourceVersionNumber != null && targetVersionNumber != null) {
            // 如果目标版本号小于源版本号，可能存在兼容性问题
            if (targetVersionNumber < sourceVersionNumber) {
                compatible = false;
                reason = "目标版本号低于源版本号，可能存在兼容性问题";
            }
        }

        // 检查配置兼容性
        String sourceConfig = sourceVersion.getVersionConfig();
        String targetConfig = targetVersion.getVersionConfig();

        if (StringUtils.isNotEmpty(sourceConfig) && StringUtils.isNotEmpty(targetConfig)) {
            // 这里可以添加更复杂的配置兼容性检查逻辑
            // 目前简化处理
            if (!sourceConfig.equals(targetConfig)) {
                result.put("configDifference", true);
            }
        }

        result.put("compatible", compatible);
        result.put("reason", reason);
        result.put("sourceVersion", sourceVersion.getVersionTag());
        result.put("targetVersion", targetVersion.getVersionTag());
        result.put("checkTime", DateUtils.getNowDate());

        return result;
    }

    /**
     * 获取路由优先级
     */
    private int getRoutePriority(Integer publishStrategy) {
        if (publishStrategy == null) return 999;
        switch (publishStrategy) {
            case 3: return 1; // 部门路由
            case 2: return 2; // A/B测试
            case 1: return 3; // 灰度发布
            case 0: return 4; // 全量发布
            default: return 999;
        }
    }

    /**
     * 检查是否匹配路由条件
     */
    private boolean matchesRouteCondition(WorkflowVersion version, String userId, String userGroups, String department, Map<String, Object> context) {
        Integer strategy = version.getPublishStrategy();
        if (strategy == null) return false;
        
        switch (strategy) {
            case 0: // 全量发布
                return version.getIsDefault() != null && version.getIsDefault();
                
            case 1: // 灰度发布
                Integer trafficRatio = version.getTrafficRatio();
                if (trafficRatio == null) return false;
                // 简单的哈希路由
                int hash = Math.abs(userId.hashCode() % 100);
                return hash < trafficRatio;
                
            case 2: // A/B测试
                String targetUsers = version.getTargetUsers();
                if (StringUtils.isEmpty(targetUsers)) return false;
                return Arrays.asList(targetUsers.split(",")).contains(userId);
                
            case 3: // 部门路由
                String targetDepartments = version.getTargetDepartments();
                if (StringUtils.isEmpty(targetDepartments) || StringUtils.isEmpty(department)) return false;
                return Arrays.asList(targetDepartments.split(",")).contains(department);
                
            default:
                return false;
        }
    }

    @Override
    public Map<String, Object> getVersionBranches(String processDefinitionKey) {
        Map<String, Object> result = new HashMap<>();

        // 获取指定流程定义的所有版本
        WorkflowVersion query = new WorkflowVersion();
        query.setProcessDefinitionKey(processDefinitionKey);
        List<WorkflowVersion> allVersions = workflowVersionMapper.selectWorkflowVersionList(query);

        // 按分支类型分组
        Map<String, List<Map<String, Object>>> branchesByType = new HashMap<>();
        branchesByType.put("main", new ArrayList<>());
        branchesByType.put("feature", new ArrayList<>());
        branchesByType.put("hotfix", new ArrayList<>());
        branchesByType.put("release", new ArrayList<>());

        for (WorkflowVersion version : allVersions) {
            Map<String, Object> versionInfo = new HashMap<>();
            versionInfo.put("versionId", version.getVersionId());
            versionInfo.put("versionTag", version.getVersionTag());
            versionInfo.put("versionName", version.getVersionName());
            versionInfo.put("publishStatus", version.getPublishStatus());
            versionInfo.put("createTime", version.getCreateTime());
            versionInfo.put("createBy", version.getCreateBy());
            versionInfo.put("parentVersionId", version.getParentVersionId());
            versionInfo.put("mergeStatus", version.getMergeStatus());

            // 根据分支类型分类
            Integer branchType = version.getBranchType();
            String branchTypeName = getBranchTypeName(branchType);
            branchesByType.get(branchTypeName).add(versionInfo);
        }

        result.put("branches", branchesByType);
        result.put("totalCount", allVersions.size());
        result.put("processDefinitionKey", processDefinitionKey);
        result.put("queryTime", DateUtils.getNowDate());

        return result;
    }

    @Override
    public int createVersionBranch(String sourceVersionId, String branchName, Integer branchType) {
        // 获取源版本信息
        WorkflowVersion sourceVersion = workflowVersionMapper.selectWorkflowVersionByVersionId(sourceVersionId);
        if (sourceVersion == null) {
            throw new RuntimeException("源版本不存在");
        }

        // 创建新的分支版本
        WorkflowVersion branchVersion = new WorkflowVersion();
        branchVersion.setVersionId(UUID.randomUUID().toString());
        branchVersion.setProcessDefinitionKey(sourceVersion.getProcessDefinitionKey());
        branchVersion.setProcessDefinitionId(sourceVersion.getProcessDefinitionId());
        branchVersion.setVersionTag(generateBranchTag(branchName, branchType));
        branchVersion.setVersionName(branchName);
        branchVersion.setVersionDescription("基于版本 " + sourceVersion.getVersionTag() + " 创建的分支");
        branchVersion.setPublishStatus(0); // 草稿状态
        branchVersion.setParentVersionId(sourceVersionId);
        branchVersion.setBranchType(branchType);
        branchVersion.setMergeStatus(0); // 未合并
        branchVersion.setIsDefault(false);
        branchVersion.setRunningInstanceCount(0);
        branchVersion.setCompletedInstanceCount(0);
        branchVersion.setCreateTime(DateUtils.getNowDate());
        branchVersion.setCreateBy(SecurityUtils.getUsername());

        // 复制源版本的配置
        branchVersion.setVersionConfig(sourceVersion.getVersionConfig());

        return workflowVersionMapper.insertWorkflowVersion(branchVersion);
    }

    @Override
    public int mergeVersionBranch(String sourceVersionId, String targetVersionId) {
        // 获取源版本和目标版本
        WorkflowVersion sourceVersion = workflowVersionMapper.selectWorkflowVersionByVersionId(sourceVersionId);
        WorkflowVersion targetVersion = workflowVersionMapper.selectWorkflowVersionByVersionId(targetVersionId);

        if (sourceVersion == null || targetVersion == null) {
            throw new RuntimeException("版本不存在");
        }

        // 检查是否可以合并
        if (!Objects.equals(sourceVersion.getProcessDefinitionKey(), targetVersion.getProcessDefinitionKey())) {
            throw new RuntimeException("不同流程定义的版本无法合并");
        }

        if (sourceVersion.getMergeStatus() != null && sourceVersion.getMergeStatus() == 1) {
            throw new RuntimeException("源版本已经被合并");
        }

        // 执行合并操作
        try {
            // 1. 更新目标版本的配置（合并源版本的配置）
            String mergedConfig = mergeVersionConfigs(targetVersion.getVersionConfig(), sourceVersion.getVersionConfig());
            targetVersion.setVersionConfig(mergedConfig);
            targetVersion.setUpdateBy(SecurityUtils.getUsername());
            targetVersion.setUpdateTime(DateUtils.getNowDate());
            workflowVersionMapper.updateWorkflowVersion(targetVersion);

            // 2. 标记源版本为已合并
            sourceVersion.setMergeStatus(1); // 已合并
            sourceVersion.setUpdateBy(SecurityUtils.getUsername());
            sourceVersion.setUpdateTime(DateUtils.getNowDate());
            workflowVersionMapper.updateWorkflowVersion(sourceVersion);

            return 1;
        } catch (Exception e) {
            // 如果合并失败，标记为冲突状态
            sourceVersion.setMergeStatus(2); // 冲突
            workflowVersionMapper.updateWorkflowVersion(sourceVersion);
            throw new RuntimeException("版本合并失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getVersionHistory(String versionId) {
        List<Map<String, Object>> history = new ArrayList<>();

        WorkflowVersion version = workflowVersionMapper.selectWorkflowVersionByVersionId(versionId);
        if (version == null) {
            return history;
        }

        // 构建版本历史链
        String currentVersionId = versionId;
        while (currentVersionId != null) {
            WorkflowVersion currentVersion = workflowVersionMapper.selectWorkflowVersionByVersionId(currentVersionId);
            if (currentVersion == null) {
                break;
            }

            Map<String, Object> historyItem = new HashMap<>();
            historyItem.put("versionId", currentVersion.getVersionId());
            historyItem.put("versionTag", currentVersion.getVersionTag());
            historyItem.put("versionName", currentVersion.getVersionName());
            historyItem.put("versionDescription", currentVersion.getVersionDescription());
            historyItem.put("publishStatus", currentVersion.getPublishStatus());
            historyItem.put("branchType", currentVersion.getBranchType());
            historyItem.put("createTime", currentVersion.getCreateTime());
            historyItem.put("createBy", currentVersion.getCreateBy());
            historyItem.put("publishTime", currentVersion.getPublishTime());
            historyItem.put("parentVersionId", currentVersion.getParentVersionId());

            history.add(historyItem);

            // 移动到父版本
            currentVersionId = currentVersion.getParentVersionId();
        }

        return history;
    }

    @Override
    public int manageVersionTags(String versionId, List<String> tags) {
        WorkflowVersion version = workflowVersionMapper.selectWorkflowVersionByVersionId(versionId);
        if (version == null) {
            throw new RuntimeException("版本不存在");
        }

        // 将标签列表转换为JSON字符串存储
        String tagsJson = "";
        if (tags != null && !tags.isEmpty()) {
            try {
                // 简单的标签处理，实际项目中可以使用JSON库
                tagsJson = String.join(",", tags);
            } catch (Exception e) {
                throw new RuntimeException("标签格式错误: " + e.getMessage());
            }
        }

        // 更新版本的标签信息（这里假设在versionConfig中存储标签）
        String currentConfig = version.getVersionConfig();
        String updatedConfig = updateVersionTags(currentConfig, tagsJson);

        version.setVersionConfig(updatedConfig);
        version.setUpdateBy(SecurityUtils.getUsername());
        version.setUpdateTime(DateUtils.getNowDate());

        return workflowVersionMapper.updateWorkflowVersion(version);
    }

    /**
     * 获取分支类型名称
     */
    private String getBranchTypeName(Integer branchType) {
        if (branchType == null) {
            return "main";
        }
        switch (branchType) {
            case 0: return "main";
            case 1: return "feature";
            case 2: return "hotfix";
            case 3: return "release";
            default: return "main";
        }
    }

    /**
     * 生成分支标签
     */
    private String generateBranchTag(String branchName, Integer branchType) {
        String prefix = getBranchTypeName(branchType);
        String timestamp = DateUtils.dateTimeNow("yyyyMMddHHmmss");
        return prefix + "/" + branchName + "-" + timestamp;
    }

    /**
     * 合并版本配置
     */
    private String mergeVersionConfigs(String targetConfig, String sourceConfig) {
        // 简化的配置合并逻辑
        if (StringUtils.isEmpty(targetConfig)) {
            return sourceConfig;
        }
        if (StringUtils.isEmpty(sourceConfig)) {
            return targetConfig;
        }

        // 实际项目中这里应该实现更复杂的JSON配置合并逻辑
        // 目前简单地将两个配置合并
        try {
            // 这里可以使用JSON库进行更智能的合并
            return targetConfig + "\n<!-- Merged from source -->\n" + sourceConfig;
        } catch (Exception e) {
            // 如果合并失败，返回目标配置
            return targetConfig;
        }
    }

    /**
     * 更新版本标签
     */
    private String updateVersionTags(String currentConfig, String tagsJson) {
        // 简化的标签更新逻辑
        if (StringUtils.isEmpty(currentConfig)) {
            return "{\"tags\": \"" + tagsJson + "\"}";
        }

        // 实际项目中这里应该使用JSON库来更新配置
        // 目前简单地在配置中添加或更新标签信息
        if (currentConfig.contains("\"tags\"")) {
            // 替换现有标签
            return currentConfig.replaceAll("\"tags\"\\s*:\\s*\"[^\"]*\"", "\"tags\": \"" + tagsJson + "\"");
        } else {
            // 添加新标签
            if (currentConfig.trim().endsWith("}")) {
                return currentConfig.substring(0, currentConfig.lastIndexOf("}")) +
                       ", \"tags\": \"" + tagsJson + "\"}";
            } else {
                return currentConfig + "\n{\"tags\": \"" + tagsJson + "\"}";
            }
        }
    }
}
