package com.research.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.research.project.domain.entity.HorizontalProject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 横向项目Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@Mapper
public interface HorizontalProjectMapper extends BaseMapper<HorizontalProject> {

    /**
     * 查询横向项目列表
     * 
     * @param horizontalProject 横向项目
     * @return 横向项目集合
     */
    List<HorizontalProject> selectHorizontalProjectList(HorizontalProject horizontalProject);

    /**
     * 根据ID查询横向项目
     * 
     * @param id 横向项目ID
     * @return 横向项目
     */
    HorizontalProject selectHorizontalProjectById(Long id);

    /**
     * 新增横向项目
     * 
     * @param horizontalProject 横向项目
     * @return 结果
     */
    int insertHorizontalProject(HorizontalProject horizontalProject);

    /**
     * 修改横向项目
     * 
     * @param horizontalProject 横向项目
     * @return 结果
     */
    int updateHorizontalProject(HorizontalProject horizontalProject);

    /**
     * 删除横向项目
     * 
     * @param id 横向项目ID
     * @return 结果
     */
    int deleteHorizontalProjectById(Long id);

    /**
     * 批量删除横向项目
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteHorizontalProjectByIds(Long[] ids);

    /**
     * 根据负责人ID查询项目列表
     * 
     * @param principalId 负责人ID
     * @return 项目列表
     */
    List<HorizontalProject> selectProjectsByPrincipalId(@Param("principalId") Long principalId);

    /**
     * 根据部门ID查询项目列表
     * 
     * @param deptId 部门ID
     * @return 项目列表
     */
    List<HorizontalProject> selectProjectsByDeptId(@Param("deptId") Long deptId);

    /**
     * 根据状态查询项目列表
     * 
     * @param status 项目状态
     * @return 项目列表
     */
    List<HorizontalProject> selectProjectsByStatus(@Param("status") Integer status);

    /**
     * 查询即将到期的项目
     * 
     * @param days 天数
     * @return 项目列表
     */
    List<HorizontalProject> selectExpiringProjects(@Param("days") Integer days);

    /**
     * 查询已逾期的项目
     * 
     * @return 项目列表
     */
    List<HorizontalProject> selectOverdueProjects();

    /**
     * 查询经费到账率低的项目
     * 
     * @param threshold 阈值
     * @return 项目列表
     */
    List<HorizontalProject> selectLowFundReceiveRateProjects(@Param("threshold") Integer threshold);

    /**
     * 根据合作单位ID查询项目列表
     * 
     * @param partnerId 合作单位ID
     * @return 项目列表
     */
    List<HorizontalProject> selectProjectsByPartnerId(@Param("partnerId") Long partnerId);

    /**
     * 根据合同ID查询项目列表
     * 
     * @param contractId 合同ID
     * @return 项目列表
     */
    List<HorizontalProject> selectProjectsByContractId(@Param("contractId") Long contractId);

    /**
     * 搜索项目
     * 
     * @param keyword 关键词
     * @return 项目列表
     */
    List<HorizontalProject> searchProjects(@Param("keyword") String keyword);

    /**
     * 查询项目统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> selectProjectStatistics();

    /**
     * 查询部门项目统计
     * 
     * @return 部门统计列表
     */
    List<Map<String, Object>> selectDeptStatistics();

    /**
     * 查询项目类型统计
     * 
     * @return 类型统计列表
     */
    List<Map<String, Object>> selectTypeStatistics();

    /**
     * 查询项目状态统计
     * 
     * @return 状态统计列表
     */
    List<Map<String, Object>> selectStatusStatistics();

    /**
     * 查询年度项目统计
     * 
     * @param year 年份
     * @return 年度统计列表
     */
    List<Map<String, Object>> selectYearlyStatistics(@Param("year") Integer year);

    /**
     * 查询月度项目统计
     * 
     * @param year 年份
     * @param month 月份
     * @return 月度统计列表
     */
    List<Map<String, Object>> selectMonthlyStatistics(@Param("year") Integer year, @Param("month") Integer month);

    /**
     * 查询合作单位项目统计
     * 
     * @return 合作单位统计列表
     */
    List<Map<String, Object>> selectPartnerStatistics();

    /**
     * 查询项目经费统计
     * 
     * @return 经费统计信息
     */
    Map<String, Object> selectFundStatistics();

    /**
     * 查询项目进度统计
     * 
     * @return 进度统计列表
     */
    List<Map<String, Object>> selectProgressStatistics();

    /**
     * 根据项目编号查询项目
     * 
     * @param projectNo 项目编号
     * @return 项目信息
     */
    HorizontalProject selectProjectByNo(@Param("projectNo") String projectNo);

    /**
     * 检查项目编号是否存在
     * 
     * @param projectNo 项目编号
     * @param id 项目ID（排除自身）
     * @return 数量
     */
    int checkProjectNoExists(@Param("projectNo") String projectNo, @Param("id") Long id);

    /**
     * 更新项目经费信息
     * 
     * @param id 项目ID
     * @param receivedFund 已到账经费
     * @return 结果
     */
    int updateProjectFund(@Param("id") Long id, @Param("receivedFund") java.math.BigDecimal receivedFund);

    /**
     * 更新项目状态
     * 
     * @param id 项目ID
     * @param status 状态
     * @return 结果
     */
    int updateProjectStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 批量更新项目状态
     * 
     * @param ids 项目ID数组
     * @param status 状态
     * @return 结果
     */
    int batchUpdateProjectStatus(@Param("ids") Long[] ids, @Param("status") Integer status);
}
