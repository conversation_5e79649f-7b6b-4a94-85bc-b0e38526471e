import request from '@/utils/request'

// 查询流程定义列表
export function listProcessDefinition(query: any) {
  return request({
    url: '/workflow/processDefinition/list',
    method: 'get',
    params: query
  })
}

// 查询流程定义详细
export function getProcessDefinition(processDefinitionId: string) {
  return request({
    url: '/workflow/processDefinition/' + processDefinitionId,
    method: 'get'
  })
}

// 部署流程定义
export function deployProcessDefinition(data: FormData) {
  return request({
    url: '/workflow/processDefinition/deploy',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除流程定义
export function delProcessDefinition(deploymentId: string, cascade: boolean = false) {
  return request({
    url: '/workflow/processDefinition/' + deploymentId + '?cascade=' + cascade,
    method: 'delete'
  })
}

// 激活或挂起流程定义
export function updateProcessDefinitionState(processDefinitionId: string, suspended: boolean) {
  return request({
    url: '/workflow/processDefinition/updateState',
    method: 'put',
    params: {
      processDefinitionId,
      suspended
    }
  })
}

// 获取流程定义XML
export function getProcessDefinitionXml(processDefinitionId: string) {
  return request({
    url: '/workflow/processDefinition/xml/' + processDefinitionId,
    method: 'get'
  })
}

// 获取流程图
export function getProcessDiagram(processDefinitionId: string) {
  return '/dev-api/workflow/processDefinition/diagram/' + processDefinitionId
}
