import request from '@/utils/request'

// 查询待办事项列表
export function listTodo(query: any) {
  return request({
    url: '/system/todo/list',
    method: 'get',
    params: query
  })
}

// 查询我的待办事项列表
export function listMyTodo(query: any) {
  return request({
    url: '/system/todo/myList',
    method: 'get',
    params: query
  })
}

// 查询我创建的待办事项列表
export function listMyCreatedTodo(query: any) {
  return request({
    url: '/system/todo/myCreatedList',
    method: 'get',
    params: query
  })
}

// 查询待办事项详细
export function getTodo(todoId: number) {
  return request({
    url: '/system/todo/' + todoId,
    method: 'get'
  })
}

// 新增待办事项
export function addTodo(data: any) {
  return request({
    url: '/system/todo',
    method: 'post',
    data: data
  })
}

// 修改待办事项
export function updateTodo(data: any) {
  return request({
    url: '/system/todo',
    method: 'put',
    data: data
  })
}

// 删除待办事项
export function delTodo(todoIds: number[]) {
  return request({
    url: '/system/todo/' + todoIds,
    method: 'delete'
  })
}

// 分配待办事项
export function assignTodo(todoId: number, assigneeId: number, assigneeName: string) {
  return request({
    url: '/system/todo/assign/' + todoId,
    method: 'post',
    params: { assigneeId, assigneeName }
  })
}

// 处理待办事项
export function processTodo(todoId: number, status: string, result?: string) {
  return request({
    url: '/system/todo/process/' + todoId,
    method: 'post',
    params: { status, result }
  })
}

// 完成待办事项
export function completeTodo(todoId: number, result?: string) {
  return request({
    url: '/system/todo/complete/' + todoId,
    method: 'post',
    params: { result }
  })
}

// 取消待办事项
export function cancelTodo(todoId: number, reason?: string) {
  return request({
    url: '/system/todo/cancel/' + todoId,
    method: 'post',
    params: { reason }
  })
}

// 重新打开待办事项
export function reopenTodo(todoId: number, reason?: string) {
  return request({
    url: '/system/todo/reopen/' + todoId,
    method: 'post',
    params: { reason }
  })
}

// 标记待办为已读
export function markTodoAsRead(todoId: number) {
  return request({
    url: '/system/todo/markRead/' + todoId,
    method: 'post'
  })
}

// 批量更新待办状态
export function batchUpdateTodoStatus(todoIds: number[], status: string) {
  return request({
    url: '/system/todo/batchUpdateStatus',
    method: 'post',
    data: todoIds,
    params: { status }
  })
}

// 查询用户待办事项统计
export function getTodoStatistics() {
  return request({
    url: '/system/todo/statistics',
    method: 'get'
  })
}

// 查询最近待办事项
export function getRecentTodos(limit: number = 5) {
  return request({
    url: '/system/todo/recent',
    method: 'get',
    params: { limit }
  })
}

// 查询即将到期的待办事项
export function getDueSoonTodos(days: number = 3) {
  return request({
    url: '/system/todo/dueSoon',
    method: 'get',
    params: { days }
  })
}

// 查询逾期的待办事项
export function getOverdueTodos() {
  return request({
    url: '/system/todo/overdue',
    method: 'get'
  })
}

// 查询待办处理历史
export function getTodoHistory(todoId: number) {
  return request({
    url: '/system/todo/history/' + todoId,
    method: 'get'
  })
}

// 查询待办事项按优先级统计
export function getTodoPriorityStats() {
  return request({
    url: '/system/todo/priorityStats',
    method: 'get'
  })
}

// 查询待办事项按状态统计
export function getTodoStatusStats() {
  return request({
    url: '/system/todo/statusStats',
    method: 'get'
  })
}

// 发送待办提醒
export function sendTodoReminder(todoId: number) {
  return request({
    url: '/system/todo/remind/' + todoId,
    method: 'post'
  })
}

// 批量发送待办提醒
export function batchSendTodoReminder(todoIds: number[]) {
  return request({
    url: '/system/todo/batchRemind',
    method: 'post',
    data: todoIds
  })
}
