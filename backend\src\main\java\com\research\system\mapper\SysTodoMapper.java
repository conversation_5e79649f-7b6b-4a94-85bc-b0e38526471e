package com.research.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.research.system.domain.SysTodo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 待办事项Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Mapper
public interface SysTodoMapper extends BaseMapper<SysTodo> {

    /**
     * 查询待办事项列表
     * 
     * @param page 分页参数
     * @param todo 查询条件
     * @return 待办事项列表
     */
    IPage<SysTodo> selectTodoList(Page<SysTodo> page, @Param("todo") SysTodo todo);

    /**
     * 查询我的待办事项列表
     * 
     * @param page 分页参数
     * @param todo 查询条件
     * @param userId 用户ID
     * @return 我的待办事项列表
     */
    IPage<SysTodo> selectMyTodoList(Page<SysTodo> page, @Param("todo") SysTodo todo, @Param("userId") Long userId);

    /**
     * 查询我创建的待办事项列表
     * 
     * @param page 分页参数
     * @param todo 查询条件
     * @param userId 用户ID
     * @return 我创建的待办事项列表
     */
    IPage<SysTodo> selectMyCreatedTodoList(Page<SysTodo> page, @Param("todo") SysTodo todo, @Param("userId") Long userId);

    /**
     * 查询待办事项详情
     * 
     * @param todoId 待办ID
     * @return 待办事项详情
     */
    SysTodo selectTodoDetail(@Param("todoId") Long todoId);

    /**
     * 查询用户待办事项统计
     * 
     * @param userId 用户ID
     * @return 待办事项统计
     */
    Map<String, Object> selectTodoStatistics(@Param("userId") Long userId);

    /**
     * 查询最近待办事项（用于工作台展示）
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近待办事项列表
     */
    List<SysTodo> selectRecentTodos(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询即将到期的待办事项
     * 
     * @param userId 用户ID
     * @param days 天数
     * @return 即将到期的待办事项列表
     */
    List<SysTodo> selectDueSoonTodos(@Param("userId") Long userId, @Param("days") Integer days);

    /**
     * 查询逾期的待办事项
     * 
     * @param userId 用户ID
     * @return 逾期的待办事项列表
     */
    List<SysTodo> selectOverdueTodos(@Param("userId") Long userId);

    /**
     * 批量更新待办状态
     * 
     * @param todoIds 待办ID列表
     * @param status 新状态
     * @param updateBy 更新人
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("todoIds") List<Long> todoIds, @Param("status") String status, @Param("updateBy") String updateBy);

    /**
     * 标记待办为已读
     * 
     * @param todoId 待办ID
     * @param userId 用户ID
     * @return 影响行数
     */
    int markAsRead(@Param("todoId") Long todoId, @Param("userId") Long userId);

    /**
     * 查询待办事项按优先级统计
     * 
     * @param userId 用户ID
     * @return 优先级统计
     */
    List<Map<String, Object>> selectTodoByPriorityStats(@Param("userId") Long userId);

    /**
     * 查询待办事项按状态统计
     * 
     * @param userId 用户ID
     * @return 状态统计
     */
    List<Map<String, Object>> selectTodoByStatusStats(@Param("userId") Long userId);
}
