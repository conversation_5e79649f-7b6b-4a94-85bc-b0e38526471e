# CORS跨域问题解决方案

## 🎯 问题描述
前端访问后端API时出现跨域错误：
```
Access to XMLHttpRequest at 'http://139.196.179.70:8989/captchaImage' from origin 'http://139.196.179.70:3000' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## 🔧 解决方案

### 1. 后端CORS配置
已创建 `CorsConfig.java` 配置类，提供完整的跨域支持：

#### 配置特性：
- ✅ 允许所有域名跨域访问 (`allowedOriginPatterns("*")`)
- ✅ 允许所有HTTP方法 (`allowedMethods("*")`)
- ✅ 允许所有请求头 (`allowedHeaders("*")`)
- ✅ 允许发送Cookie (`allowCredentials(true)`)
- ✅ 预检请求缓存3600秒
- ✅ 暴露必要的响应头

#### 配置文件位置：
```
backend/src/main/java/com/research/framework/config/CorsConfig.java
```

### 2. Spring Security集成
修改了 `SecurityConfig.java`，确保CORS配置与安全配置协同工作：

#### 修改内容：
- ✅ 启用CORS支持 (`.cors()`)
- ✅ 添加CORS过滤器到过滤器链
- ✅ 确保CORS过滤器在JWT过滤器之前执行

## 🚀 部署更新

### 方法一：使用更新脚本（推荐）
```bash
# 设置执行权限
chmod +x update-backend.sh

# 执行更新
sudo ./update-backend.sh
```

### 方法二：手动更新
```bash
# 1. 停止服务
sudo /opt/research-management/stop.sh

# 2. 备份当前版本
sudo cp /opt/research-management/research-management-1.0.0.jar /opt/research-management/backup/

# 3. 复制新版本
sudo cp backend/target/research-management-1.0.0.jar /opt/research-management/

# 4. 启动服务
sudo /opt/research-management/start.sh

# 5. 检查日志
tail -f /opt/research-management/app.log
```

## 🔍 验证修复

### 1. 检查服务状态
```bash
# 检查进程
ps aux | grep research-management

# 检查端口
netstat -tlnp | grep :8989

# 检查日志
tail -f /opt/research-management/app.log
```

### 2. 测试CORS
```bash
# 测试预检请求
curl -X OPTIONS \
  -H "Origin: http://139.196.179.70:3000" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: Content-Type" \
  -v http://139.196.179.70:8989/captchaImage

# 测试实际请求
curl -X GET \
  -H "Origin: http://139.196.179.70:3000" \
  -v http://139.196.179.70:8989/captchaImage
```

### 3. 浏览器测试
1. 打开 http://139.196.179.70:3000
2. 打开浏览器开发者工具
3. 尝试登录，检查网络请求
4. 确认不再出现CORS错误

## 📋 CORS响应头说明

修复后，API响应应包含以下CORS头：
```
Access-Control-Allow-Origin: http://139.196.179.70:3000
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
Access-Control-Allow-Headers: *
Access-Control-Allow-Credentials: true
Access-Control-Max-Age: 3600
```

## 🛡️ 安全考虑

### 生产环境建议：
1. **限制允许的域名**：
   ```java
   // 替换 allowedOriginPatterns("*") 为具体域名
   config.addAllowedOrigin("http://139.196.179.70:3000");
   config.addAllowedOrigin("https://yourdomain.com");
   ```

2. **限制允许的方法**：
   ```java
   // 只允许必要的HTTP方法
   config.addAllowedMethod("GET");
   config.addAllowedMethod("POST");
   config.addAllowedMethod("PUT");
   config.addAllowedMethod("DELETE");
   ```

3. **限制允许的请求头**：
   ```java
   // 只允许必要的请求头
   config.addAllowedHeader("Content-Type");
   config.addAllowedHeader("Authorization");
   ```

## 🆘 故障排除

### 问题1: 仍然出现CORS错误
**解决方案**：
1. 检查服务是否重启成功
2. 清除浏览器缓存
3. 检查nginx配置是否有冲突

### 问题2: 预检请求失败
**解决方案**：
1. 检查OPTIONS方法是否被允许
2. 检查Spring Security配置
3. 查看后端日志

### 问题3: Cookie无法发送
**解决方案**：
1. 确认 `allowCredentials(true)` 已设置
2. 前端请求需要设置 `withCredentials: true`
3. 不能同时使用 `allowedOrigins("*")` 和 `allowCredentials(true)`

## 📞 技术支持

如果问题仍然存在，请提供：
1. 浏览器控制台的完整错误信息
2. 后端日志内容
3. 网络请求的详细信息（Headers、Response等）
4. 服务器环境信息

## 📝 更新日志

- **2024-07-31**: 创建CORS配置类
- **2024-07-31**: 集成Spring Security CORS支持
- **2024-07-31**: 创建后端更新脚本
