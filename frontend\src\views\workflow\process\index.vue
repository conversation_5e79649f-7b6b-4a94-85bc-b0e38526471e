<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>流程管理与测试</span>
          <el-button @click="showDebugInfo">调试信息</el-button>
        </div>
      </template>

      <!-- 流程部署区域 -->
      <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>流程部署</span>
            </template>
            <el-space direction="vertical" style="width: 100%;">
              <el-button type="primary" @click="deploySampleProcess" :loading="deployLoading">
                <el-icon><Upload /></el-icon>
                部署简单审批流程
              </el-button>
              <el-button type="success" @click="deployResearchProcess" :loading="deployLoading">
                <el-icon><Upload /></el-icon>
                部署科研项目申请流程
              </el-button>
            </el-space>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>流程启动测试</span>
            </template>
            <el-space direction="vertical" style="width: 100%;">
              <el-button type="warning" @click="startSimpleProcess" :loading="startLoading">
                <el-icon><VideoPlay /></el-icon>
                启动简单审批流程
              </el-button>
              <el-button type="info" @click="startResearchProcess" :loading="startLoading">
                <el-icon><VideoPlay /></el-icon>
                启动科研项目申请流程
              </el-button>
            </el-space>
          </el-card>
        </el-col>
      </el-row>

      <!-- 流程定义列表 -->
      <el-card shadow="hover" style="margin-bottom: 20px;">
        <template #header>
          <span>流程定义列表</span>
          <el-button style="float: right;" @click="refreshDefinitions" :loading="refreshLoading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </template>

        <el-table :data="processDefinitions" style="width: 100%;" v-loading="tableLoading">
          <el-table-column prop="processName" label="流程名称" width="200" />
          <el-table-column prop="processKey" label="流程Key" width="180" />
          <el-table-column prop="version" label="版本" width="80" />
          <el-table-column prop="category" label="分类" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.suspended ? 'danger' : 'success'">
                {{ scope.row.suspended ? '已挂起' : '激活' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="deploymentId" label="部署ID" width="200" />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button size="small" type="primary" @click="startProcessById(scope.row.processDefinitionId)">
                启动
              </el-button>
              <el-button
                size="small"
                :type="scope.row.suspended ? 'success' : 'warning'"
                @click="toggleProcessStatus(scope.row)"
              >
                {{ scope.row.suspended ? '激活' : '挂起' }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 操作日志 -->
      <el-card shadow="hover">
        <template #header>
          <span>操作日志</span>
          <el-button style="float: right;" @click="clearLogs">
            <el-icon><Delete /></el-icon>
            清空
          </el-button>
        </template>
        <div class="log-container">
          <div v-for="(log, index) in operationLogs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span :class="['log-level', log.level]">{{ log.level }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </el-card>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, VideoPlay, Refresh, Delete } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import {
  getProcessDefinitionList,
  deploySampleProcess as apiDeploySample,
  deployResearchProcess as apiDeployResearch,
  startSimpleProcess as apiStartSimple,
  suspendProcessDefinition,
  activateProcessDefinition
} from '@/api/workflow/process'
import { useMenuStore } from '@/store/modules/menu'
import { useUserStore } from '@/store/modules/user'

// 响应式数据
const deployLoading = ref(false)
const startLoading = ref(false)
const refreshLoading = ref(false)
const tableLoading = ref(false)
const processDefinitions = ref([])
const operationLogs = ref([])

// 添加日志
const addLog = (level: string, message: string) => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  operationLogs.value.unshift({
    time,
    level,
    message
  })
  // 保持最多50条日志
  if (operationLogs.value.length > 50) {
    operationLogs.value = operationLogs.value.slice(0, 50)
  }
}

// 部署简单审批流程
const deploySampleProcess = async () => {
  deployLoading.value = true
  try {
    const response = await apiDeploySample()
    if (response.code === 200) {
      ElMessage.success('简单审批流程部署成功')
      addLog('SUCCESS', `简单审批流程部署成功，部署ID: ${response.data}`)
      await refreshDefinitions()
    } else {
      ElMessage.error(response.msg || '部署失败')
      addLog('ERROR', `简单审批流程部署失败: ${response.msg}`)
    }
  } catch (error: any) {
    ElMessage.error('部署失败: ' + error.message)
    addLog('ERROR', `简单审批流程部署失败: ${error.message}`)
  } finally {
    deployLoading.value = false
  }
}

// 部署科研项目申请流程
const deployResearchProcess = async () => {
  deployLoading.value = true
  try {
    const response = await apiDeployResearch()
    if (response.code === 200) {
      ElMessage.success('科研项目申请流程部署成功')
      addLog('SUCCESS', `科研项目申请流程部署成功，部署ID: ${response.data}`)
      await refreshDefinitions()
    } else {
      ElMessage.error(response.msg || '部署失败')
      addLog('ERROR', `科研项目申请流程部署失败: ${response.msg}`)
    }
  } catch (error: any) {
    ElMessage.error('部署失败: ' + error.message)
    addLog('ERROR', `科研项目申请流程部署失败: ${error.message}`)
  } finally {
    deployLoading.value = false
  }
}

// 启动简单审批流程
const startSimpleProcess = async () => {
  startLoading.value = true
  try {
    const response = await apiStartSimple({
      title: '测试申请',
      content: '这是一个测试申请内容'
    })
    if (response.code === 200) {
      ElMessage.success('简单审批流程启动成功')
      addLog('SUCCESS', `简单审批流程启动成功，实例ID: ${response.data.processInstanceId}`)
    } else {
      ElMessage.error(response.msg || '启动失败')
      addLog('ERROR', `简单审批流程启动失败: ${response.msg}`)
    }
  } catch (error: any) {
    ElMessage.error('启动失败: ' + error.message)
    addLog('ERROR', `简单审批流程启动失败: ${error.message}`)
  } finally {
    startLoading.value = false
  }
}

// 启动科研项目申请流程
const startResearchProcess = async () => {
  ElMessage.info('科研项目申请流程启动功能开发中...')
  addLog('INFO', '科研项目申请流程启动功能开发中...')
}

// 通过ID启动流程
const startProcessById = async (definitionId: string) => {
  try {
    await ElMessageBox.confirm('确定要启动此流程吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 这里可以添加启动流程的逻辑
    ElMessage.success('流程启动成功')
    addLog('SUCCESS', `流程启动成功，定义ID: ${definitionId}`)
  } catch {
    // 用户取消
  }
}

// 切换流程状态
const toggleProcessStatus = async (process: any) => {
  try {
    if (process.suspended) {
      await activateProcessDefinition(process.processDefinitionId)
      ElMessage.success('流程激活成功')
      addLog('SUCCESS', `流程激活成功: ${process.processName}`)
    } else {
      await suspendProcessDefinition(process.processDefinitionId)
      ElMessage.success('流程挂起成功')
      addLog('SUCCESS', `流程挂起成功: ${process.processName}`)
    }
    await refreshDefinitions()
  } catch (error: any) {
    ElMessage.error('操作失败: ' + error.message)
    addLog('ERROR', `流程状态切换失败: ${error.message}`)
  }
}

// 刷新流程定义列表
const refreshDefinitions = async () => {
  refreshLoading.value = true
  tableLoading.value = true
  try {
    const response = await getProcessDefinitionList({
      pageNum: 1,
      pageSize: 100
    })
    if (response.code === 200) {
      processDefinitions.value = response.rows || []
      addLog('INFO', `刷新流程定义列表成功，共 ${processDefinitions.value.length} 个流程`)
    } else {
      ElMessage.error(response.msg || '获取流程定义列表失败')
      addLog('ERROR', `获取流程定义列表失败: ${response.msg}`)
    }
  } catch (error: any) {
    ElMessage.error('获取流程定义列表失败: ' + error.message)
    addLog('ERROR', `获取流程定义列表失败: ${error.message}`)
  } finally {
    refreshLoading.value = false
    tableLoading.value = false
  }
}

// 清空日志
const clearLogs = () => {
  operationLogs.value = []
  ElMessage.success('日志已清空')
}

// 刷新菜单数据
const refreshMenus = async () => {
  try {
    const userStore = useUserStore()
    await userStore.getUserInfo()
    ElMessage.success('菜单数据刷新成功')
    addLog('SUCCESS', '菜单数据已刷新')
  } catch (error: any) {
    ElMessage.error('刷新菜单失败: ' + error.message)
    addLog('ERROR', `刷新菜单失败: ${error.message}`)
  }
}

// 显示调试信息
const showDebugInfo = () => {
  const menuStore = useMenuStore()
  const userStore = useUserStore()

  console.log('=== 调试信息 ===')
  console.log('菜单数据:', menuStore.menus)
  console.log('动态路由:', menuStore.routes)
  console.log('用户信息:', userStore.userInfo)
  console.log('用户角色:', userStore.roles)
  console.log('用户权限:', userStore.permissions)
  console.log('当前路由:', window.location.href)

  // 查找工作流相关菜单
  const workflowMenu = menuStore.menus.find(menu => menu.path === 'workflow')
  console.log('工作流菜单:', workflowMenu)

  if (workflowMenu && workflowMenu.children) {
    const processMenu = workflowMenu.children.find(child => child.path === 'process')
    console.log('流程管理菜单:', processMenu)
  }

  // 检查Vue Router中的路由
  const router = useRouter()
  console.log('Vue Router所有路由:', router.getRoutes())

  addLog('INFO', `菜单数量: ${menuStore.menus.length}`)
  addLog('INFO', `动态路由数量: ${menuStore.routes.length}`)
  addLog('INFO', `用户: ${userStore.userInfo.userName || '未登录'}`)
  addLog('INFO', `角色: ${userStore.roles.join(', ') || '无'}`)

  ElMessage.info('调试信息已输出到控制台和日志')
}

// 组件挂载时获取流程定义列表
onMounted(() => {
  console.log('🎉 流程管理页面组件已成功加载！')
  addLog('SUCCESS', '流程管理页面组件已成功加载！')
  refreshDefinitions()
  addLog('INFO', '流程管理页面初始化完成')
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  margin-bottom: 5px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-time {
  color: #666;
  margin-right: 10px;
}

.log-level {
  margin-right: 10px;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: bold;
}

.log-level.SUCCESS {
  background-color: #67c23a;
  color: white;
}

.log-level.ERROR {
  background-color: #f56c6c;
  color: white;
}

.log-level.INFO {
  background-color: #409eff;
  color: white;
}

.log-message {
  color: #333;
}
</style>
