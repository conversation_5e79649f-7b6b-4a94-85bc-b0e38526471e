-- 纵向项目管理相关表结构

-- 1. 纵向项目表
DROP TABLE IF EXISTS vertical_project;
CREATE TABLE vertical_project (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '项目ID',
    project_no VARCHAR(50) NOT NULL UNIQUE COMMENT '项目编号',
    project_name VARCHAR(255) NOT NULL COMMENT '项目名称',
    project_type VARCHAR(50) COMMENT '项目类型',
    project_level VARCHAR(50) COMMENT '项目级别',
    status INT DEFAULT 0 COMMENT '项目状态：0-申请中，1-立项，2-执行中，3-变更中，4-结项中，5-已结项，6-已撤销',
    start_date DATE COMMENT '项目开始日期',
    end_date DATE COMMENT '项目结束日期',
    budget DECIMAL(15,2) COMMENT '项目预算',
    used_budget DECIMAL(15,2) DEFAULT 0.00 COMMENT '已使用预算',
    principal_id BIGINT COMMENT '项目负责人ID',
    principal_name VARCHAR(100) COMMENT '项目负责人姓名',
    dept_id BIGINT COMMENT '所属部门ID',
    dept_name VARCHAR(100) COMMENT '所属部门名称',
    funding_agency VARCHAR(255) COMMENT '资助机构',
    project_source VARCHAR(255) COMMENT '项目来源',
    project_summary TEXT COMMENT '项目简介',
    research_content TEXT COMMENT '研究内容',
    expected_results TEXT COMMENT '预期成果',
    keywords VARCHAR(500) COMMENT '关键词',
    subject_category VARCHAR(100) COMMENT '学科分类',
    application_file_path VARCHAR(500) COMMENT '申请书文件路径',
    approval_file_path VARCHAR(500) COMMENT '立项批文文件路径',
    contract_file_path VARCHAR(500) COMMENT '合同书文件路径',
    process_instance_id VARCHAR(64) COMMENT '工作流实例ID',
    current_node VARCHAR(100) COMMENT '当前审批节点',
    approval_status VARCHAR(50) COMMENT '审批状态',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_project_no (project_no),
    INDEX idx_principal_id (principal_id),
    INDEX idx_dept_id (dept_id),
    INDEX idx_status (status),
    INDEX idx_project_type (project_type),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='纵向项目表';

-- 2. 项目成员表
DROP TABLE IF EXISTS project_member;
CREATE TABLE project_member (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '成员ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    user_name VARCHAR(100) NOT NULL COMMENT '用户姓名',
    user_no VARCHAR(50) COMMENT '用户工号',
    dept_id BIGINT COMMENT '所属部门ID',
    dept_name VARCHAR(100) COMMENT '所属部门名称',
    member_role INT DEFAULT 2 COMMENT '成员角色：0-项目负责人，1-主要参与人，2-一般参与人，3-学生参与人',
    member_role_name VARCHAR(50) COMMENT '成员角色名称',
    workload DECIMAL(8,2) COMMENT '工作量（人月）',
    position VARCHAR(100) COMMENT '职务/职称',
    specialty VARCHAR(200) COMMENT '专业领域',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '电子邮箱',
    responsibility TEXT COMMENT '主要职责',
    join_date DATE COMMENT '加入日期',
    leave_date DATE COMMENT '离开日期',
    status INT DEFAULT 0 COMMENT '成员状态：0-正常，1-已离开，2-暂停',
    is_external BOOLEAN DEFAULT FALSE COMMENT '是否外聘专家',
    external_unit VARCHAR(200) COMMENT '外聘单位',
    sort_order INT DEFAULT 0 COMMENT '排序号',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_project_id (project_id),
    INDEX idx_user_id (user_id),
    INDEX idx_member_role (member_role),
    INDEX idx_status (status),
    UNIQUE KEY uk_project_user (project_id, user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目成员表';

-- 3. 项目变更表
DROP TABLE IF EXISTS project_change;
CREATE TABLE project_change (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '变更ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    project_name VARCHAR(255) COMMENT '项目名称',
    change_no VARCHAR(50) NOT NULL UNIQUE COMMENT '变更编号',
    change_type INT NOT NULL COMMENT '变更类型：0-基本信息变更，1-成员变更，2-预算变更，3-时间变更，4-内容变更，5-其他变更',
    change_type_name VARCHAR(50) COMMENT '变更类型名称',
    change_reason TEXT COMMENT '变更原因',
    change_content TEXT COMMENT '变更内容',
    before_content TEXT COMMENT '变更前内容',
    after_content TEXT COMMENT '变更后内容',
    impact_analysis TEXT COMMENT '变更影响分析',
    status INT DEFAULT 0 COMMENT '变更状态：0-申请中，1-审核中，2-已批准，3-已拒绝，4-已撤销',
    applicant_id BIGINT COMMENT '申请人ID',
    applicant_name VARCHAR(100) COMMENT '申请人姓名',
    apply_time DATETIME COMMENT '申请时间',
    reviewer_id BIGINT COMMENT '审核人ID',
    reviewer_name VARCHAR(100) COMMENT '审核人姓名',
    review_time DATETIME COMMENT '审核时间',
    review_comment TEXT COMMENT '审核意见',
    approver_id BIGINT COMMENT '批准人ID',
    approver_name VARCHAR(100) COMMENT '批准人姓名',
    approve_time DATETIME COMMENT '批准时间',
    approve_comment TEXT COMMENT '批准意见',
    process_instance_id VARCHAR(64) COMMENT '工作流实例ID',
    current_node VARCHAR(100) COMMENT '当前审批节点',
    approval_status VARCHAR(50) COMMENT '审批状态',
    change_file_path VARCHAR(500) COMMENT '变更文件路径',
    approval_file_path VARCHAR(500) COMMENT '批准文件路径',
    urgency INT DEFAULT 0 COMMENT '紧急程度：0-一般，1-紧急，2-非常紧急',
    need_review BOOLEAN DEFAULT FALSE COMMENT '是否需要重新评审',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_project_id (project_id),
    INDEX idx_change_no (change_no),
    INDEX idx_change_type (change_type),
    INDEX idx_status (status),
    INDEX idx_applicant_id (applicant_id),
    INDEX idx_apply_time (apply_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目变更表';

-- 4. 项目预算表
DROP TABLE IF EXISTS project_budget;
CREATE TABLE project_budget (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '预算ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    budget_category VARCHAR(100) NOT NULL COMMENT '预算类别',
    budget_item VARCHAR(200) NOT NULL COMMENT '预算项目',
    planned_amount DECIMAL(15,2) NOT NULL COMMENT '计划金额',
    used_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '已使用金额',
    remaining_amount DECIMAL(15,2) COMMENT '剩余金额',
    budget_year INT COMMENT '预算年度',
    budget_description TEXT COMMENT '预算说明',
    status INT DEFAULT 0 COMMENT '状态：0-正常，1-冻结，2-调整中',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_project_id (project_id),
    INDEX idx_budget_category (budget_category),
    INDEX idx_budget_year (budget_year)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目预算表';

-- 5. 项目里程碑表
DROP TABLE IF EXISTS project_milestone;
CREATE TABLE project_milestone (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '里程碑ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    milestone_name VARCHAR(200) NOT NULL COMMENT '里程碑名称',
    milestone_description TEXT COMMENT '里程碑描述',
    planned_date DATE COMMENT '计划完成日期',
    actual_date DATE COMMENT '实际完成日期',
    completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率',
    status INT DEFAULT 0 COMMENT '状态：0-未开始，1-进行中，2-已完成，3-已延期',
    responsible_person VARCHAR(100) COMMENT '负责人',
    deliverables TEXT COMMENT '交付物',
    sort_order INT DEFAULT 0 COMMENT '排序号',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_project_id (project_id),
    INDEX idx_planned_date (planned_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目里程碑表';

-- 6. 项目文档表
DROP TABLE IF EXISTS project_document;
CREATE TABLE project_document (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '文档ID',
    project_id BIGINT NOT NULL COMMENT '项目ID',
    document_name VARCHAR(255) NOT NULL COMMENT '文档名称',
    document_type VARCHAR(50) COMMENT '文档类型',
    document_category VARCHAR(100) COMMENT '文档分类',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT COMMENT '文件大小',
    file_type VARCHAR(50) COMMENT '文件类型',
    version VARCHAR(20) DEFAULT '1.0' COMMENT '版本号',
    upload_person VARCHAR(100) COMMENT '上传人',
    upload_time DATETIME COMMENT '上传时间',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    status INT DEFAULT 0 COMMENT '状态：0-正常，1-已删除',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注',
    INDEX idx_project_id (project_id),
    INDEX idx_document_type (document_type),
    INDEX idx_upload_time (upload_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目文档表';

-- 插入初始数据
-- 项目类型字典数据
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) VALUES 
('项目类型', 'project_type', '0', 'admin', NOW(), '纵向项目类型');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES 
(1, '国家级', '国家级', 'project_type', '', 'primary', 'N', '0', 'admin', NOW(), '国家级项目'),
(2, '省部级', '省部级', 'project_type', '', 'success', 'N', '0', 'admin', NOW(), '省部级项目'),
(3, '市厅级', '市厅级', 'project_type', '', 'info', 'N', '0', 'admin', NOW(), '市厅级项目'),
(4, '校级', '校级', 'project_type', '', 'warning', 'N', '0', 'admin', NOW(), '校级项目');

-- 项目级别字典数据
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) VALUES 
('项目级别', 'project_level', '0', 'admin', NOW(), '纵向项目级别');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES 
(1, '重点项目', '重点项目', 'project_level', '', 'danger', 'N', '0', 'admin', NOW(), '重点项目'),
(2, '一般项目', '一般项目', 'project_level', '', 'primary', 'N', '0', 'admin', NOW(), '一般项目'),
(3, '青年项目', '青年项目', 'project_level', '', 'success', 'N', '0', 'admin', NOW(), '青年项目'),
(4, '专项项目', '专项项目', 'project_level', '', 'warning', 'N', '0', 'admin', NOW(), '专项项目');

-- 项目状态字典数据
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) VALUES 
('项目状态', 'project_status', '0', 'admin', NOW(), '纵向项目状态');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES 
(1, '申请中', '0', 'project_status', '', 'info', 'N', '0', 'admin', NOW(), '申请中'),
(2, '立项', '1', 'project_status', '', 'primary', 'N', '0', 'admin', NOW(), '立项'),
(3, '执行中', '2', 'project_status', '', 'success', 'N', '0', 'admin', NOW(), '执行中'),
(4, '变更中', '3', 'project_status', '', 'warning', 'N', '0', 'admin', NOW(), '变更中'),
(5, '结项中', '4', 'project_status', '', 'info', 'N', '0', 'admin', NOW(), '结项中'),
(6, '已结项', '5', 'project_status', '', 'success', 'N', '0', 'admin', NOW(), '已结项'),
(7, '已撤销', '6', 'project_status', '', 'danger', 'N', '0', 'admin', NOW(), '已撤销');

-- 成员角色字典数据
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) VALUES 
('成员角色', 'member_role', '0', 'admin', NOW(), '项目成员角色');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES 
(1, '项目负责人', '0', 'member_role', '', 'danger', 'N', '0', 'admin', NOW(), '项目负责人'),
(2, '主要参与人', '1', 'member_role', '', 'primary', 'N', '0', 'admin', NOW(), '主要参与人'),
(3, '一般参与人', '2', 'member_role', '', 'success', 'N', '0', 'admin', NOW(), '一般参与人'),
(4, '学生参与人', '3', 'member_role', '', 'info', 'N', '0', 'admin', NOW(), '学生参与人');
