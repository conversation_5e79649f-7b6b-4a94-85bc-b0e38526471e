<template>
  <div class="process-statistics">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon active">
              <el-icon><VideoPlay /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.activeInstances }}</div>
              <div class="stat-label">运行中实例</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon completed">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.completedInstances }}</div>
              <div class="stat-label">已完成实例</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.pendingTasks }}</div>
              <div class="stat-label">待处理任务</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon overdue">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.overdueTasks }}</div>
              <div class="stat-label">超时任务</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20">
      <!-- 流程启动趋势 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>流程启动趋势</span>
              <el-select v-model="trendPeriod" size="small" @change="updateTrendChart">
                <el-option label="最近7天" value="7days" />
                <el-option label="最近30天" value="30days" />
                <el-option label="最近3个月" value="3months" />
              </el-select>
            </div>
          </template>
          <div ref="trendChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>

      <!-- 流程状态分布 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>流程状态分布</span>
          </template>
          <div ref="statusChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt20">
      <!-- 流程类型统计 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>流程类型统计</span>
          </template>
          <div ref="typeChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>

      <!-- 平均处理时长 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>平均处理时长</span>
          </template>
          <div ref="durationChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计表格 -->
    <el-card class="mt20">
      <template #header>
        <span>流程详细统计</span>
      </template>
      <el-table :data="processStats" v-loading="loading">
        <el-table-column label="流程名称" prop="processName" />
        <el-table-column label="总启动次数" prop="totalInstances" width="120" align="center" />
        <el-table-column label="运行中" prop="activeInstances" width="100" align="center">
          <template #default="scope">
            <el-tag type="success" size="small">{{ scope.row.activeInstances }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="已完成" prop="completedInstances" width="100" align="center">
          <template #default="scope">
            <el-tag type="info" size="small">{{ scope.row.completedInstances }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="已终止" prop="terminatedInstances" width="100" align="center">
          <template #default="scope">
            <el-tag type="danger" size="small">{{ scope.row.terminatedInstances }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="平均耗时" prop="avgDuration" width="120" align="center">
          <template #default="scope">
            {{ formatDuration(scope.row.avgDuration) }}
          </template>
        </el-table-column>
        <el-table-column label="完成率" prop="completionRate" width="100" align="center">
          <template #default="scope">
            <el-progress 
              :percentage="scope.row.completionRate" 
              :stroke-width="8"
              :show-text="false"
              :color="getProgressColor(scope.row.completionRate)"
            />
            <span class="ml5">{{ scope.row.completionRate }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="最后启动时间" prop="lastStartTime" width="160" align="center">
          <template #default="scope">
            {{ parseTime(scope.row.lastStartTime) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { parseTime } from '@/utils/common'
import { getStatistics, getTrendData, getDetailStatistics } from '@/api/workflow/process'

// 数据
const loading = ref(false)
const trendPeriod = ref('7days')

// 统计数据
const statistics = reactive({
  activeInstances: 156,
  completedInstances: 1234,
  pendingTasks: 89,
  overdueTasks: 12
})

// 流程统计数据
const processStats = ref([
  {
    processName: '请假申请流程',
    totalInstances: 245,
    activeInstances: 23,
    completedInstances: 210,
    terminatedInstances: 12,
    avgDuration: 2.5 * 24 * 60 * 60 * 1000, // 2.5天
    completionRate: 86,
    lastStartTime: new Date()
  },
  {
    processName: '报销审批流程',
    totalInstances: 189,
    activeInstances: 15,
    completedInstances: 168,
    terminatedInstances: 6,
    avgDuration: 1.8 * 24 * 60 * 60 * 1000, // 1.8天
    completionRate: 89,
    lastStartTime: new Date(Date.now() - 2 * 60 * 60 * 1000)
  },
  {
    processName: '采购申请流程',
    totalInstances: 156,
    activeInstances: 12,
    completedInstances: 138,
    terminatedInstances: 6,
    avgDuration: 3.2 * 24 * 60 * 60 * 1000, // 3.2天
    completionRate: 88,
    lastStartTime: new Date(Date.now() - 5 * 60 * 60 * 1000)
  },
  {
    processName: '合同审批流程',
    totalInstances: 98,
    activeInstances: 8,
    completedInstances: 85,
    terminatedInstances: 5,
    avgDuration: 4.1 * 24 * 60 * 60 * 1000, // 4.1天
    completionRate: 87,
    lastStartTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
  }
])

// 图表引用
const trendChartRef = ref()
const statusChartRef = ref()
const typeChartRef = ref()
const durationChartRef = ref()

// 图表实例
let trendChart: echarts.ECharts | null = null
let statusChart: echarts.ECharts | null = null
let typeChart: echarts.ECharts | null = null
let durationChart: echarts.ECharts | null = null

// 格式化耗时
const formatDuration = (duration: number) => {
  if (!duration) return '-'
  const days = Math.floor(duration / (24 * 60 * 60 * 1000))
  const hours = Math.floor((duration % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000))
  
  if (days > 0) return `${days}天${hours}小时`
  return `${hours}小时`
}

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage >= 90) return '#67c23a'
  if (percentage >= 70) return '#e6a23c'
  return '#f56c6c'
}

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChartRef.value) return
  
  trendChart = echarts.init(trendChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['启动数量', '完成数量']
    },
    xAxis: {
      type: 'category',
      data: ['07-25', '07-26', '07-27', '07-28', '07-29', '07-30', '07-31']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '启动数量',
        type: 'line',
        data: [12, 15, 18, 22, 16, 20, 25],
        smooth: true,
        itemStyle: { color: '#409eff' }
      },
      {
        name: '完成数量',
        type: 'line',
        data: [8, 12, 14, 18, 15, 17, 21],
        smooth: true,
        itemStyle: { color: '#67c23a' }
      }
    ]
  }
  
  trendChart.setOption(option)
}

// 初始化状态分布图表
const initStatusChart = () => {
  if (!statusChartRef.value) return
  
  statusChart = echarts.init(statusChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        type: 'pie',
        radius: '50%',
        data: [
          { value: 156, name: '运行中', itemStyle: { color: '#409eff' } },
          { value: 1234, name: '已完成', itemStyle: { color: '#67c23a' } },
          { value: 45, name: '已挂起', itemStyle: { color: '#e6a23c' } },
          { value: 23, name: '已终止', itemStyle: { color: '#f56c6c' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  statusChart.setOption(option)
}

// 初始化类型统计图表
const initTypeChart = () => {
  if (!typeChartRef.value) return
  
  typeChart = echarts.init(typeChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: ['请假申请', '报销审批', '采购申请', '合同审批', '项目申请']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        type: 'bar',
        data: [245, 189, 156, 98, 67],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }
    ]
  }
  
  typeChart.setOption(option)
}

// 初始化处理时长图表
const initDurationChart = () => {
  if (!durationChartRef.value) return
  
  durationChart = echarts.init(durationChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: ['请假申请', '报销审批', '采购申请', '合同审批', '项目申请']
    },
    yAxis: {
      type: 'value',
      name: '天数'
    },
    series: [
      {
        type: 'bar',
        data: [2.5, 1.8, 3.2, 4.1, 5.6],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#ffd666' },
            { offset: 0.5, color: '#f7ba2a' },
            { offset: 1, color: '#f7ba2a' }
          ])
        }
      }
    ]
  }
  
  durationChart.setOption(option)
}

// 更新趋势图表
const updateTrendChart = () => {
  // 根据选择的时间段更新数据
  console.log('更新趋势图表:', trendPeriod.value)
  // 这里可以重新获取数据并更新图表
}

// 初始化所有图表
const initCharts = () => {
  nextTick(() => {
    initTrendChart()
    initStatusChart()
    initTypeChart()
    initDurationChart()
  })
}

// 刷新数据
const refreshData = () => {
  loading.value = true

  // 获取统计数据
  Promise.all([
    getStatistics(),
    getTrendData(parseInt(trendPeriod.value.replace('days', '').replace('months', '')) * (trendPeriod.value.includes('months') ? 30 : 1)),
    getDetailStatistics()
  ]).then(([statsRes, trendRes, detailRes]) => {
    // 更新统计数据
    if (statsRes.data) {
      Object.assign(statistics, statsRes.data)
    }

    // 更新详细统计
    if (detailRes.data) {
      processStats.value = detailRes.data
    }

    loading.value = false
    // 重新初始化图表
    initCharts()
  }).catch(() => {
    loading.value = false
    // 重新初始化图表
    initCharts()
  })
}

// 页面挂载
onMounted(() => {
  initCharts()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    trendChart?.resize()
    statusChart?.resize()
    typeChart?.resize()
    durationChart?.resize()
  })
})

// 暴露方法
defineExpose({
  refreshData
})
</script>

<style scoped>
.process-statistics {
  height: 100%;
  background: #fafbfc;
  border-radius: 8px;
  padding: 20px;
}

.mb20 {
  margin-bottom: 24px;
}

.mt20 {
  margin-top: 24px;
}

.ml5 {
  margin-left: 5px;
}

/* 统计卡片样式 */
.stat-card {
  height: 140px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background: white;
  overflow: hidden;
  position: relative;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.stat-icon {
  width: 70px;
  height: 70px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: white;
  margin-right: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-icon.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-icon.overdue {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 36px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 15px;
  color: #6b7280;
  font-weight: 500;
}

/* 卡片样式 */
:deep(.el-card) {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

:deep(.el-card:hover) {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

:deep(.el-card__header) {
  background: #f8fafc;
  border-bottom: 2px solid #e5e7eb;
  padding: 20px 24px;
  border-radius: 12px 12px 0 0;
}

:deep(.el-card__body) {
  padding: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

/* 表格样式 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

:deep(.el-table__header) {
  background: #f8fafc;
}

:deep(.el-table th) {
  background: #f8fafc !important;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f3f4f6;
}

:deep(.el-table__row:hover) {
  background: #f8fafc;
}

/* 标签样式 */
:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
  border: none;
  padding: 4px 12px;
}

:deep(.el-tag--info) {
  background: rgba(107, 114, 128, 0.1);
  color: #4b5563;
}

:deep(.el-tag--success) {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
}

:deep(.el-tag--warning) {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
}

:deep(.el-tag--danger) {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

/* 进度条样式 */
:deep(.el-progress-bar__outer) {
  border-radius: 6px;
  background: #f3f4f6;
}

:deep(.el-progress-bar__inner) {
  border-radius: 6px;
}

/* 选择器样式 */
:deep(.el-select) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stat-card {
    height: 120px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
    margin-right: 16px;
  }

  .stat-number {
    font-size: 28px;
  }

  .stat-label {
    font-size: 13px;
  }

  :deep(.el-card__header) {
    padding: 16px 20px;
  }

  :deep(.el-card__body) {
    padding: 20px;
  }
}
</style>
