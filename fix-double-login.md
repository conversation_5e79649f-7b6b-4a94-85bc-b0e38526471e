# 双登录框问题修复指南

## 🐛 问题描述
在本地开发环境中出现两个登录框：
- 一个带验证码的登录框
- 一个不带验证码的登录框
- 带验证码的会跳转到不带验证码的

## 🔍 问题原因分析

### 1. **环境配置混乱**
- 开发环境应该使用 `/dev-api` 代理
- 生产环境使用直接的服务器地址
- 可能存在多个环境配置同时生效

### 2. **代理配置错误**
- Vite代理目标地址可能配置错误
- 导致请求被转发到错误的后端服务

### 3. **缓存问题**
- 浏览器缓存了旧的配置
- 前端构建缓存问题

## ✅ 已修复的配置

### 1. **开发环境配置** (`.env.development`)
```env
VITE_APP_ENV = 'development'
VITE_APP_BASE_API = '/dev-api'  # 使用代理
```

### 2. **Vite代理配置** (`vite.config.ts`)
```typescript
proxy: {
  '/dev-api': {
    target: 'http://localhost:8989',  # 指向本地后端
    changeOrigin: true,
    rewrite: (p) => p.replace(/^\/dev-api/, '')
  }
}
```

### 3. **生产环境配置** (`.env.production`)
```env
VITE_APP_ENV = 'production'
VITE_APP_BASE_API = 'http://**************:8989'  # 直接指向服务器
```

## 🔧 修复步骤

### 步骤1: 清除缓存
```bash
# 清除前端构建缓存
cd frontend
rm -rf node_modules/.vite
rm -rf dist

# 清除浏览器缓存
# 在浏览器中按 Ctrl+Shift+Delete 清除缓存
```

### 步骤2: 确认后端服务
```bash
# 检查本地后端是否运行在8989端口
netstat -an | findstr :8989

# 如果没有运行，启动本地后端
cd backend
mvn spring-boot:run
```

### 步骤3: 重启前端开发服务
```bash
cd frontend
npm run dev
```

### 步骤4: 验证配置
```bash
# 检查当前环境变量
echo $NODE_ENV

# 访问本地开发地址
# http://localhost:3000
```

## 🎯 验证方法

### 1. **检查网络请求**
打开浏览器开发者工具 → Network 标签：
- 登录请求应该是：`http://localhost:3000/dev-api/login`
- 验证码请求应该是：`http://localhost:3000/dev-api/captchaImage`

### 2. **检查控制台**
确认没有以下错误：
- CORS跨域错误
- 404 Not Found错误
- 网络连接错误

### 3. **检查环境变量**
在浏览器控制台执行：
```javascript
console.log(import.meta.env.VITE_APP_BASE_API)
// 应该输出: "/dev-api"
```

## 🚨 常见问题排查

### 问题1: 仍然出现双登录框
**解决方案：**
1. 完全关闭浏览器，重新打开
2. 使用无痕模式访问
3. 检查是否有多个标签页同时打开

### 问题2: 验证码无法加载
**解决方案：**
1. 检查后端服务是否正常运行
2. 检查代理配置是否正确
3. 查看浏览器控制台的网络请求

### 问题3: 登录后跳转异常
**解决方案：**
1. 检查路由配置
2. 清除localStorage和sessionStorage
3. 检查用户权限配置

## 📝 配置文件检查清单

### ✅ 开发环境配置检查
- [ ] `.env.development` 中 `VITE_APP_BASE_API = '/dev-api'`
- [ ] `vite.config.ts` 中代理指向 `http://localhost:8989`
- [ ] 本地后端服务运行在8989端口
- [ ] 前端开发服务运行在3000端口

### ✅ 生产环境配置检查
- [ ] `.env.production` 中 `VITE_APP_BASE_API = 'http://**************:8989'`
- [ ] 服务器后端运行在8989端口
- [ ] 服务器前端部署在3000端口

## 🔄 重启服务命令

### 本地开发环境
```bash
# 停止前端服务 (Ctrl+C)
# 重启前端服务
cd frontend
npm run dev

# 重启后端服务
cd backend
# 停止当前进程 (Ctrl+C)
mvn spring-boot:run
```

### 服务器生产环境
```bash
# 重启后端
sudo /opt/research-management/restart.sh

# 重新加载Nginx
sudo nginx -s reload
```

## 📞 技术支持

如果问题仍然存在，请提供：
1. 浏览器控制台的完整错误信息
2. Network标签中的请求详情
3. 当前的环境配置文件内容
4. 后端服务的运行状态

---

**修复时间：** 2024-07-31  
**修复状态：** ✅ 已修复配置文件  
**测试状态：** 🔄 需要重启服务验证
