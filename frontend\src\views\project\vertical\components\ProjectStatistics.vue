<template>
  <el-dialog
    title="项目统计分析"
    v-model="visible"
    width="1400px"
    append-to-body
    @close="handleClose"
  >
    <div v-loading="loading" class="statistics-container">
      <!-- 统计概览 -->
      <el-row :gutter="20" class="statistics-overview">
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic
              title="项目总数"
              :value="overviewData.totalProjects"
              class="stat-item"
            >
              <template #suffix>
                <span class="stat-unit">个</span>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic
              title="总预算金额"
              :value="overviewData.totalBudget"
              :precision="2"
              prefix="¥"
              class="stat-item"
            />
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic
              title="执行中项目"
              :value="overviewData.executingProjects"
              class="stat-item"
            >
              <template #suffix>
                <span class="stat-unit">个</span>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic
              title="已结项项目"
              :value="overviewData.completedProjects"
              class="stat-item"
            >
              <template #suffix>
                <span class="stat-unit">个</span>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
      </el-row>

      <!-- 图表区域 -->
      <el-row :gutter="20" class="charts-section">
        <!-- 项目类型分布 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>项目类型分布</span>
            </template>
            <div ref="typeChartRef" class="chart-container"></div>
          </el-card>
        </el-col>

        <!-- 项目状态分布 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>项目状态分布</span>
            </template>
            <div ref="statusChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="charts-section">
        <!-- 年度项目趋势 -->
        <el-col :span="24">
          <el-card>
            <template #header>
              <span>年度项目趋势</span>
            </template>
            <div ref="trendChartRef" class="chart-container-large"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="charts-section">
        <!-- 部门项目统计 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>部门项目统计</span>
            </template>
            <div ref="deptChartRef" class="chart-container"></div>
          </el-card>
        </el-col>

        <!-- 预算使用情况 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>预算使用情况</span>
            </template>
            <div ref="budgetChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 数据表格 -->
      <el-card class="data-table-section">
        <template #header>
          <span>详细数据</span>
          <el-button type="primary" size="small" style="float: right;" @click="exportData">
            导出数据
          </el-button>
        </template>
        <el-table :data="detailData" border>
          <el-table-column label="项目类型" prop="projectType" />
          <el-table-column label="项目数量" prop="projectCount" />
          <el-table-column label="总预算" prop="totalBudget">
            <template #default="scope">
              ¥{{ Number(scope.row.totalBudget).toLocaleString() }}
            </template>
          </el-table-column>
          <el-table-column label="已使用预算" prop="usedBudget">
            <template #default="scope">
              ¥{{ Number(scope.row.usedBudget).toLocaleString() }}
            </template>
          </el-table-column>
          <el-table-column label="预算使用率" prop="usageRate">
            <template #default="scope">
              <el-progress :percentage="scope.row.usageRate" :color="getProgressColor(scope.row.usageRate)" />
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import * as echarts from 'echarts'
import { 
  getProjectStatistics, 
  getTypeStatistics, 
  getDeptStatistics,
  getYearlyStatistics 
} from "@/api/project/vertical"

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const loading = ref(false)
const typeChartRef = ref()
const statusChartRef = ref()
const trendChartRef = ref()
const deptChartRef = ref()
const budgetChartRef = ref()

// 数据
const overviewData = ref({
  totalProjects: 0,
  totalBudget: 0,
  executingProjects: 0,
  completedProjects: 0
})

const detailData = ref([])

// 图表实例
let typeChart = null
let statusChart = null
let trendChart = null
let deptChart = null
let budgetChart = null

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal) {
    loadStatisticsData()
    nextTick(() => {
      initCharts()
    })
  } else {
    destroyCharts()
  }
})

/** 加载统计数据 */
function loadStatisticsData() {
  loading.value = true

  // 模拟统计数据
  setTimeout(() => {
    // 概览数据
    overviewData.value = {
      totalProjects: 25,
      totalBudget: 3500000,
      executingProjects: 15,
      completedProjects: 8
    }

    // 详细数据
    const mockTypeStats = [
      { projectType: '国家级', projectCount: 5, totalBudget: 2000000, usedBudget: 800000 },
      { projectType: '省部级', projectCount: 8, totalBudget: 1200000, usedBudget: 600000 },
      { projectType: '市厅级', projectCount: 7, totalBudget: 250000, usedBudget: 150000 },
      { projectType: '校级', projectCount: 5, totalBudget: 50000, usedBudget: 30000 }
    ]

    const mockDeptStats = [
      { deptName: '计算机系', projectCount: 8 },
      { deptName: '法学系', projectCount: 6 },
      { deptName: '数据科学系', projectCount: 5 },
      { deptName: '管理系', projectCount: 4 },
      { deptName: '外语系', projectCount: 2 }
    ]

    detailData.value = mockTypeStats.map(item => ({
      ...item,
      usageRate: item.totalBudget > 0 ? Math.round((item.usedBudget / item.totalBudget) * 100) : 0
    }))

    // 更新图表
    updateCharts(mockTypeStats, mockDeptStats, [])
    loading.value = false
  }, 1000)

  // 实际API调用（暂时注释）
  // Promise.all([
  //   getProjectStatistics(),
  //   getTypeStatistics(),
  //   getDeptStatistics(),
  //   getYearlyStatistics(new Date().getFullYear())
  // ]).then(([overview, typeStats, deptStats, yearlyStats]) => {
  //   // 处理概览数据
  //   overviewData.value = overview.data
  //
  //   // 处理详细数据
  //   detailData.value = typeStats.data.map(item => ({
  //     ...item,
  //     usageRate: item.totalBudget > 0 ? Math.round((item.usedBudget / item.totalBudget) * 100) : 0
  //   }))
  //
  //   // 更新图表
  //   updateCharts(typeStats.data, deptStats.data, yearlyStats.data)
  // }).finally(() => {
  //   loading.value = false
  // })
}

/** 初始化图表 */
function initCharts() {
  // 项目类型分布饼图
  typeChart = echarts.init(typeChartRef.value)
  
  // 项目状态分布饼图
  statusChart = echarts.init(statusChartRef.value)
  
  // 年度趋势折线图
  trendChart = echarts.init(trendChartRef.value)
  
  // 部门统计柱状图
  deptChart = echarts.init(deptChartRef.value)
  
  // 预算使用情况
  budgetChart = echarts.init(budgetChartRef.value)
}

/** 更新图表数据 */
function updateCharts(typeData, deptData, yearlyData) {
  if (!typeChart || !statusChart || !trendChart || !deptChart || !budgetChart) return
  
  // 项目类型分布
  const typeOption = {
    title: { text: '项目类型分布', left: 'center' },
    tooltip: { trigger: 'item' },
    series: [{
      type: 'pie',
      radius: '60%',
      data: typeData.map(item => ({
        name: item.projectType,
        value: item.projectCount
      }))
    }]
  }
  typeChart.setOption(typeOption)
  
  // 项目状态分布
  const statusData = [
    { name: '申请中', value: 5 },
    { name: '立项', value: 8 },
    { name: '执行中', value: 15 },
    { name: '已结项', value: 12 },
    { name: '已撤销', value: 2 }
  ]
  const statusOption = {
    title: { text: '项目状态分布', left: 'center' },
    tooltip: { trigger: 'item' },
    series: [{
      type: 'pie',
      radius: '60%',
      data: statusData
    }]
  }
  statusChart.setOption(statusOption)
  
  // 年度趋势
  const trendOption = {
    title: { text: '年度项目趋势', left: 'center' },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: ['2020', '2021', '2022', '2023', '2024']
    },
    yAxis: { type: 'value' },
    series: [{
      name: '项目数量',
      type: 'line',
      data: [10, 15, 20, 25, 30],
      smooth: true
    }]
  }
  trendChart.setOption(trendOption)
  
  // 部门统计
  const deptOption = {
    title: { text: '部门项目统计', left: 'center' },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: deptData.map(item => item.deptName)
    },
    yAxis: { type: 'value' },
    series: [{
      name: '项目数量',
      type: 'bar',
      data: deptData.map(item => item.projectCount)
    }]
  }
  deptChart.setOption(deptOption)
  
  // 预算使用情况
  const budgetOption = {
    title: { text: '预算使用情况', left: 'center' },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: typeData.map(item => item.projectType)
    },
    yAxis: { type: 'value' },
    series: [
      {
        name: '总预算',
        type: 'bar',
        data: typeData.map(item => item.totalBudget)
      },
      {
        name: '已使用',
        type: 'bar',
        data: typeData.map(item => item.usedBudget)
      }
    ]
  }
  budgetChart.setOption(budgetOption)
}

/** 销毁图表 */
function destroyCharts() {
  if (typeChart) {
    typeChart.dispose()
    typeChart = null
  }
  if (statusChart) {
    statusChart.dispose()
    statusChart = null
  }
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
  if (deptChart) {
    deptChart.dispose()
    deptChart = null
  }
  if (budgetChart) {
    budgetChart.dispose()
    budgetChart = null
  }
}

/** 关闭对话框 */
function handleClose() {
  visible.value = false
}

/** 导出数据 */
function exportData() {
  // TODO: 实现数据导出功能
  console.log('导出统计数据')
}

/** 获取进度条颜色 */
function getProgressColor(percentage) {
  if (percentage >= 90) return '#f56c6c'
  if (percentage >= 70) return '#e6a23c'
  return '#67c23a'
}

// 组件卸载时销毁图表
onUnmounted(() => {
  destroyCharts()
})
</script>

<style scoped>
.statistics-container {
  min-height: 600px;
}

.statistics-overview {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-item {
  padding: 20px 0;
}

.stat-unit {
  font-size: 14px;
  color: #909399;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
}

.chart-container-large {
  height: 400px;
}

.data-table-section {
  margin-top: 20px;
}
</style>
