package com.research.workflow.exception;

/**
 * 工作流异常类
 * 
 * <AUTHOR>
 */
public class WorkflowException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误详情
     */
    private String errorDetail;

    public WorkflowException(String message) {
        super(message);
    }

    public WorkflowException(String message, Throwable cause) {
        super(message, cause);
    }

    public WorkflowException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public WorkflowException(String errorCode, String message, String errorDetail) {
        super(message);
        this.errorCode = errorCode;
        this.errorDetail = errorDetail;
    }

    public WorkflowException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorDetail() {
        return errorDetail;
    }

    public void setErrorDetail(String errorDetail) {
        this.errorDetail = errorDetail;
    }

    /**
     * 流程定义相关异常
     */
    public static class ProcessDefinitionException extends WorkflowException {
        public ProcessDefinitionException(String message) {
            super("PROCESS_DEFINITION_ERROR", message);
        }

        public ProcessDefinitionException(String message, Throwable cause) {
            super("PROCESS_DEFINITION_ERROR", message, cause);
        }
    }

    /**
     * 流程实例相关异常
     */
    public static class ProcessInstanceException extends WorkflowException {
        public ProcessInstanceException(String message) {
            super("PROCESS_INSTANCE_ERROR", message);
        }

        public ProcessInstanceException(String message, Throwable cause) {
            super("PROCESS_INSTANCE_ERROR", message, cause);
        }
    }

    /**
     * 任务相关异常
     */
    public static class TaskException extends WorkflowException {
        public TaskException(String message) {
            super("TASK_ERROR", message);
        }

        public TaskException(String message, Throwable cause) {
            super("TASK_ERROR", message, cause);
        }
    }

    /**
     * 流程部署相关异常
     */
    public static class DeploymentException extends WorkflowException {
        public DeploymentException(String message) {
            super("DEPLOYMENT_ERROR", message);
        }

        public DeploymentException(String message, Throwable cause) {
            super("DEPLOYMENT_ERROR", message, cause);
        }
    }

    /**
     * 权限相关异常
     */
    public static class PermissionException extends WorkflowException {
        public PermissionException(String message) {
            super("PERMISSION_ERROR", message);
        }

        public PermissionException(String message, Throwable cause) {
            super("PERMISSION_ERROR", message, cause);
        }
    }
}
