<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="消息标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入消息标题"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="消息类型" prop="messageType">
        <el-select v-model="queryParams.messageType" placeholder="消息类型" clearable>
          <el-option label="系统消息" value="1" />
          <el-option label="通知消息" value="2" />
          <el-option label="私信消息" value="3" />
          <el-option label="提醒消息" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否已读" prop="isRead">
        <el-select v-model="queryParams.isRead" placeholder="是否已读" clearable>
          <el-option label="未读" value="0" />
          <el-option label="已读" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="EditPen"
          @click="handleCompose"
        >写消息</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Check"
          :disabled="multiple"
          @click="handleMarkRead"
        >标记已读</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="CircleCheck"
          @click="handleMarkAllRead"
        >全部已读</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 标签页 -->
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <el-tab-pane name="inbox">
        <template #label>
          <span>
            收件箱
            <el-badge v-if="unreadCount > 0" :value="unreadCount" class="item" />
          </span>
        </template>
        <MessageTable 
          :data="messageList" 
          :loading="loading"
          type="inbox"
          @selection-change="handleSelectionChange"
          @view="handleView"
          @reply="handleReply"
          @forward="handleForward"
          @delete="handleDelete"
          @mark-read="handleMarkRead"
        />
      </el-tab-pane>
      <el-tab-pane label="发件箱" name="outbox">
        <MessageTable 
          :data="messageList" 
          :loading="loading"
          type="outbox"
          @selection-change="handleSelectionChange"
          @view="handleView"
          @delete="handleDelete"
        />
      </el-tab-pane>
    </el-tabs>
    
    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 写消息对话框 -->
    <el-dialog :title="composeTitle" v-model="composeVisible" width="800px" append-to-body>
      <ComposeForm 
        ref="composeFormRef"
        :form="composeForm"
        :type="composeType"
        @submit="handleComposeSubmit"
        @cancel="composeVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup name="Message" lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  listInboxMessage, 
  listOutboxMessage, 
  batchMarkMessageAsRead,
  markAllMessageAsRead,
  batchDelMessage,
  getUnreadMessageCount
} from '@/api/system/message'

// 导入子组件
import MessageTable from './components/MessageTable.vue'
import ComposeForm from './components/ComposeForm.vue'

const { proxy } = getCurrentInstance() as any

// 响应式数据
const messageList = ref([])
const composeVisible = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const multiple = ref(true)
const total = ref(0)
const activeTab = ref('inbox')
const unreadCount = ref(0)
const composeType = ref('new')
const composeTitle = computed(() => {
  const titles = {
    new: '写消息',
    reply: '回复消息',
    forward: '转发消息'
  }
  return titles[composeType.value] || '写消息'
})

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: undefined,
    messageType: undefined,
    isRead: undefined
  },
  composeForm: {}
})

const { queryParams, composeForm } = toRefs(data)

/** 查询消息列表 */
function getList() {
  loading.value = true
  
  const apiCall = activeTab.value === 'inbox' 
    ? listInboxMessage(queryParams.value)
    : listOutboxMessage(queryParams.value)
  
  apiCall.then(response => {
    messageList.value = response.rows
    total.value = response.total
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}

/** 获取未读消息数量 */
function getUnreadCount() {
  getUnreadMessageCount().then(response => {
    unreadCount.value = response.data
  })
}

/** 标签页切换 */
function handleTabChange(tabName: string) {
  activeTab.value = tabName
  queryParams.value.pageNum = 1
  getList()
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.messageId)
  multiple.value = !selection.length
}

/** 写消息 */
function handleCompose() {
  composeType.value = 'new'
  composeForm.value = {}
  composeVisible.value = true
}

/** 查看消息 */
function handleView(row: any) {
  proxy.$router.push({ path: '/system/message/detail/' + row.messageId })
}

/** 回复消息 */
function handleReply(row: any) {
  composeType.value = 'reply'
  composeForm.value = {
    originalMessageId: row.messageId,
    receiverId: row.senderId,
    receiverName: row.senderName,
    title: '回复：' + row.title
  }
  composeVisible.value = true
}

/** 转发消息 */
function handleForward(row: any) {
  composeType.value = 'forward'
  composeForm.value = {
    originalMessageId: row.messageId,
    title: '转发：' + row.title,
    content: row.content
  }
  composeVisible.value = true
}

/** 标记已读 */
function handleMarkRead(row?: any) {
  const messageIds = row ? [row.messageId] : ids.value
  
  if (messageIds.length === 0) {
    ElMessage.warning('请选择要标记的消息')
    return
  }
  
  batchMarkMessageAsRead(messageIds).then(() => {
    ElMessage.success('标记成功')
    getList()
    getUnreadCount()
  })
}

/** 全部标记已读 */
function handleMarkAllRead() {
  proxy.$modal.confirm('是否确认将所有未读消息标记为已读？').then(() => {
    return markAllMessageAsRead()
  }).then(() => {
    ElMessage.success('全部标记已读成功')
    getList()
    getUnreadCount()
  }).catch(() => {})
}

/** 删除消息 */
function handleDelete(row?: any) {
  const messageIds = row ? [row.messageId] : ids.value
  
  if (messageIds.length === 0) {
    ElMessage.warning('请选择要删除的消息')
    return
  }
  
  proxy.$modal.confirm('是否确认删除选中的消息？').then(() => {
    return batchDelMessage(messageIds)
  }).then(() => {
    ElMessage.success('删除成功')
    getList()
    getUnreadCount()
  }).catch(() => {})
}

/** 发送消息提交 */
function handleComposeSubmit() {
  composeVisible.value = false
  ElMessage.success('消息发送成功')
  if (activeTab.value === 'outbox') {
    getList()
  }
}

onMounted(() => {
  getList()
  getUnreadCount()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.item {
  margin-left: 5px;
}
</style>
