<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="流程名称" prop="processDefinitionName">
        <el-input
          v-model="queryParams.processDefinitionName"
          placeholder="请输入流程名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="流程Key" prop="processDefinitionKey">
        <el-input
          v-model="queryParams.processDefinitionKey"
          placeholder="请输入流程Key"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业务Key" prop="businessKey">
        <el-input
          v-model="queryParams.businessKey"
          placeholder="请输入业务Key"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="启动人" prop="startUserId">
        <el-input
          v-model="queryParams.startUserId"
          placeholder="请输入启动人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleStart"
          v-hasPermi="['workflow:processInstance:add']"
        >启动流程</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['workflow:processInstance:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="运行中" name="running">
        <el-table v-loading="loading" :data="instanceList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="流程名称" align="center" prop="processDefinitionName" :show-overflow-tooltip="true" />
          <el-table-column label="流程Key" align="center" prop="processDefinitionKey" :show-overflow-tooltip="true" />
          <el-table-column label="版本" align="center" prop="processDefinitionVersion" width="80">
            <template #default="scope">
              <el-tag>v{{ scope.row.processDefinitionVersion }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="业务Key" align="center" prop="businessKey" :show-overflow-tooltip="true" />
          <el-table-column label="启动人" align="center" prop="startUserId" />
          <el-table-column label="状态" align="center" prop="suspended" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.suspended ? 'danger' : 'success'">
                {{ scope.row.suspended ? '挂起' : '运行中' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="启动时间" align="center" prop="startTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="250">
            <template #default="scope">
              <el-button link type="primary" @click="handleView(scope.row)">
                <el-icon><View /></el-icon>查看
              </el-button>
              <el-button link type="primary" @click="handleVariables(scope.row)">
                <el-icon><Setting /></el-icon>变量
              </el-button>
              <el-button 
                link 
                :type="scope.row.suspended ? 'success' : 'warning'" 
                @click="handleUpdateState(scope.row)"
                v-hasPermi="['workflow:processInstance:edit']"
              >
                <el-icon><Switch /></el-icon>{{ scope.row.suspended ? '激活' : '挂起' }}
              </el-button>
              <el-button 
                link 
                type="danger" 
                @click="handleDelete(scope.row)"
                v-hasPermi="['workflow:processInstance:remove']"
              >
                <el-icon><Delete /></el-icon>删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      
      <el-tab-pane label="已完成" name="history">
        <el-table v-loading="loading" :data="instanceList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="流程名称" align="center" prop="processDefinitionName" :show-overflow-tooltip="true" />
          <el-table-column label="流程Key" align="center" prop="processDefinitionKey" :show-overflow-tooltip="true" />
          <el-table-column label="版本" align="center" prop="processDefinitionVersion" width="80">
            <template #default="scope">
              <el-tag>v{{ scope.row.processDefinitionVersion }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="业务Key" align="center" prop="businessKey" :show-overflow-tooltip="true" />
          <el-table-column label="启动人" align="center" prop="startUserId" />
          <el-table-column label="状态" align="center" prop="ended" width="100">
            <template #default="scope">
              <el-tag type="info">已完成</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="启动时间" align="center" prop="startTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="结束时间" align="center" prop="endTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="持续时间" align="center" prop="durationInMillis" width="120">
            <template #default="scope">
              <span>{{ formatDuration(scope.row.durationInMillis) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
            <template #default="scope">
              <el-button link type="primary" @click="handleView(scope.row)">
                <el-icon><View /></el-icon>查看
              </el-button>
              <el-button link type="primary" @click="handleVariables(scope.row)">
                <el-icon><Setting /></el-icon>变量
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 启动流程对话框 -->
    <el-dialog title="启动流程" v-model="start.open" width="600px" append-to-body>
      <el-form ref="startRef" :model="start.form" :rules="start.rules" label-width="100px">
        <el-form-item label="流程定义" prop="processDefinitionKey">
          <el-select v-model="start.form.processDefinitionKey" placeholder="请选择流程定义" style="width: 100%">
            <el-option
              v-for="item in processDefinitions"
              :key="item.key"
              :label="item.name"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="业务Key" prop="businessKey">
          <el-input v-model="start.form.businessKey" placeholder="请输入业务Key" />
        </el-form-item>
        <el-form-item label="流程变量">
          <el-input
            v-model="start.form.variablesJson"
            type="textarea"
            :rows="4"
            placeholder="请输入JSON格式的流程变量，如：{&quot;key1&quot;: &quot;value1&quot;, &quot;key2&quot;: &quot;value2&quot;}"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitStartForm">确 定</el-button>
          <el-button @click="cancelStart">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 流程变量对话框 -->
    <el-dialog title="流程变量" v-model="variables.open" width="600px" append-to-body>
      <el-table :data="variables.list" style="width: 100%">
        <el-table-column label="变量名" prop="name" />
        <el-table-column label="变量值" prop="value" />
        <el-table-column label="类型" prop="type" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup name="ProcessInstance" lang="ts">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { 
  listRunningProcessInstance,
  listHistoryProcessInstance,
  getProcessInstance,
  startProcessInstance,
  delProcessInstance,
  suspendProcessInstance,
  activateProcessInstance,
  getProcessVariables
} from '@/api/workflow/processInstance'
import { listProcessDefinition } from '@/api/workflow/processDefinition'

const { proxy } = getCurrentInstance() as any
const { parseTime } = proxy

const instanceList = ref([])
const processDefinitions = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const activeTab = ref('running')

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  processDefinitionName: '',
  processDefinitionKey: '',
  businessKey: '',
  startUserId: ''
})

// 启动流程参数
const start = reactive({
  open: false,
  form: {
    processDefinitionKey: '',
    businessKey: '',
    variablesJson: ''
  },
  rules: {
    processDefinitionKey: [
      { required: true, message: '流程定义不能为空', trigger: 'change' }
    ]
  }
})

// 流程变量参数
const variables = reactive({
  open: false,
  list: []
})

/** 查询流程实例列表 */
function getList() {
  loading.value = true
  const queryFunc = activeTab.value === 'running' ? listRunningProcessInstance : listHistoryProcessInstance
  queryFunc(queryParams).then(response => {
    instanceList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef')
  handleQuery()
}

/** 标签页切换 */
function handleTabClick() {
  getList()
}

// 多选框选中数据
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 启动流程按钮操作 */
function handleStart() {
  // 获取流程定义列表
  listProcessDefinition({}).then(response => {
    processDefinitions.value = response.rows
    start.open = true
  })
}

/** 查看按钮操作 */
function handleView(row: any) {
  proxy.$modal.msgSuccess('查看功能开发中...')
}

/** 流程变量按钮操作 */
function handleVariables(row: any) {
  getProcessVariables(row.id).then(response => {
    variables.list = Object.keys(response.data).map(key => ({
      name: key,
      value: response.data[key],
      type: typeof response.data[key]
    }))
    variables.open = true
  })
}

/** 激活/挂起按钮操作 */
function handleUpdateState(row: any) {
  const text = row.suspended ? '激活' : '挂起'
  const action = row.suspended ? activateProcessInstance : suspendProcessInstance
  proxy.$modal.confirm('确认要"' + text + '""' + row.processDefinitionName + '"流程实例吗？').then(() => {
    return action(row.id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess(text + '成功')
  }).catch(() => {})
}

/** 删除按钮操作 */
function handleDelete(row?: any) {
  const instanceIds = row?.id || ids.value
  proxy.$modal.confirm('是否确认删除流程实例编号为"' + instanceIds + '"的数据项？').then(() => {
    return delProcessInstance(instanceIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess('删除成功')
  }).catch(() => {})
}

/** 提交启动表单 */
function submitStartForm() {
  proxy.$refs['startRef'].validate((valid: boolean) => {
    if (valid) {
      const variables = start.form.variablesJson ? JSON.parse(start.form.variablesJson) : {}
      const data = {
        processDefinitionKey: start.form.processDefinitionKey,
        businessKey: start.form.businessKey,
        variables: variables
      }
      startProcessInstance(data).then(response => {
        proxy.$modal.msgSuccess('启动成功')
        start.open = false
        getList()
      })
    }
  })
}

/** 取消启动 */
function cancelStart() {
  start.open = false
  proxy.resetForm('startRef')
}

/** 格式化持续时间 */
function formatDuration(milliseconds: number) {
  if (!milliseconds) return '-'
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) return `${days}天${hours % 24}小时`
  if (hours > 0) return `${hours}小时${minutes % 60}分钟`
  if (minutes > 0) return `${minutes}分钟${seconds % 60}秒`
  return `${seconds}秒`
}

onMounted(() => {
  getList()
})
</script>
