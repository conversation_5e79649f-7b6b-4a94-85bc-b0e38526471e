import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'

// 引入Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
// 暂时注释图标插件
// import installIcons from '@/plugins/icons'

// 引入全局样式
import '@/styles/index.scss'

// 引入权限控制
import '@/permission'

// 引入权限指令
import directive from '@/directive/permission'

// 引入权限工具
import '@/utils/permission'

// 引入进度条
import 'nprogress/nprogress.css'

const app = createApp(App)

// 暂时注释图标注册
// installIcons(app)

// 注册权限指令
app.directive('hasPermi', directive.hasPermi)
app.directive('hasRole', directive.hasRole)

// 使用插件
app.use(createPinia())
app.use(router)
app.use(ElementPlus)

app.mount('#app')
