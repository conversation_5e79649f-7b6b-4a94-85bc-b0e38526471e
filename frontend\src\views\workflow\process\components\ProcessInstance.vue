<template>
  <div class="process-instance">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="流程名称" prop="processName">
        <el-input
          v-model="queryParams.processName"
          placeholder="请输入流程名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发起人" prop="startUser">
        <el-input
          v-model="queryParams.startUser"
          placeholder="请输入发起人"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="实例状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="运行中" value="ACTIVE" />
          <el-option label="已完成" value="COMPLETED" />
          <el-option label="已挂起" value="SUSPENDED" />
          <el-option label="已终止" value="TERMINATED" />
        </el-select>
      </el-form-item>
      <el-form-item label="开始时间">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleBatchTerminate"
        >批量终止</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
        >导出数据</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="instanceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="80" align="center" />
      <el-table-column label="实例ID" prop="instanceId" width="120" :show-overflow-tooltip="true" />
      <el-table-column label="流程名称" prop="processName" :show-overflow-tooltip="true" />
      <el-table-column label="业务标题" prop="businessTitle" :show-overflow-tooltip="true" />
      <el-table-column label="发起人" prop="startUser" width="100" align="center" />
      <el-table-column label="当前节点" prop="currentTask" width="120" :show-overflow-tooltip="true" />
      <el-table-column label="当前处理人" prop="currentAssignee" width="120" align="center" />
      <el-table-column label="状态" prop="status" width="100" align="center">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="优先级" prop="priority" width="80" align="center">
        <template #default="scope">
          <el-tag :type="getPriorityType(scope.row.priority)" size="small">
            {{ getPriorityText(scope.row.priority) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" prop="startTime" width="160" align="center">
        <template #default="scope">
          {{ parseTime(scope.row.startTime) }}
        </template>
      </el-table-column>
      <el-table-column label="耗时" prop="duration" width="100" align="center">
        <template #default="scope">
          <span :class="getDurationClass(scope.row.duration)">
            {{ formatDuration(scope.row.duration) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleViewDetail(scope.row)">
            <el-icon><View /></el-icon>详情
          </el-button>
          <el-button link type="info" @click="handleViewDiagram(scope.row)">
            <el-icon><Histogram /></el-icon>流程图
          </el-button>
          <el-button link type="success" @click="handleViewHistory(scope.row)">
            <el-icon><Clock /></el-icon>历史
          </el-button>
          <el-button 
            v-if="scope.row.status === 'ACTIVE'" 
            link 
            type="warning" 
            @click="handleSuspend(scope.row)"
          >
            <el-icon><VideoPause /></el-icon>挂起
          </el-button>
          <el-button 
            v-if="scope.row.status === 'SUSPENDED'" 
            link 
            type="success" 
            @click="handleActivate(scope.row)"
          >
            <el-icon><VideoPlay /></el-icon>激活
          </el-button>
          <el-button 
            v-if="['ACTIVE', 'SUSPENDED'].includes(scope.row.status)" 
            link 
            type="danger" 
            @click="handleTerminate(scope.row)"
          >
            <el-icon><Close /></el-icon>终止
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 实例详情对话框 -->
    <el-dialog v-model="detailVisible" title="流程实例详情" width="80%" draggable>
      <div v-if="currentInstance">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="实例ID">{{ currentInstance.instanceId }}</el-descriptions-item>
          <el-descriptions-item label="流程名称">{{ currentInstance.processName }}</el-descriptions-item>
          <el-descriptions-item label="业务标题">{{ currentInstance.businessTitle }}</el-descriptions-item>
          <el-descriptions-item label="发起人">{{ currentInstance.startUser }}</el-descriptions-item>
          <el-descriptions-item label="当前节点">{{ currentInstance.currentTask }}</el-descriptions-item>
          <el-descriptions-item label="当前处理人">{{ currentInstance.currentAssignee }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentInstance.status)">
              {{ getStatusText(currentInstance.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityType(currentInstance.priority)">
              {{ getPriorityText(currentInstance.priority) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ parseTime(currentInstance.startTime) }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{ parseTime(currentInstance.endTime) }}</el-descriptions-item>
          <el-descriptions-item label="耗时" span="2">{{ formatDuration(currentInstance.duration) }}</el-descriptions-item>
        </el-descriptions>
        
        <!-- 流程变量 -->
        <el-divider content-position="left">流程变量</el-divider>
        <el-table :data="currentInstance.variables" size="small">
          <el-table-column label="变量名" prop="name" />
          <el-table-column label="变量值" prop="value" />
          <el-table-column label="类型" prop="type" width="100" />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { parseTime } from '@/utils/common'
import Pagination from '@/components/Pagination/index.vue'
import RightToolbar from '@/components/RightToolbar/index.vue'
import { listInstances, suspendInstance, activateInstance, terminateInstance, batchTerminateInstances } from '@/api/workflow/process'

// 数据
const loading = ref(false)
const showSearch = ref(true)
const instanceList = ref([])
const total = ref(0)
const multiple = ref(true)
const ids = ref([])
const dateRange = ref([])
const detailVisible = ref(false)
const currentInstance = ref(null)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  processName: '',
  startUser: '',
  status: ''
})

// 获取流程实例列表
const getList = () => {
  loading.value = true
  listInstances(queryParams).then(response => {
    instanceList.value = response.rows || []
    total.value = response.total || 0
    loading.value = false
    emitDataLoaded()
  }).catch(() => {
    loading.value = false
    ElMessage.error('获取流程实例列表失败')
    emitDataLoaded()
  })
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置
const resetQuery = () => {
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    processName: '',
    startUser: '',
    status: ''
  })
  dateRange.value = []
  getList()
}

// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
  ids.value = selection.map(item => item.instanceId)
  multiple.value = !selection.length
}

// 获取状态类型
const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    ACTIVE: 'success',
    COMPLETED: 'info',
    SUSPENDED: 'warning',
    TERMINATED: 'danger'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    ACTIVE: '运行中',
    COMPLETED: '已完成',
    SUSPENDED: '已挂起',
    TERMINATED: '已终止'
  }
  return texts[status] || '未知'
}

// 获取优先级类型
const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    LOW: 'info',
    NORMAL: 'success',
    HIGH: 'warning',
    URGENT: 'danger'
  }
  return types[priority] || 'info'
}

// 获取优先级文本
const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    LOW: '低',
    NORMAL: '普通',
    HIGH: '高',
    URGENT: '紧急'
  }
  return texts[priority] || '未知'
}

// 格式化耗时
const formatDuration = (duration: number) => {
  if (!duration) return '-'
  const days = Math.floor(duration / (24 * 60 * 60 * 1000))
  const hours = Math.floor((duration % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000))
  const minutes = Math.floor((duration % (60 * 60 * 1000)) / (60 * 1000))
  
  if (days > 0) return `${days}天${hours}小时`
  if (hours > 0) return `${hours}小时${minutes}分钟`
  return `${minutes}分钟`
}

// 获取耗时样式类
const getDurationClass = (duration: number) => {
  if (!duration) return ''
  const days = duration / (24 * 60 * 60 * 1000)
  if (days > 7) return 'text-danger'
  if (days > 3) return 'text-warning'
  return 'text-success'
}

// 查看详情
const handleViewDetail = (row: any) => {
  currentInstance.value = row
  detailVisible.value = true
}

// 查看流程图
const handleViewDiagram = (row: any) => {
  ElMessage.info(`查看流程图: ${row.instanceId}`)
}

// 查看历史
const handleViewHistory = (row: any) => {
  ElMessage.info(`查看历史: ${row.instanceId}`)
}

// 挂起实例
const handleSuspend = (row: any) => {
  ElMessageBox.confirm(`确认挂起流程实例"${row.instanceId}"吗？`, '提示', {
    type: 'warning'
  }).then(() => {
    row.status = 'SUSPENDED'
    ElMessage.success('挂起成功')
  })
}

// 激活实例
const handleActivate = (row: any) => {
  ElMessageBox.confirm(`确认激活流程实例"${row.instanceId}"吗？`, '提示', {
    type: 'warning'
  }).then(() => {
    row.status = 'ACTIVE'
    ElMessage.success('激活成功')
  })
}

// 终止实例
const handleTerminate = (row: any) => {
  ElMessageBox.confirm(`确认终止流程实例"${row.instanceId}"吗？`, '提示', {
    type: 'warning'
  }).then(() => {
    row.status = 'TERMINATED'
    ElMessage.success('终止成功')
  })
}

// 批量终止
const handleBatchTerminate = () => {
  ElMessageBox.confirm('确认终止选中的流程实例吗？', '提示', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('批量终止成功')
    getList()
  })
}

// 导出数据
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 刷新数据
const refreshData = () => {
  getList()
}

// 页面挂载
onMounted(() => {
  getList()
})

// 定义emit
const emit = defineEmits(['data-loaded'])

// 暴露方法
defineExpose({
  refreshData
})

// 在获取数据后触发事件
const emitDataLoaded = () => {
  emit('data-loaded', total.value)
}
</script>

<style scoped>
.process-instance {
  height: 100%;
  background: #fafbfc;
  border-radius: 8px;
  padding: 20px;
}

.mb8 {
  margin-bottom: 16px;
}

.text-success {
  color: #10b981;
  font-weight: 500;
}

.text-warning {
  color: #f59e0b;
  font-weight: 500;
}

.text-danger {
  color: #ef4444;
  font-weight: 500;
}

/* 继承父组件的样式 */
:deep(.el-form--inline .el-form-item) {
  margin-right: 20px;
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

:deep(.el-button--primary:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

:deep(.el-button--warning) {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

:deep(.el-table__header) {
  background: #f8fafc;
}

:deep(.el-table th) {
  background: #f8fafc !important;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f3f4f6;
}

:deep(.el-table__row:hover) {
  background: #f8fafc;
}

:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
  border: none;
  padding: 4px 12px;
}

:deep(.el-tag--success) {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
}

:deep(.el-tag--info) {
  background: rgba(107, 114, 128, 0.1);
  color: #4b5563;
}

:deep(.el-tag--warning) {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
}

:deep(.el-tag--danger) {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

:deep(.el-dialog__header) {
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  border-radius: 12px 12px 0 0;
  padding: 20px 24px;
}

:deep(.el-dialog__title) {
  font-weight: 600;
  color: #1f2937;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-descriptions) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-descriptions__header) {
  background: #f8fafc;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #374151;
}

:deep(.el-descriptions__content) {
  color: #6b7280;
}

/* 分页样式 */
:deep(.el-pagination) {
  margin-top: 20px;
  justify-content: center;
}

:deep(.el-pagination .el-pager li) {
  border-radius: 6px;
  margin: 0 2px;
}

:deep(.el-pagination .el-pager li.is-active) {
  background: #3b82f6;
  color: white;
}
</style>
