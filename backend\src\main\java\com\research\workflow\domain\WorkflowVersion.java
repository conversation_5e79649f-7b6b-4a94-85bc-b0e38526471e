package com.research.workflow.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.research.common.annotation.Excel;
import com.research.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 工作流版本对象 workflow_version
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class WorkflowVersion extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 版本ID */
    private String versionId;

    /** 流程定义Key */
    @Excel(name = "流程定义Key")
    private String processDefinitionKey;

    /** 流程定义ID */
    @Excel(name = "流程定义ID")
    private String processDefinitionId;

    /** 版本标签 */
    @Excel(name = "版本标签")
    private String versionTag;

    /** 版本名称 */
    @Excel(name = "版本名称")
    private String versionName;

    /** 版本描述 */
    @Excel(name = "版本描述")
    private String versionDescription;

    /** 发布状态（0草稿 1已发布 2已停用 3已归档） */
    @Excel(name = "发布状态", readConverterExp = "0=草稿,1=已发布,2=已停用,3=已归档")
    private Integer publishStatus;

    /** 发布策略（0全量发布 1灰度发布 2A/B测试） */
    @Excel(name = "发布策略", readConverterExp = "0=全量发布,1=灰度发布,2=A/B测试")
    private Integer publishStrategy;

    /** 流量比例（灰度发布时使用） */
    @Excel(name = "流量比例")
    private Integer trafficRatio;

    /** 目标用户（A/B测试时使用） */
    private String targetUsers;

    /** 目标部门（部门路由时使用） */
    private String targetDepartments;

    /** 是否默认版本 */
    @Excel(name = "是否默认版本", readConverterExp = "0=否,1=是")
    private Boolean isDefault;

    /** 运行中实例数 */
    @Excel(name = "运行中实例数")
    private Integer runningInstanceCount;

    /** 已完成实例数 */
    @Excel(name = "已完成实例数")
    private Integer completedInstanceCount;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /** 停用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "停用时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date deprecateTime;

    /** 版本配置JSON */
    private String versionConfig;

    /** 路由规则JSON */
    private String routeRules;

    /** 版本号 */
    @Excel(name = "版本号")
    private Integer version;

    /** 父版本ID */
    private String parentVersionId;

    /** 分支类型（0主分支 1特性分支 2修复分支） */
    @Excel(name = "分支类型", readConverterExp = "0=主分支,1=特性分支,2=修复分支")
    private Integer branchType;

    /** 合并状态（0未合并 1已合并 2冲突） */
    @Excel(name = "合并状态", readConverterExp = "0=未合并,1=已合并,2=冲突")
    private Integer mergeStatus;

    public void setVersionId(String versionId) {
        this.versionId = versionId;
    }

    public String getVersionId() {
        return versionId;
    }

    public void setProcessDefinitionKey(String processDefinitionKey) {
        this.processDefinitionKey = processDefinitionKey;
    }

    public String getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    public void setProcessDefinitionId(String processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }

    public String getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setVersionTag(String versionTag) {
        this.versionTag = versionTag;
    }

    public String getVersionTag() {
        return versionTag;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionDescription(String versionDescription) {
        this.versionDescription = versionDescription;
    }

    public String getVersionDescription() {
        return versionDescription;
    }

    public void setPublishStatus(Integer publishStatus) {
        this.publishStatus = publishStatus;
    }

    public Integer getPublishStatus() {
        return publishStatus;
    }

    public void setPublishStrategy(Integer publishStrategy) {
        this.publishStrategy = publishStrategy;
    }

    public Integer getPublishStrategy() {
        return publishStrategy;
    }

    public void setTrafficRatio(Integer trafficRatio) {
        this.trafficRatio = trafficRatio;
    }

    public Integer getTrafficRatio() {
        return trafficRatio;
    }

    public void setTargetUsers(String targetUsers) {
        this.targetUsers = targetUsers;
    }

    public String getTargetUsers() {
        return targetUsers;
    }

    public void setTargetDepartments(String targetDepartments) {
        this.targetDepartments = targetDepartments;
    }

    public String getTargetDepartments() {
        return targetDepartments;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setRunningInstanceCount(Integer runningInstanceCount) {
        this.runningInstanceCount = runningInstanceCount;
    }

    public Integer getRunningInstanceCount() {
        return runningInstanceCount;
    }

    public void setCompletedInstanceCount(Integer completedInstanceCount) {
        this.completedInstanceCount = completedInstanceCount;
    }

    public Integer getCompletedInstanceCount() {
        return completedInstanceCount;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setDeprecateTime(Date deprecateTime) {
        this.deprecateTime = deprecateTime;
    }

    public Date getDeprecateTime() {
        return deprecateTime;
    }

    public void setVersionConfig(String versionConfig) {
        this.versionConfig = versionConfig;
    }

    public String getVersionConfig() {
        return versionConfig;
    }

    public void setRouteRules(String routeRules) {
        this.routeRules = routeRules;
    }

    public String getRouteRules() {
        return routeRules;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getVersion() {
        return version;
    }

    public void setParentVersionId(String parentVersionId) {
        this.parentVersionId = parentVersionId;
    }

    public String getParentVersionId() {
        return parentVersionId;
    }

    public void setBranchType(Integer branchType) {
        this.branchType = branchType;
    }

    public Integer getBranchType() {
        return branchType;
    }

    public void setMergeStatus(Integer mergeStatus) {
        this.mergeStatus = mergeStatus;
    }

    public Integer getMergeStatus() {
        return mergeStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("versionId", getVersionId())
            .append("processDefinitionKey", getProcessDefinitionKey())
            .append("processDefinitionId", getProcessDefinitionId())
            .append("versionTag", getVersionTag())
            .append("versionName", getVersionName())
            .append("versionDescription", getVersionDescription())
            .append("publishStatus", getPublishStatus())
            .append("publishStrategy", getPublishStrategy())
            .append("trafficRatio", getTrafficRatio())
            .append("targetUsers", getTargetUsers())
            .append("targetDepartments", getTargetDepartments())
            .append("isDefault", getIsDefault())
            .append("runningInstanceCount", getRunningInstanceCount())
            .append("completedInstanceCount", getCompletedInstanceCount())
            .append("publishTime", getPublishTime())
            .append("deprecateTime", getDeprecateTime())
            .append("versionConfig", getVersionConfig())
            .append("routeRules", getRouteRules())
            .append("version", getVersion())
            .append("parentVersionId", getParentVersionId())
            .append("branchType", getBranchType())
            .append("mergeStatus", getMergeStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
