-- 工作流任务表
CREATE TABLE IF NOT EXISTS `workflow_task` (
  `id` varchar(64) NOT NULL COMMENT '任务ID',
  `name` varchar(200) NOT NULL COMMENT '任务名称',
  `description` varchar(500) DEFAULT NULL COMMENT '任务描述',
  `task_key` varchar(100) DEFAULT NULL COMMENT '任务Key',
  `process_instance_id` varchar(64) NOT NULL COMMENT '流程实例ID',
  `process_definition_id` varchar(64) NOT NULL COMMENT '流程定义ID',
  `process_definition_key` varchar(100) NOT NULL COMMENT '流程定义Key',
  `process_definition_name` varchar(200) DEFAULT NULL COMMENT '流程定义名称',
  `assignee` varchar(64) DEFAULT NULL COMMENT '任务负责人',
  `assignee_name` varchar(100) DEFAULT NULL COMMENT '任务负责人姓名',
  `candidate_users` text DEFAULT NULL COMMENT '候选用户',
  `candidate_groups` text DEFAULT NULL COMMENT '候选组',
  `status` varchar(20) NOT NULL DEFAULT '0' COMMENT '任务状态（0待处理 1处理中 2已完成 3已取消）',
  `priority` int(11) DEFAULT '50' COMMENT '任务优先级',
  `due_date` datetime DEFAULT NULL COMMENT '任务到期时间',
  `follow_up_date` datetime DEFAULT NULL COMMENT '任务跟进时间',
  `category` varchar(50) DEFAULT NULL COMMENT '任务分类',
  `form_key` varchar(200) DEFAULT NULL COMMENT '表单Key',
  `business_key` varchar(100) DEFAULT NULL COMMENT '业务Key',
  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户ID',
  `start_time` datetime NOT NULL COMMENT '任务开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '任务结束时间',
  `duration` bigint(20) DEFAULT NULL COMMENT '任务持续时间(毫秒)',
  `delete_reason` varchar(500) DEFAULT NULL COMMENT '删除原因',
  `parent_task_id` varchar(64) DEFAULT NULL COMMENT '父任务ID',
  `suspended` tinyint(1) DEFAULT '0' COMMENT '是否挂起',
  `executor_id` bigint(20) DEFAULT NULL COMMENT '任务执行人ID',
  `executor_name` varchar(100) DEFAULT NULL COMMENT '任务执行人姓名',
  `delegate_id` bigint(20) DEFAULT NULL COMMENT '任务委派人ID',
  `delegate_name` varchar(100) DEFAULT NULL COMMENT '任务委派人姓名',
  `owner_id` bigint(20) DEFAULT NULL COMMENT '任务所有者ID',
  `owner_name` varchar(100) DEFAULT NULL COMMENT '任务所有者姓名',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_process_instance_id` (`process_instance_id`),
  KEY `idx_process_definition_id` (`process_definition_id`),
  KEY `idx_process_definition_key` (`process_definition_key`),
  KEY `idx_assignee` (`assignee`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_business_key` (`business_key`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_executor_id` (`executor_id`),
  KEY `idx_owner_id` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流任务表';

-- 任务历史表
CREATE TABLE IF NOT EXISTS `workflow_task_history` (
  `id` varchar(64) NOT NULL COMMENT '历史记录ID',
  `task_id` varchar(64) NOT NULL COMMENT '任务ID',
  `task_name` varchar(200) NOT NULL COMMENT '任务名称',
  `process_instance_id` varchar(64) NOT NULL COMMENT '流程实例ID',
  `process_definition_id` varchar(64) NOT NULL COMMENT '流程定义ID',
  `process_definition_key` varchar(100) NOT NULL COMMENT '流程定义Key',
  `assignee` varchar(64) DEFAULT NULL COMMENT '任务负责人',
  `assignee_name` varchar(100) DEFAULT NULL COMMENT '任务负责人姓名',
  `start_time` datetime NOT NULL COMMENT '任务开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '任务结束时间',
  `duration` bigint(20) DEFAULT NULL COMMENT '任务持续时间(毫秒)',
  `delete_reason` varchar(500) DEFAULT NULL COMMENT '删除原因',
  `task_definition_key` varchar(100) DEFAULT NULL COMMENT '任务定义Key',
  `form_key` varchar(200) DEFAULT NULL COMMENT '表单Key',
  `priority` int(11) DEFAULT '50' COMMENT '任务优先级',
  `due_date` datetime DEFAULT NULL COMMENT '任务到期时间',
  `category` varchar(50) DEFAULT NULL COMMENT '任务分类',
  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_process_instance_id` (`process_instance_id`),
  KEY `idx_process_definition_key` (`process_definition_key`),
  KEY `idx_assignee` (`assignee`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流任务历史表';

-- 任务变量表
CREATE TABLE IF NOT EXISTS `workflow_task_variable` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '变量ID',
  `task_id` varchar(64) NOT NULL COMMENT '任务ID',
  `variable_name` varchar(100) NOT NULL COMMENT '变量名',
  `variable_value` longtext COMMENT '变量值',
  `variable_type` varchar(50) DEFAULT 'string' COMMENT '变量类型',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_variable` (`task_id`, `variable_name`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_variable_name` (`variable_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流任务变量表';

-- 插入示例任务数据
INSERT INTO `workflow_task` (`id`, `name`, `description`, `task_key`, `process_instance_id`, `process_definition_id`, `process_definition_key`, `process_definition_name`, `assignee`, `assignee_name`, `status`, `priority`, `start_time`, `business_key`, `category`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('task_001', '项目申请审批', '审批横向项目申请', 'approve_project', 'PI001', 'def_001', 'project_approval', '项目申请流程', '2', '李经理', '0', 80, NOW(), 'PROJECT_001', 'approval', 'admin', NOW(), 'admin', NOW(), '项目申请审批任务'),
('task_002', '合同审核', '审核合作协议合同', 'review_contract', 'PI002', 'def_004', 'contract_process', '合同审批流程', '3', '王主任', '0', 70, NOW(), 'CONTRACT_001', 'review', 'admin', NOW(), 'admin', NOW(), '合同审核任务'),
('task_003', '预算审批', '审批项目预算申请', 'approve_budget', 'PI003', 'def_002', 'budget_approval', '预算审批流程', '2', '李经理', '0', 60, NOW(), 'BUDGET_001', 'approval', 'admin', NOW(), 'admin', NOW(), '预算审批任务'),
('task_004', '部门审批', '部门级别审批任务', 'dept_approve', 'PI004', 'def_003', 'dept_approval', '部门审批流程', NULL, NULL, '0', 50, NOW(), 'DEPT_001', 'approval', 'admin', NOW(), 'admin', NOW(), '部门审批任务');

-- 插入候选任务（无具体负责人）
UPDATE `workflow_task` SET `candidate_users` = '2,3,4', `candidate_groups` = 'dept_manager,project_manager' WHERE `id` = 'task_004';

-- 插入任务变量示例数据
INSERT INTO `workflow_task_variable` (`task_id`, `variable_name`, `variable_value`, `variable_type`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
('task_001', 'applicant', '张三', 'string', 'admin', NOW(), 'admin', NOW()),
('task_001', 'amount', '100000', 'number', 'admin', NOW(), 'admin', NOW()),
('task_001', 'department', '研发部', 'string', 'admin', NOW(), 'admin', NOW()),
('task_001', 'approved', 'false', 'boolean', 'admin', NOW(), 'admin', NOW()),
('task_002', 'contractType', '技术服务合同', 'string', 'admin', NOW(), 'admin', NOW()),
('task_002', 'contractAmount', '50000', 'number', 'admin', NOW(), 'admin', NOW()),
('task_002', 'partner', 'ABC科技公司', 'string', 'admin', NOW(), 'admin', NOW()),
('task_003', 'budgetType', '研发预算', 'string', 'admin', NOW(), 'admin', NOW()),
('task_003', 'requestAmount', '80000', 'number', 'admin', NOW(), 'admin', NOW()),
('task_003', 'purpose', '新产品开发', 'string', 'admin', NOW(), 'admin', NOW());

-- 插入历史任务示例数据
INSERT INTO `workflow_task_history` (`id`, `task_id`, `task_name`, `process_instance_id`, `process_definition_id`, `process_definition_key`, `assignee`, `assignee_name`, `start_time`, `end_time`, `duration`, `task_definition_key`, `priority`, `category`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('hist_001', 'task_history_001', '已完成的项目审批', 'PI_HIST_001', 'def_001', 'project_approval', '2', '李经理', DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), 86400000, 'approve_project', 50, 'approval', 'admin', NOW(), 'admin', NOW(), '已完成的项目审批任务'),
('hist_002', 'task_history_002', '已完成的合同审核', 'PI_HIST_002', 'def_004', 'contract_process', '3', '王主任', DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY), 86400000, 'review_contract', 70, 'review', 'admin', NOW(), 'admin', NOW(), '已完成的合同审核任务');

-- 创建索引以提高查询性能
CREATE INDEX idx_task_assignee_status ON workflow_task(assignee, status);
CREATE INDEX idx_task_priority_start_time ON workflow_task(priority, start_time);
CREATE INDEX idx_task_category_status ON workflow_task(category, status);
CREATE INDEX idx_task_history_assignee_end_time ON workflow_task_history(assignee, end_time);
CREATE INDEX idx_task_history_process_end_time ON workflow_task_history(process_definition_key, end_time);
