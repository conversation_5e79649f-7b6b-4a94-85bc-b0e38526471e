<template>
  <el-table v-loading="loading" :data="data" @selection-change="handleSelectionChange">
    <el-table-column type="selection" width="55" align="center" />
    <el-table-column label="序号" type="index" width="80" align="center" />
    <el-table-column label="消息标题" prop="title" :show-overflow-tooltip="true">
      <template #default="scope">
        <div class="message-title">
          <el-icon v-if="scope.row.isRead === '0'" class="unread-dot"><InfoFilled /></el-icon>
          <span class="message-type-badge" :class="getMessageTypeClass(scope.row.messageType)">
            {{ getMessageTypeText(scope.row.messageType) }}
          </span>
          <el-tag v-if="scope.row.priority === '3' || scope.row.priority === '4'" 
                  :type="scope.row.priority === '4' ? 'danger' : 'warning'" 
                  size="small">
            {{ scope.row.priority === '4' ? '紧急' : '重要' }}
          </el-tag>
          <span class="title-text" @click="$emit('view', scope.row)">{{ scope.row.title }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column v-if="type === 'inbox'" label="发送人" align="center" prop="senderName" width="120" />
    <el-table-column v-if="type === 'outbox'" label="接收人" align="center" prop="receiverName" width="120" />
    <el-table-column label="消息类型" align="center" prop="messageType" width="100">
      <template #default="scope">
        <el-tag :type="getMessageTypeTagType(scope.row.messageType)" size="small">
          {{ getMessageTypeText(scope.row.messageType) }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column v-if="type === 'inbox'" label="状态" align="center" prop="isRead" width="80">
      <template #default="scope">
        <el-tag :type="scope.row.isRead === '1' ? 'success' : 'warning'" size="small">
          {{ scope.row.isRead === '1' ? '已读' : '未读' }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column label="发送时间" align="center" prop="createTime" width="180">
      <template #default="scope">
        <span>{{ formatTime(scope.row.createTime) }}</span>
      </template>
    </el-table-column>
    <el-table-column v-if="type === 'inbox'" label="阅读时间" align="center" prop="readTime" width="180">
      <template #default="scope">
        <span>{{ formatTime(scope.row.readTime) || '-' }}</span>
      </template>
    </el-table-column>
    <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="250">
      <template #default="scope">
        <el-button link type="primary" @click="$emit('view', scope.row)">
          <el-icon><View /></el-icon>查看
        </el-button>
        <el-button 
          v-if="type === 'inbox'" 
          link 
          type="success" 
          @click="$emit('reply', scope.row)"
        >
          <el-icon><ChatDotRound /></el-icon>回复
        </el-button>
        <el-button 
          v-if="type === 'inbox'" 
          link 
          type="info" 
          @click="$emit('forward', scope.row)"
        >
          <el-icon><Share /></el-icon>转发
        </el-button>
        <el-button 
          v-if="type === 'inbox' && scope.row.isRead === '0'" 
          link 
          type="warning" 
          @click="$emit('mark-read', scope.row)"
        >
          <el-icon><Check /></el-icon>已读
        </el-button>
        <el-button link type="danger" @click="$emit('delete', scope.row)">
          <el-icon><Delete /></el-icon>删除
        </el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import {
  InfoFilled, View, ChatDotRound, Share, Check, Delete
} from '@element-plus/icons-vue'

// Props
interface Props {
  data: any[]
  loading: boolean
  type: 'inbox' | 'outbox'
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits([
  'selection-change', 'view', 'reply', 'forward', 'delete', 'mark-read'
])

// 多选框选中数据
const handleSelectionChange = (selection: any) => {
  emit('selection-change', selection)
}

// 获取消息类型样式类
const getMessageTypeClass = (type: string) => {
  const classes: Record<string, string> = {
    '1': 'system',      // 系统消息
    '2': 'notification', // 通知消息
    '3': 'private',     // 私信消息
    '4': 'reminder'     // 提醒消息
  }
  return classes[type] || 'default'
}

// 获取消息类型文本
const getMessageTypeText = (type: string) => {
  const texts: Record<string, string> = {
    '1': '系统',
    '2': '通知',
    '3': '私信',
    '4': '提醒'
  }
  return texts[type] || '消息'
}

// 获取消息类型标签类型
const getMessageTypeTagType = (type: string) => {
  const types: Record<string, string> = {
    '1': 'info',      // 系统消息
    '2': 'primary',   // 通知消息
    '3': 'success',   // 私信消息
    '4': 'warning'    // 提醒消息
  }
  return types[type] || ''
}

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return ''
  return new Date(time).toLocaleString('zh-CN')
}
</script>

<style scoped>
.message-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.unread-dot {
  color: #f56c6c;
  font-size: 8px;
}

.message-type-badge {
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
  color: white;
}

.message-type-badge.system { background-color: #909399; }
.message-type-badge.notification { background-color: #409eff; }
.message-type-badge.private { background-color: #67c23a; }
.message-type-badge.reminder { background-color: #e6a23c; }

.title-text {
  cursor: pointer;
  color: #409eff;
  flex: 1;
}

.title-text:hover {
  text-decoration: underline;
}
</style>
