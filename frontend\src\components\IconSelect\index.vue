<template>
  <div class="icon-body">
    <el-input v-model="name" placeholder="请输入图标名称搜索，点击选择图标" readonly>
      <template #suffix>
        <!-- <el-icon class="el-input__icon"><search /></el-icon> -->
        <span style="font-size: 16px;">🔍</span>
      </template>
    </el-input>
    <div class="icon-list">
      <div v-for="(item, index) in iconList" :key="index" @click="selectedIcon(item)" class="icon-item">
        <svg-icon :icon-class="item" class-name="disabled" />
        <span>{{ item }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import icons from './requireIcons'

const emit = defineEmits(['selected'])

const name = ref('')
const iconList = ref(icons)

watch(name, () => {
  iconList.value = icons.filter(item => item.includes(name.value))
})

function selectedIcon(name: string) {
  emit('selected', name)
}

function reset() {
  name.value = ''
  iconList.value = icons
}

defineExpose({
  reset
})
</script>

<style scoped>
.icon-body {
  width: 100%;
  padding: 10px;
}

.icon-list {
  height: 200px;
  overflow-y: scroll;
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 5px;
  width: 70px;
  height: 70px;
  cursor: pointer;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
}

.icon-item:hover {
  border-color: #409eff;
  color: #409eff;
}

.icon-item span {
  font-size: 12px;
  margin-top: 5px;
  word-break: break-all;
  text-align: center;
  line-height: 1.2;
}

.disabled {
  pointer-events: none;
}
</style>
