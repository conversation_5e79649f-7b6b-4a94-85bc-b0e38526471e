-- 部门管理功能增强SQL脚本
-- 兼容MySQL 8.0+

USE research_db;

-- 为部门表添加扩展字段
ALTER TABLE sys_dept ADD COLUMN dept_code VARCHAR(50) COMMENT '部门编号' AFTER dept_name;
ALTER TABLE sys_dept ADD COLUMN dept_type VARCHAR(20) DEFAULT 'department' COMMENT '部门类型（college=学院,department=系部,office=行政部门,center=中心,lab=实验室,other=其他）' AFTER dept_code;
ALTER TABLE sys_dept ADD COLUMN unit_name VARCHAR(100) COMMENT '所在单位' AFTER dept_type;
ALTER TABLE sys_dept ADD COLUMN dept_level INT DEFAULT 1 COMMENT '部门层级' AFTER unit_name;
ALTER TABLE sys_dept ADD COLUMN dept_path VARCHAR(500) COMMENT '部门路径' AFTER dept_level;
ALTER TABLE sys_dept ADD COLUMN staff_count INT DEFAULT 0 COMMENT '人员数量' AFTER dept_path;
ALTER TABLE sys_dept ADD COLUMN description TEXT COMMENT '部门描述' AFTER staff_count;

-- 创建部门类型字典表
CREATE TABLE sys_dept_type (
    type_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '类型ID',
    type_code VARCHAR(20) NOT NULL UNIQUE COMMENT '类型编码',
    type_name VARCHAR(50) NOT NULL COMMENT '类型名称',
    type_desc VARCHAR(200) COMMENT '类型描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='部门类型字典表';

-- 插入部门类型数据
INSERT INTO sys_dept_type (type_code, type_name, type_desc, sort_order) VALUES
('college', '学院', '二级学院', 1),
('department', '系部', '学院下属系部', 2),
('office', '行政部门', '行政管理部门', 3),
('center', '中心', '研究中心、服务中心等', 4),
('lab', '实验室', '教学实验室、科研实验室', 5),
('institute', '研究所', '科研院所', 6),
('library', '图书馆', '图书文献机构', 7),
('hospital', '医院', '附属医院', 8),
('other', '其他', '其他类型部门', 9);

-- 更新现有部门数据的扩展字段
UPDATE sys_dept SET 
    dept_code = CONCAT('DEPT', LPAD(dept_id, 4, '0')),
    dept_type = CASE 
        WHEN dept_name LIKE '%学院%' THEN 'college'
        WHEN dept_name LIKE '%系%' THEN 'department'
        WHEN dept_name LIKE '%处%' OR dept_name LIKE '%部%' THEN 'office'
        WHEN dept_name LIKE '%中心%' THEN 'center'
        WHEN dept_name LIKE '%实验室%' THEN 'lab'
        ELSE 'other'
    END,
    unit_name = '龙湖大学',
    dept_level = CASE 
        WHEN parent_id = 0 THEN 1
        ELSE 2
    END,
    dept_path = CASE 
        WHEN parent_id = 0 THEN dept_name
        ELSE CONCAT((SELECT dept_name FROM sys_dept p WHERE p.dept_id = sys_dept.parent_id), '/', dept_name)
    END
WHERE dept_id > 0;

-- 创建部门层级视图
CREATE VIEW v_dept_hierarchy AS
SELECT 
    d.dept_id,
    d.dept_code,
    d.dept_name,
    d.dept_type,
    dt.type_name as dept_type_name,
    d.parent_id,
    p.dept_name as parent_name,
    d.unit_name,
    d.dept_level,
    d.dept_path,
    d.ancestors,
    d.order_num,
    d.leader,
    d.phone,
    d.email,
    d.staff_count,
    d.status,
    d.description,
    d.create_time,
    d.update_time,
    -- 计算子部门数量
    (SELECT COUNT(*) FROM sys_dept c WHERE c.parent_id = d.dept_id AND c.del_flag = '0') as children_count,
    -- 计算该部门及子部门总人数
    (SELECT COALESCE(SUM(staff_count), 0) FROM sys_dept sub WHERE FIND_IN_SET(d.dept_id, sub.ancestors) OR sub.dept_id = d.dept_id) as total_staff_count
FROM sys_dept d
LEFT JOIN sys_dept p ON d.parent_id = p.dept_id
LEFT JOIN sys_dept_type dt ON d.dept_type = dt.type_code
WHERE d.del_flag = '0'
ORDER BY d.dept_level, d.order_num, d.dept_id;

-- 创建部门统计视图
CREATE VIEW v_dept_statistics AS
SELECT 
    dept_type,
    dt.type_name,
    COUNT(*) as dept_count,
    SUM(staff_count) as total_staff,
    AVG(staff_count) as avg_staff_per_dept
FROM sys_dept d
LEFT JOIN sys_dept_type dt ON d.dept_type = dt.type_code
WHERE d.del_flag = '0' AND d.status = '0'
GROUP BY dept_type, dt.type_name
ORDER BY dept_count DESC;

-- 创建索引优化查询性能
CREATE INDEX idx_dept_parent_id ON sys_dept(parent_id);
CREATE INDEX idx_dept_type ON sys_dept(dept_type);
CREATE INDEX idx_dept_code ON sys_dept(dept_code);
CREATE INDEX idx_dept_level ON sys_dept(dept_level);
CREATE INDEX idx_dept_status ON sys_dept(status, del_flag);

-- 插入示例部门数据
INSERT INTO sys_dept (dept_name, dept_code, dept_type, parent_id, ancestors, unit_name, dept_level, dept_path, order_num, leader, phone, email, status, del_flag, create_by, description) VALUES
-- 顶级部门（学校）
('龙湖大学', 'UNIV001', 'other', 0, '0', '龙湖大学', 1, '龙湖大学', 1, '校长', '0755-12345678', '<EMAIL>', '0', '0', 'admin', '龙湖大学主体'),

-- 学院
('计算机学院', 'CS001', 'college', 1, '0,1', '龙湖大学', 2, '龙湖大学/计算机学院', 1, '张院长', '0755-12345679', '<EMAIL>', '0', '0', 'admin', '计算机科学与技术学院'),
('数学学院', 'MATH001', 'college', 1, '0,1', '龙湖大学', 2, '龙湖大学/数学学院', 2, '李院长', '0755-12345680', '<EMAIL>', '0', '0', 'admin', '数学与统计学院'),
('经济管理学院', 'ECON001', 'college', 1, '0,1', '龙湖大学', 2, '龙湖大学/经济管理学院', 3, '王院长', '0755-12345681', '<EMAIL>', '0', '0', 'admin', '经济管理学院'),

-- 行政部门
('教务处', 'ACAD001', 'office', 1, '0,1', '龙湖大学', 2, '龙湖大学/教务处', 4, '陈处长', '0755-12345682', '<EMAIL>', '0', '0', 'admin', '教学事务管理部门'),
('人事处', 'HR001', 'office', 1, '0,1', '龙湖大学', 2, '龙湖大学/人事处', 5, '刘处长', '0755-12345683', '<EMAIL>', '0', '0', 'admin', '人力资源管理部门'),
('财务处', 'FIN001', 'office', 1, '0,1', '龙湖大学', 2, '龙湖大学/财务处', 6, '赵处长', '0755-12345684', '<EMAIL>', '0', '0', 'admin', '财务管理部门'),

-- 系部（计算机学院下属）
('软件工程系', 'SE001', 'department', 2, '0,1,2', '龙湖大学', 3, '龙湖大学/计算机学院/软件工程系', 1, '周主任', '0755-12345685', '<EMAIL>', '0', '0', 'admin', '软件工程专业系部'),
('计算机科学系', 'CS002', 'department', 2, '0,1,2', '龙湖大学', 3, '龙湖大学/计算机学院/计算机科学系', 2, '吴主任', '0755-12345686', '<EMAIL>', '0', '0', 'admin', '计算机科学与技术专业系部'),
('人工智能系', 'AI001', 'department', 2, '0,1,2', '龙湖大学', 3, '龙湖大学/计算机学院/人工智能系', 3, '郑主任', '0755-12345687', '<EMAIL>', '0', '0', 'admin', '人工智能专业系部'),

-- 实验室和中心
('计算机实验中心', 'LAB001', 'center', 2, '0,1,2', '龙湖大学', 3, '龙湖大学/计算机学院/计算机实验中心', 4, '孙主任', '0755-12345688', '<EMAIL>', '0', '0', 'admin', '计算机教学实验中心'),
('人工智能研究所', 'AI002', 'institute', 2, '0,1,2', '龙湖大学', 3, '龙湖大学/计算机学院/人工智能研究所', 5, '钱所长', '0755-12345689', '<EMAIL>', '0', '0', 'admin', '人工智能科研机构');

-- 更新人员数量（示例数据）
UPDATE sys_dept SET staff_count = CASE dept_id
    WHEN 1 THEN 0  -- 学校本部
    WHEN 2 THEN 120  -- 计算机学院
    WHEN 3 THEN 80   -- 数学学院
    WHEN 4 THEN 150  -- 经济管理学院
    WHEN 5 THEN 25   -- 教务处
    WHEN 6 THEN 20   -- 人事处
    WHEN 7 THEN 15   -- 财务处
    WHEN 8 THEN 35   -- 软件工程系
    WHEN 9 THEN 40   -- 计算机科学系
    WHEN 10 THEN 30  -- 人工智能系
    WHEN 11 THEN 10  -- 计算机实验中心
    WHEN 12 THEN 15  -- 人工智能研究所
    ELSE 0
END
WHERE dept_id BETWEEN 1 AND 12;

-- 查询验证数据
SELECT 
    dept_id,
    dept_code,
    dept_name,
    dept_type_name,
    parent_name,
    dept_level,
    dept_path,
    staff_count,
    children_count,
    status
FROM v_dept_hierarchy
ORDER BY dept_level, order_num;

-- 查询部门统计
SELECT * FROM v_dept_statistics;
