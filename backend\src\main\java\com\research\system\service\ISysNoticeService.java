package com.research.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.research.system.domain.SysNotice;

import java.util.List;
import java.util.Map;

/**
 * 通知公告Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
public interface ISysNoticeService extends IService<SysNotice> {

    /**
     * 查询通知公告列表
     * 
     * @param page 分页参数
     * @param notice 查询条件
     * @param userId 当前用户ID
     * @return 通知公告列表
     */
    IPage<SysNotice> selectNoticeList(Page<SysNotice> page, SysNotice notice, Long userId);

    /**
     * 查询用户可见的通知公告列表
     * 
     * @param page 分页参数
     * @param notice 查询条件
     * @param userId 用户ID
     * @return 通知公告列表
     */
    IPage<SysNotice> selectUserNoticeList(Page<SysNotice> page, SysNotice notice, Long userId);

    /**
     * 查询通知公告详情
     * 
     * @param noticeId 公告ID
     * @param userId 用户ID
     * @return 通知公告详情
     */
    SysNotice selectNoticeDetail(Long noticeId, Long userId);

    /**
     * 新增通知公告
     * 
     * @param notice 通知公告
     * @return 结果
     */
    boolean insertNotice(SysNotice notice);

    /**
     * 修改通知公告
     * 
     * @param notice 通知公告
     * @return 结果
     */
    boolean updateNotice(SysNotice notice);

    /**
     * 删除通知公告
     * 
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    boolean deleteNoticeByIds(Long[] noticeIds);

    /**
     * 发布通知公告
     * 
     * @param noticeId 公告ID
     * @param publishBy 发布人
     * @return 结果
     */
    boolean publishNotice(Long noticeId, String publishBy);

    /**
     * 撤回通知公告
     * 
     * @param noticeId 公告ID
     * @return 结果
     */
    boolean withdrawNotice(Long noticeId);

    /**
     * 置顶/取消置顶通知公告
     * 
     * @param noticeId 公告ID
     * @param isTop 是否置顶
     * @return 结果
     */
    boolean setNoticeTop(Long noticeId, String isTop);

    /**
     * 记录公告阅读
     * 
     * @param noticeId 公告ID
     * @param userId 用户ID
     * @param readDuration 阅读时长
     * @return 结果
     */
    boolean recordNoticeRead(Long noticeId, Long userId, Integer readDuration);

    /**
     * 查询最新公告列表（用于工作台展示）
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最新公告列表
     */
    List<SysNotice> selectLatestNotices(Long userId, Integer limit);

    /**
     * 查询用户未读公告数量
     * 
     * @param userId 用户ID
     * @return 未读公告数量
     */
    Long selectUnreadNoticeCount(Long userId);

    /**
     * 查询公告阅读统计
     * 
     * @param noticeId 公告ID
     * @return 阅读统计信息
     */
    Map<String, Object> selectNoticeReadStats(Long noticeId);

    /**
     * 全文搜索公告
     * 
     * @param page 分页参数
     * @param keyword 搜索关键词
     * @param userId 用户ID
     * @return 搜索结果
     */
    IPage<SysNotice> searchNotices(Page<SysNotice> page, String keyword, Long userId);

    /**
     * 导出公告数据
     * 
     * @param notice 查询条件
     * @return 公告列表
     */
    List<SysNotice> exportNoticeList(SysNotice notice);

    /**
     * 批量导入公告
     * 
     * @param noticeList 公告列表
     * @param isUpdateSupport 是否支持更新
     * @param operName 操作人
     * @return 导入结果
     */
    String importNotice(List<SysNotice> noticeList, Boolean isUpdateSupport, String operName);
}
