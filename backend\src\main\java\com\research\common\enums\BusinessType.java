package com.research.common.enums;

/**
 * 业务操作类型
 *
 * <AUTHOR>
 */
public enum BusinessType {
    /**
     * 其它
     */
    OTHER,

    /**
     * 新增
     */
    INSERT,

    /**
     * 修改
     */
    UPDATE,

    /**
     * 删除
     */
    DELETE,

    /**
     * 授权
     */
    GRANT,

    /**
     * 导出
     */
    EXPORT,

    /**
     * 导入
     */
    IMPORT,

    /**
     * 强退
     */
    FORCE,

    /**
     * 生成代码
     */
    GENCODE,

    /**
     * 清空数据
     */
    CLEAN,

    /**
     * 审批
     */
    APPROVE,

    /**
     * 驳回
     */
    REJECT,

    /**
     * 提交
     */
    SUBMIT,

    /**
     * 撤回
     */
    WITHDRAW,

    /**
     * 启动
     */
    START,

    /**
     * 停止
     */
    STOP,

    /**
     * 重置
     */
    RESET,

    /**
     * 发布
     */
    PUBLISH,

    /**
     * 取消发布
     */
    UNPUBLISH
}