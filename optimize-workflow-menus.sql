-- 工作流菜单优化SQL脚本
-- 按照精简合并方案重新配置工作流菜单

-- 1. 删除旧的工作流菜单（保留数据，只是重新配置）
DELETE FROM sys_role_menu WHERE menu_id BETWEEN 200 AND 299;
DELETE FROM sys_menu WHERE menu_id BETWEEN 200 AND 299;

-- 2. 插入优化后的工作流菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES

-- 工作流管理主菜单
(200, '工作流管理', 0, 3, 'workflow', NULL, '', 1, 0, 'M', '0', '0', '', 'workflow', 'admin', NOW(), '工作流管理目录'),

-- 子菜单（优化后的5个）
(201, '流程设计器', 200, 1, 'designer', 'workflow/designer/index', '', 1, 0, 'C', '0', '0', 'workflow:designer:view', 'build', 'admin', NOW(), '流程设计器菜单'),
(202, '流程管理', 200, 2, 'process', 'workflow/process/index', '', 1, 0, 'C', '0', '0', 'workflow:process:list', 'tree-table', 'admin', NOW(), '流程管理菜单'),
(203, '我的任务', 200, 3, 'mytask', 'workflow/mytask/index', '', 1, 0, 'C', '0', '0', 'workflow:mytask:list', 'skill', 'admin', NOW(), '我的任务菜单'),
(204, '版本管理', 200, 4, 'version', 'workflow/version/index', '', 1, 0, 'C', '0', '0', 'workflow:version:list', 'collection-tag', 'admin', NOW(), '版本管理菜单'),
(205, '监控大屏', 200, 5, 'monitor', 'workflow/monitor/dashboard', '', 1, 0, 'C', '0', '0', 'workflow:monitor:view', 'monitor', 'admin', NOW(), '监控大屏菜单');

-- 3. 添加流程设计器的按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, perms, create_by, create_time) VALUES
('设计器查看', 201, 1, 'F', 'workflow:designer:view', 'admin', NOW()),
('模型创建', 201, 2, 'F', 'workflow:designer:create', 'admin', NOW()),
('模型编辑', 201, 3, 'F', 'workflow:designer:edit', 'admin', NOW()),
('模型删除', 201, 4, 'F', 'workflow:designer:delete', 'admin', NOW()),
('模型部署', 201, 5, 'F', 'workflow:designer:deploy', 'admin', NOW()),
('模型导入', 201, 6, 'F', 'workflow:designer:import', 'admin', NOW()),
('模型导出', 201, 7, 'F', 'workflow:designer:export', 'admin', NOW());

-- 4. 添加流程管理的按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, perms, create_by, create_time) VALUES
('流程查询', 202, 1, 'F', 'workflow:process:query', 'admin', NOW()),
('流程启动', 202, 2, 'F', 'workflow:process:start', 'admin', NOW()),
('流程挂起', 202, 3, 'F', 'workflow:process:suspend', 'admin', NOW()),
('流程激活', 202, 4, 'F', 'workflow:process:activate', 'admin', NOW()),
('流程删除', 202, 5, 'F', 'workflow:process:delete', 'admin', NOW()),
('实例查看', 202, 6, 'F', 'workflow:process:instance', 'admin', NOW()),
('历史查看', 202, 7, 'F', 'workflow:process:history', 'admin', NOW());

-- 5. 添加我的任务的按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, perms, create_by, create_time) VALUES
('任务查询', 203, 1, 'F', 'workflow:mytask:query', 'admin', NOW()),
('任务处理', 203, 2, 'F', 'workflow:mytask:handle', 'admin', NOW()),
('任务委托', 203, 3, 'F', 'workflow:mytask:delegate', 'admin', NOW()),
('任务转办', 203, 4, 'F', 'workflow:mytask:transfer', 'admin', NOW()),
('任务退回', 203, 5, 'F', 'workflow:mytask:return', 'admin', NOW()),
('任务加签', 203, 6, 'F', 'workflow:mytask:addsign', 'admin', NOW()),
('任务跳转', 203, 7, 'F', 'workflow:mytask:jump', 'admin', NOW());

-- 6. 添加版本管理的按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, perms, create_by, create_time) VALUES
('版本查询', 204, 1, 'F', 'workflow:version:query', 'admin', NOW()),
('版本发布', 204, 2, 'F', 'workflow:version:publish', 'admin', NOW()),
('版本回滚', 204, 3, 'F', 'workflow:version:rollback', 'admin', NOW()),
('版本对比', 204, 4, 'F', 'workflow:version:compare', 'admin', NOW()),
('版本删除', 204, 5, 'F', 'workflow:version:delete', 'admin', NOW());

-- 7. 添加监控大屏的按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, menu_type, perms, create_by, create_time) VALUES
('监控查看', 205, 1, 'F', 'workflow:monitor:view', 'admin', NOW()),
('统计分析', 205, 2, 'F', 'workflow:monitor:statistics', 'admin', NOW()),
('性能监控', 205, 3, 'F', 'workflow:monitor:performance', 'admin', NOW()),
('报表导出', 205, 4, 'F', 'workflow:monitor:export', 'admin', NOW());

-- 8. 为超级管理员角色分配工作流菜单权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(1, 200), -- 工作流管理
(1, 201), -- 流程设计器
(1, 202), -- 流程管理
(1, 203), -- 我的任务
(1, 204), -- 版本管理
(1, 205); -- 监控大屏

-- 9. 为超级管理员角色分配所有按钮权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu 
WHERE parent_id IN (201, 202, 203, 204, 205) AND menu_type = 'F';

-- 10. 为科研管理员角色分配部分工作流权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(2, 200), -- 工作流管理
(2, 202), -- 流程管理
(2, 203), -- 我的任务
(2, 205); -- 监控大屏

-- 11. 为项目经理角色分配工作流权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(3, 200), -- 工作流管理
(3, 202), -- 流程管理
(3, 203); -- 我的任务

-- 12. 验证配置结果
SELECT 
    m.menu_id as '菜单ID',
    m.menu_name as '菜单名称',
    m.parent_id as '父菜单ID',
    m.path as '路径',
    m.component as '组件',
    m.menu_type as '类型',
    m.visible as '可见',
    m.status as '状态',
    m.icon as '图标'
FROM sys_menu m
WHERE m.menu_id BETWEEN 200 AND 299
ORDER BY m.parent_id, m.order_num;

-- 13. 验证角色权限分配
SELECT 
    r.role_name as '角色名称',
    m.menu_name as '菜单名称',
    m.menu_type as '菜单类型'
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.menu_id BETWEEN 200 AND 299
ORDER BY r.role_id, m.parent_id, m.order_num;
