<template>
  <span>
    <el-tag
      v-if="supTag"
      :type="tagType(value)"
      :class="{ 'dict-tag': true }"
      :size="size"
      :effect="effect"
    >
      {{ tagValue }}
    </el-tag>
    <span v-else>{{ tagValue }}</span>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  options?: any[]
  value?: any
  size?: string
  effect?: string
  supTag?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  options: () => [],
  value: '',
  size: 'default',
  effect: 'light',
  supTag: true
})

const tagValue = computed(() => {
  if (props.options && props.options.length > 0) {
    const option = props.options.find(item => item.value === props.value)
    return option ? option.label : props.value
  }
  return props.value
})

const tagType = (value: any) => {
  if (props.options && props.options.length > 0) {
    const option = props.options.find(item => item.value === value)
    if (option && option.listClass) {
      return option.listClass === 'primary' ? '' : option.listClass
    }
  }
  return ''
}
</script>

<style scoped>
.dict-tag {
  margin-right: 5px;
}
</style>
