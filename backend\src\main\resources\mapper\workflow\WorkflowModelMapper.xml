<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.research.workflow.mapper.WorkflowModelMapper">
    
    <resultMap type="WorkflowModel" id="WorkflowModelResult">
        <result property="id"                       column="id"                       />
        <result property="name"                     column="name"                     />
        <result property="key"                      column="model_key"                />
        <result property="category"                 column="category"                 />
        <result property="description"              column="description"              />
        <result property="version"                  column="version"                  />
        <result property="modelJson"                column="model_json"               />
        <result property="modelXml"                 column="model_xml"                />
        <result property="hasEditorSource"         column="has_editor_source"        />
        <result property="hasEditorSourceExtra"    column="has_editor_source_extra"  />
        <result property="lastUpdateTime"          column="last_update_time"         />
        <result property="deploymentId"            column="deployment_id"            />
        <result property="processDefinitionId"     column="process_definition_id"    />
        <result property="deployed"                column="deployed"                 />
        <result property="deployTime"              column="deploy_time"              />
        <result property="status"                  column="status"                   />
        <result property="icon"                    column="icon"                     />
        <result property="color"                   column="color"                    />
        <result property="orderNum"                column="order_num"                />
        <result property="visible"                 column="visible"                  />
        <result property="tags"                    column="tags"                     />
        <result property="formConfig"              column="form_config"              />
        <result property="permissionConfig"        column="permission_config"        />
        <result property="notificationConfig"      column="notification_config"      />
        <result property="timeoutConfig"           column="timeout_config"           />
        <result property="createBy"                column="create_by"                />
        <result property="createTime"              column="create_time"              />
        <result property="updateBy"                column="update_by"                />
        <result property="updateTime"              column="update_time"              />
        <result property="remark"                  column="remark"                   />
    </resultMap>

    <sql id="selectWorkflowModelVo">
        select id, name, model_key, category, description, version, model_json, model_xml,
               has_editor_source, has_editor_source_extra, last_update_time, deployment_id,
               process_definition_id, deployed, deploy_time, status, icon, color, order_num,
               visible, tags, form_config, permission_config, notification_config, timeout_config,
               create_by, create_time, update_by, update_time, remark
        from workflow_model
    </sql>

    <!-- 查询工作流模型列表 -->
    <select id="selectModelList" resultMap="WorkflowModelResult">
        <include refid="selectWorkflowModelVo"/>
        <where>
            <if test="model != null">
                <if test="model.name != null and model.name != ''">
                    AND name like concat('%', #{model.name}, '%')
                </if>
                <if test="model.key != null and model.key != ''">
                    AND model_key like concat('%', #{model.key}, '%')
                </if>
                <if test="model.category != null and model.category != ''">
                    AND category = #{model.category}
                </if>
                <if test="model.status != null and model.status != ''">
                    AND status = #{model.status}
                </if>
                <if test="model.deployed != null">
                    AND deployed = #{model.deployed}
                </if>
                <if test="model.visible != null and model.visible != ''">
                    AND visible = #{model.visible}
                </if>
            </if>
        </where>
        order by order_num asc, create_time desc
    </select>

    <!-- 查询工作流模型详情 -->
    <select id="selectModelById" resultMap="WorkflowModelResult">
        <include refid="selectWorkflowModelVo"/>
        where id = #{id}
    </select>

    <!-- 根据模型Key查询模型 -->
    <select id="selectModelByKey" resultMap="WorkflowModelResult">
        <include refid="selectWorkflowModelVo"/>
        where model_key = #{key}
        order by version desc
        limit 1
    </select>

    <!-- 查询模型的最大版本号 -->
    <select id="selectMaxVersionByKey" resultType="java.lang.Integer">
        select max(version) from workflow_model where model_key = #{key}
    </select>

    <!-- 查询已部署的模型列表 -->
    <select id="selectDeployedModels" resultMap="WorkflowModelResult">
        <include refid="selectWorkflowModelVo"/>
        where deployed = 1 and status = '1'
        order by deploy_time desc
    </select>

    <!-- 查询模型统计信息 -->
    <select id="selectModelStatistics" resultType="java.util.Map">
        select 
            count(*) as total,
            count(case when status = '0' then 1 end) as draft,
            count(case when status = '1' then 1 end) as published,
            count(case when status = '2' then 1 end) as disabled,
            count(case when deployed = 1 then 1 end) as deployed,
            count(case when category = 'approval' then 1 end) as approval,
            count(case when category = 'business' then 1 end) as business,
            count(case when category = 'system' then 1 end) as system
        from workflow_model
    </select>

    <!-- 根据分类查询模型数量 -->
    <select id="selectCountByCategory" resultType="java.lang.Long">
        select count(*) from workflow_model where category = #{category}
    </select>

    <!-- 查询最近创建的模型 -->
    <select id="selectRecentModels" resultMap="WorkflowModelResult">
        <include refid="selectWorkflowModelVo"/>
        where visible = '1'
        order by create_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <!-- 查询最近更新的模型 -->
    <select id="selectRecentUpdatedModels" resultMap="WorkflowModelResult">
        <include refid="selectWorkflowModelVo"/>
        where visible = '1'
        order by last_update_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <!-- 批量更新模型状态 -->
    <update id="batchUpdateStatus">
        update workflow_model 
        set status = #{status}, update_by = #{updateBy}, update_time = now()
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 清理未部署的草稿模型 -->
    <delete id="cleanDraftModels">
        delete from workflow_model 
        where status = '0' 
          and deployed = 0 
          and create_time &lt; date_sub(now(), interval #{days} day)
    </delete>

</mapper>
