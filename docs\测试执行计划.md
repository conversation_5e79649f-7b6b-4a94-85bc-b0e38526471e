# 第二阶段测试执行计划

## 测试概述
本文档描述了第二阶段核心业务功能的详细测试计划，包括环境准备、测试用例、执行步骤和结果记录。

## 测试环境

### 后端环境
- **框架**: Spring Boot 2.7.x
- **数据库**: H2内存数据库
- **端口**: 8080
- **启动命令**: `mvn spring-boot:run`

### 前端环境
- **框架**: Vue 3 + TypeScript
- **UI库**: Element Plus
- **构建工具**: Vite
- **端口**: 3000
- **启动命令**: `npm run dev`

## 测试阶段

### 阶段1：环境准备测试 (预计30分钟)

#### 1.1 后端服务启动测试
**目标**: 验证后端服务能否正常启动
**步骤**:
1. 检查Java环境 (`java -version`)
2. 检查Maven环境 (`mvn -version`)
3. 编译后端项目 (`mvn clean compile`)
4. 启动后端服务 (`mvn spring-boot:run`)
5. 验证服务启动 (访问 `http://localhost:8080`)

**预期结果**: 
- 服务正常启动，无错误日志
- 可以访问健康检查接口
- H2数据库正常初始化

#### 1.2 前端项目启动测试
**目标**: 验证前端项目能否正常启动
**步骤**:
1. 检查Node.js环境 (`node -v`)
2. 检查npm环境 (`npm -v`)
3. 安装前端依赖 (`npm install`)
4. 启动前端服务 (`npm run dev`)
5. 验证页面访问 (访问 `http://localhost:3000`)

**预期结果**:
- 前端服务正常启动
- 页面能够正常加载
- 无TypeScript编译错误

#### 1.3 数据库初始化测试
**目标**: 验证数据库表和数据是否正确初始化
**步骤**:
1. 访问H2控制台 (`http://localhost:8080/h2-console`)
2. 连接数据库 (JDBC URL: `jdbc:h2:mem:testdb`)
3. 检查表结构是否正确创建
4. 检查初始数据是否正确插入

**预期结果**:
- 所有8个业务表正确创建
- 初始数据正确插入
- 表结构符合设计要求

### 阶段2：API接口测试 (预计60分钟)

#### 2.1 工作台接口测试
**接口列表**:
- `GET /workspace/data` - 获取工作台数据
- `GET /workspace/userInfo` - 获取用户信息
- `GET /workspace/todoStats` - 获取待办统计
- `GET /workspace/noticeStats` - 获取公告统计
- `GET /workspace/messageStats` - 获取消息统计
- `GET /workspace/quickApps` - 获取快捷应用

**测试方法**: 使用Postman或curl进行接口测试

#### 2.2 通知公告接口测试
**接口列表**:
- `GET /system/notice/list` - 公告列表查询
- `GET /system/notice/{id}` - 公告详情查询
- `POST /system/notice` - 新增公告
- `PUT /system/notice` - 修改公告
- `DELETE /system/notice/{ids}` - 删除公告
- `POST /system/notice/publish/{id}` - 发布公告

#### 2.3 待办事项接口测试
**接口列表**:
- `GET /system/todo/myList` - 我的待办列表
- `GET /system/todo/{id}` - 待办详情
- `POST /system/todo` - 新增待办
- `PUT /system/todo` - 修改待办
- `POST /system/todo/assign/{id}` - 分配待办
- `POST /system/todo/complete/{id}` - 完成待办

#### 2.4 站内消息接口测试
**接口列表**:
- `GET /system/message/inbox` - 收件箱列表
- `GET /system/message/outbox` - 发件箱列表
- `POST /system/message/send` - 发送消息
- `POST /system/message/reply/{id}` - 回复消息
- `POST /system/message/markRead/{id}` - 标记已读

### 阶段3：前端功能测试 (预计90分钟)

#### 3.1 个人工作台测试
**测试用例**:
1. 访问工作台主页 (`/workspace`)
2. 验证用户信息卡片显示
3. 验证待办事项统计卡片
4. 验证通知公告卡片
5. 验证站内消息卡片
6. 验证快捷应用卡片
7. 测试工作台设置功能

#### 3.2 通知公告管理测试
**测试用例**:
1. 访问公告列表页面 (`/system/notice`)
2. 测试公告搜索功能
3. 测试公告筛选功能
4. 测试新增公告功能
5. 测试编辑公告功能
6. 测试删除公告功能
7. 测试公告发布/撤回
8. 访问公告详情页面
9. 测试公告阅读统计

#### 3.3 待办事项管理测试
**测试用例**:
1. 访问待办列表页面 (`/system/todo`)
2. 测试标签页切换功能
3. 测试待办搜索筛选
4. 测试新增待办功能
5. 测试编辑待办功能
6. 测试待办分配功能
7. 测试待办处理功能
8. 测试待办完成功能
9. 测试批量操作功能

#### 3.4 站内消息管理测试
**测试用例**:
1. 访问消息列表页面 (`/system/message`)
2. 测试收件箱/发件箱切换
3. 测试消息搜索功能
4. 测试发送消息功能
5. 测试回复消息功能
6. 测试转发消息功能
7. 测试标记已读功能
8. 测试批量删除功能

### 阶段4：集成测试 (预计60分钟)

#### 4.1 端到端流程测试
**测试场景**:
1. 用户登录 → 查看工作台 → 处理待办 → 查看消息
2. 管理员发布公告 → 用户接收通知 → 查看公告详情
3. 创建待办 → 分配给用户 → 用户处理 → 完成待办
4. 发送消息 → 接收回复 → 转发消息

#### 4.2 权限控制测试
**测试场景**:
1. 不同角色用户的菜单权限
2. 不同角色用户的操作权限
3. 数据权限控制测试

#### 4.3 异常处理测试
**测试场景**:
1. 网络异常处理
2. 服务器错误处理
3. 数据验证错误处理
4. 权限不足错误处理

## 测试工具

### 后端接口测试工具
- **Postman**: API接口测试
- **curl**: 命令行接口测试
- **H2 Console**: 数据库查看

### 前端功能测试工具
- **Chrome DevTools**: 网络请求和控制台调试
- **Vue DevTools**: Vue组件调试
- **Element Plus**: UI组件测试

### 性能测试工具
- **JMeter**: 接口性能测试
- **Lighthouse**: 前端性能测试

## 测试记录模板

### 测试用例记录
```
测试用例ID: TC001
测试名称: 工作台数据加载测试
测试步骤: 
1. 访问/workspace页面
2. 检查各卡片数据加载
预期结果: 所有卡片正常显示数据
实际结果: [待填写]
测试状态: [通过/失败/阻塞]
问题描述: [如有问题请描述]
测试时间: [测试执行时间]
测试人员: [测试人员姓名]
```

### 缺陷记录
```
缺陷ID: BUG001
缺陷标题: [简短描述]
缺陷描述: [详细描述]
重现步骤: [详细步骤]
预期结果: [预期结果]
实际结果: [实际结果]
严重程度: [高/中/低]
优先级: [高/中/低]
发现时间: [发现时间]
发现人员: [发现人员]
状态: [新建/已分配/已修复/已验证/已关闭]
```

## 测试完成标准

### 功能完整性
- [ ] 所有API接口正常响应
- [ ] 所有前端页面正常显示
- [ ] 所有业务流程正常运行
- [ ] 所有异常情况正确处理

### 性能要求
- [ ] 页面加载时间 < 3秒
- [ ] 接口响应时间 < 1秒
- [ ] 并发用户数 > 100
- [ ] 内存使用率 < 80%

### 用户体验
- [ ] 界面美观友好
- [ ] 操作流程顺畅
- [ ] 错误提示清晰
- [ ] 响应式布局正常

## 风险评估

### 高风险项
1. **数据库连接问题**: H2数据库配置错误
2. **端口冲突**: 8080或3000端口被占用
3. **依赖版本冲突**: Node.js或Java版本不兼容

### 中风险项
1. **网络代理问题**: 前后端接口调用失败
2. **权限配置问题**: 用户权限设置错误
3. **数据初始化问题**: 示例数据不完整

### 低风险项
1. **UI样式问题**: 界面显示异常
2. **浏览器兼容性**: 特定浏览器问题
3. **性能优化**: 响应速度较慢

---

**测试负责人**: 开发团队
**计划执行时间**: 2024-07-29
**预计总时长**: 4小时
**文档版本**: v1.0
