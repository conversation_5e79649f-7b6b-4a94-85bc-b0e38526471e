-- 人员管理功能增强SQL脚本
-- 兼容MySQL 8.0+

USE research_db;

-- 为用户表添加扩展字段
ALTER TABLE sys_user ADD COLUMN real_name VARCHAR(50) COMMENT '真实姓名' AFTER nick_name;
ALTER TABLE sys_user ADD COLUMN id_card VARCHAR(18) COMMENT '身份证号' AFTER real_name;
ALTER TABLE sys_user ADD COLUMN birth_date DATE COMMENT '出生日期' AFTER id_card;
ALTER TABLE sys_user ADD COLUMN age INT COMMENT '年龄' AFTER birth_date;
ALTER TABLE sys_user ADD COLUMN nation VARCHAR(20) COMMENT '民族' AFTER age;
ALTER TABLE sys_user ADD COLUMN political_status VARCHAR(20) COMMENT '政治面貌' AFTER nation;
ALTER TABLE sys_user ADD COLUMN education VARCHAR(20) COMMENT '学历' AFTER political_status;
ALTER TABLE sys_user ADD COLUMN major VARCHAR(50) COMMENT '专业' AFTER education;
ALTER TABLE sys_user ADD COLUMN graduation_school VARCHAR(100) COMMENT '毕业院校' AFTER major;
ALTER TABLE sys_user ADD COLUMN graduation_date DATE COMMENT '毕业时间' AFTER graduation_school;
ALTER TABLE sys_user ADD COLUMN work_date DATE COMMENT '参加工作时间' AFTER graduation_date;
ALTER TABLE sys_user ADD COLUMN join_date DATE COMMENT '入职时间' AFTER work_date;
ALTER TABLE sys_user ADD COLUMN leave_date DATE COMMENT '离职时间' AFTER join_date;
ALTER TABLE sys_user ADD COLUMN retire_date DATE COMMENT '退休时间' AFTER leave_date;
ALTER TABLE sys_user ADD COLUMN personnel_type VARCHAR(20) COMMENT '人员类型（在职=active,退休=retired,离职=resigned,实习=intern,临时=temporary）' AFTER retire_date;
ALTER TABLE sys_user ADD COLUMN employee_number VARCHAR(30) COMMENT '工号' AFTER personnel_type;
ALTER TABLE sys_user ADD COLUMN job_title VARCHAR(50) COMMENT '职称' AFTER employee_number;
ALTER TABLE sys_user ADD COLUMN job_level VARCHAR(20) COMMENT '职级' AFTER job_title;
ALTER TABLE sys_user ADD COLUMN salary DECIMAL(10,2) COMMENT '薪资' AFTER job_level;
ALTER TABLE sys_user ADD COLUMN address VARCHAR(200) COMMENT '家庭住址' AFTER salary;
ALTER TABLE sys_user ADD COLUMN emergency_contact VARCHAR(50) COMMENT '紧急联系人' AFTER address;
ALTER TABLE sys_user ADD COLUMN emergency_phone VARCHAR(20) COMMENT '紧急联系电话' AFTER emergency_contact;
ALTER TABLE sys_user ADD COLUMN bank_account VARCHAR(30) COMMENT '银行账号' AFTER emergency_phone;
ALTER TABLE sys_user ADD COLUMN bank_name VARCHAR(50) COMMENT '开户银行' AFTER bank_account;
ALTER TABLE sys_user ADD COLUMN social_security_number VARCHAR(30) COMMENT '社保号' AFTER bank_name;
ALTER TABLE sys_user ADD COLUMN work_experience TEXT COMMENT '工作经历' AFTER social_security_number;
ALTER TABLE sys_user ADD COLUMN skills TEXT COMMENT '技能特长' AFTER work_experience;
ALTER TABLE sys_user ADD COLUMN certificates TEXT COMMENT '证书资质' AFTER skills;
ALTER TABLE sys_user ADD COLUMN health_status VARCHAR(20) COMMENT '健康状况' AFTER certificates;
ALTER TABLE sys_user ADD COLUMN marital_status VARCHAR(10) COMMENT '婚姻状况' AFTER health_status;

-- 创建人员类型字典表
CREATE TABLE sys_personnel_type (
    type_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '类型ID',
    type_code VARCHAR(20) NOT NULL UNIQUE COMMENT '类型编码',
    type_name VARCHAR(50) NOT NULL COMMENT '类型名称',
    type_desc VARCHAR(200) COMMENT '类型描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='人员类型字典表';

-- 插入人员类型数据
INSERT INTO sys_personnel_type (type_code, type_name, type_desc, sort_order) VALUES
('active', '在职人员', '正常在职工作的人员', 1),
('retired', '退休人员', '已退休的人员', 2),
('resigned', '离职人员', '已离职的人员', 3),
('intern', '实习人员', '实习生或见习人员', 4),
('temporary', '临时人员', '临时聘用人员', 5),
('visiting', '访问学者', '访问学者或交流人员', 6),
('part_time', '兼职人员', '兼职工作人员', 7);

-- 创建学历字典表
CREATE TABLE sys_education_level (
    level_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '学历ID',
    level_code VARCHAR(20) NOT NULL UNIQUE COMMENT '学历编码',
    level_name VARCHAR(50) NOT NULL COMMENT '学历名称',
    level_desc VARCHAR(200) COMMENT '学历描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='学历字典表';

-- 插入学历数据
INSERT INTO sys_education_level (level_code, level_name, level_desc, sort_order) VALUES
('primary', '小学', '小学学历', 1),
('junior', '初中', '初中学历', 2),
('senior', '高中', '高中学历', 3),
('technical', '中专/技校', '中等专业学校或技工学校', 4),
('college', '大专', '大学专科学历', 5),
('bachelor', '本科', '大学本科学历', 6),
('master', '硕士', '硕士研究生学历', 7),
('doctor', '博士', '博士研究生学历', 8),
('postdoc', '博士后', '博士后研究经历', 9);

-- 创建职称字典表
CREATE TABLE sys_job_title (
    title_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '职称ID',
    title_code VARCHAR(20) NOT NULL UNIQUE COMMENT '职称编码',
    title_name VARCHAR(50) NOT NULL COMMENT '职称名称',
    title_category VARCHAR(20) COMMENT '职称类别',
    title_level INT COMMENT '职称级别',
    title_desc VARCHAR(200) COMMENT '职称描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='职称字典表';

-- 插入职称数据
INSERT INTO sys_job_title (title_code, title_name, title_category, title_level, title_desc, sort_order) VALUES
-- 教学系列
('professor', '教授', '教学', 4, '教授职称', 1),
('associate_prof', '副教授', '教学', 3, '副教授职称', 2),
('lecturer', '讲师', '教学', 2, '讲师职称', 3),
('assistant', '助教', '教学', 1, '助教职称', 4),
-- 研究系列
('researcher', '研究员', '科研', 4, '研究员职称', 5),
('associate_researcher', '副研究员', '科研', 3, '副研究员职称', 6),
('assistant_researcher', '助理研究员', '科研', 2, '助理研究员职称', 7),
('research_assistant', '研究实习员', '科研', 1, '研究实习员职称', 8),
-- 工程系列
('senior_engineer', '高级工程师', '工程', 3, '高级工程师职称', 9),
('engineer', '工程师', '工程', 2, '工程师职称', 10),
('assistant_engineer', '助理工程师', '工程', 1, '助理工程师职称', 11),
-- 管理系列
('senior_manager', '高级管理人员', '管理', 3, '高级管理职称', 12),
('manager', '中级管理人员', '管理', 2, '中级管理职称', 13),
('junior_manager', '初级管理人员', '管理', 1, '初级管理职称', 14);

-- 创建人员档案表
CREATE TABLE sys_personnel_archive (
    archive_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '档案ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    archive_number VARCHAR(30) COMMENT '档案编号',
    file_location VARCHAR(100) COMMENT '档案存放位置',
    entry_date DATE COMMENT '建档日期',
    archive_status VARCHAR(20) COMMENT '档案状态',
    keeper VARCHAR(50) COMMENT '档案保管员',
    notes TEXT COMMENT '档案备注',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES sys_user(user_id) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='人员档案表';

-- 创建人员变动记录表
CREATE TABLE sys_personnel_change (
    change_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '变动ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    change_type VARCHAR(20) NOT NULL COMMENT '变动类型（入职=join,离职=leave,退休=retire,调动=transfer,晋升=promotion,降级=demotion）',
    change_date DATE NOT NULL COMMENT '变动日期',
    old_dept_id BIGINT COMMENT '原部门ID',
    new_dept_id BIGINT COMMENT '新部门ID',
    old_post_id BIGINT COMMENT '原岗位ID',
    new_post_id BIGINT COMMENT '新岗位ID',
    old_title VARCHAR(50) COMMENT '原职称',
    new_title VARCHAR(50) COMMENT '新职称',
    change_reason VARCHAR(200) COMMENT '变动原因',
    approver VARCHAR(50) COMMENT '审批人',
    approval_date DATE COMMENT '审批日期',
    effective_date DATE COMMENT '生效日期',
    notes TEXT COMMENT '备注',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES sys_user(user_id) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='人员变动记录表';

-- 创建人员培训记录表
CREATE TABLE sys_personnel_training (
    training_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '培训ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    training_name VARCHAR(100) NOT NULL COMMENT '培训名称',
    training_type VARCHAR(20) COMMENT '培训类型',
    training_institution VARCHAR(100) COMMENT '培训机构',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    training_hours INT COMMENT '培训学时',
    training_result VARCHAR(20) COMMENT '培训结果',
    certificate_number VARCHAR(50) COMMENT '证书编号',
    certificate_file VARCHAR(200) COMMENT '证书文件',
    notes TEXT COMMENT '备注',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES sys_user(user_id) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='人员培训记录表';

-- 创建人员考核记录表
CREATE TABLE sys_personnel_assessment (
    assessment_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '考核ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    assessment_year INT NOT NULL COMMENT '考核年度',
    assessment_period VARCHAR(20) COMMENT '考核期间',
    assessment_type VARCHAR(20) COMMENT '考核类型',
    work_performance DECIMAL(5,2) COMMENT '工作业绩得分',
    work_attitude DECIMAL(5,2) COMMENT '工作态度得分',
    professional_ability DECIMAL(5,2) COMMENT '专业能力得分',
    total_score DECIMAL(5,2) COMMENT '总分',
    assessment_result VARCHAR(20) COMMENT '考核结果',
    assessor VARCHAR(50) COMMENT '考核人',
    assessment_date DATE COMMENT '考核日期',
    comments TEXT COMMENT '考核意见',
    improvement_plan TEXT COMMENT '改进计划',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES sys_user(user_id) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='人员考核记录表';

-- 创建人员详细视图
CREATE VIEW v_personnel_detail AS
SELECT
    u.user_id,
    u.user_name,
    u.nick_name,
    u.real_name,
    u.id_card,
    u.birth_date,
    u.age,
    u.sex,
    CASE u.sex
        WHEN '0' THEN '男'
        WHEN '1' THEN '女'
        ELSE '未知'
    END as sex_name,
    u.nation,
    u.political_status,
    u.education,
    el.level_name as education_name,
    u.major,
    u.graduation_school,
    u.graduation_date,
    u.work_date,
    u.join_date,
    u.leave_date,
    u.retire_date,
    u.personnel_type,
    pt.type_name as personnel_type_name,
    u.employee_number,
    u.job_title,
    jt.title_name as job_title_name,
    jt.title_category,
    jt.title_level,
    u.job_level,
    u.salary,
    u.email,
    u.phonenumber,
    u.address,
    u.emergency_contact,
    u.emergency_phone,
    u.bank_account,
    u.bank_name,
    u.social_security_number,
    u.work_experience,
    u.skills,
    u.certificates,
    u.health_status,
    u.marital_status,
    u.dept_id,
    d.dept_name,
    d.dept_type,
    u.status,
    u.create_time,
    u.update_time,
    u.remark,
    -- 计算工作年限
    CASE
        WHEN u.work_date IS NOT NULL THEN
            TIMESTAMPDIFF(YEAR, u.work_date, COALESCE(u.leave_date, u.retire_date, CURDATE()))
        ELSE NULL
    END as work_years,
    -- 计算在本单位工作年限
    CASE
        WHEN u.join_date IS NOT NULL THEN
            TIMESTAMPDIFF(YEAR, u.join_date, COALESCE(u.leave_date, u.retire_date, CURDATE()))
        ELSE NULL
    END as company_years
FROM sys_user u
LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
LEFT JOIN sys_personnel_type pt ON u.personnel_type = pt.type_code
LEFT JOIN sys_education_level el ON u.education = el.level_code
LEFT JOIN sys_job_title jt ON u.job_title = jt.title_code;

-- 创建人员统计视图
CREATE VIEW v_personnel_statistics AS
SELECT
    personnel_type,
    pt.type_name,
    COUNT(*) as personnel_count,
    COUNT(CASE WHEN sex = '0' THEN 1 END) as male_count,
    COUNT(CASE WHEN sex = '1' THEN 1 END) as female_count,
    AVG(age) as avg_age,
    AVG(CASE
        WHEN work_date IS NOT NULL THEN
            TIMESTAMPDIFF(YEAR, work_date, COALESCE(leave_date, retire_date, CURDATE()))
        ELSE NULL
    END) as avg_work_years,
    AVG(salary) as avg_salary
FROM sys_user u
LEFT JOIN sys_personnel_type pt ON u.personnel_type = pt.type_code
WHERE u.del_flag = '0'
GROUP BY personnel_type, pt.type_name
ORDER BY personnel_count DESC;

-- 更新现有用户数据的扩展字段
UPDATE sys_user SET
    real_name = CASE
        WHEN nick_name IS NOT NULL THEN nick_name
        ELSE user_name
    END,
    age = CASE
        WHEN user_name = 'admin' THEN 35
        WHEN user_name LIKE '%teacher%' THEN 40
        WHEN user_name LIKE '%student%' THEN 22
        ELSE FLOOR(25 + RAND() * 15)
    END,
    personnel_type = CASE
        WHEN user_name = 'admin' THEN 'active'
        WHEN user_name LIKE '%teacher%' THEN 'active'
        WHEN user_name LIKE '%student%' THEN 'intern'
        ELSE 'active'
    END,
    employee_number = CONCAT('EMP', LPAD(user_id, 6, '0')),
    education = CASE
        WHEN user_name = 'admin' THEN 'master'
        WHEN user_name LIKE '%teacher%' THEN 'doctor'
        WHEN user_name LIKE '%student%' THEN 'bachelor'
        ELSE 'bachelor'
    END,
    job_title = CASE
        WHEN user_name = 'admin' THEN 'senior_manager'
        WHEN user_name LIKE '%teacher%' THEN 'professor'
        WHEN user_name LIKE '%student%' THEN NULL
        ELSE 'engineer'
    END,
    join_date = DATE_SUB(CURDATE(), INTERVAL FLOOR(1 + RAND() * 5) YEAR),
    work_date = DATE_SUB(CURDATE(), INTERVAL FLOOR(3 + RAND() * 10) YEAR),
    birth_date = DATE_SUB(CURDATE(), INTERVAL (age + FLOOR(RAND() * 2)) YEAR),
    nation = '汉族',
    political_status = CASE
        WHEN user_name = 'admin' THEN '中共党员'
        WHEN RAND() > 0.7 THEN '中共党员'
        WHEN RAND() > 0.5 THEN '共青团员'
        ELSE '群众'
    END,
    health_status = '良好',
    marital_status = CASE
        WHEN age >= 25 THEN '已婚'
        ELSE '未婚'
    END
WHERE user_id > 0;

-- 插入示例人员数据
INSERT INTO sys_user (user_name, nick_name, real_name, user_type, email, phonenumber, sex, password, dept_id, personnel_type, employee_number, education, job_title, age, birth_date, join_date, work_date, nation, political_status, health_status, marital_status, status, create_by) VALUES
-- 在职教师
('teacher001', '张教授', '张明华', '00', '<EMAIL>', '13800001001', '0', '$2a$10$7JB720yubVSOfvVWbfXCL.VqGOZTH4CYqSO8.VqGOZTH4CYqSO8.Vq', 2, 'active', 'EMP000101', 'doctor', 'professor', 45, '1978-03-15', '2005-09-01', '2000-07-01', '汉族', '中共党员', '良好', '已婚', '0', 'admin'),
('teacher002', '李副教授', '李晓红', '00', '<EMAIL>', '13800001002', '1', '$2a$10$7JB720yubVSOfvVWbfXCL.VqGOZTH4CYqSO8.VqGOZTH4CYqSO8.Vq', 2, 'active', 'EMP000102', 'doctor', 'associate_prof', 38, '1985-07-22', '2010-03-01', '2008-07-01', '汉族', '中共党员', '良好', '已婚', '0', 'admin'),
('teacher003', '王讲师', '王建国', '00', '<EMAIL>', '13800001003', '0', '$2a$10$7JB720yubVSOfvVWbfXCL.VqGOZTH4CYqSO8.VqGOZTH4CYqSO8.Vq', 3, 'active', 'EMP000103', 'master', 'lecturer', 32, '1991-11-08', '2015-09-01', '2013-07-01', '汉族', '共青团员', '良好', '已婚', '0', 'admin'),

-- 在职职工
('staff001', '刘管理员', '刘德华', '00', '<EMAIL>', '13800001004', '0', '$2a$10$7JB720yubVSOfvVWbfXCL.VqGOZTH4CYqSO8.VqGOZTH4CYqSO8.Vq', 5, 'active', 'EMP000104', 'bachelor', 'senior_manager', 42, '1981-05-12', '2008-06-01', '2003-07-01', '汉族', '中共党员', '良好', '已婚', '0', 'admin'),
('staff002', '陈工程师', '陈小明', '00', '<EMAIL>', '13800001005', '0', '$2a$10$7JB720yubVSOfvVWbfXCL.VqGOZTH4CYqSO8.VqGOZTH4CYqSO8.Vq', 2, 'active', 'EMP000105', 'bachelor', 'senior_engineer', 35, '1988-09-18', '2012-04-01', '2010-07-01', '汉族', '群众', '良好', '已婚', '0', 'admin'),

-- 退休人员
('retired001', '老张', '张退休', '00', '<EMAIL>', '13800001006', '0', '$2a$10$7JB720yubVSOfvVWbfXCL.VqGOZTH4CYqSO8.VqGOZTH4CYqSO8.Vq', 2, 'retired', 'EMP000106', 'bachelor', 'professor', 65, '1958-02-28', '1982-08-01', '1980-07-01', '汉族', '中共党员', '良好', '已婚', '1', 'admin'),
('retired002', '老李', '李退休', '00', '<EMAIL>', '13800001007', '1', '$2a$10$7JB720yubVSOfvVWbfXCL.VqGOZTH4CYqSO8.VqGOZTH4CYqSO8.Vq', 3, 'retired', 'EMP000107', 'master', 'associate_prof', 62, '1961-06-15', '1985-09-01', '1983-07-01', '汉族', '中共党员', '良好', '已婚', '1', 'admin'),

-- 离职人员
('resigned001', '王离职', '王小离', '00', '<EMAIL>', '13800001008', '1', '$2a$10$7JB720yubVSOfvVWbfXCL.VqGOZTH4CYqSO8.VqGOZTH4CYqSO8.Vq', 2, 'resigned', 'EMP000108', 'master', 'lecturer', 30, '1993-04-10', '2018-03-01', '2016-07-01', '汉族', '群众', '良好', '未婚', '1', 'admin'),

-- 实习人员
('intern001', '小张', '张实习', '00', '<EMAIL>', '13800001009', '0', '$2a$10$7JB720yubVSOfvVWbfXCL.VqGOZTH4CYqSO8.VqGOZTH4CYqSO8.Vq', 2, 'intern', 'EMP000109', 'bachelor', NULL, 23, '2000-08-20', '2023-09-01', '2023-09-01', '汉族', '共青团员', '良好', '未婚', '0', 'admin'),
('intern002', '小李', '李实习', '00', '<EMAIL>', '13800001010', '1', '$2a$10$7JB720yubVSOfvVWbfXCL.VqGOZTH4CYqSO8.VqGOZTH4CYqSO8.Vq', 3, 'intern', 'EMP000110', 'bachelor', NULL, 22, '2001-12-05', '2023-09-01', '2023-09-01', '汉族', '共青团员', '良好', '未婚', '0', 'admin');

-- 更新退休人员的退休时间
UPDATE sys_user SET retire_date = '2023-02-28' WHERE user_name = 'retired001';
UPDATE sys_user SET retire_date = '2023-06-15' WHERE user_name = 'retired002';

-- 更新离职人员的离职时间
UPDATE sys_user SET leave_date = '2022-12-31' WHERE user_name = 'resigned001';

-- 创建索引优化查询性能
CREATE INDEX idx_user_personnel_type ON sys_user(personnel_type);
CREATE INDEX idx_user_dept_id ON sys_user(dept_id);
CREATE INDEX idx_user_employee_number ON sys_user(employee_number);
CREATE INDEX idx_user_real_name ON sys_user(real_name);
CREATE INDEX idx_user_age ON sys_user(age);
CREATE INDEX idx_user_education ON sys_user(education);
CREATE INDEX idx_user_job_title ON sys_user(job_title);
CREATE INDEX idx_user_join_date ON sys_user(join_date);
CREATE INDEX idx_user_birth_date ON sys_user(birth_date);

-- 查询验证数据
SELECT
    user_id,
    user_name,
    real_name,
    sex_name,
    age,
    personnel_type_name,
    dept_name,
    education_name,
    job_title_name,
    work_years,
    company_years
FROM v_personnel_detail
ORDER BY personnel_type, dept_id, user_id;

-- 查询人员统计
SELECT * FROM v_personnel_statistics;