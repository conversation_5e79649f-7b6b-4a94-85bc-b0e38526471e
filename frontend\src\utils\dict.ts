/**
 * 数据字典工具函数
 */

// 模拟数据字典数据
const dictData: Record<string, any[]> = {
  sys_normal_disable: [
    { label: '正常', value: '0', elTagType: 'primary' },
    { label: '停用', value: '1', elTagType: 'danger' }
  ],
  sys_notice_status: [
    { label: '正常', value: '0', elTagType: 'primary' },
    { label: '关闭', value: '1', elTagType: 'danger' }
  ],
  sys_notice_type: [
    { label: '通知', value: '1', elTagType: 'warning' },
    { label: '公告', value: '2', elTagType: 'success' }
  ],
  sys_show_hide: [
    { label: '显示', value: '0', elTagType: 'primary' },
    { label: '隐藏', value: '1', elTagType: 'danger' }
  ],
  sys_yes_no: [
    { label: '是', value: 'Y', elTagType: 'primary' },
    { label: '否', value: 'N', elTagType: 'danger' }
  ]
}

/**
 * 获取数据字典
 * @param dictTypes 字典类型数组
 * @returns 字典数据对象
 */
export function useDict(...dictTypes: string[]) {
  const result: Record<string, any[]> = {}
  
  dictTypes.forEach(dictType => {
    result[dictType] = dictData[dictType] || []
  })
  
  return result
}

/**
 * 根据字典值获取标签
 * @param dictType 字典类型
 * @param value 字典值
 * @returns 字典标签
 */
export function getDictLabel(dictType: string, value: string): string {
  const dict = dictData[dictType]
  if (!dict) return value
  
  const item = dict.find(item => item.value === value)
  return item ? item.label : value
}

/**
 * 根据字典值获取标签类型
 * @param dictType 字典类型
 * @param value 字典值
 * @returns 标签类型
 */
export function getDictTagType(dictType: string, value: string): string {
  const dict = dictData[dictType]
  if (!dict) return 'primary'
  
  const item = dict.find(item => item.value === value)
  return item ? item.elTagType : 'primary'
}

export default useDict
