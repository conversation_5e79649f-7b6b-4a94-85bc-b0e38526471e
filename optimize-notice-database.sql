-- 通知公告表优化SQL脚本
-- 添加索引以提升查询性能

-- 1. 为常用查询字段添加索引
-- 状态字段索引（最重要，因为默认查询会用到）
CREATE INDEX idx_notice_status ON sys_notice(status);

-- 创建时间索引（用于排序）
CREATE INDEX idx_notice_create_time ON sys_notice(create_time DESC);

-- 置顶字段索引（用于排序）
CREATE INDEX idx_notice_is_top ON sys_notice(is_top);

-- 重要程度索引（用于排序）
CREATE INDEX idx_notice_importance ON sys_notice(importance);

-- 公告类型索引（常用筛选条件）
CREATE INDEX idx_notice_type ON sys_notice(notice_type);

-- 2. 复合索引优化
-- 状态+置顶+重要程度+创建时间复合索引（覆盖默认查询的排序）
CREATE INDEX idx_notice_status_top_importance_time ON sys_notice(status, is_top DESC, importance DESC, create_time DESC);

-- 状态+类型复合索引（常用组合查询）
CREATE INDEX idx_notice_status_type ON sys_notice(status, notice_type);

-- 3. 标题搜索优化（如果需要全文搜索）
-- 为标题字段添加全文索引（MySQL 5.7+支持）
-- ALTER TABLE sys_notice ADD FULLTEXT(notice_title);

-- 4. 查看当前表的索引情况
-- SHOW INDEX FROM sys_notice;

-- 5. 分析查询性能
-- EXPLAIN SELECT * FROM sys_notice WHERE status = '0' ORDER BY is_top DESC, importance DESC, create_time DESC LIMIT 10;

-- 6. 清理无用数据（可选）
-- 删除过期的关闭状态公告（根据业务需求）
-- DELETE FROM sys_notice WHERE status = '1' AND create_time < DATE_SUB(NOW(), INTERVAL 1 YEAR);

-- 7. 表统计信息更新
-- ANALYZE TABLE sys_notice;
