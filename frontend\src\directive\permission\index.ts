import { useUserStore } from '@/store/modules/user'
import type { Directive, DirectiveBinding } from 'vue'

/**
 * 权限指令
 * 使用方式：v-hasPermi="['system:user:list']"
 */
export const hasPermi: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding
    const userStore = useUserStore()
    const permissions = userStore.permissions

    if (value && value instanceof Array && value.length > 0) {
      const permissionFlag = value
      const hasPermissions = permissions.some((permission: string) => {
        return permissionFlag.includes(permission)
      })

      if (!hasPermissions) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error('请设置操作权限标签值')
    }
  }
}

/**
 * 角色权限指令
 * 使用方式：v-hasRole="['admin']"
 */
export const hasRole: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding
    const userStore = useUserStore()
    const roles = userStore.roles

    if (value && value instanceof Array && value.length > 0) {
      const roleFlag = value
      const hasRoles = roles.some((role: string) => {
        return roleFlag.includes(role)
      })

      if (!hasRoles) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error('请设置角色权限标签值')
    }
  }
}

/**
 * 权限检查函数
 * 用于在模板表达式中使用
 */
export function checkPermi(permission: string | string[]): boolean {
  const userStore = useUserStore()
  const permissions = userStore.permissions
  
  if (typeof permission === 'string') {
    return permissions.includes(permission)
  }
  
  if (Array.isArray(permission)) {
    return permission.some(p => permissions.includes(p))
  }
  
  return false
}

/**
 * 角色检查函数
 * 用于在模板表达式中使用
 */
export function checkRole(role: string | string[]): boolean {
  const userStore = useUserStore()
  const roles = userStore.roles
  
  if (typeof role === 'string') {
    return roles.includes(role)
  }
  
  if (Array.isArray(role)) {
    return role.some(r => roles.includes(r))
  }
  
  return false
}

export default {
  hasPermi,
  hasRole
}
