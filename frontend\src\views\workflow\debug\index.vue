<template>
  <div class="debug-container">
    <el-card>
      <template #header>
        <span>API调试工具</span>
      </template>
      
      <el-space direction="vertical" style="width: 100%;">
        <!-- 用户API测试 -->
        <el-card shadow="never">
          <template #header>
            <span>用户API测试</span>
          </template>
          <el-button @click="testUserAPI" :loading="loadingUser">测试用户API</el-button>
          <div v-if="userResult" style="margin-top: 10px;">
            <el-tag type="success" v-if="userResult.success">成功</el-tag>
            <el-tag type="danger" v-else>失败</el-tag>
            <pre style="margin-top: 10px; background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ JSON.stringify(userResult.data, null, 2) }}</pre>
          </div>
        </el-card>

        <!-- 部门API测试 -->
        <el-card shadow="never">
          <template #header>
            <span>部门API测试</span>
          </template>
          <el-button @click="testDeptAPI" :loading="loadingDept">测试部门API</el-button>
          <div v-if="deptResult" style="margin-top: 10px;">
            <el-tag type="success" v-if="deptResult.success">成功</el-tag>
            <el-tag type="danger" v-else>失败</el-tag>
            <pre style="margin-top: 10px; background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ JSON.stringify(deptResult.data, null, 2) }}</pre>
          </div>
        </el-card>

        <!-- 角色API测试 -->
        <el-card shadow="never">
          <template #header>
            <span>角色API测试</span>
          </template>
          <el-button @click="testRoleAPI" :loading="loadingRole">测试角色API</el-button>
          <div v-if="roleResult" style="margin-top: 10px;">
            <el-tag type="success" v-if="roleResult.success">成功</el-tag>
            <el-tag type="danger" v-else>失败</el-tag>
            <pre style="margin-top: 10px; background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ JSON.stringify(roleResult.data, null, 2) }}</pre>
          </div>
        </el-card>
      </el-space>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { listUser } from '@/api/system/user'
import { listDept } from '@/api/system/dept'
import { listRole } from '@/api/system/role'

const loadingUser = ref(false)
const loadingDept = ref(false)
const loadingRole = ref(false)

const userResult = ref(null)
const deptResult = ref(null)
const roleResult = ref(null)

const testUserAPI = async () => {
  loadingUser.value = true
  try {
    const response = await listUser({
      pageNum: 1,
      pageSize: 10
    })
    userResult.value = {
      success: response.code === 0 || response.code === 200,
      data: response
    }
    if (response.code === 0 || response.code === 200) {
      ElMessage.success(`用户API测试成功，获取到 ${response.rows?.length || 0} 个用户`)
    } else {
      ElMessage.error(`用户API测试失败: ${response.msg}`)
    }
  } catch (error) {
    userResult.value = {
      success: false,
      data: error
    }
    ElMessage.error(`用户API测试异常: ${error.message}`)
  } finally {
    loadingUser.value = false
  }
}

const testDeptAPI = async () => {
  loadingDept.value = true
  try {
    const response = await listDept({})
    deptResult.value = {
      success: response.code === 0 || response.code === 200,
      data: response
    }
    if (response.code === 0 || response.code === 200) {
      // 部门API返回的数据在 data 字段中
      const deptCount = response.data?.length || 0
      ElMessage.success(`部门API测试成功，获取到 ${deptCount} 个部门`)
    } else {
      ElMessage.error(`部门API测试失败: ${response.msg}`)
    }
  } catch (error) {
    deptResult.value = {
      success: false,
      data: error
    }
    ElMessage.error(`部门API测试异常: ${error.message}`)
  } finally {
    loadingDept.value = false
  }
}

const testRoleAPI = async () => {
  loadingRole.value = true
  try {
    const response = await listRole({
      pageNum: 1,
      pageSize: 10
    })
    roleResult.value = {
      success: response.code === 0 || response.code === 200,
      data: response
    }
    if (response.code === 0 || response.code === 200) {
      ElMessage.success(`角色API测试成功，获取到 ${response.rows?.length || 0} 个角色`)
    } else {
      ElMessage.error(`角色API测试失败: ${response.msg}`)
    }
  } catch (error) {
    roleResult.value = {
      success: false,
      data: error
    }
    ElMessage.error(`角色API测试异常: ${error.message}`)
  } finally {
    loadingRole.value = false
  }
}
</script>

<style scoped>
.debug-container {
  padding: 20px;
}

pre {
  max-height: 300px;
  overflow-y: auto;
  font-size: 12px;
}
</style>
