# 多阶段构建
# 第一阶段：构建前端
FROM node:16-alpine as frontend-build

WORKDIR /app/frontend

# 复制前端项目文件
COPY frontend/package*.json ./
RUN npm install

COPY frontend/ ./
RUN npm run build:prod

# 第二阶段：构建后端
FROM maven:3.8.6-openjdk-8-slim as backend-build

WORKDIR /app/backend

# 复制后端项目文件
COPY backend/pom.xml ./
RUN mvn dependency:go-offline -B

COPY backend/src ./src
RUN mvn clean package -DskipTests

# 第三阶段：运行时镜像
FROM openjdk:8-jre-alpine

# 安装nginx
RUN apk add --no-cache nginx

# 创建应用目录
WORKDIR /app

# 复制后端jar包
COPY --from=backend-build /app/backend/target/*.jar app.jar

# 复制前端构建文件到nginx目录
COPY --from=frontend-build /app/frontend/dist /usr/share/nginx/html

# 复制nginx配置
COPY docker/nginx.conf /etc/nginx/nginx.conf

# 创建启动脚本
RUN echo '#!/bin/sh' > /app/start.sh && \
    echo 'nginx &' >> /app/start.sh && \
    echo 'java -jar /app/app.jar' >> /app/start.sh && \
    chmod +x /app/start.sh

# 暴露端口
EXPOSE 80 8080

# 启动应用
CMD ["/app/start.sh"]
