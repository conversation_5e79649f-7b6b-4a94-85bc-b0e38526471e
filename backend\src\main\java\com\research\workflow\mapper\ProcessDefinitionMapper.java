package com.research.workflow.mapper;

import com.research.workflow.domain.ProcessDefinition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流程定义Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@Mapper
public interface ProcessDefinitionMapper {
    
    /**
     * 查询流程定义
     * 
     * @param id 流程定义主键
     * @return 流程定义
     */
    ProcessDefinition selectProcessDefinitionById(Long id);

    /**
     * 查询流程定义列表
     * 
     * @param processDefinition 流程定义
     * @return 流程定义集合
     */
    List<ProcessDefinition> selectProcessDefinitionList(ProcessDefinition processDefinition);

    /**
     * 新增流程定义
     * 
     * @param processDefinition 流程定义
     * @return 结果
     */
    int insertProcessDefinition(ProcessDefinition processDefinition);

    /**
     * 修改流程定义
     * 
     * @param processDefinition 流程定义
     * @return 结果
     */
    int updateProcessDefinition(ProcessDefinition processDefinition);

    /**
     * 删除流程定义
     * 
     * @param id 流程定义主键
     * @return 结果
     */
    int deleteProcessDefinitionById(Long id);

    /**
     * 批量删除流程定义
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteProcessDefinitionByIds(Long[] ids);

    /**
     * 根据流程定义Key查询最新版本
     * 
     * @param processKey 流程定义Key
     * @return 流程定义
     */
    ProcessDefinition selectLatestProcessDefinitionByKey(@Param("processKey") String processKey);

    /**
     * 查询已部署的流程定义列表
     * 
     * @return 流程定义集合
     */
    List<ProcessDefinition> selectDeployedProcessDefinitionList();

    /**
     * 根据部署ID查询流程定义
     * 
     * @param deploymentId 部署ID
     * @return 流程定义集合
     */
    List<ProcessDefinition> selectProcessDefinitionByDeploymentId(@Param("deploymentId") String deploymentId);

    /**
     * 根据流程Key和版本查询流程定义
     * 
     * @param processKey 流程Key
     * @param version 版本号
     * @return 流程定义
     */
    ProcessDefinition selectProcessDefinitionByKeyAndVersion(@Param("processKey") String processKey, @Param("version") Integer version);

    /**
     * 查询流程定义的所有版本
     * 
     * @param processKey 流程Key
     * @return 流程定义集合
     */
    List<ProcessDefinition> selectProcessDefinitionVersions(@Param("processKey") String processKey);

    /**
     * 更新流程定义状态
     * 
     * @param id 流程定义ID
     * @param status 状态
     * @return 结果
     */
    int updateProcessDefinitionStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 更新流程定义挂起状态
     * 
     * @param id 流程定义ID
     * @param suspended 是否挂起
     * @return 结果
     */
    int updateProcessDefinitionSuspended(@Param("id") Long id, @Param("suspended") Boolean suspended);

    /**
     * 增加流程定义使用次数
     * 
     * @param id 流程定义ID
     * @return 结果
     */
    int incrementUsageCount(@Param("id") Long id);

    /**
     * 更新最后使用时间
     * 
     * @param id 流程定义ID
     * @return 结果
     */
    int updateLastUsedTime(@Param("id") Long id);
}
