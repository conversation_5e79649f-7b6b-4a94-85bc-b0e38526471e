<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="流程Key" prop="processDefinitionKey">
        <el-input
          v-model="queryParams.processDefinitionKey"
          placeholder="请输入流程Key"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业务Key" prop="businessKey">
        <el-input
          v-model="queryParams.businessKey"
          placeholder="请输入业务Key"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Check"
          :disabled="multiple"
          @click="handleBatchClaim"
          v-hasPermi="['workflow:task:claim']"
        >批量签收</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="我的任务" name="my">
        <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="任务名称" align="center" prop="name" :show-overflow-tooltip="true" />
          <el-table-column label="流程名称" align="center" prop="processDefinitionName" :show-overflow-tooltip="true" />
          <el-table-column label="业务Key" align="center" prop="businessKey" :show-overflow-tooltip="true" />
          <el-table-column label="任务执行人" align="center" prop="assignee" />
          <el-table-column label="优先级" align="center" prop="priority" width="100">
            <template #default="scope">
              <el-tag :type="getPriorityType(scope.row.priority)">
                {{ getPriorityText(scope.row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="到期时间" align="center" prop="dueDate" width="180">
            <template #default="scope">
              <span :class="{ 'overdue': isOverdue(scope.row.dueDate) }">
                {{ parseTime(scope.row.dueDate, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
            <template #default="scope">
              <el-button link type="primary" @click="handleView(scope.row)">
                <el-icon><View /></el-icon>查看
              </el-button>
              <el-button link type="success" @click="handleComplete(scope.row)" v-hasPermi="['workflow:task:complete']">
                <el-icon><Check /></el-icon>完成
              </el-button>
              <el-button link type="warning" @click="handleAssign(scope.row)" v-hasPermi="['workflow:task:assign']">
                <el-icon><User /></el-icon>转办
              </el-button>
              <el-button link type="info" @click="handleDelegate(scope.row)" v-hasPermi="['workflow:task:delegate']">
                <el-icon><Share /></el-icon>委派
              </el-button>
              <el-button link type="primary" @click="handleVariables(scope.row)">
                <el-icon><Setting /></el-icon>变量
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      
      <el-tab-pane label="候选任务" name="candidate">
        <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="任务名称" align="center" prop="name" :show-overflow-tooltip="true" />
          <el-table-column label="流程名称" align="center" prop="processDefinitionName" :show-overflow-tooltip="true" />
          <el-table-column label="业务Key" align="center" prop="businessKey" :show-overflow-tooltip="true" />
          <el-table-column label="候选用户" align="center" prop="candidateUsers" />
          <el-table-column label="候选组" align="center" prop="candidateGroups" />
          <el-table-column label="优先级" align="center" prop="priority" width="100">
            <template #default="scope">
              <el-tag :type="getPriorityType(scope.row.priority)">
                {{ getPriorityText(scope.row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
            <template #default="scope">
              <el-button link type="primary" @click="handleView(scope.row)">
                <el-icon><View /></el-icon>查看
              </el-button>
              <el-button link type="success" @click="handleClaim(scope.row)" v-hasPermi="['workflow:task:claim']">
                <el-icon><UserFilled /></el-icon>签收
              </el-button>
              <el-button link type="primary" @click="handleVariables(scope.row)">
                <el-icon><Setting /></el-icon>变量
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      
      <el-tab-pane label="历史任务" name="history">
        <el-table v-loading="loading" :data="taskList">
          <el-table-column label="任务名称" align="center" prop="name" :show-overflow-tooltip="true" />
          <el-table-column label="流程名称" align="center" prop="processDefinitionName" :show-overflow-tooltip="true" />
          <el-table-column label="业务Key" align="center" prop="businessKey" :show-overflow-tooltip="true" />
          <el-table-column label="任务执行人" align="center" prop="assignee" />
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="持续时间" align="center" prop="durationInMillis" width="120">
            <template #default="scope">
              <span>{{ formatDuration(scope.row.durationInMillis) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="删除原因" align="center" prop="deleteReason" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
            <template #default="scope">
              <el-button link type="primary" @click="handleView(scope.row)">
                <el-icon><View /></el-icon>查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 完成任务对话框 -->
    <el-dialog title="完成任务" v-model="complete.open" width="600px" append-to-body>
      <el-form ref="completeRef" :model="complete.form" label-width="100px">
        <el-form-item label="处理意见">
          <el-input
            v-model="complete.form.comment"
            type="textarea"
            :rows="4"
            placeholder="请输入处理意见"
          />
        </el-form-item>
        <el-form-item label="任务变量">
          <el-input
            v-model="complete.form.variablesJson"
            type="textarea"
            :rows="4"
            placeholder="请输入JSON格式的任务变量，如：{&quot;key1&quot;: &quot;value1&quot;, &quot;key2&quot;: &quot;value2&quot;}"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitCompleteForm">确 定</el-button>
          <el-button @click="cancelComplete">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 转办任务对话框 -->
    <el-dialog title="转办任务" v-model="assign.open" width="400px" append-to-body>
      <el-form ref="assignRef" :model="assign.form" :rules="assign.rules" label-width="100px">
        <el-form-item label="转办给" prop="userId">
          <el-input v-model="assign.form.userId" placeholder="请输入用户ID" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitAssignForm">确 定</el-button>
          <el-button @click="cancelAssign">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 委派任务对话框 -->
    <el-dialog title="委派任务" v-model="delegate.open" width="400px" append-to-body>
      <el-form ref="delegateRef" :model="delegate.form" :rules="delegate.rules" label-width="100px">
        <el-form-item label="委派给" prop="userId">
          <el-input v-model="delegate.form.userId" placeholder="请输入用户ID" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitDelegateForm">确 定</el-button>
          <el-button @click="cancelDelegate">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 任务变量对话框 -->
    <el-dialog title="任务变量" v-model="variables.open" width="600px" append-to-body>
      <el-table :data="variables.list" style="width: 100%">
        <el-table-column label="变量名" prop="name" />
        <el-table-column label="变量值" prop="value" />
        <el-table-column label="类型" prop="type" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup name="Task" lang="ts">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { 
  listMyTask,
  listCandidateTask,
  listHistoryTask,
  getTask,
  claimTask,
  completeTask,
  assignTask,
  delegateTask,
  getTaskVariables
} from '@/api/workflow/task'

const { proxy } = getCurrentInstance() as any
const { parseTime } = proxy

const taskList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const activeTab = ref('my')

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: '',
  processDefinitionKey: '',
  businessKey: ''
})

// 完成任务参数
const complete = reactive({
  open: false,
  taskId: '',
  form: {
    comment: '',
    variablesJson: ''
  }
})

// 转办任务参数
const assign = reactive({
  open: false,
  taskId: '',
  form: {
    userId: ''
  },
  rules: {
    userId: [
      { required: true, message: '用户ID不能为空', trigger: 'blur' }
    ]
  }
})

// 委派任务参数
const delegate = reactive({
  open: false,
  taskId: '',
  form: {
    userId: ''
  },
  rules: {
    userId: [
      { required: true, message: '用户ID不能为空', trigger: 'blur' }
    ]
  }
})

// 任务变量参数
const variables = reactive({
  open: false,
  list: []
})

/** 查询任务列表 */
function getList() {
  loading.value = true
  let queryFunc
  switch (activeTab.value) {
    case 'my':
      queryFunc = listMyTask
      break
    case 'candidate':
      queryFunc = listCandidateTask
      break
    case 'history':
      queryFunc = listHistoryTask
      break
    default:
      queryFunc = listMyTask
  }
  
  queryFunc(queryParams).then(response => {
    taskList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef')
  handleQuery()
}

/** 标签页切换 */
function handleTabClick() {
  getList()
}

// 多选框选中数据
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 查看按钮操作 */
function handleView(row: any) {
  proxy.$modal.msgSuccess('查看功能开发中...')
}

/** 签收按钮操作 */
function handleClaim(row: any) {
  proxy.$modal.confirm('确认要签收任务"' + row.name + '"吗？').then(() => {
    return claimTask(row.id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess('签收成功')
  }).catch(() => {})
}

/** 批量签收按钮操作 */
function handleBatchClaim() {
  const taskIds = ids.value
  proxy.$modal.confirm('确认要批量签收选中的任务吗？').then(() => {
    const promises = taskIds.map(taskId => claimTask(taskId))
    return Promise.all(promises)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess('批量签收成功')
  }).catch(() => {})
}

/** 完成按钮操作 */
function handleComplete(row: any) {
  complete.taskId = row.id
  complete.open = true
}

/** 转办按钮操作 */
function handleAssign(row: any) {
  assign.taskId = row.id
  assign.open = true
}

/** 委派按钮操作 */
function handleDelegate(row: any) {
  delegate.taskId = row.id
  delegate.open = true
}

/** 任务变量按钮操作 */
function handleVariables(row: any) {
  getTaskVariables(row.id).then(response => {
    variables.list = Object.keys(response.data).map(key => ({
      name: key,
      value: response.data[key],
      type: typeof response.data[key]
    }))
    variables.open = true
  })
}

/** 提交完成表单 */
function submitCompleteForm() {
  const variables = complete.form.variablesJson ? JSON.parse(complete.form.variablesJson) : {}
  const data = {
    comment: complete.form.comment,
    variables: variables
  }
  completeTask(complete.taskId, data).then(response => {
    proxy.$modal.msgSuccess('完成成功')
    complete.open = false
    getList()
  })
}

/** 取消完成 */
function cancelComplete() {
  complete.open = false
  proxy.resetForm('completeRef')
}

/** 提交转办表单 */
function submitAssignForm() {
  proxy.$refs['assignRef'].validate((valid: boolean) => {
    if (valid) {
      assignTask(assign.taskId, assign.form.userId).then(response => {
        proxy.$modal.msgSuccess('转办成功')
        assign.open = false
        getList()
      })
    }
  })
}

/** 取消转办 */
function cancelAssign() {
  assign.open = false
  proxy.resetForm('assignRef')
}

/** 提交委派表单 */
function submitDelegateForm() {
  proxy.$refs['delegateRef'].validate((valid: boolean) => {
    if (valid) {
      delegateTask(delegate.taskId, delegate.form.userId).then(response => {
        proxy.$modal.msgSuccess('委派成功')
        delegate.open = false
        getList()
      })
    }
  })
}

/** 取消委派 */
function cancelDelegate() {
  delegate.open = false
  proxy.resetForm('delegateRef')
}

/** 获取优先级类型 */
function getPriorityType(priority: number) {
  if (priority >= 80) return 'danger'
  if (priority >= 60) return 'warning'
  if (priority >= 40) return 'primary'
  return 'info'
}

/** 获取优先级文本 */
function getPriorityText(priority: number) {
  if (priority >= 80) return '紧急'
  if (priority >= 60) return '高'
  if (priority >= 40) return '中'
  return '低'
}

/** 判断是否逾期 */
function isOverdue(dueDate: string) {
  if (!dueDate) return false
  return new Date(dueDate) < new Date()
}

/** 格式化持续时间 */
function formatDuration(milliseconds: number) {
  if (!milliseconds) return '-'
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) return `${days}天${hours % 24}小时`
  if (hours > 0) return `${hours}小时${minutes % 60}分钟`
  if (minutes > 0) return `${minutes}分钟${seconds % 60}秒`
  return `${seconds}秒`
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.overdue {
  color: #f56c6c;
  font-weight: bold;
}
</style>
