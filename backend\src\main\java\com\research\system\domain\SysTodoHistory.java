package com.research.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 待办处理历史实体类
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_todo_history")
public class SysTodoHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 待办ID */
    @TableField("todo_id")
    private Long todoId;

    /** 操作类型（1创建 2分配 3处理 4完成 5取消 6重新打开） */
    @TableField("action_type")
    private String actionType;

    /** 操作描述 */
    @TableField("action_desc")
    private String actionDesc;

    /** 操作人ID */
    @TableField("operator_id")
    private Long operatorId;

    /** 操作人姓名 */
    @TableField("operator_name")
    private String operatorName;

    /** 原状态 */
    @TableField("old_status")
    private String oldStatus;

    /** 新状态 */
    @TableField("new_status")
    private String newStatus;

    /** 原负责人ID */
    @TableField("old_assignee_id")
    private Long oldAssigneeId;

    /** 新负责人ID */
    @TableField("new_assignee_id")
    private Long newAssigneeId;

    /** 处理结果 */
    @TableField("process_result")
    private String processResult;

    /** 附件路径 */
    @TableField("attachment_path")
    private String attachmentPath;

    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 备注 */
    @TableField("remark")
    private String remark;

    // 非数据库字段
    /** 原负责人姓名 */
    @TableField(exist = false)
    private String oldAssigneeName;

    /** 新负责人姓名 */
    @TableField(exist = false)
    private String newAssigneeName;

    // 手动添加getter和setter方法以解决Lombok编译问题
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTodoId() {
        return todoId;
    }

    public void setTodoId(Long todoId) {
        this.todoId = todoId;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public String getActionDesc() {
        return actionDesc;
    }

    public void setActionDesc(String actionDesc) {
        this.actionDesc = actionDesc;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getOldStatus() {
        return oldStatus;
    }

    public void setOldStatus(String oldStatus) {
        this.oldStatus = oldStatus;
    }

    public String getNewStatus() {
        return newStatus;
    }

    public void setNewStatus(String newStatus) {
        this.newStatus = newStatus;
    }

    public Long getOldAssigneeId() {
        return oldAssigneeId;
    }

    public void setOldAssigneeId(Long oldAssigneeId) {
        this.oldAssigneeId = oldAssigneeId;
    }

    public Long getNewAssigneeId() {
        return newAssigneeId;
    }

    public void setNewAssigneeId(Long newAssigneeId) {
        this.newAssigneeId = newAssigneeId;
    }

    public String getProcessResult() {
        return processResult;
    }

    public void setProcessResult(String processResult) {
        this.processResult = processResult;
    }

    public String getAttachmentPath() {
        return attachmentPath;
    }

    public void setAttachmentPath(String attachmentPath) {
        this.attachmentPath = attachmentPath;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
