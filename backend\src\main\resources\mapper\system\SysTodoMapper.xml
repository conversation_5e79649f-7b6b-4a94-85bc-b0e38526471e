<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.research.system.mapper.SysTodoMapper">
    
    <resultMap type="SysTodo" id="SysTodoResult">
        <result property="todoId"           column="todo_id"               />
        <result property="title"            column="title"                 />
        <result property="content"          column="content"               />
        <result property="todoType"         column="todo_type"             />
        <result property="priority"         column="priority"              />
        <result property="status"           column="status"                />
        <result property="assigneeId"       column="assignee_id"           />
        <result property="assigneeName"     column="assignee_name"         />
        <result property="dueTime"          column="due_time"              />
        <result property="completeTime"     column="complete_time"         />
        <result property="businessId"       column="business_id"           />
        <result property="businessType"     column="business_type"         />
        <result property="businessTitle"    column="business_title"        />
        <result property="result"           column="result"                />
        <result property="isRead"           column="is_read"               />
        <result property="remindTime"       column="remind_time"           />
        <result property="isReminded"       column="is_reminded"           />
        <result property="tags"             column="tags"                  />
        <result property="attachmentPath"   column="attachment_path"       />
        <result property="createBy"         column="create_by"             />
        <result property="createTime"       column="create_time"           />
        <result property="updateBy"         column="update_by"             />
        <result property="updateTime"       column="update_time"           />
        <result property="remark"           column="remark"                />
        <result property="createByName"     column="create_by_name"        />
    </resultMap>

    <sql id="selectTodoVo">
        select t.todo_id, t.title, t.content, t.todo_type, t.priority, t.status, 
               t.assignee_id, t.assignee_name, t.due_time, t.complete_time,
               t.business_id, t.business_type, t.business_title, t.result,
               t.is_read, t.remind_time, t.is_reminded, t.tags, t.attachment_path,
               t.create_by, t.create_time, t.update_by, t.update_time, t.remark,
               u.nick_name as create_by_name
        from sys_todo t
        left join sys_user u on t.create_by = u.user_name
    </sql>

    <!-- 查询待办事项列表 -->
    <select id="selectTodoList" resultMap="SysTodoResult">
        <include refid="selectTodoVo"/>
        <where>
            <if test="todo != null">
                <if test="todo.title != null and todo.title != ''">
                    AND t.title like concat('%', #{todo.title}, '%')
                </if>
                <if test="todo.priority != null and todo.priority != ''">
                    AND t.priority = #{todo.priority}
                </if>
                <if test="todo.status != null and todo.status != ''">
                    AND t.status = #{todo.status}
                </if>
                <if test="todo.assigneeName != null and todo.assigneeName != ''">
                    AND t.assignee_name like concat('%', #{todo.assigneeName}, '%')
                </if>
                <if test="todo.todoType != null and todo.todoType != ''">
                    AND t.todo_type = #{todo.todoType}
                </if>
            </if>
        </where>
        order by t.priority desc, t.create_time desc
    </select>

    <!-- 查询我的待办事项列表 -->
    <select id="selectMyTodoList" resultMap="SysTodoResult">
        <include refid="selectTodoVo"/>
        <where>
            AND t.assignee_id = #{userId}
            <if test="todo != null">
                <if test="todo.title != null and todo.title != ''">
                    AND t.title like concat('%', #{todo.title}, '%')
                </if>
                <if test="todo.priority != null and todo.priority != ''">
                    AND t.priority = #{todo.priority}
                </if>
                <if test="todo.status != null and todo.status != ''">
                    AND t.status = #{todo.status}
                </if>
                <if test="todo.todoType != null and todo.todoType != ''">
                    AND t.todo_type = #{todo.todoType}
                </if>
            </if>
        </where>
        order by t.priority desc, t.create_time desc
    </select>

    <!-- 查询我创建的待办事项列表 -->
    <select id="selectMyCreatedTodoList" resultMap="SysTodoResult">
        <include refid="selectTodoVo"/>
        <where>
            AND t.create_by = (select user_name from sys_user where user_id = #{userId})
            <if test="todo != null">
                <if test="todo.title != null and todo.title != ''">
                    AND t.title like concat('%', #{todo.title}, '%')
                </if>
                <if test="todo.priority != null and todo.priority != ''">
                    AND t.priority = #{todo.priority}
                </if>
                <if test="todo.status != null and todo.status != ''">
                    AND t.status = #{todo.status}
                </if>
                <if test="todo.assigneeName != null and todo.assigneeName != ''">
                    AND t.assignee_name like concat('%', #{todo.assigneeName}, '%')
                </if>
                <if test="todo.todoType != null and todo.todoType != ''">
                    AND t.todo_type = #{todo.todoType}
                </if>
            </if>
        </where>
        order by t.priority desc, t.create_time desc
    </select>

    <!-- 查询待办事项详情 -->
    <select id="selectTodoDetail" resultMap="SysTodoResult">
        <include refid="selectTodoVo"/>
        where t.todo_id = #{todoId}
    </select>

    <!-- 查询用户待办事项统计 -->
    <select id="selectTodoStatistics" resultType="java.util.Map">
        select 
            count(*) as total,
            count(case when status = '0' then 1 end) as pending,
            count(case when status = '1' then 1 end) as processing,
            count(case when status = '2' then 1 end) as completed,
            count(case when status = '3' then 1 end) as cancelled,
            count(case when due_time &lt; now() and status in ('0', '1') then 1 end) as overdue
        from sys_todo 
        where assignee_id = #{userId}
    </select>

    <!-- 查询最近待办事项 -->
    <select id="selectRecentTodos" resultMap="SysTodoResult">
        <include refid="selectTodoVo"/>
        where t.assignee_id = #{userId}
        order by t.create_time desc
        limit #{limit}
    </select>

    <!-- 查询即将到期的待办事项 -->
    <select id="selectDueSoonTodos" resultMap="SysTodoResult">
        <include refid="selectTodoVo"/>
        where t.assignee_id = #{userId}
          and t.status in ('0', '1')
          and t.due_time between now() and date_add(now(), interval #{days} day)
        order by t.due_time asc
    </select>

    <!-- 查询逾期的待办事项 -->
    <select id="selectOverdueTodos" resultMap="SysTodoResult">
        <include refid="selectTodoVo"/>
        where t.assignee_id = #{userId}
          and t.status in ('0', '1')
          and t.due_time &lt; now()
        order by t.due_time asc
    </select>

    <!-- 批量更新待办状态 -->
    <update id="batchUpdateStatus">
        update sys_todo 
        set status = #{status}, update_by = #{updateBy}, update_time = now()
        where todo_id in
        <foreach collection="todoIds" item="todoId" open="(" separator="," close=")">
            #{todoId}
        </foreach>
    </update>

    <!-- 标记待办为已读 -->
    <update id="markAsRead">
        update sys_todo 
        set is_read = '1', update_time = now()
        where todo_id = #{todoId} and assignee_id = #{userId}
    </update>

    <!-- 查询待办事项按优先级统计 -->
    <select id="selectTodoByPriorityStats" resultType="java.util.Map">
        select 
            priority,
            count(*) as count,
            case priority
                when '1' then '低'
                when '2' then '中'
                when '3' then '高'
                when '4' then '紧急'
                else '未知'
            end as priorityName
        from sys_todo 
        where assignee_id = #{userId}
        group by priority
        order by priority desc
    </select>

    <!-- 查询待办事项按状态统计 -->
    <select id="selectTodoByStatusStats" resultType="java.util.Map">
        select 
            status,
            count(*) as count,
            case status
                when '0' then '待处理'
                when '1' then '处理中'
                when '2' then '已完成'
                when '3' then '已取消'
                else '未知'
            end as statusName
        from sys_todo 
        where assignee_id = #{userId}
        group by status
        order by status
    </select>

</mapper>
