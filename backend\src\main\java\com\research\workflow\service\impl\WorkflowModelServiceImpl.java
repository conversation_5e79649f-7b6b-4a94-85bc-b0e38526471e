package com.research.workflow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.research.common.utils.SecurityUtils;
import com.research.common.utils.StringUtils;
import com.research.common.utils.IdUtils;
import com.research.workflow.domain.WorkflowModel;
import com.research.workflow.mapper.WorkflowModelMapper;
import com.research.workflow.service.IWorkflowModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作流模型Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class WorkflowModelServiceImpl extends ServiceImpl<WorkflowModelMapper, WorkflowModel> implements IWorkflowModelService {

    @Autowired
    private WorkflowModelMapper modelMapper;

    /**
     * 查询工作流模型列表
     */
    @Override
    public IPage<WorkflowModel> selectModelList(Page<WorkflowModel> page, WorkflowModel model) {
        return modelMapper.selectModelList(page, model);
    }

    /**
     * 查询工作流模型详情
     */
    @Override
    public WorkflowModel selectModelById(String id) {
        return modelMapper.selectModelById(id);
    }

    /**
     * 新增工作流模型
     */
    @Override
    public boolean insertModel(WorkflowModel model) {
        // 设置默认值
        if (StringUtils.isEmpty(model.getId())) {
            model.setId(IdUtils.fastSimpleUUID());
        }
        if (model.getVersion() == null) {
            // 获取该Key的最大版本号
            Integer maxVersion = modelMapper.selectMaxVersionByKey(model.getKey());
            model.setVersion(maxVersion == null ? 1 : maxVersion + 1);
        }
        if (StringUtils.isEmpty(model.getStatus())) {
            model.setStatus("0"); // 默认草稿状态
        }
        if (model.getHasEditorSource() == null) {
            model.setHasEditorSource(true);
        }
        if (model.getHasEditorSourceExtra() == null) {
            model.setHasEditorSourceExtra(false);
        }
        if (model.getDeployed() == null) {
            model.setDeployed(false);
        }
        if (StringUtils.isEmpty(model.getVisible())) {
            model.setVisible("1"); // 默认可见
        }
        
        model.setCreateBy(SecurityUtils.getUsername());
        model.setCreateTime(new Date());
        model.setLastUpdateTime(new Date());
        
        return this.save(model);
    }

    /**
     * 修改工作流模型
     */
    @Override
    public boolean updateModel(WorkflowModel model) {
        model.setUpdateBy(SecurityUtils.getUsername());
        model.setUpdateTime(new Date());
        model.setLastUpdateTime(new Date());
        
        return this.updateById(model);
    }

    /**
     * 删除工作流模型
     */
    @Override
    public boolean deleteModelById(String id) {
        return this.removeById(id);
    }

    /**
     * 批量删除工作流模型
     */
    @Override
    public boolean deleteModelByIds(List<String> ids) {
        return this.removeByIds(ids);
    }

    /**
     * 保存模型JSON内容
     */
    @Override
    public boolean saveModelJson(String id, String json) {
        WorkflowModel model = new WorkflowModel();
        model.setId(id);
        model.setModelJson(json);
        model.setUpdateBy(SecurityUtils.getUsername());
        model.setUpdateTime(new Date());
        model.setLastUpdateTime(new Date());
        
        return this.updateById(model);
    }

    /**
     * 保存模型XML内容
     */
    @Override
    public boolean saveModelXml(String id, String xml) {
        WorkflowModel model = new WorkflowModel();
        model.setId(id);
        model.setModelXml(xml);
        model.setUpdateBy(SecurityUtils.getUsername());
        model.setUpdateTime(new Date());
        model.setLastUpdateTime(new Date());
        
        return this.updateById(model);
    }

    /**
     * 获取模型JSON内容
     */
    @Override
    public String getModelJson(String id) {
        WorkflowModel model = this.getById(id);
        return model != null ? model.getModelJson() : null;
    }

    /**
     * 获取模型XML内容
     */
    @Override
    public String getModelXml(String id) {
        WorkflowModel model = this.getById(id);
        if (model != null && StringUtils.isNotEmpty(model.getModelXml())) {
            return model.getModelXml();
        }
        
        // 返回默认的BPMN XML
        return getDefaultBpmnXml(model != null ? model.getKey() : "Process_1");
    }

    /**
     * 复制模型
     */
    @Override
    public WorkflowModel copyModel(String sourceId, String newName, String newKey) {
        WorkflowModel sourceModel = this.getById(sourceId);
        if (sourceModel == null) {
            return null;
        }
        
        WorkflowModel newModel = new WorkflowModel();
        newModel.setId(IdUtils.fastSimpleUUID());
        newModel.setName(newName);
        newModel.setKey(newKey);
        newModel.setCategory(sourceModel.getCategory());
        newModel.setDescription("复制自: " + sourceModel.getName());
        newModel.setVersion(1);
        newModel.setModelJson(sourceModel.getModelJson());
        newModel.setModelXml(sourceModel.getModelXml());
        newModel.setHasEditorSource(sourceModel.getHasEditorSource());
        newModel.setHasEditorSourceExtra(sourceModel.getHasEditorSourceExtra());
        newModel.setStatus("0"); // 草稿状态
        newModel.setDeployed(false);
        newModel.setVisible("1");
        newModel.setIcon(sourceModel.getIcon());
        newModel.setColor(sourceModel.getColor());
        newModel.setTags(sourceModel.getTags());
        newModel.setFormConfig(sourceModel.getFormConfig());
        newModel.setPermissionConfig(sourceModel.getPermissionConfig());
        newModel.setNotificationConfig(sourceModel.getNotificationConfig());
        newModel.setTimeoutConfig(sourceModel.getTimeoutConfig());
        
        if (this.insertModel(newModel)) {
            return newModel;
        }
        return null;
    }

    /**
     * 部署模型
     */
    @Override
    public Map<String, Object> deployModel(String id) {
        WorkflowModel model = this.getById(id);
        if (model == null) {
            return null;
        }
        
        // 模拟部署过程
        String deploymentId = "deployment_" + System.currentTimeMillis();
        String processDefinitionId = "process_" + System.currentTimeMillis();
        
        // 更新模型部署信息
        model.setDeploymentId(deploymentId);
        model.setProcessDefinitionId(processDefinitionId);
        model.setDeployed(true);
        model.setDeployTime(new Date());
        model.setStatus("1"); // 已发布状态
        
        this.updateModel(model);
        
        Map<String, Object> result = new HashMap<>();
        result.put("deploymentId", deploymentId);
        result.put("processDefinitionId", processDefinitionId);
        result.put("deployTime", new Date());
        
        return result;
    }

    /**
     * 导入模型
     */
    @Override
    public WorkflowModel importModel(String name, String key, String xml) {
        WorkflowModel model = new WorkflowModel();
        model.setName(name);
        model.setKey(key);
        model.setDescription("导入的模型");
        model.setModelXml(xml);
        model.setCategory("imported");
        
        if (this.insertModel(model)) {
            return model;
        }
        return null;
    }

    /**
     * 导出模型
     */
    @Override
    public String exportModel(String id) {
        return this.getModelXml(id);
    }

    /**
     * 根据模型Key查询模型
     */
    @Override
    public WorkflowModel selectModelByKey(String key) {
        return modelMapper.selectModelByKey(key);
    }

    /**
     * 查询已部署的模型列表
     */
    @Override
    public List<WorkflowModel> selectDeployedModels() {
        return modelMapper.selectDeployedModels();
    }

    /**
     * 查询模型统计信息
     */
    @Override
    public Map<String, Object> selectModelStatistics() {
        return modelMapper.selectModelStatistics();
    }

    /**
     * 查询最近创建的模型
     */
    @Override
    public List<WorkflowModel> selectRecentModels(Integer limit) {
        return modelMapper.selectRecentModels(limit);
    }

    /**
     * 查询最近更新的模型
     */
    @Override
    public List<WorkflowModel> selectRecentUpdatedModels(Integer limit) {
        return modelMapper.selectRecentUpdatedModels(limit);
    }

    /**
     * 批量更新模型状态
     */
    @Override
    public boolean batchUpdateStatus(List<String> ids, String status) {
        String updateBy = SecurityUtils.getUsername();
        return modelMapper.batchUpdateStatus(ids, status, updateBy) > 0;
    }

    /**
     * 清理未部署的草稿模型
     */
    @Override
    public int cleanDraftModels(Integer days) {
        return modelMapper.cleanDraftModels(days);
    }

    /**
     * 验证模型Key是否唯一
     */
    @Override
    public boolean checkModelKeyUnique(String key, String excludeId) {
        QueryWrapper<WorkflowModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("model_key", key);
        if (StringUtils.isNotEmpty(excludeId)) {
            queryWrapper.ne("id", excludeId);
        }
        // 使用Mapper查询避免MyBatis-Plus版本兼容问题
        List<WorkflowModel> list = modelMapper.selectList(queryWrapper);
        return list == null || list.isEmpty();
    }

    /**
     * 获取默认的BPMN XML
     */
    private String getDefaultBpmnXml(String processKey) {
        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<bpmn2:definitions xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" " +
                "xmlns:bpmn2=\"http://www.omg.org/spec/BPMN/20100524/MODEL\" " +
                "xmlns:bpmndi=\"http://www.omg.org/spec/BPMN/20100524/DI\" " +
                "xmlns:dc=\"http://www.omg.org/spec/DD/20100524/DC\" " +
                "xmlns:di=\"http://www.omg.org/spec/DD/20100524/DI\" " +
                "id=\"sample-diagram\" targetNamespace=\"http://bpmn.io/schema/bpmn\">\n" +
                "  <bpmn2:process id=\"" + processKey + "\" isExecutable=\"true\">\n" +
                "    <bpmn2:startEvent id=\"StartEvent_1\"/>\n" +
                "  </bpmn2:process>\n" +
                "  <bpmndi:BPMNDiagram id=\"BPMNDiagram_1\">\n" +
                "    <bpmndi:BPMNPlane id=\"BPMNPlane_1\" bpmnElement=\"" + processKey + "\">\n" +
                "      <bpmndi:BPMNShape id=\"_BPMNShape_StartEvent_2\" bpmnElement=\"StartEvent_1\">\n" +
                "        <dc:Bounds x=\"173\" y=\"102\" width=\"36\" height=\"36\"/>\n" +
                "      </bpmndi:BPMNShape>\n" +
                "    </bpmndi:BPMNPlane>\n" +
                "  </bpmndi:BPMNDiagram>\n" +
                "</bpmn2:definitions>";
    }
}
