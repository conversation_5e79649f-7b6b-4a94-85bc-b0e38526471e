package com.research.workflow.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.research.common.annotation.Excel;
import com.research.common.core.domain.BaseEntity;

import java.util.Date;
import java.util.Map;

/**
 * 工作流任务对象 workflow_task
 * 
 * <AUTHOR>
 */
@TableName("workflow_task")
public class WorkflowTask extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String name;

    /** 任务描述 */
    @Excel(name = "任务描述")
    private String description;

    /** 任务Key */
    @Excel(name = "任务Key")
    private String taskKey;

    /** 流程实例ID */
    @Excel(name = "流程实例ID")
    private String processInstanceId;

    /** 流程定义ID */
    @Excel(name = "流程定义ID")
    private String processDefinitionId;

    /** 流程定义Key */
    @Excel(name = "流程定义Key")
    private String processDefinitionKey;

    /** 流程定义名称 */
    @Excel(name = "流程定义名称")
    private String processDefinitionName;

    /** 任务负责人 */
    @Excel(name = "任务负责人")
    private String assignee;

    /** 任务负责人姓名 */
    @Excel(name = "任务负责人姓名")
    private String assigneeName;

    /** 候选用户 */
    @Excel(name = "候选用户")
    private String candidateUsers;

    /** 候选组 */
    @Excel(name = "候选组")
    private String candidateGroups;

    /** 任务状态 */
    @Excel(name = "任务状态", readConverterExp = "0=待处理,1=处理中,2=已完成,3=已取消")
    private String status;

    /** 任务优先级 */
    @Excel(name = "任务优先级")
    private Integer priority;

    /** 任务到期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "任务到期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date dueDate;

    /** 任务跟进时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "任务跟进时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date followUpDate;

    /** 任务分类 */
    @Excel(name = "任务分类")
    private String category;

    /** 表单Key */
    @Excel(name = "表单Key")
    private String formKey;

    /** 业务Key */
    @Excel(name = "业务Key")
    private String businessKey;

    /** 租户ID */
    @Excel(name = "租户ID")
    private String tenantId;

    /** 任务开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "任务开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 任务结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "任务结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 任务持续时间(毫秒) */
    @Excel(name = "任务持续时间")
    private Long duration;

    /** 删除原因 */
    @Excel(name = "删除原因")
    private String deleteReason;

    /** 父任务ID */
    @Excel(name = "父任务ID")
    private String parentTaskId;

    /** 任务变量 */
    @TableField(exist = false)
    private Map<String, Object> variables;

    /** 任务评论 */
    @TableField(exist = false)
    private String comment;

    /** 是否挂起 */
    @Excel(name = "是否挂起", readConverterExp = "0=否,1=是")
    private Boolean suspended;

    /** 任务执行人ID */
    @Excel(name = "任务执行人ID")
    private Long executorId;

    /** 任务执行人姓名 */
    @Excel(name = "任务执行人姓名")
    private String executorName;

    /** 任务委派人ID */
    @Excel(name = "任务委派人ID")
    private Long delegateId;

    /** 任务委派人姓名 */
    @Excel(name = "任务委派人姓名")
    private String delegateName;

    /** 任务所有者ID */
    @Excel(name = "任务所有者ID")
    private Long ownerId;

    /** 任务所有者姓名 */
    @Excel(name = "任务所有者姓名")
    private String ownerName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTaskKey() {
        return taskKey;
    }

    public void setTaskKey(String taskKey) {
        this.taskKey = taskKey;
    }

    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public String getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(String processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }

    public String getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    public void setProcessDefinitionKey(String processDefinitionKey) {
        this.processDefinitionKey = processDefinitionKey;
    }

    public String getProcessDefinitionName() {
        return processDefinitionName;
    }

    public void setProcessDefinitionName(String processDefinitionName) {
        this.processDefinitionName = processDefinitionName;
    }

    public String getAssignee() {
        return assignee;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }

    public String getAssigneeName() {
        return assigneeName;
    }

    public void setAssigneeName(String assigneeName) {
        this.assigneeName = assigneeName;
    }

    public String getCandidateUsers() {
        return candidateUsers;
    }

    public void setCandidateUsers(String candidateUsers) {
        this.candidateUsers = candidateUsers;
    }

    public String getCandidateGroups() {
        return candidateGroups;
    }

    public void setCandidateGroups(String candidateGroups) {
        this.candidateGroups = candidateGroups;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }

    public Date getFollowUpDate() {
        return followUpDate;
    }

    public void setFollowUpDate(Date followUpDate) {
        this.followUpDate = followUpDate;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getFormKey() {
        return formKey;
    }

    public void setFormKey(String formKey) {
        this.formKey = formKey;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public String getDeleteReason() {
        return deleteReason;
    }

    public void setDeleteReason(String deleteReason) {
        this.deleteReason = deleteReason;
    }

    public String getParentTaskId() {
        return parentTaskId;
    }

    public void setParentTaskId(String parentTaskId) {
        this.parentTaskId = parentTaskId;
    }

    public Map<String, Object> getVariables() {
        return variables;
    }

    public void setVariables(Map<String, Object> variables) {
        this.variables = variables;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Boolean getSuspended() {
        return suspended;
    }

    public void setSuspended(Boolean suspended) {
        this.suspended = suspended;
    }

    public Long getExecutorId() {
        return executorId;
    }

    public void setExecutorId(Long executorId) {
        this.executorId = executorId;
    }

    public String getExecutorName() {
        return executorName;
    }

    public void setExecutorName(String executorName) {
        this.executorName = executorName;
    }

    public Long getDelegateId() {
        return delegateId;
    }

    public void setDelegateId(Long delegateId) {
        this.delegateId = delegateId;
    }

    public String getDelegateName() {
        return delegateName;
    }

    public void setDelegateName(String delegateName) {
        this.delegateName = delegateName;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }
}
