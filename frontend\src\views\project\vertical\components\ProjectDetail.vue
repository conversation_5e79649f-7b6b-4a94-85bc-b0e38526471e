<template>
  <el-dialog
    title="项目详情"
    v-model="visible"
    width="1200px"
    append-to-body
    @close="handleClose"
  >
    <div v-loading="loading" class="project-detail">
      <el-tabs v-model="activeTab" type="card">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="项目编号">{{ projectInfo.projectNo }}</el-descriptions-item>
            <el-descriptions-item label="项目名称">{{ projectInfo.projectName }}</el-descriptions-item>
            <el-descriptions-item label="项目类型">{{ projectInfo.projectType }}</el-descriptions-item>
            <el-descriptions-item label="项目级别">{{ projectInfo.projectLevel }}</el-descriptions-item>
            <el-descriptions-item label="项目状态">
              <DictTag :options="statusOptions" :value="projectInfo.status"/>
            </el-descriptions-item>
            <el-descriptions-item label="项目负责人">{{ projectInfo.principalName }}</el-descriptions-item>
            <el-descriptions-item label="所属部门">{{ projectInfo.deptName }}</el-descriptions-item>
            <el-descriptions-item label="资助机构">{{ projectInfo.fundingAgency }}</el-descriptions-item>
            <el-descriptions-item label="项目来源">{{ projectInfo.projectSource }}</el-descriptions-item>
            <el-descriptions-item label="学科分类">{{ projectInfo.subjectCategory }}</el-descriptions-item>
            <el-descriptions-item label="项目周期">
              {{ formatDateRange(projectInfo.startDate, projectInfo.endDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="关键词">{{ projectInfo.keywords }}</el-descriptions-item>
          </el-descriptions>
          
          <el-divider content-position="left">项目简介</el-divider>
          <div class="content-section">
            {{ projectInfo.projectSummary || '暂无项目简介' }}
          </div>
          
          <el-divider content-position="left">研究内容</el-divider>
          <div class="content-section">
            {{ projectInfo.researchContent || '暂无研究内容' }}
          </div>
          
          <el-divider content-position="left">预期成果</el-divider>
          <div class="content-section">
            {{ projectInfo.expectedResults || '暂无预期成果' }}
          </div>
        </el-tab-pane>

        <!-- 预算信息 -->
        <el-tab-pane label="预算信息" name="budget">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card class="budget-card">
                <template #header>
                  <span>预算概览</span>
                </template>
                <el-statistic
                  title="项目总预算"
                  :value="projectInfo.budget || 0"
                  :precision="2"
                  prefix="¥"
                  class="budget-statistic"
                />
                <el-statistic
                  title="已使用预算"
                  :value="projectInfo.usedBudget || 0"
                  :precision="2"
                  prefix="¥"
                  class="budget-statistic"
                />
                <el-statistic
                  title="剩余预算"
                  :value="(projectInfo.budget || 0) - (projectInfo.usedBudget || 0)"
                  :precision="2"
                  prefix="¥"
                  class="budget-statistic"
                />
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card class="budget-card">
                <template #header>
                  <span>预算使用率</span>
                </template>
                <div class="budget-progress">
                  <el-progress
                    type="circle"
                    :percentage="getBudgetUsageRate()"
                    :color="getBudgetUsageColor()"
                    :width="150"
                  >
                    <template #default="{ percentage }">
                      <span class="percentage-value">{{ percentage }}%</span>
                      <span class="percentage-label">使用率</span>
                    </template>
                  </el-progress>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 项目成员 -->
        <el-tab-pane label="项目成员" name="members">
          <el-button type="primary" icon="Plus" @click="handleAddMember" style="margin-bottom: 16px;">
            添加成员
          </el-button>
          <el-table :data="memberList" border>
            <el-table-column label="姓名" prop="userName" />
            <el-table-column label="角色" prop="role" />
            <el-table-column label="工作量" prop="workload">
              <template #default="scope">
                {{ scope.row.workload }}%
              </template>
            </el-table-column>
            <el-table-column label="加入时间" prop="joinDate" />
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button link type="primary" @click="handleEditMember(scope.row)">编辑</el-button>
                <el-button link type="danger" @click="handleRemoveMember(scope.row)">移除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 项目文档 -->
        <el-tab-pane label="项目文档" name="documents">
          <el-upload
            class="upload-demo"
            drag
            action="/api/upload"
            multiple
            :on-success="handleUploadSuccess"
            style="margin-bottom: 16px;"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 jpg/png/pdf/doc/docx 文件，且不超过 10MB
              </div>
            </template>
          </el-upload>
          
          <el-table :data="documentList" border>
            <el-table-column label="文档名称" prop="fileName" />
            <el-table-column label="文档类型" prop="fileType" />
            <el-table-column label="文件大小" prop="fileSize" />
            <el-table-column label="上传时间" prop="uploadTime" />
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button link type="primary" @click="handleDownload(scope.row)">下载</el-button>
                <el-button link type="danger" @click="handleDeleteDoc(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 变更记录 -->
        <el-tab-pane label="变更记录" name="changes">
          <el-timeline>
            <el-timeline-item
              v-for="change in changeList"
              :key="change.id"
              :timestamp="change.applyTime"
              placement="top"
            >
              <el-card>
                <h4>{{ change.changeType }}</h4>
                <p>变更原因：{{ change.changeReason }}</p>
                <p>变更内容：{{ change.changeContent }}</p>
                <p>状态：<DictTag :options="changeStatusOptions" :value="change.status"/></p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getVerticalProject } from "@/api/project/vertical"
import DictTag from '@/components/DictTag/index.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  projectId: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const loading = ref(false)
const activeTab = ref('basic')
const projectInfo = ref({})
const memberList = ref([])
const documentList = ref([])
const changeList = ref([])

// 状态选项
const statusOptions = ref([
  { label: "申请中", value: "0" },
  { label: "立项", value: "1" },
  { label: "执行中", value: "2" },
  { label: "变更中", value: "3" },
  { label: "结项中", value: "4" },
  { label: "已结项", value: "5" },
  { label: "已撤销", value: "6" }
])

const changeStatusOptions = ref([
  { label: "申请中", value: "0" },
  { label: "已通过", value: "1" },
  { label: "已拒绝", value: "2" }
])

// 监听项目ID变化
watch(() => props.projectId, (newId) => {
  if (newId && visible.value) {
    loadProjectDetail()
  }
}, { immediate: true })

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal && props.projectId) {
    loadProjectDetail()
  }
})

/** 加载项目详情 */
function loadProjectDetail() {
  if (!props.projectId) return
  
  loading.value = true
  getVerticalProject(props.projectId).then(response => {
    projectInfo.value = response.data
    // 这里可以加载其他相关数据
    loadMemberList()
    loadDocumentList()
    loadChangeList()
  }).finally(() => {
    loading.value = false
  })
}

/** 加载成员列表 */
function loadMemberList() {
  // TODO: 调用成员列表API
  memberList.value = [
    {
      id: 1,
      userName: '张三',
      role: '项目负责人',
      workload: 50,
      joinDate: '2024-01-01'
    },
    {
      id: 2,
      userName: '李四',
      role: '主要参与者',
      workload: 30,
      joinDate: '2024-01-15'
    }
  ]
}

/** 加载文档列表 */
function loadDocumentList() {
  // TODO: 调用文档列表API
  documentList.value = [
    {
      id: 1,
      fileName: '项目申请书.pdf',
      fileType: 'PDF',
      fileSize: '2.5MB',
      uploadTime: '2024-01-01 10:00:00'
    },
    {
      id: 2,
      fileName: '立项批文.pdf',
      fileType: 'PDF',
      fileSize: '1.2MB',
      uploadTime: '2024-02-01 14:30:00'
    }
  ]
}

/** 加载变更记录 */
function loadChangeList() {
  // TODO: 调用变更记录API
  changeList.value = [
    {
      id: 1,
      changeType: '预算调整',
      changeReason: '研究需要调整设备采购预算',
      changeContent: '将设备费从10万调整为15万',
      status: '1',
      applyTime: '2024-03-01 09:00:00'
    }
  ]
}

/** 关闭对话框 */
function handleClose() {
  visible.value = false
  activeTab.value = 'basic'
}

/** 添加成员 */
function handleAddMember() {
  // TODO: 实现添加成员功能
  console.log('添加成员')
}

/** 编辑成员 */
function handleEditMember(row) {
  // TODO: 实现编辑成员功能
  console.log('编辑成员', row)
}

/** 移除成员 */
function handleRemoveMember(row) {
  // TODO: 实现移除成员功能
  console.log('移除成员', row)
}

/** 文件上传成功 */
function handleUploadSuccess(response, file) {
  // TODO: 处理文件上传成功
  console.log('上传成功', response, file)
  loadDocumentList()
}

/** 下载文档 */
function handleDownload(row) {
  // TODO: 实现文档下载
  console.log('下载文档', row)
}

/** 删除文档 */
function handleDeleteDoc(row) {
  // TODO: 实现文档删除
  console.log('删除文档', row)
}

// 工具函数
function formatDateRange(startDate, endDate) {
  if (!startDate || !endDate) return '-'
  return `${startDate} 至 ${endDate}`
}

function getBudgetUsageRate() {
  if (!projectInfo.value.budget || projectInfo.value.budget === 0) return 0
  return Math.round((projectInfo.value.usedBudget || 0) / projectInfo.value.budget * 100)
}

function getBudgetUsageColor() {
  const rate = getBudgetUsageRate()
  if (rate >= 90) return '#f56c6c'
  if (rate >= 70) return '#e6a23c'
  return '#67c23a'
}
</script>

<style scoped>
.project-detail {
  min-height: 400px;
}

.content-section {
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  line-height: 1.6;
  min-height: 60px;
}

.budget-card {
  height: 100%;
}

.budget-statistic {
  margin-bottom: 16px;
}

.budget-progress {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.percentage-value {
  display: block;
  font-size: 24px;
  font-weight: bold;
}

.percentage-label {
  display: block;
  font-size: 12px;
  color: #909399;
}

.upload-demo {
  margin-bottom: 20px;
}
</style>
