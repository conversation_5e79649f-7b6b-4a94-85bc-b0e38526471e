#!/bin/bash

# 科研成果多维敏捷管控中心 - 后端更新脚本
# 用于更新已部署的后端服务

echo "=========================================="
echo "科研成果多维敏捷管控中心 - 后端更新脚本"
echo "=========================================="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用sudo运行此脚本"
    exit 1
fi

# 设置变量
APP_NAME="research-management"
APP_DIR="/opt/$APP_NAME"
JAR_FILE="research-management-1.0.0.jar"
BACKUP_DIR="$APP_DIR/backup"

echo "开始更新后端服务..."

# 1. 检查新的JAR文件是否存在
if [ ! -f "backend/target/$JAR_FILE" ]; then
    echo "错误: 找不到新的JAR文件 backend/target/$JAR_FILE"
    echo "请先运行 'cd backend && mvn clean package -DskipTests' 进行打包"
    exit 1
fi

# 2. 创建备份目录
mkdir -p "$BACKUP_DIR"

# 3. 备份当前版本
if [ -f "$APP_DIR/$JAR_FILE" ]; then
    BACKUP_NAME="$JAR_FILE.backup.$(date +%Y%m%d_%H%M%S)"
    echo "备份当前版本到: $BACKUP_DIR/$BACKUP_NAME"
    cp "$APP_DIR/$JAR_FILE" "$BACKUP_DIR/$BACKUP_NAME"
fi

# 4. 停止当前服务
echo "停止当前服务..."
if [ -f "$APP_DIR/stop.sh" ]; then
    cd "$APP_DIR"
    ./stop.sh
    sleep 3
else
    echo "警告: 找不到停止脚本，尝试手动停止进程"
    if [ -f "$APP_DIR/app.pid" ]; then
        PID=$(cat "$APP_DIR/app.pid")
        if kill -0 $PID 2>/dev/null; then
            kill $PID
            sleep 3
            if kill -0 $PID 2>/dev/null; then
                kill -9 $PID
            fi
        fi
        rm -f "$APP_DIR/app.pid"
    fi
fi

# 5. 更新JAR文件
echo "更新JAR文件..."
cp "backend/target/$JAR_FILE" "$APP_DIR/"
echo "JAR文件已更新"

# 6. 启动服务
echo "启动服务..."
if [ -f "$APP_DIR/start.sh" ]; then
    cd "$APP_DIR"
    ./start.sh
else
    echo "错误: 找不到启动脚本"
    exit 1
fi

# 7. 验证服务启动
echo "验证服务启动..."
sleep 5

if [ -f "$APP_DIR/app.pid" ]; then
    PID=$(cat "$APP_DIR/app.pid")
    if kill -0 $PID 2>/dev/null; then
        echo "✓ 服务启动成功 (PID: $PID)"
    else
        echo "✗ 服务启动失败"
        echo "请检查日志: tail -f $APP_DIR/app.log"
        exit 1
    fi
else
    echo "✗ 服务启动失败，找不到PID文件"
    exit 1
fi

# 8. 检查端口监听
if netstat -tlnp | grep ":8989 " > /dev/null; then
    echo "✓ 端口8989监听正常"
else
    echo "✗ 端口8989未监听"
    echo "请检查日志: tail -f $APP_DIR/app.log"
fi

# 9. 测试API
sleep 3
if curl -s -o /dev/null -w "%{http_code}" "http://localhost:8989/captchaImage" | grep -q "200"; then
    echo "✓ API响应正常"
else
    echo "✗ API响应异常"
    echo "请检查日志: tail -f $APP_DIR/app.log"
fi

echo "=========================================="
echo "后端更新完成！"
echo "=========================================="
echo "服务状态: 运行中"
echo "PID: $(cat $APP_DIR/app.pid 2>/dev/null || echo '未知')"
echo "日志文件: $APP_DIR/app.log"
echo "备份文件: $BACKUP_DIR/"
echo ""
echo "管理命令:"
echo "查看日志: tail -f $APP_DIR/app.log"
echo "重启服务: $APP_DIR/restart.sh"
echo "停止服务: $APP_DIR/stop.sh"
echo "=========================================="
