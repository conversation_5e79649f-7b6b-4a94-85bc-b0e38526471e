<template>
  <div class="inner-link">
    <iframe 
      :src="iframeSrc" 
      frameborder="0" 
      width="100%" 
      height="100%"
      style="min-height: 500px;"
    ></iframe>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

// 计算iframe的src地址
const iframeSrc = computed(() => {
  // 从路由参数或meta中获取链接地址
  return route.query.src as string || route.meta?.link as string || ''
})
</script>

<style scoped>
.inner-link {
  width: 100%;
  height: 100%;
}
</style>
