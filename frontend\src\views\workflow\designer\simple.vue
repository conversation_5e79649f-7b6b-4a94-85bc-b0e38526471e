<template>
  <div class="simple-designer">
    <div class="designer-header">
      <div class="header-left">
        <h3>可视化流程设计器</h3>
        <span class="subtitle">拖拽配置工作流程</span>
      </div>
      <div class="header-right">
        <el-button @click="clearCanvas">清空画布</el-button>
        <el-button type="primary" @click="saveProcess">保存流程</el-button>
        <el-button type="success" @click="previewXml">预览XML</el-button>
      </div>
    </div>

    <div class="designer-body">
      <!-- 左侧工具栏 -->
      <div class="toolbar">
        <div class="toolbar-title">流程元素</div>
        <div class="element-group">
          <div class="group-title">事件</div>
          <div 
            class="element-item" 
            draggable="true"
            @dragstart="handleDragStart($event, 'start')"
          >
            <div class="element-icon start-event"></div>
            <span>开始事件</span>
          </div>
          <div 
            class="element-item" 
            draggable="true"
            @dragstart="handleDragStart($event, 'end')"
          >
            <div class="element-icon end-event"></div>
            <span>结束事件</span>
          </div>
        </div>

        <div class="element-group">
          <div class="group-title">任务</div>
          <div 
            class="element-item" 
            draggable="true"
            @dragstart="handleDragStart($event, 'user-task')"
          >
            <div class="element-icon user-task"></div>
            <span>用户任务</span>
          </div>
          <div 
            class="element-item" 
            draggable="true"
            @dragstart="handleDragStart($event, 'service-task')"
          >
            <div class="element-icon service-task"></div>
            <span>服务任务</span>
          </div>
        </div>

        <div class="element-group">
          <div class="group-title">网关</div>
          <div 
            class="element-item" 
            draggable="true"
            @dragstart="handleDragStart($event, 'exclusive-gateway')"
          >
            <div class="element-icon exclusive-gateway"></div>
            <span>排他网关</span>
          </div>
          <div 
            class="element-item" 
            draggable="true"
            @dragstart="handleDragStart($event, 'parallel-gateway')"
          >
            <div class="element-icon parallel-gateway"></div>
            <span>并行网关</span>
          </div>
        </div>
      </div>

      <!-- 中间画布 -->
      <div class="canvas-container">
        <div 
          class="canvas" 
          ref="canvasRef"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @click="handleCanvasClick"
        >
          <!-- 网格背景 -->
          <div class="grid-background"></div>
          
          <!-- 流程元素 -->
          <div
            v-for="element in processElements"
            :key="element.id"
            :class="['process-element', element.type, { selected: selectedElement?.id === element.id }]"
            :style="{ left: element.x + 'px', top: element.y + 'px' }"
            @click.stop="selectElement(element)"
            @mousedown="startDrag(element, $event)"
          >
            <div class="element-content">
              <div class="element-icon-large" :class="element.type"></div>
              <div class="element-label">{{ element.name }}</div>
            </div>
            
            <!-- 连接点 -->
            <div class="connection-points">
              <div class="connection-point left" @click.stop="startConnection(element, 'left')"></div>
              <div class="connection-point right" @click.stop="startConnection(element, 'right')"></div>
              <div class="connection-point top" @click.stop="startConnection(element, 'top')"></div>
              <div class="connection-point bottom" @click.stop="startConnection(element, 'bottom')"></div>
            </div>
          </div>

          <!-- 连接线 -->
          <svg class="connections-layer" :style="{ width: '100%', height: '100%' }">
            <line
              v-for="connection in connections"
              :key="connection.id"
              :x1="connection.x1"
              :y1="connection.y1"
              :x2="connection.x2"
              :y2="connection.y2"
              stroke="#666"
              stroke-width="2"
              marker-end="url(#arrowhead)"
            />
            <!-- 箭头标记 -->
            <defs>
              <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                      refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
              </marker>
            </defs>
          </svg>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="properties-panel">
        <div class="panel-title">属性配置</div>
        <div v-if="selectedElement" class="properties-content">
          <el-form :model="selectedElement" label-width="80px" size="small">
            <el-form-item label="元素ID">
              <el-input v-model="selectedElement.id" disabled />
            </el-form-item>
            <el-form-item label="元素名称">
              <el-input v-model="selectedElement.name" @input="updateElementName" />
            </el-form-item>
            <el-form-item label="元素类型">
              <el-input v-model="selectedElement.type" disabled />
            </el-form-item>
            <el-form-item v-if="selectedElement.type === 'user-task'" label="执行人">
              <el-input v-model="selectedElement.assignee" placeholder="请输入执行人" />
            </el-form-item>
            <el-form-item v-if="selectedElement.type === 'user-task'" label="候选组">
              <el-input v-model="selectedElement.candidateGroups" placeholder="请输入候选组" />
            </el-form-item>
            <el-form-item>
              <el-button type="danger" size="small" @click="deleteElement">删除元素</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div v-else class="no-selection">
          <el-empty description="请选择一个流程元素" />
        </div>
      </div>
    </div>

    <!-- XML预览对话框 -->
    <el-dialog title="流程XML预览" v-model="xmlDialog.visible" width="70%">
      <el-input
        v-model="xmlDialog.content"
        type="textarea"
        :rows="20"
        readonly
        style="font-family: 'Courier New', monospace;"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="xmlDialog.visible = false">关闭</el-button>
          <el-button type="primary" @click="downloadXml">下载XML</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

interface ProcessElement {
  id: string
  type: string
  name: string
  x: number
  y: number
  assignee?: string
  candidateGroups?: string
}

interface Connection {
  id: string
  from: string
  to: string
  x1: number
  y1: number
  x2: number
  y2: number
}

const canvasRef = ref<HTMLElement>()
const processElements = ref<ProcessElement[]>([])
const connections = ref<Connection[]>([])
const selectedElement = ref<ProcessElement | null>(null)
const draggedElement = ref<ProcessElement | null>(null)
const elementCounter = ref(0)

const xmlDialog = reactive({
  visible: false,
  content: ''
})

// 拖拽相关状态
const dragState = reactive({
  isDragging: false,
  startX: 0,
  startY: 0,
  elementStartX: 0,
  elementStartY: 0
})

/** 处理拖拽开始 */
const handleDragStart = (event: DragEvent, elementType: string) => {
  event.dataTransfer!.setData('elementType', elementType)
}

/** 处理拖拽悬停 */
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
}

/** 处理放置 */
const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  const elementType = event.dataTransfer!.getData('elementType')
  const rect = canvasRef.value!.getBoundingClientRect()
  const x = event.clientX - rect.left - 50 // 减去元素宽度的一半
  const y = event.clientY - rect.top - 25  // 减去元素高度的一半

  createElement(elementType, x, y)
}

/** 创建流程元素 */
const createElement = (type: string, x: number, y: number) => {
  elementCounter.value++
  const element: ProcessElement = {
    id: `${type}_${elementCounter.value}`,
    type,
    name: getElementName(type),
    x,
    y
  }
  
  processElements.value.push(element)
  ElMessage.success(`已添加${element.name}`)
}

/** 获取元素名称 */
const getElementName = (type: string): string => {
  const nameMap: Record<string, string> = {
    'start': '开始事件',
    'end': '结束事件',
    'user-task': '用户任务',
    'service-task': '服务任务',
    'exclusive-gateway': '排他网关',
    'parallel-gateway': '并行网关'
  }
  return nameMap[type] || type
}

/** 选择元素 */
const selectElement = (element: ProcessElement) => {
  selectedElement.value = element
}

/** 画布点击 */
const handleCanvasClick = () => {
  selectedElement.value = null
}

/** 开始拖拽元素 */
const startDrag = (element: ProcessElement, event: MouseEvent) => {
  draggedElement.value = element
  dragState.isDragging = true
  dragState.startX = event.clientX
  dragState.startY = event.clientY
  dragState.elementStartX = element.x
  dragState.elementStartY = element.y

  const handleMouseMove = (e: MouseEvent) => {
    if (dragState.isDragging && draggedElement.value) {
      const deltaX = e.clientX - dragState.startX
      const deltaY = e.clientY - dragState.startY
      draggedElement.value.x = dragState.elementStartX + deltaX
      draggedElement.value.y = dragState.elementStartY + deltaY
    }
  }

  const handleMouseUp = () => {
    dragState.isDragging = false
    draggedElement.value = null
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

/** 更新元素名称 */
const updateElementName = () => {
  // 实时更新元素名称
}

/** 删除元素 */
const deleteElement = () => {
  if (selectedElement.value) {
    const index = processElements.value.findIndex(el => el.id === selectedElement.value!.id)
    if (index > -1) {
      processElements.value.splice(index, 1)
      // 删除相关连接线
      connections.value = connections.value.filter(
        conn => conn.from !== selectedElement.value!.id && conn.to !== selectedElement.value!.id
      )
      selectedElement.value = null
      ElMessage.success('元素已删除')
    }
  }
}

/** 清空画布 */
const clearCanvas = () => {
  processElements.value = []
  connections.value = []
  selectedElement.value = null
  elementCounter.value = 0
  ElMessage.success('画布已清空')
}

/** 保存流程 */
const saveProcess = () => {
  if (processElements.value.length === 0) {
    ElMessage.warning('请先添加流程元素')
    return
  }
  
  const processData = {
    elements: processElements.value,
    connections: connections.value
  }
  
  console.log('保存流程数据:', processData)
  ElMessage.success('流程保存成功')
}

/** 预览XML */
const previewXml = () => {
  if (processElements.value.length === 0) {
    ElMessage.warning('请先添加流程元素')
    return
  }

  // 生成简单的BPMN XML
  const xml = generateBpmnXml()
  xmlDialog.content = xml
  xmlDialog.visible = true
}

/** 生成BPMN XML */
const generateBpmnXml = (): string => {
  let processContent = ''
  
  processElements.value.forEach(element => {
    switch (element.type) {
      case 'start':
        processContent += `    <bpmn:startEvent id="${element.id}" name="${element.name}"/>\n`
        break
      case 'end':
        processContent += `    <bpmn:endEvent id="${element.id}" name="${element.name}"/>\n`
        break
      case 'user-task':
        processContent += `    <bpmn:userTask id="${element.id}" name="${element.name}"${element.assignee ? ` camunda:assignee="${element.assignee}"` : ''}${element.candidateGroups ? ` camunda:candidateGroups="${element.candidateGroups}"` : ''}/>\n`
        break
      case 'service-task':
        processContent += `    <bpmn:serviceTask id="${element.id}" name="${element.name}"/>\n`
        break
      case 'exclusive-gateway':
        processContent += `    <bpmn:exclusiveGateway id="${element.id}" name="${element.name}"/>\n`
        break
      case 'parallel-gateway':
        processContent += `    <bpmn:parallelGateway id="${element.id}" name="${element.name}"/>\n`
        break
    }
  })

  return `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  xmlns:camunda="http://camunda.org/schema/1.0/bpmn"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" 
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI" 
                  id="Definitions_1" 
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1" isExecutable="true">
${processContent}  </bpmn:process>
</bpmn:definitions>`
}

/** 下载XML */
const downloadXml = () => {
  const blob = new Blob([xmlDialog.content], { type: 'application/xml' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = 'process.bpmn'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  ElMessage.success('XML文件下载成功')
}

/** 开始连接 */
const startConnection = (element: ProcessElement, point: string) => {
  console.log('开始连接:', element.id, point)
  // 连接功能的实现可以在后续完善
}
</script>

<style scoped>
.simple-designer {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.designer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-left h3 {
  margin: 0;
  color: #303133;
}

.subtitle {
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.designer-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.toolbar {
  width: 240px;
  background: white;
  border-right: 1px solid #e4e7ed;
  padding: 16px;
  overflow-y: auto;
}

.toolbar-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 16px;
}

.element-group {
  margin-bottom: 20px;
}

.group-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid #f0f0f0;
}

.element-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s;
}

.element-item:hover {
  background: #f0f9ff;
  color: #409eff;
}

.element-item:active {
  cursor: grabbing;
}

.element-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
}

.element-icon.start-event {
  background: #67c23a;
}

.element-icon.end-event {
  background: #f56c6c;
}

.element-icon.user-task {
  background: #409eff;
  border-radius: 4px;
}

.element-icon.service-task {
  background: #e6a23c;
  border-radius: 4px;
}

.element-icon.exclusive-gateway {
  background: #909399;
  transform: rotate(45deg);
  border-radius: 0;
}

.element-icon.parallel-gateway {
  background: #606266;
  transform: rotate(45deg);
  border-radius: 0;
}

.canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.canvas {
  width: 100%;
  height: 100%;
  position: relative;
  background: white;
  overflow: auto;
}

.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(to right, #f0f0f0 1px, transparent 1px),
    linear-gradient(to bottom, #f0f0f0 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
}

.process-element {
  position: absolute;
  width: 100px;
  height: 50px;
  cursor: move;
  user-select: none;
}

.process-element.selected {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}

.element-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: white;
  border: 2px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: all 0.2s;
}

.process-element:hover .element-content {
  border-color: #409eff;
  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.2);
}

.element-icon-large {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

.element-icon-large.start {
  background: #67c23a;
  border-radius: 50%;
}

.element-icon-large.end {
  background: #f56c6c;
  border-radius: 50%;
}

.element-icon-large.user-task {
  background: #409eff;
  border-radius: 4px;
}

.element-icon-large.service-task {
  background: #e6a23c;
  border-radius: 4px;
}

.element-icon-large.exclusive-gateway {
  background: #909399;
  transform: rotate(45deg);
}

.element-icon-large.parallel-gateway {
  background: #606266;
  transform: rotate(45deg);
}

.element-label {
  font-size: 12px;
  color: #303133;
  text-align: center;
  line-height: 1.2;
}

.connection-points {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.connection-point {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #409eff;
  border: 2px solid white;
  border-radius: 50%;
  cursor: crosshair;
  pointer-events: auto;
  opacity: 0;
  transition: opacity 0.2s;
}

.process-element:hover .connection-point {
  opacity: 1;
}

.connection-point.left {
  left: -4px;
  top: 50%;
  transform: translateY(-50%);
}

.connection-point.right {
  right: -4px;
  top: 50%;
  transform: translateY(-50%);
}

.connection-point.top {
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
}

.connection-point.bottom {
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
}

.connections-layer {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
}

.properties-panel {
  width: 300px;
  background: white;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.panel-title {
  padding: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
}

.properties-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.no-selection {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
