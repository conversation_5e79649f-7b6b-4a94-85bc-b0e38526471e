<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.research.system.mapper.SysMessageMapper">
    
    <resultMap type="SysMessage" id="SysMessageResult">
        <result property="messageId"        column="message_id"            />
        <result property="title"            column="title"                 />
        <result property="content"          column="content"               />
        <result property="messageType"      column="message_type"          />
        <result property="priority"         column="priority"              />
        <result property="senderId"         column="sender_id"             />
        <result property="senderName"       column="sender_name"           />
        <result property="receiverId"       column="receiver_id"           />
        <result property="receiverName"     column="receiver_name"         />
        <result property="isRead"           column="is_read"               />
        <result property="readTime"         column="read_time"             />
        <result property="businessId"       column="business_id"           />
        <result property="businessType"     column="business_type"         />
        <result property="businessTitle"    column="business_title"        />
        <result property="attachmentPath"   column="attachment_path"       />
        <result property="status"           column="status"                />
        <result property="needReply"        column="need_reply"            />
        <result property="replyContent"     column="reply_content"         />
        <result property="replyTime"        column="reply_time"            />
        <result property="tags"             column="tags"                  />
        <result property="createBy"         column="create_by"             />
        <result property="createTime"       column="create_time"           />
        <result property="updateBy"         column="update_by"             />
        <result property="updateTime"       column="update_time"           />
        <result property="remark"           column="remark"                />
    </resultMap>

    <sql id="selectMessageVo">
        select m.message_id, m.title, m.content, m.message_type, m.priority,
               m.sender_id, m.sender_name, m.receiver_id, m.receiver_name,
               m.is_read, m.read_time, m.business_id, m.business_type, m.business_title,
               m.attachment_path, m.status, m.need_reply, m.reply_content, m.reply_time,
               m.tags, m.create_by, m.create_time, m.update_by, m.update_time, m.remark
        from sys_message m
    </sql>

    <!-- 查询消息列表 -->
    <select id="selectMessageList" resultMap="SysMessageResult">
        <include refid="selectMessageVo"/>
        <where>
            m.status = '0'
            <if test="message != null">
                <if test="message.title != null and message.title != ''">
                    AND m.title like concat('%', #{message.title}, '%')
                </if>
                <if test="message.messageType != null and message.messageType != ''">
                    AND m.message_type = #{message.messageType}
                </if>
                <if test="message.priority != null and message.priority != ''">
                    AND m.priority = #{message.priority}
                </if>
                <if test="message.isRead != null and message.isRead != ''">
                    AND m.is_read = #{message.isRead}
                </if>
                <if test="message.senderName != null and message.senderName != ''">
                    AND m.sender_name like concat('%', #{message.senderName}, '%')
                </if>
                <if test="message.receiverName != null and message.receiverName != ''">
                    AND m.receiver_name like concat('%', #{message.receiverName}, '%')
                </if>
            </if>
        </where>
        order by m.priority desc, m.create_time desc
    </select>

    <!-- 查询我的消息列表 -->
    <select id="selectMyMessageList" resultMap="SysMessageResult">
        <include refid="selectMessageVo"/>
        <where>
            m.status = '0'
            AND m.receiver_id = #{userId}
            <if test="message != null">
                <if test="message.title != null and message.title != ''">
                    AND m.title like concat('%', #{message.title}, '%')
                </if>
                <if test="message.messageType != null and message.messageType != ''">
                    AND m.message_type = #{message.messageType}
                </if>
                <if test="message.priority != null and message.priority != ''">
                    AND m.priority = #{message.priority}
                </if>
                <if test="message.isRead != null and message.isRead != ''">
                    AND m.is_read = #{message.isRead}
                </if>
            </if>
        </where>
        order by m.priority desc, m.create_time desc
    </select>

    <!-- 查询收件箱消息列表 -->
    <select id="selectInboxMessageList" resultMap="SysMessageResult">
        <include refid="selectMessageVo"/>
        <where>
            m.status = '0'
            AND m.receiver_id = #{userId}
            <if test="message != null">
                <if test="message.title != null and message.title != ''">
                    AND m.title like concat('%', #{message.title}, '%')
                </if>
                <if test="message.messageType != null and message.messageType != ''">
                    AND m.message_type = #{message.messageType}
                </if>
                <if test="message.priority != null and message.priority != ''">
                    AND m.priority = #{message.priority}
                </if>
                <if test="message.isRead != null and message.isRead != ''">
                    AND m.is_read = #{message.isRead}
                </if>
            </if>
        </where>
        order by m.is_read asc, m.priority desc, m.create_time desc
    </select>

    <!-- 查询收件箱消息总数 -->
    <select id="selectInboxMessageCount" resultType="long">
        select count(1) from sys_message m
        <where>
            m.status = '0'
            AND m.receiver_id = #{userId}
            <if test="message != null">
                <if test="message.title != null and message.title != ''">
                    AND m.title like concat('%', #{message.title}, '%')
                </if>
                <if test="message.messageType != null and message.messageType != ''">
                    AND m.message_type = #{message.messageType}
                </if>
                <if test="message.priority != null and message.priority != ''">
                    AND m.priority = #{message.priority}
                </if>
                <if test="message.isRead != null and message.isRead != ''">
                    AND m.is_read = #{message.isRead}
                </if>
            </if>
        </where>
    </select>

    <!-- 查询发件箱消息列表 -->
    <select id="selectOutboxMessageList" resultMap="SysMessageResult">
        <include refid="selectMessageVo"/>
        <where>
            m.status = '0'
            AND m.sender_id = #{userId}
            <if test="message != null">
                <if test="message.title != null and message.title != ''">
                    AND m.title like concat('%', #{message.title}, '%')
                </if>
                <if test="message.messageType != null and message.messageType != ''">
                    AND m.message_type = #{message.messageType}
                </if>
                <if test="message.priority != null and message.priority != ''">
                    AND m.priority = #{message.priority}
                </if>
                <if test="message.receiverName != null and message.receiverName != ''">
                    AND m.receiver_name like concat('%', #{message.receiverName}, '%')
                </if>
            </if>
        </where>
        order by m.create_time desc
    </select>

    <!-- 查询发件箱消息总数 -->
    <select id="selectOutboxMessageCount" resultType="long">
        select count(1) from sys_message m
        <where>
            m.status = '0'
            AND m.sender_id = #{userId}
            <if test="message != null">
                <if test="message.title != null and message.title != ''">
                    AND m.title like concat('%', #{message.title}, '%')
                </if>
                <if test="message.messageType != null and message.messageType != ''">
                    AND m.message_type = #{message.messageType}
                </if>
                <if test="message.priority != null and message.priority != ''">
                    AND m.priority = #{message.priority}
                </if>
                <if test="message.receiverName != null and message.receiverName != ''">
                    AND m.receiver_name like concat('%', #{message.receiverName}, '%')
                </if>
            </if>
        </where>
    </select>

    <!-- 查询我发送的消息列表 -->
    <select id="selectMySentMessageList" resultMap="SysMessageResult">
        <include refid="selectMessageVo"/>
        <where>
            m.status = '0'
            AND m.sender_id = #{userId}
            <if test="message != null">
                <if test="message.title != null and message.title != ''">
                    AND m.title like concat('%', #{message.title}, '%')
                </if>
                <if test="message.messageType != null and message.messageType != ''">
                    AND m.message_type = #{message.messageType}
                </if>
                <if test="message.priority != null and message.priority != ''">
                    AND m.priority = #{message.priority}
                </if>
                <if test="message.receiverName != null and message.receiverName != ''">
                    AND m.receiver_name like concat('%', #{message.receiverName}, '%')
                </if>
            </if>
        </where>
        order by m.create_time desc
    </select>

    <!-- 查询消息详情 -->
    <select id="selectMessageDetail" resultMap="SysMessageResult">
        <include refid="selectMessageVo"/>
        where m.message_id = #{messageId} and m.status = '0'
    </select>

    <!-- 查询未读消息数量 -->
    <select id="selectUnreadMessageCount" resultType="java.lang.Long">
        select count(*) from sys_message
        where receiver_id = #{userId}
          and is_read = '0'
          and status = '0'
    </select>

    <!-- 查询最近消息 -->
    <select id="selectRecentMessages" resultMap="SysMessageResult">
        <include refid="selectMessageVo"/>
        where m.receiver_id = #{userId}
          and m.status = '0'
        order by m.create_time desc
        limit #{limit}
    </select>

    <!-- 查询消息统计 -->
    <select id="selectMessageStatistics" resultType="java.util.Map">
        select 
            count(*) as total,
            count(case when is_read = '0' then 1 end) as unread,
            count(case when is_read = '1' then 1 end) as read,
            count(case when message_type = '1' then 1 end) as system,
            count(case when message_type = '2' then 1 end) as notice,
            count(case when message_type = '3' then 1 end) as todo,
            count(case when message_type = '4' then 1 end) as approval,
            count(case when priority = '4' then 1 end) as urgent,
            count(case when priority = '3' then 1 end) as high
        from sys_message
        where receiver_id = #{userId} and status = '0'
    </select>

    <!-- 标记消息为已读 -->
    <update id="markAsRead">
        update sys_message 
        set is_read = '1', read_time = now(), update_time = now()
        where message_id = #{messageId} and receiver_id = #{userId}
    </update>

    <!-- 批量标记消息为已读 -->
    <update id="batchMarkAsRead">
        update sys_message 
        set is_read = '1', read_time = now(), update_time = now()
        where receiver_id = #{userId} and message_id in
        <foreach collection="messageIds" item="messageId" open="(" separator="," close=")">
            #{messageId}
        </foreach>
    </update>

    <!-- 标记所有消息为已读 -->
    <update id="markAllAsRead">
        update sys_message 
        set is_read = '1', read_time = now(), update_time = now()
        where receiver_id = #{userId} and is_read = '0' and status = '0'
    </update>

    <!-- 软删除消息 -->
    <update id="deleteMessage">
        update sys_message
        set status = '1', update_time = now()
        where message_id = #{messageId} and receiver_id = #{userId}
    </update>

    <!-- 批量软删除消息 -->
    <update id="batchDeleteMessage">
        update sys_message
        set status = '1', update_time = now()
        where receiver_id = #{userId} and message_id in
        <foreach collection="messageIds" item="messageId" open="(" separator="," close=")">
            #{messageId}
        </foreach>
    </update>

    <!-- 查询消息按类型统计 -->
    <select id="selectMessageByTypeStats" resultType="java.util.Map">
        select 
            message_type,
            count(*) as count,
            case message_type
                when '1' then '系统消息'
                when '2' then '通知消息'
                when '3' then '待办消息'
                when '4' then '审批消息'
                else '其他'
            end as typeName
        from sys_message 
        where receiver_id = #{userId} and status = '0'
        group by message_type
        order by message_type
    </select>

    <!-- 查询消息按优先级统计 -->
    <select id="selectMessageByPriorityStats" resultType="java.util.Map">
        select 
            priority,
            count(*) as count,
            case priority
                when '1' then '低'
                when '2' then '中'
                when '3' then '高'
                when '4' then '紧急'
                else '未知'
            end as priorityName
        from sys_message 
        where receiver_id = #{userId} and status = '0'
        group by priority
        order by priority desc
    </select>

    <!-- 清理已删除的消息（物理删除） -->
    <delete id="cleanDeletedMessages">
        delete from sys_message
        where status = '1'
          and update_time &lt; date_sub(now(), interval #{days} day)
    </delete>

</mapper>
