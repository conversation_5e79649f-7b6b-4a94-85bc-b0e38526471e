<template>
  <div class="chart-card">
    <div class="chart-header">
      <div class="header-left">
        <h3 class="chart-title">{{ title }}</h3>
        <el-tag :type="tagType" size="small">{{ tagText }}</el-tag>
      </div>
      <div class="header-right">
        <slot name="controls"></slot>
        <el-button 
          v-if="showRefresh" 
          size="small" 
          @click="handleRefresh"
          :loading="loading"
        >
          <el-icon><Refresh /></el-icon>
        </el-button>
        <el-button 
          v-if="showFullscreen" 
          size="small" 
          @click="toggleFullscreen"
        >
          <el-icon><FullScreen /></el-icon>
        </el-button>
      </div>
    </div>
    <div class="chart-content" :class="{ 'fullscreen': isFullscreen }">
      <div 
        ref="chartRef" 
        class="chart-container"
        :style="{ height: chartHeight }"
      ></div>
      <div v-if="loading" class="chart-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>
      <div v-if="error" class="chart-error">
        <el-icon><Warning /></el-icon>
        <span>{{ error }}</span>
        <el-button size="small" @click="handleRefresh">重试</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

interface Props {
  title: string
  tagText?: string
  tagType?: 'success' | 'info' | 'warning' | 'danger'
  showRefresh?: boolean
  showFullscreen?: boolean
  chartHeight?: string
  options?: any
  loading?: boolean
  error?: string
}

interface Emits {
  (e: 'refresh'): void
  (e: 'fullscreen', isFullscreen: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  tagText: '实时',
  tagType: 'info',
  showRefresh: true,
  showFullscreen: true,
  chartHeight: '300px',
  loading: false,
  error: ''
})

const emit = defineEmits<Emits>()

const chartRef = ref<HTMLElement>()
const isFullscreen = ref(false)
let chartInstance: echarts.ECharts | null = null

/** 初始化图表 */
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  
  // 设置默认配置
  const defaultOptions = {
    backgroundColor: 'transparent',
    textStyle: {
      color: '#fff'
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'rgba(255, 255, 255, 0.2)',
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    }
  }

  // 合并用户配置
  if (props.options) {
    const mergedOptions = echarts.util.merge(defaultOptions, props.options)
    chartInstance.setOption(mergedOptions)
  }

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

/** 更新图表 */
const updateChart = (options: any) => {
  if (!chartInstance) return

  try {
    chartInstance.setOption(options, true)
  } catch (error) {
    console.error('更新图表失败:', error)
  }
}

/** 处理窗口大小变化 */
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

/** 处理刷新 */
const handleRefresh = () => {
  emit('refresh')
}

/** 切换全屏 */
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  emit('fullscreen', isFullscreen.value)
  
  nextTick(() => {
    if (chartInstance) {
      chartInstance.resize()
    }
  })
}

/** 销毁图表 */
const destroyChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
}

// 监听配置变化
watch(() => props.options, (newOptions) => {
  if (newOptions && chartInstance) {
    updateChart(newOptions)
  }
}, { deep: true })

// 监听加载状态
watch(() => props.loading, (loading) => {
  if (chartInstance) {
    if (loading) {
      chartInstance.showLoading({
        text: '加载中...',
        color: '#409eff',
        textColor: '#fff',
        maskColor: 'rgba(0, 0, 0, 0.4)'
      })
    } else {
      chartInstance.hideLoading()
    }
  }
})

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

onUnmounted(() => {
  destroyChart()
})

// 暴露方法给父组件
defineExpose({
  updateChart,
  getChartInstance: () => chartInstance,
  resize: handleResize
})
</script>

<style scoped>
.chart-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chart-title {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-content {
  flex: 1;
  position: relative;
  min-height: 0;
}

.chart-content.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.9);
  padding: 20px;
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 200px;
}

.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #fff;
  font-size: 14px;
}

.chart-loading .el-icon {
  font-size: 24px;
}

.chart-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #f56c6c;
  font-size: 14px;
  text-align: center;
}

.chart-error .el-icon {
  font-size: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-card {
    padding: 15px;
  }
  
  .chart-title {
    font-size: 16px;
  }
  
  .header-right {
    gap: 4px;
  }
}
</style>
