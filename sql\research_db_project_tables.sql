-- 科研管理系统项目管理模块扩展表结构
-- 继续添加项目管理相关表

USE research_db;

-- ===========================================
-- 项目管理核心表结构
-- ===========================================

-- 纵向项目表（兼容BIZ_VERTICAL_PROJECT）
CREATE TABLE biz_vertical_project (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    project_name VARCHAR(1024) COMMENT '项目名称',
    project_code VARCHAR(200) COMMENT '项目编号',
    project_type_id VARCHAR(32) COMMENT '项目类型ID',
    project_level_id VARCHAR(32) COMMENT '项目级别ID',
    project_source_id VARCHAR(32) COMMENT '项目来源ID',
    subject_class_id VARCHAR(32) COMMENT '学科分类ID',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    total_fund DECIMAL(15,2) COMMENT '总经费',
    approved_fund DECIMAL(15,2) COMMENT '批复经费',
    matching_fund DECIMAL(15,2) COMMENT '配套经费',
    external_fund DECIMAL(15,2) COMMENT '外拨经费',
    principal_id VARCHAR(32) COMMENT '负责人ID',
    principal_name VARCHAR(500) COMMENT '负责人姓名',
    principal_account VARCHAR(50) COMMENT '负责人账号',
    unit_id VARCHAR(32) COMMENT '单位ID',
    cooperate_units TEXT COMMENT '合作单位',
    project_status VARCHAR(32) COMMENT '项目状态',
    check_status VARCHAR(32) COMMENT '审核状态',
    check_date DATE COMMENT '审核日期',
    checker VARCHAR(50) COMMENT '审核人',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    note TEXT COMMENT '备注',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    last_edit_user_id VARCHAR(50) COMMENT '最后编辑用户ID',
    create_date DATE COMMENT '创建日期',
    last_edit_date DATE COMMENT '最后编辑日期',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    last_edit_user_name VARCHAR(50) COMMENT '最后编辑用户名',
    invalid_flag VARCHAR(32) COMMENT '无效标志',
    remove_flag VARCHAR(32) COMMENT '删除标志',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='纵向项目表';

-- 纵向项目成员表
CREATE TABLE biz_vertical_project_member (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    project_id VARCHAR(32) COMMENT '项目ID',
    person_id VARCHAR(32) COMMENT '人员ID',
    member_name VARCHAR(50) COMMENT '成员姓名',
    member_account VARCHAR(50) COMMENT '成员账号',
    member_type VARCHAR(32) COMMENT '成员类型',
    member_role VARCHAR(32) COMMENT '成员角色',
    work_ratio DECIMAL(10,2) COMMENT '工作比例',
    order_id INT COMMENT '排序',
    unit_id VARCHAR(32) COMMENT '单位ID',
    title_id VARCHAR(32) COMMENT '职称ID',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='纵向项目成员表';

-- 横向项目表（兼容BIZ_HORIZONTAL_PROJECT）
CREATE TABLE biz_horizontal_project (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    project_name VARCHAR(1024) COMMENT '项目名称',
    project_code VARCHAR(200) COMMENT '项目编号',
    contract_code VARCHAR(200) COMMENT '合同编号',
    contract_type_id VARCHAR(32) COMMENT '合同类型ID',
    cooperate_unit VARCHAR(500) COMMENT '合作单位',
    cooperate_unit_id VARCHAR(32) COMMENT '合作单位ID',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    total_fund DECIMAL(15,2) COMMENT '总经费',
    received_fund DECIMAL(15,2) COMMENT '到账经费',
    principal_id VARCHAR(32) COMMENT '负责人ID',
    principal_name VARCHAR(500) COMMENT '负责人姓名',
    principal_account VARCHAR(50) COMMENT '负责人账号',
    unit_id VARCHAR(32) COMMENT '单位ID',
    project_status VARCHAR(32) COMMENT '项目状态',
    check_status VARCHAR(32) COMMENT '审核状态',
    check_date DATE COMMENT '审核日期',
    checker VARCHAR(50) COMMENT '审核人',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    note TEXT COMMENT '备注',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    last_edit_user_id VARCHAR(50) COMMENT '最后编辑用户ID',
    create_date DATE COMMENT '创建日期',
    last_edit_date DATE COMMENT '最后编辑日期',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    last_edit_user_name VARCHAR(50) COMMENT '最后编辑用户名',
    invalid_flag VARCHAR(32) COMMENT '无效标志',
    remove_flag VARCHAR(32) COMMENT '删除标志',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='横向项目表';

-- 横向项目成员表
CREATE TABLE biz_horizontal_project_member (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    project_id VARCHAR(32) COMMENT '项目ID',
    person_id VARCHAR(32) COMMENT '人员ID',
    member_name VARCHAR(50) COMMENT '成员姓名',
    member_account VARCHAR(50) COMMENT '成员账号',
    member_type VARCHAR(32) COMMENT '成员类型',
    member_role VARCHAR(32) COMMENT '成员角色',
    work_ratio DECIMAL(10,2) COMMENT '工作比例',
    order_id INT COMMENT '排序',
    unit_id VARCHAR(32) COMMENT '单位ID',
    title_id VARCHAR(32) COMMENT '职称ID',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='横向项目成员表';

-- 校级项目表
CREATE TABLE biz_school_project (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    project_name VARCHAR(1024) COMMENT '项目名称',
    project_code VARCHAR(200) COMMENT '项目编号',
    project_type_id VARCHAR(32) COMMENT '项目类型ID',
    project_category_id VARCHAR(32) COMMENT '项目类别ID',
    subject_class_id VARCHAR(32) COMMENT '学科分类ID',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    total_fund DECIMAL(15,2) COMMENT '总经费',
    principal_id VARCHAR(32) COMMENT '负责人ID',
    principal_name VARCHAR(500) COMMENT '负责人姓名',
    principal_account VARCHAR(50) COMMENT '负责人账号',
    unit_id VARCHAR(32) COMMENT '单位ID',
    project_status VARCHAR(32) COMMENT '项目状态',
    check_status VARCHAR(32) COMMENT '审核状态',
    check_date DATE COMMENT '审核日期',
    checker VARCHAR(50) COMMENT '审核人',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    note TEXT COMMENT '备注',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    last_edit_user_id VARCHAR(50) COMMENT '最后编辑用户ID',
    create_date DATE COMMENT '创建日期',
    last_edit_date DATE COMMENT '最后编辑日期',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    last_edit_user_name VARCHAR(50) COMMENT '最后编辑用户名',
    invalid_flag VARCHAR(32) COMMENT '无效标志',
    remove_flag VARCHAR(32) COMMENT '删除标志',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='校级项目表';

-- 教学项目表（强制要求★）
CREATE TABLE biz_teaching_project (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    project_name VARCHAR(1024) COMMENT '项目名称',
    project_code VARCHAR(200) COMMENT '项目编号',
    project_type_id VARCHAR(32) COMMENT '项目类型ID',
    project_level_id VARCHAR(32) COMMENT '项目级别ID（国家级、省级、市级、校级）',
    subject_class_id VARCHAR(32) COMMENT '学科分类ID',
    teaching_field VARCHAR(200) COMMENT '教学领域',
    course_name VARCHAR(500) COMMENT '课程名称',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    total_fund DECIMAL(15,2) COMMENT '总经费',
    principal_id VARCHAR(32) COMMENT '负责人ID',
    principal_name VARCHAR(500) COMMENT '负责人姓名',
    principal_account VARCHAR(50) COMMENT '负责人账号',
    unit_id VARCHAR(32) COMMENT '单位ID',
    project_status VARCHAR(32) COMMENT '项目状态',
    check_status VARCHAR(32) COMMENT '审核状态',
    check_date DATE COMMENT '审核日期',
    checker VARCHAR(50) COMMENT '审核人',
    achievement_type VARCHAR(100) COMMENT '成果类型',
    achievement_description TEXT COMMENT '成果描述',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    note TEXT COMMENT '备注',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    last_edit_user_id VARCHAR(50) COMMENT '最后编辑用户ID',
    create_date DATE COMMENT '创建日期',
    last_edit_date DATE COMMENT '最后编辑日期',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    last_edit_user_name VARCHAR(50) COMMENT '最后编辑用户名',
    invalid_flag VARCHAR(32) COMMENT '无效标志',
    remove_flag VARCHAR(32) COMMENT '删除标志',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='教学项目表（强制要求★）';

-- 教学项目成员表
CREATE TABLE biz_teaching_project_member (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    project_id VARCHAR(32) COMMENT '项目ID',
    person_id VARCHAR(32) COMMENT '人员ID',
    member_name VARCHAR(50) COMMENT '成员姓名',
    member_account VARCHAR(50) COMMENT '成员账号',
    member_type VARCHAR(32) COMMENT '成员类型',
    member_role VARCHAR(32) COMMENT '成员角色',
    work_ratio DECIMAL(10,2) COMMENT '工作比例',
    order_id INT COMMENT '排序',
    unit_id VARCHAR(32) COMMENT '单位ID',
    title_id VARCHAR(32) COMMENT '职称ID',
    teaching_experience TEXT COMMENT '教学经验',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='教学项目成员表';

-- 项目变更记录表
CREATE TABLE biz_project_change (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    project_id VARCHAR(32) COMMENT '项目ID',
    project_type VARCHAR(32) COMMENT '项目类型（vertical/horizontal/school/teaching）',
    change_type VARCHAR(32) COMMENT '变更类型（member/budget/schedule/content）',
    change_reason TEXT COMMENT '变更原因',
    change_content TEXT COMMENT '变更内容',
    change_before TEXT COMMENT '变更前内容',
    change_after TEXT COMMENT '变更后内容',
    change_status VARCHAR(32) COMMENT '变更状态',
    apply_date DATE COMMENT '申请日期',
    approve_date DATE COMMENT '批准日期',
    approver VARCHAR(50) COMMENT '批准人',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    note TEXT COMMENT '备注',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='项目变更记录表';

-- 项目结项表
CREATE TABLE biz_project_completion (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    project_id VARCHAR(32) COMMENT '项目ID',
    project_type VARCHAR(32) COMMENT '项目类型（vertical/horizontal/school/teaching）',
    completion_type VARCHAR(32) COMMENT '结项类型（normal/early/extension）',
    completion_date DATE COMMENT '结项日期',
    actual_end_date DATE COMMENT '实际结束日期',
    completion_status VARCHAR(32) COMMENT '结项状态',
    achievement_summary TEXT COMMENT '成果总结',
    fund_usage_summary TEXT COMMENT '经费使用总结',
    evaluation_result VARCHAR(100) COMMENT '评价结果',
    evaluator VARCHAR(50) COMMENT '评价人',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    note TEXT COMMENT '备注',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='项目结项表';
