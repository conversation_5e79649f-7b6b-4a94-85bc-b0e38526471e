<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="待办标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入待办标题"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-select v-model="queryParams.priority" placeholder="优先级" clearable>
          <el-option label="低" value="1" />
          <el-option label="中" value="2" />
          <el-option label="高" value="3" />
          <el-option label="紧急" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="状态" clearable>
          <el-option label="待处理" value="0" />
          <el-option label="处理中" value="1" />
          <el-option label="已完成" value="2" />
          <el-option label="已取消" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="负责人" prop="assigneeName">
        <el-input
          v-model="queryParams.assigneeName"
          placeholder="请输入负责人"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:todo:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:todo:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:todo:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Bell"
          :disabled="multiple"
          @click="handleRemind"
          v-hasPermi="['system:todo:remind']"
        >发送提醒</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 标签页 -->
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <el-tab-pane label="我的待办" name="my">
        <TodoTable 
          :data="todoList" 
          :loading="loading"
          @selection-change="handleSelectionChange"
          @view="handleView"
          @edit="handleUpdate"
          @delete="handleDelete"
          @assign="handleAssign"
          @process="handleProcess"
          @complete="handleComplete"
          @cancel="handleCancel"
          @reopen="handleReopen"
        />
      </el-tab-pane>
      <el-tab-pane label="我创建的" name="created">
        <TodoTable 
          :data="todoList" 
          :loading="loading"
          @selection-change="handleSelectionChange"
          @view="handleView"
          @edit="handleUpdate"
          @delete="handleDelete"
          @assign="handleAssign"
          @process="handleProcess"
          @complete="handleComplete"
          @cancel="handleCancel"
          @reopen="handleReopen"
        />
      </el-tab-pane>
      <el-tab-pane label="全部待办" name="all" v-if="hasPermission(['system:todo:list'])">
        <TodoTable 
          :data="todoList" 
          :loading="loading"
          @selection-change="handleSelectionChange"
          @view="handleView"
          @edit="handleUpdate"
          @delete="handleDelete"
          @assign="handleAssign"
          @process="handleProcess"
          @complete="handleComplete"
          @cancel="handleCancel"
          @reopen="handleReopen"
        />
      </el-tab-pane>
    </el-tabs>
    
    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改待办对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <TodoForm 
        ref="todoFormRef"
        :form="form"
        :is-edit="isEdit"
        @submit="submitForm"
        @cancel="cancel"
      />
    </el-dialog>

    <!-- 分配待办对话框 -->
    <el-dialog title="分配待办" v-model="assignVisible" width="500px">
      <AssignForm 
        ref="assignFormRef"
        :todo-id="currentTodoId"
        @submit="handleAssignSubmit"
        @cancel="assignVisible = false"
      />
    </el-dialog>

    <!-- 处理待办对话框 -->
    <el-dialog title="处理待办" v-model="processVisible" width="600px">
      <ProcessForm 
        ref="processFormRef"
        :todo-id="currentTodoId"
        @submit="handleProcessSubmit"
        @cancel="processVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup name="Todo" lang="ts">
import { ref, reactive, onMounted, getCurrentInstance, toRefs } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  listMyTodo, 
  listMyCreatedTodo, 
  listTodo, 
  getTodo, 
  addTodo, 
  updateTodo, 
  delTodo,
  batchSendTodoReminder
} from '@/api/system/todo'

// 导入子组件
import TodoTable from './components/TodoTable.vue'
import TodoForm from './components/TodoForm.vue'
import AssignForm from './components/AssignForm.vue'
import ProcessForm from './components/ProcessForm.vue'
import Pagination from '@/components/Pagination/index.vue'

// 导入权限检查函数
import { checkPermi } from '@/directive/permission'

// 权限检查函数
const hasPermission = (permissions: string[]) => {
  return checkPermi(permissions)
}

const { proxy } = getCurrentInstance() as any

// 响应式数据
const todoList = ref([])
const open = ref(false)
const assignVisible = ref(false)
const processVisible = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const isEdit = ref(false)
const activeTab = ref('my')
const currentTodoId = ref<number>()

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: undefined,
    priority: undefined,
    status: undefined,
    assigneeName: undefined
  }
})

const { queryParams, form } = toRefs(data)

/** 查询待办列表 */
function getList() {
  loading.value = true
  
  let apiCall
  switch (activeTab.value) {
    case 'my':
      apiCall = listMyTodo(queryParams.value)
      break
    case 'created':
      apiCall = listMyCreatedTodo(queryParams.value)
      break
    case 'all':
      apiCall = listTodo(queryParams.value)
      break
    default:
      apiCall = listMyTodo(queryParams.value)
  }
  
  apiCall.then(response => {
    todoList.value = response.rows
    total.value = response.total
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}

/** 标签页切换 */
function handleTabChange(tabName: string) {
  activeTab.value = tabName
  queryParams.value.pageNum = 1
  getList()
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.value = {
    todoId: undefined,
    title: undefined,
    content: undefined,
    priority: "2",
    assigneeId: undefined,
    assigneeName: undefined,
    dueTime: undefined,
    tags: undefined
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.todoId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加待办"
  isEdit.value = false
}

/** 修改按钮操作 */
function handleUpdate(row?: any) {
  reset()
  const todoId = row?.todoId || ids.value[0]
  getTodo(todoId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改待办"
    isEdit.value = true
  })
}

/** 查看按钮操作 */
function handleView(row: any) {
  proxy.$router.push({ path: '/system/todo/detail/' + row.todoId })
}

/** 提交表单 */
function submitForm(formData: any) {
  if (isEdit.value) {
    updateTodo(formData).then(() => {
      proxy.$modal.msgSuccess("修改成功")
      open.value = false
      getList()
    })
  } else {
    addTodo(formData).then(() => {
      proxy.$modal.msgSuccess("新增成功")
      open.value = false
      getList()
    })
  }
}

/** 删除按钮操作 */
function handleDelete(row?: any) {
  const todoIds = row?.todoId ? [row.todoId] : ids.value
  proxy.$modal.confirm('是否确认删除选中的待办事项？').then(() => {
    return delTodo(todoIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 分配待办 */
function handleAssign(row: any) {
  currentTodoId.value = row.todoId
  assignVisible.value = true
}

/** 分配提交 */
function handleAssignSubmit() {
  assignVisible.value = false
  getList()
  ElMessage.success('分配成功')
}

/** 处理待办 */
function handleProcess(row: any) {
  currentTodoId.value = row.todoId
  processVisible.value = true
}

/** 处理提交 */
function handleProcessSubmit() {
  processVisible.value = false
  getList()
  ElMessage.success('处理成功')
}

/** 完成待办 */
function handleComplete(row: any) {
  proxy.$modal.prompt('请输入完成说明', '完成待办', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(({ value }) => {
    // 调用完成API
    ElMessage.success('待办已完成')
    getList()
  }).catch(() => {})
}

/** 取消待办 */
function handleCancel(row: any) {
  proxy.$modal.prompt('请输入取消原因', '取消待办', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(({ value }) => {
    // 调用取消API
    ElMessage.success('待办已取消')
    getList()
  }).catch(() => {})
}

/** 重新打开待办 */
function handleReopen(row: any) {
  proxy.$modal.prompt('请输入重新打开原因', '重新打开待办', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(({ value }) => {
    // 调用重新打开API
    ElMessage.success('待办已重新打开')
    getList()
  }).catch(() => {})
}

/** 发送提醒 */
function handleRemind() {
  if (ids.value.length === 0) {
    ElMessage.warning('请选择要提醒的待办事项')
    return
  }
  
  proxy.$modal.confirm('是否确认发送提醒？').then(() => {
    return batchSendTodoReminder(ids.value)
  }).then(() => {
    ElMessage.success('提醒发送成功')
  }).catch(() => {})
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
