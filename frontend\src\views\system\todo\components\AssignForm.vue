<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
    <el-form-item label="负责人" prop="assigneeName">
      <el-input v-model="form.assigneeName" placeholder="请输入负责人姓名" />
    </el-form-item>
    <el-form-item label="分配说明" prop="assignRemark">
      <el-input
        v-model="form.assignRemark"
        type="textarea"
        :rows="3"
        placeholder="请输入分配说明（可选）"
      />
    </el-form-item>
    
    <div class="form-actions">
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
      <el-button @click="handleCancel">取 消</el-button>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { assignTodo } from '@/api/system/todo'

// Props
interface Props {
  todoId: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits(['submit', 'cancel'])

// 响应式数据
const formRef = ref()
const form = reactive({
  assigneeName: '',
  assignRemark: ''
})

// 表单验证规则
const rules = {
  assigneeName: [
    { required: true, message: '请输入负责人姓名', trigger: 'blur' }
  ]
}

// 提交表单
const handleSubmit = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        // 这里应该先通过用户名查询用户ID，简化处理直接使用姓名
        const assigneeId = 1 // 实际应该通过API查询用户ID
        
        await assignTodo(props.todoId, assigneeId, form.assigneeName)
        ElMessage.success('分配成功')
        emit('submit')
      } catch (error) {
        console.error('分配失败:', error)
        ElMessage.error('分配失败')
      }
    } else {
      ElMessage.error('请检查表单输入')
    }
  })
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.form-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}
</style>
