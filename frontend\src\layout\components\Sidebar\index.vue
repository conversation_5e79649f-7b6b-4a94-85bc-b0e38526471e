<template>
  <div class="sidebar-container">
    <div class="logo">
      <div class="logo-content">
        <img src="/logo2.png" alt="科研管理系统" class="logo-image" />
      </div>
    </div>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        background-color="#edf0f5"
        text-color="#606266"
        active-text-color="#4f7cff"
        mode="vertical"
        @select="handleMenuSelect"
      >
        <!-- 动态菜单 -->
        <dynamic-menu :menus="menuStore.menus" />

        <!-- 如果没有动态菜单，显示默认菜单 -->
        <template v-if="menuStore.menus.length === 0">
          <el-menu-item index="/workspace">
            <el-icon><monitor /></el-icon>
            <span>个人工作台</span>
          </el-menu-item>
          <el-sub-menu index="/system">
            <template #title>
              <el-icon><setting /></el-icon>
              <span>系统管理</span>
            </template>
            <el-menu-item index="/system/user">
              <el-icon><user /></el-icon>
              <span>用户管理</span>
            </el-menu-item>
            <el-menu-item index="/system/role">
              <el-icon><user-filled /></el-icon>
              <span>角色管理</span>
            </el-menu-item>
            <el-menu-item index="/system/menu">
              <span style="font-size: 16px; margin-right: 8px;">🌲</span>
              <span>菜单管理</span>
            </el-menu-item>
            <el-menu-item index="/system/dept">
              <el-icon><office-building /></el-icon>
              <span>部门管理</span>
            </el-menu-item>
          </el-sub-menu>
        </template>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { computed, inject } from 'vue'
import { useRoute } from 'vue-router'
import { useMenuStore } from '@/store/modules/menu'
import DynamicMenu from '@/components/DynamicMenu/index.vue'
import { Monitor, Setting, User, UserFilled, OfficeBuilding } from '@element-plus/icons-vue'

const route = useRoute()
const menuStore = useMenuStore()

// 注入动态内容组件的方法
const dynamicContent = inject('dynamicContent') as any

const activeMenu = computed(() => {
  // 默认选中个人工作台
  return '/workspace'
})

// 处理菜单选择 - 动态查找菜单信息
const handleMenuSelect = (index: string) => {
  console.log('菜单选择:', index)
  console.log('当前菜单数据:', menuStore.menus)

  // 如果菜单数据还没有加载，等待一下再尝试
  if (menuStore.menus.length === 0) {
    console.log('菜单数据还未加载，等待加载...')
    // 延迟一下再尝试，给菜单数据加载一些时间
    setTimeout(() => {
      handleMenuSelect(index)
    }, 100)
    return
  }

  // 动态查找菜单信息
  const menuInfo = findMenuByPath(index)

  if (menuInfo && dynamicContent?.loadPageByPath) {
    console.log('找到菜单信息，加载页面:', menuInfo)
    dynamicContent.loadPageByPath(index, menuInfo.name, menuInfo.parent)
  } else {
    console.warn('未找到菜单信息:', index)
    // 如果找不到菜单信息，尝试使用默认的处理方式
    if (dynamicContent?.loadPageByPath) {
      const defaultName = index.split('/').pop() || '未知页面'
      dynamicContent.loadPageByPath(index, defaultName)
    }
  }
}

// 动态查找菜单信息
const findMenuByPath = (targetPath: string): { name: string, parent?: string } | null => {
  // 递归查找菜单项
  const findInMenus = (menus: any[], parentName?: string): { name: string, parent?: string } | null => {
    for (const menu of menus) {
      // 构建完整路径
      let fullPath = menu.path
      if (!fullPath.startsWith('/')) {
        fullPath = `/${fullPath}`
      }

      // 如果是子菜单，需要拼接父路径
      if (parentName && menu.parentId && menu.parentId !== 0) {
        // 查找父菜单的路径
        const parentMenu = findParentMenu(menu.parentId, menuStore.menus)
        if (parentMenu) {
          let parentPath = parentMenu.path
          if (!parentPath.startsWith('/')) {
            parentPath = `/${parentPath}`
          }
          if (!menu.path.startsWith('/')) {
            fullPath = `${parentPath}/${menu.path}`
          }
        }
      }

      console.log('检查菜单路径:', { menuName: menu.menuName, fullPath, targetPath })

      // 如果路径匹配
      if (fullPath === targetPath) {
        return {
          name: menu.menuName,
          parent: parentName
        }
      }

      // 递归查找子菜单
      if (menu.children && menu.children.length > 0) {
        const result = findInMenus(menu.children, menu.menuName)
        if (result) return result
      }
    }
    return null
  }

  // 从菜单数据中查找
  const result = findInMenus(menuStore.menus)

  // 如果没找到，返回默认值
  if (!result) {
    // 特殊处理个人工作台
    if (targetPath === '/workspace') {
      return { name: '个人工作台' }
    }

    // 从路径推断名称
    const pathParts = targetPath.split('/').filter(Boolean)
    if (pathParts.length > 0) {
      const name = pathParts[pathParts.length - 1]
      return { name }
    }
  }

  return result
}

// 查找父菜单
const findParentMenu = (parentId: number, menus: any[]): any => {
  for (const menu of menus) {
    if (menu.menuId === parentId) {
      return menu
    }
    if (menu.children && menu.children.length > 0) {
      const result = findParentMenu(parentId, menu.children)
      if (result) return result
    }
  }
  return null
}
</script>

<style lang="scss" scoped>
.sidebar-container {
  height: 100%;
  background: #edf0f5;
  border-right: 1px solid #e4e7ed;
  box-shadow: 2px 0 12px rgba(0, 0, 0, 0.08);

  .logo {
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
    border-bottom: 1px solid #e4e7ed;
    padding: 0 20px;

    .logo-content {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
    }

    .logo-image {
      max-height: 50px;
      max-width: 180px;
      object-fit: contain;
    }
  }

  .scrollbar-wrapper {
    height: calc(100% - 70px);
  }

  :deep(.el-scrollbar__wrap) {
    background: #edf0f5;
  }

  :deep(.el-menu) {
    border: none;
    height: 100%;
    width: 100% !important;
    background: #edf0f5;
  }

  :deep(.el-menu-item) {
    margin: 4px 12px;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(79, 124, 255, 0.05) 0%, rgba(107, 140, 255, 0.05) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%) !important;
      color: #4f7cff !important;
      transform: translateX(4px);
      box-shadow:
        0 4px 12px rgba(79, 124, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);

      &::before {
        opacity: 1;
      }
    }

    &.is-active {
      background: linear-gradient(135deg, #4f7cff 0%, #6b8cff 100%) !important;
      color: #ffffff !important;
      transform: translateX(6px);
      box-shadow:
        0 6px 20px rgba(79, 124, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
      border-left: 4px solid rgba(255, 255, 255, 0.3);

      &::before {
        opacity: 0;
      }
    }
  }

  :deep(.el-sub-menu) {
    margin: 4px 12px;
    border-radius: 8px;
    overflow: hidden;

    .el-sub-menu__title {
      border-radius: 8px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(79, 124, 255, 0.05) 0%, rgba(107, 140, 255, 0.05) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%) !important;
        color: #4f7cff !important;
        transform: translateX(4px);
        box-shadow:
          0 4px 12px rgba(79, 124, 255, 0.15),
          inset 0 1px 0 rgba(255, 255, 255, 0.8);

        &::before {
          opacity: 1;
        }
      }
    }
  }

  :deep(.el-sub-menu.is-active) {
    .el-sub-menu__title {
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%) !important;
      color: #4f7cff !important;
      transform: translateX(4px);
      box-shadow:
        0 4px 12px rgba(79, 124, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    }
  }

  :deep(.el-menu-item-group) {
    .el-menu-item {
      margin-left: 24px;
      margin-right: 12px;
    }
  }
}
</style>
