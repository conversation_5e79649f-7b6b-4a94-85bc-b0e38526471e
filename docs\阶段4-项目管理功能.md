# 第四阶段：项目管理功能 (3-4周)

## 阶段概述
**目标**: 实现各类科研项目的全生命周期管理，这是系统的核心业务功能
**预计时间**: 3-4周
**人力投入**: 5-6人
**前置条件**: 工作流引擎集成完成

## 验收标准
- [ ] 纵向项目管理流程完整，支持立项、变更、结项全流程
- [ ] 横向项目管理功能完善，支持合同和合作单位管理
- [ ] 校级项目和教学项目管理功能正常
- [ ] 所有项目类型都有对应的工作流程
- [ ] 项目数据统计和报表功能可用

---

## 纵向项目管理

### 立项管理
- [ ] **项目立项申请接口**
  - [ ] 创建VerticalProjectController
  - [ ] 实现立项申请提交功能
  - [ ] 支持项目基本信息录入
  - [ ] 实现立项申请书上传
  - [ ] 添加申请数据验证
  - [ ] 启动立项审批工作流

- [ ] **项目基本信息管理接口**
  - [ ] 实现项目信息查询接口
  - [ ] 支持项目信息编辑功能
  - [ ] 实现项目状态管理
  - [ ] 添加项目编号生成规则
  - [ ] 支持项目分类管理

- [ ] **项目成员管理接口**
  - [ ] 实现项目成员添加功能
  - [ ] 支持成员角色分配
  - [ ] 实现成员权限管理
  - [ ] 添加成员变更记录
  - [ ] 支持成员工作量统计

- [ ] **项目预算管理接口**
  - [ ] 实现预算科目管理
  - [ ] 支持预算分配功能
  - [ ] 实现预算执行监控
  - [ ] 添加预算变更管理
  - [ ] 支持预算报表生成

- [ ] **项目文档管理接口**
  - [ ] 实现项目文档上传
  - [ ] 支持文档分类管理
  - [ ] 实现文档版本控制
  - [ ] 添加文档权限控制
  - [ ] 支持文档全文检索

### 变更管理
- [ ] **项目变更申请接口**
  - [ ] 实现变更申请提交
  - [ ] 支持变更类型分类
  - [ ] 实现变更原因说明
  - [ ] 添加变更影响分析
  - [ ] 启动变更审批流程

- [ ] **项目变更审核接口**
  - [ ] 实现变更审核功能
  - [ ] 支持多级审核流程
  - [ ] 添加审核意见记录
  - [ ] 实现变更决策管理
  - [ ] 支持变更通知功能

- [ ] **项目变更记录接口**
  - [ ] 实现变更历史记录
  - [ ] 支持变更对比功能
  - [ ] 添加变更统计分析
  - [ ] 实现变更报告生成
  - [ ] 支持变更数据导出

### 结项管理
- [ ] **项目结项申请接口**
  - [ ] 实现结项申请提交
  - [ ] 支持结项材料上传
  - [ ] 实现结项自查功能
  - [ ] 添加结项条件检查
  - [ ] 启动结项审核流程

- [ ] **项目结项审核接口**
  - [ ] 实现结项审核功能
  - [ ] 支持专家评审管理
  - [ ] 添加审核结果记录
  - [ ] 实现结项决策管理
  - [ ] 支持结项通知功能

- [ ] **项目成果提交接口**
  - [ ] 实现成果信息录入
  - [ ] 支持成果文件上传
  - [ ] 实现成果分类管理
  - [ ] 添加成果验证功能
  - [ ] 支持成果统计分析

- [ ] **项目归档接口**
  - [ ] 实现项目归档功能
  - [ ] 支持归档材料整理
  - [ ] 实现归档状态管理
  - [ ] 添加归档检索功能
  - [ ] 支持归档数据备份

---

## 横向项目管理

### 横向项目核心功能
- [x] **横向项目实体设计**
  - [x] 创建HorizontalProject实体类
  - [x] 创建HorizontalProjectDto
  - [x] 创建HorizontalProjectMapper接口
  - [x] 创建HorizontalProjectService接口和实现
  - [x] 创建HorizontalProjectController

- [x] **横向项目基础功能**
  - [x] 实现项目CRUD操作
  - [x] 支持项目申请提交
  - [x] 实现项目审批流程
  - [x] 添加项目状态管理
  - [x] 支持项目经费管理
  - [x] 实现项目统计分析

### 合同管理
- [x] **合同实体设计**
  - [x] 创建Contract实体类
  - [x] 创建ContractDto
  - [x] 创建ContractMapper接口
  - [x] 创建ContractService接口
  - [x] 创建ContractController
  - [x] 实现ContractServiceImpl

- [x] **合同核心功能**
  - [x] 实现合同CRUD操作
  - [x] 支持合同状态管理
  - [x] 实现合同审批流程
  - [x] 添加合同签署管理
  - [x] 支持合同统计分析

- [x] **合同签审流程**
  - [x] 实现合同起草功能
  - [x] 实现合同审核流程
  - [x] 添加合同签署管理
  - [x] 支持合同状态流转
  - [ ] 支持合同变更流程

- [x] **合同备案管理**
  - [x] 实现合同备案功能
  - [x] 支持备案材料上传
  - [x] 添加备案状态跟踪
  - [x] 支持备案查询统计
  - [ ] 实现备案审核流程

- [ ] **合同变更管理**
  - [ ] 实现合同变更申请
  - [ ] 支持变更内容对比
  - [ ] 实现变更审批流程
  - [ ] 添加变更记录管理
  - [ ] 支持变更通知功能

- [ ] **合同模板管理**
  - [ ] 实现模板创建功能
  - [ ] 支持模板分类管理
  - [ ] 实现模板版本控制
  - [ ] 添加模板使用统计
  - [ ] 支持模板导入导出

### 合作单位管理
- [x] **合作单位实体设计**
  - [x] 创建Partner实体类
  - [x] 创建PartnerDto
  - [x] 创建PartnerMapper接口
  - [x] 创建PartnerService接口
  - [x] 创建PartnerController
  - [x] 实现PartnerServiceImpl

- [x] **合作单位核心功能**
  - [x] 实现单位CRUD操作
  - [x] 支持单位状态管理
  - [x] 实现单位审核功能
  - [x] 添加合作等级管理
  - [x] 支持统计分析功能

- [x] **合作单位信息管理**
  - [x] 实现单位信息录入
  - [x] 支持单位信息查询
  - [x] 实现单位信息编辑
  - [x] 添加单位状态管理
  - [x] 支持单位分类管理

- [x] **合作单位评价管理**
  - [x] 实现评价功能接口
  - [x] 支持评价数据录入
  - [x] 实现评价结果计算
  - [x] 支持评价历史查询
  - [ ] 添加评价报告生成

- [ ] **合作单位资质管理**
  - [ ] 实现资质信息录入
  - [ ] 支持资质文件上传
  - [ ] 实现资质有效期管理
  - [ ] 添加资质到期提醒
  - [ ] 支持资质审核功能

---

## 校级项目管理

### 校级项目流程
- [ ] **校级项目立项流程**
  - [ ] 创建SchoolProjectController
  - [ ] 实现立项申请功能
  - [ ] 配置校级审批流程
  - [ ] 添加立项条件检查
  - [ ] 支持立项结果通知

- [ ] **校级项目变更流程**
  - [ ] 实现变更申请功能
  - [ ] 配置变更审批流程
  - [ ] 添加变更影响评估
  - [ ] 支持变更决策管理

- [ ] **校级项目结项流程**
  - [ ] 实现结项申请功能
  - [ ] 配置结项审核流程
  - [ ] 添加结项验收管理
  - [ ] 支持结项成果管理

---

## 教学项目管理

### 教学项目流程
- [ ] **教学项目立项流程**
  - [ ] 创建TeachingProjectController
  - [ ] 实现立项申请功能
  - [ ] 配置教学项目审批流程
  - [ ] 添加教学条件检查
  - [ ] 支持立项结果管理

- [ ] **教学项目变更流程**
  - [ ] 实现变更申请功能
  - [ ] 配置教学项目变更流程
  - [ ] 添加变更合理性检查
  - [ ] 支持变更记录管理

- [ ] **教学项目结项流程**
  - [ ] 实现结项申请功能
  - [ ] 配置教学项目结项流程
  - [ ] 添加教学成果验收
  - [ ] 支持结项评价管理

---

## 项目管理前端页面

### 纵向项目页面
- [ ] **项目列表页面**
  - [ ] 创建VerticalProjectList页面
  - [ ] 实现项目列表展示
  - [ ] 添加项目搜索筛选
  - [ ] 支持项目状态管理

- [ ] **项目详情页面**
  - [ ] 创建ProjectDetail页面
  - [ ] 显示项目基本信息
  - [ ] 实现项目进度展示
  - [ ] 添加项目操作功能

- [ ] **立项申请页面**
  - [ ] 创建ProjectApplication页面
  - [ ] 实现立项表单填写
  - [ ] 添加文件上传功能
  - [ ] 支持申请提交功能

- [ ] **项目变更页面**
  - [ ] 创建ProjectChange页面
  - [ ] 实现变更申请表单
  - [ ] 添加变更对比功能
  - [ ] 支持变更审核功能

- [ ] **项目结项页面**
  - [ ] 创建ProjectCompletion页面
  - [ ] 实现结项申请表单
  - [ ] 添加成果提交功能
  - [ ] 支持结项审核功能

### 横向项目页面
- [ ] **合同管理页面**
  - [ ] 创建ContractManagement页面
  - [ ] 实现合同列表展示
  - [ ] 添加合同编辑功能
  - [ ] 支持合同审批功能

- [ ] **合作单位页面**
  - [ ] 创建PartnerManagement页面
  - [ ] 实现单位信息管理
  - [ ] 添加资质管理功能
  - [ ] 支持评价管理功能

### 项目统计页面
- [ ] **项目统计大屏**
  - [ ] 创建ProjectStatistics页面
  - [ ] 实现项目数据统计
  - [ ] 添加图表展示功能
  - [ ] 支持数据导出功能

- [ ] **项目报表页面**
  - [ ] 创建ProjectReports页面
  - [ ] 实现报表模板管理
  - [ ] 添加报表生成功能
  - [ ] 支持报表下载功能

---

## 数据库设计

### 项目相关表
```sql
-- 纵向项目表
CREATE TABLE vertical_project (
    id BIGINT PRIMARY KEY,
    project_no VARCHAR(50) UNIQUE,
    project_name VARCHAR(200),
    project_type VARCHAR(50),
    status VARCHAR(20),
    start_date DATE,
    end_date DATE,
    budget DECIMAL(15,2),
    principal_id BIGINT,
    dept_id BIGINT,
    created_time DATETIME,
    updated_time DATETIME
);

-- 项目成员表
CREATE TABLE project_member (
    id BIGINT PRIMARY KEY,
    project_id BIGINT,
    user_id BIGINT,
    role VARCHAR(50),
    workload DECIMAL(5,2),
    join_date DATE,
    leave_date DATE
);

-- 项目变更表
CREATE TABLE project_change (
    id BIGINT PRIMARY KEY,
    project_id BIGINT,
    change_type VARCHAR(50),
    change_reason TEXT,
    change_content TEXT,
    status VARCHAR(20),
    apply_time DATETIME,
    approve_time DATETIME
);
```

---

## 阶段总结

### 技术要点
- [ ] 复杂业务流程设计
- [ ] 工作流与业务深度集成
- [ ] 项目数据统计分析
- [ ] 文档管理和版本控制

### 完成标志
- [ ] 所有项目类型管理功能完整
- [ ] 工作流程运行稳定
- [ ] 数据统计功能正常
- [ ] 用户操作体验良好

### 下一阶段准备
- [ ] 确认项目管理功能稳定性
- [ ] 准备申报评审功能开发
- [ ] 优化系统性能表现
- [ ] 收集用户使用反馈
