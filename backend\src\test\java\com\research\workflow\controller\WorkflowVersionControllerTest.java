package com.research.workflow.controller;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * WorkflowVersionController编译测试
 */
@SpringBootTest
public class WorkflowVersionControllerTest {

    @Test
    public void testCompilation() {
        // 这个测试只是为了验证WorkflowVersionController能够正常编译
        WorkflowVersionController controller = new WorkflowVersionController();
        // 如果能创建实例，说明编译通过
        assert controller != null;
    }
}
