package com.research.system.controller;

import com.research.common.core.domain.AjaxResult;
import com.research.system.domain.SysUser;
import com.research.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test")
public class TestController {
    
    @Autowired
    private ISysUserService userService;
    
    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * 测试用户查询
     */
    @GetMapping("/user")
    public AjaxResult testUser() {
        SysUser user = userService.selectUserByUserName("admin");
        if (user != null) {
            // 不返回密码
            user.setPassword("***");
        }
        return AjaxResult.success(user);
    }

    /**
     * 测试密码验证
     */
    @GetMapping("/password")
    public AjaxResult testPassword() {
        SysUser user = userService.selectUserByUserName("admin");
        if (user != null) {
            String storedPassword = user.getPassword();
            boolean matches123 = passwordEncoder.matches("admin123", storedPassword);
            boolean matches456 = passwordEncoder.matches("123456", storedPassword);

            // 测试data.sql中的密码哈希
            String dataSqlHash = "$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye";
            boolean dataSqlMatches = passwordEncoder.matches("admin123", dataSqlHash);

            // 生成新的admin123密码哈希用于对比
            String newHash = passwordEncoder.encode("admin123");

            return AjaxResult.success("存储的密码: " + storedPassword +
                                    ", admin123匹配: " + matches123 +
                                    ", 123456匹配: " + matches456 +
                                    ", data.sql哈希匹配admin123: " + dataSqlMatches +
                                    ", 新生成的admin123哈希: " + newHash);
        }
        return AjaxResult.error("用户不存在");
    }

    /**
     * 修复admin用户密码
     */
    @GetMapping("/fix-password")
    public AjaxResult fixPassword() {
        try {
            SysUser user = userService.selectUserByUserName("admin");
            if (user != null) {
                // 使用data.sql中的正确密码哈希
                String correctHash = "$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye";
                user.setPassword(correctHash);
                userService.updateById(user);

                // 验证修复结果
                SysUser updatedUser = userService.selectUserByUserName("admin");
                boolean matches = passwordEncoder.matches("admin123", updatedUser.getPassword());

                return AjaxResult.success("密码修复成功，admin123匹配: " + matches);
            }
            return AjaxResult.error("admin用户不存在");
        } catch (Exception e) {
            return AjaxResult.error("密码修复失败: " + e.getMessage());
        }
    }
}
