-- =============================================
-- 科研成果多维敏捷管控中心 - 快速初始化脚本
-- 仅包含核心测试数据，用于快速部署和测试
-- =============================================

-- 清空现有数据（可选，谨慎使用）
-- TRUNCATE TABLE sys_user_role;
-- TRUNCATE TABLE sys_role_menu;
-- TRUNCATE TABLE sys_user_post;
-- TRUNCATE TABLE sys_user;
-- TRUNCATE TABLE sys_role;
-- TRUNCATE TABLE sys_post;
-- TRUNCATE TABLE sys_dept;
-- TRUNCATE TABLE sys_menu;
-- TRUNCATE TABLE sys_notice;

-- =============================================
-- 1. 核心部门数据
-- =============================================
INSERT IGNORE INTO sys_dept (dept_id, parent_id, ancestors, dept_name, order_num, leader, phone, email, status, del_flag, create_by, create_time) VALUES
(100, 0, '0', '科研大学', 0, '校长', '15888888888', '<EMAIL>', '0', '0', 'admin', NOW()),
(101, 100, '0,100', '科研管理部', 1, '科研处长', '15888888889', '<EMAIL>', '0', '0', 'admin', NOW()),
(103, 100, '0,100', '信息中心', 3, '信息中心主任', '15888888891', '<EMAIL>', '0', '0', 'admin', NOW()),
(104, 100, '0,100', '计算机学院', 4, '院长', '15888888892', '<EMAIL>', '0', '0', 'admin', NOW()),
(105, 100, '0,100', '管理学院', 5, '院长', '15888888893', '<EMAIL>', '0', '0', 'admin', NOW());

-- =============================================
-- 2. 核心岗位数据
-- =============================================
INSERT IGNORE INTO sys_post (post_id, post_code, post_name, post_sort, status, create_by, create_time, remark) VALUES
(1, 'admin', '系统管理员', 1, '0', 'admin', NOW(), '系统管理员'),
(2, 'dean', '院长', 2, '0', 'admin', NOW(), '学院院长'),
(3, 'professor', '教授', 3, '0', 'admin', NOW(), '教授'),
(4, 'teacher', '教师', 4, '0', 'admin', NOW(), '普通教师');

-- =============================================
-- 3. 核心角色数据
-- =============================================
INSERT IGNORE INTO sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) VALUES
(1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', NOW(), '超级管理员'),
(2, '科研管理员', 'research_admin', 2, '2', 1, 1, '0', '0', 'admin', NOW(), '科研管理员'),
(3, '普通用户', 'common', 3, '5', 1, 1, '0', '0', 'admin', NOW(), '普通用户');

-- =============================================
-- 4. 核心用户数据（密码都是：admin123）
-- =============================================
INSERT IGNORE INTO sys_user (user_id, dept_id, user_name, nick_name, user_type, email, phonenumber, sex, avatar, password, status, del_flag, login_ip, login_date, create_by, create_time, remark) VALUES
(1, 103, 'admin', '系统管理员', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', '127.0.0.1', NOW(), 'admin', NOW(), '系统管理员'),
(2, 101, 'research', '科研管理员', '00', '<EMAIL>', '15666666666', '1', '', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', '', NULL, 'admin', NOW(), '科研管理员'),
(3, 104, 'teacher', '教师用户', '00', '<EMAIL>', '15777777777', '0', '', '$2a$10$X0.9vciJBLBGtwMZZrD1qe3y4rFboetV6TCKFsELrNABGAfKtdHye', '0', '0', '', NULL, 'admin', NOW(), '教师用户');

-- =============================================
-- 5. 用户角色关联
-- =============================================
INSERT IGNORE INTO sys_user_role (user_id, role_id) VALUES
(1, 1),  -- admin -> 超级管理员
(2, 2),  -- research -> 科研管理员
(3, 3);  -- teacher -> 普通用户

-- =============================================
-- 6. 用户岗位关联
-- =============================================
INSERT IGNORE INTO sys_user_post (user_id, post_id) VALUES
(1, 1),  -- admin -> 系统管理员
(2, 1),  -- research -> 系统管理员
(3, 4);  -- teacher -> 教师

-- =============================================
-- 7. 核心菜单数据
-- =============================================
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
-- 主菜单
(1, '系统管理', 0, 1, 'system', NULL, '', 1, 0, 'M', '0', '0', '', 'system', 'admin', NOW(), '系统管理目录'),
(2, '科研管理', 0, 2, 'research', NULL, '', 1, 0, 'M', '0', '0', '', 'research', 'admin', NOW(), '科研管理目录'),

-- 系统管理子菜单
(100, '用户管理', 1, 1, 'user', 'system/user/index', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', NOW(), '用户管理菜单'),
(101, '角色管理', 1, 2, 'role', 'system/role/index', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', NOW(), '角色管理菜单'),
(102, '部门管理', 1, 3, 'dept', 'system/dept/index', '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', NOW(), '部门管理菜单'),

-- 科研管理子菜单
(200, '项目管理', 2, 1, 'project', 'research/project/index', '', 1, 0, 'C', '0', '0', 'research:project:list', 'project', 'admin', NOW(), '科研项目管理'),
(201, '成果管理', 2, 2, 'achievement', 'research/achievement/index', '', 1, 0, 'C', '0', '0', 'research:achievement:list', 'achievement', 'admin', NOW(), '科研成果管理');

-- =============================================
-- 8. 角色菜单权限关联
-- =============================================
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES
-- 超级管理员拥有所有权限
(1, 1), (1, 2), (1, 100), (1, 101), (1, 102), (1, 200), (1, 201),
-- 科研管理员权限
(2, 2), (2, 200), (2, 201),
-- 普通用户权限
(3, 2), (3, 200), (3, 201);

-- =============================================
-- 9. 示例通知公告
-- =============================================
INSERT IGNORE INTO sys_notice (notice_id, notice_title, notice_type, notice_content, status, create_by, create_time, remark) VALUES
(1, '系统上线通知', '1', '科研成果多维敏捷管控中心已正式上线，欢迎使用！', '0', 'admin', NOW(), '系统上线'),
(2, '功能更新说明', '2', '系统新增了用户管理、角色管理等基础功能，请及时体验。', '0', 'admin', NOW(), '功能更新');

-- =============================================
-- 10. 示例待办事项
-- =============================================
INSERT IGNORE INTO sys_todo (todo_id, title, content, priority, status, due_date, assigned_to, assigned_by, create_by, create_time, remark) VALUES
(1, '完善系统功能', '继续完善科研管理系统的各项功能模块', 'HIGH', 'PENDING', '2024-03-31', 2, 1, 'admin', NOW(), '系统开发'),
(2, '用户培训', '组织用户进行系统使用培训', 'MEDIUM', 'PENDING', '2024-02-29', 2, 1, 'admin', NOW(), '用户培训');

-- 提交事务
COMMIT;

-- 验证数据
SELECT '=== 快速初始化完成 ===' as info;
SELECT COUNT(*) as user_count FROM sys_user;
SELECT COUNT(*) as role_count FROM sys_role;
SELECT COUNT(*) as dept_count FROM sys_dept;
SELECT COUNT(*) as menu_count FROM sys_menu;

-- 显示登录账号信息
SELECT '=== 可用登录账号 ===' as info;
SELECT user_name as '用户名', nick_name as '昵称', 'admin123' as '密码', email as '邮箱' FROM sys_user ORDER BY user_id;

SELECT '快速初始化完成！可以使用以上账号登录系统进行测试。' as result;
