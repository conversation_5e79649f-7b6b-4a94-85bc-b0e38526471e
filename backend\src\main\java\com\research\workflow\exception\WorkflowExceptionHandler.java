package com.research.workflow.exception;

import com.research.common.core.domain.AjaxResult;
import org.activiti.engine.ActivitiException;
import org.activiti.engine.ActivitiObjectNotFoundException;
import org.activiti.engine.ActivitiOptimisticLockingException;
import org.activiti.engine.ActivitiTaskAlreadyClaimedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 工作流异常处理器
 * 
 * <AUTHOR>
 */
@RestControllerAdvice(basePackages = "com.research.workflow")
public class WorkflowExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(WorkflowExceptionHandler.class);

    /**
     * 处理自定义工作流异常
     */
    @ExceptionHandler(WorkflowException.class)
    public AjaxResult handleWorkflowException(WorkflowException e) {
        logger.error("工作流异常: {}", e.getMessage(), e);
        return AjaxResult.error(e.getErrorCode(), e.getMessage());
    }

    /**
     * 处理流程定义异常
     */
    @ExceptionHandler(WorkflowException.ProcessDefinitionException.class)
    public AjaxResult handleProcessDefinitionException(WorkflowException.ProcessDefinitionException e) {
        logger.error("流程定义异常: {}", e.getMessage(), e);
        return AjaxResult.error("流程定义错误: " + e.getMessage());
    }

    /**
     * 处理流程实例异常
     */
    @ExceptionHandler(WorkflowException.ProcessInstanceException.class)
    public AjaxResult handleProcessInstanceException(WorkflowException.ProcessInstanceException e) {
        logger.error("流程实例异常: {}", e.getMessage(), e);
        return AjaxResult.error("流程实例错误: " + e.getMessage());
    }

    /**
     * 处理任务异常
     */
    @ExceptionHandler(WorkflowException.TaskException.class)
    public AjaxResult handleTaskException(WorkflowException.TaskException e) {
        logger.error("任务异常: {}", e.getMessage(), e);
        return AjaxResult.error("任务错误: " + e.getMessage());
    }

    /**
     * 处理部署异常
     */
    @ExceptionHandler(WorkflowException.DeploymentException.class)
    public AjaxResult handleDeploymentException(WorkflowException.DeploymentException e) {
        logger.error("部署异常: {}", e.getMessage(), e);
        return AjaxResult.error("部署错误: " + e.getMessage());
    }

    /**
     * 处理权限异常
     */
    @ExceptionHandler(WorkflowException.PermissionException.class)
    public AjaxResult handlePermissionException(WorkflowException.PermissionException e) {
        logger.error("权限异常: {}", e.getMessage(), e);
        return AjaxResult.error("权限错误: " + e.getMessage());
    }

    /**
     * 处理Activiti对象未找到异常
     */
    @ExceptionHandler(ActivitiObjectNotFoundException.class)
    public AjaxResult handleActivitiObjectNotFoundException(ActivitiObjectNotFoundException e) {
        logger.error("Activiti对象未找到: {}", e.getMessage(), e);
        return AjaxResult.error("对象不存在: " + e.getMessage());
    }

    /**
     * 处理任务已被签收异常
     */
    @ExceptionHandler(ActivitiTaskAlreadyClaimedException.class)
    public AjaxResult handleActivitiTaskAlreadyClaimedException(ActivitiTaskAlreadyClaimedException e) {
        logger.error("任务已被签收: {}", e.getMessage(), e);
        return AjaxResult.error("任务已被其他用户签收");
    }

    /**
     * 处理乐观锁异常
     */
    @ExceptionHandler(ActivitiOptimisticLockingException.class)
    public AjaxResult handleActivitiOptimisticLockingException(ActivitiOptimisticLockingException e) {
        logger.error("乐观锁异常: {}", e.getMessage(), e);
        return AjaxResult.error("数据已被其他用户修改，请刷新后重试");
    }

    /**
     * 处理通用Activiti异常
     */
    @ExceptionHandler(ActivitiException.class)
    public AjaxResult handleActivitiException(ActivitiException e) {
        logger.error("Activiti引擎异常: {}", e.getMessage(), e);
        return AjaxResult.error("工作流引擎错误: " + e.getMessage());
    }

    /**
     * 处理其他运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public AjaxResult handleRuntimeException(RuntimeException e) {
        logger.error("运行时异常: {}", e.getMessage(), e);
        return AjaxResult.error("系统错误: " + e.getMessage());
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public AjaxResult handleException(Exception e) {
        logger.error("未知异常: {}", e.getMessage(), e);
        return AjaxResult.error("系统异常，请联系管理员");
    }
}
