<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="welcome-card">
          <div class="welcome-content">
            <h2>欢迎使用科研成果多维敏捷管控中心</h2>
            <p>基于敏捷平台的科研成果管理服务平台</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon">
              <!-- <el-icon><User /></el-icon> -->
              <span style="font-size: 40px;">👤</span>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ userCount }}</div>
              <div class="stat-label">用户总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ projectCount }}</div>
              <div class="stat-label">项目总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon">
              <el-icon><Trophy /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ achievementCount }}</div>
              <div class="stat-label">成果总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon">
              <el-icon><Bell /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ noticeCount }}</div>
              <div class="stat-label">通知公告</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>最新通知</span>
          </template>
          <el-empty v-if="notices.length === 0" description="暂无通知" />
          <div v-else>
            <div v-for="notice in notices" :key="notice.id" class="notice-item">
              <div class="notice-title">{{ notice.title }}</div>
              <div class="notice-time">{{ notice.createTime }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>待办事项</span>
          </template>
          <el-empty v-if="todos.length === 0" description="暂无待办" />
          <div v-else>
            <div v-for="todo in todos" :key="todo.id" class="todo-item">
              <div class="todo-title">{{ todo.title }}</div>
              <div class="todo-time">{{ todo.deadline }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
// import { User, Document, Trophy, Bell } from '@element-plus/icons-vue'

// 统计数据
const userCount = ref(0)
const projectCount = ref(0)
const achievementCount = ref(0)
const noticeCount = ref(0)

// 通知列表
const notices = ref([])

// 待办列表
const todos = ref([])

// 获取统计数据
const getStatistics = async () => {
  // 这里应该调用API获取真实数据
  userCount.value = 156
  projectCount.value = 89
  achievementCount.value = 234
  noticeCount.value = 12
}

// 获取通知列表
const getNotices = async () => {
  // 这里应该调用API获取真实数据
  notices.value = []
}

// 获取待办列表
const getTodos = async () => {
  // 这里应该调用API获取真实数据
  todos.value = []
}

onMounted(() => {
  getStatistics()
  getNotices()
  getTodos()
})
</script>

<style lang="scss" scoped>
.dashboard {
  padding: 20px;
}

.welcome-card {
  .welcome-content {
    text-align: center;
    padding: 20px;
    
    h2 {
      color: #409EFF;
      margin-bottom: 10px;
    }
    
    p {
      color: #666;
      font-size: 16px;
    }
  }
}

.stat-card {
  .stat-item {
    display: flex;
    align-items: center;
    padding: 10px;
    
    .stat-icon {
      font-size: 40px;
      color: #409EFF;
      margin-right: 20px;
    }
    
    .stat-content {
      .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #333;
      }
      
      .stat-label {
        color: #666;
        font-size: 14px;
      }
    }
  }
}

.notice-item, .todo-item {
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  .notice-title, .todo-title {
    font-size: 14px;
    color: #333;
    margin-bottom: 5px;
  }
  
  .notice-time, .todo-time {
    font-size: 12px;
    color: #999;
  }
}
</style>
