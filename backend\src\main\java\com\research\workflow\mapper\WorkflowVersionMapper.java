package com.research.workflow.mapper;

import com.research.workflow.domain.WorkflowVersion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作流版本管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface WorkflowVersionMapper {
    
    /**
     * 查询工作流版本
     * 
     * @param versionId 工作流版本主键
     * @return 工作流版本
     */
    WorkflowVersion selectWorkflowVersionByVersionId(String versionId);

    /**
     * 查询工作流版本列表
     * 
     * @param workflowVersion 工作流版本
     * @return 工作流版本集合
     */
    List<WorkflowVersion> selectWorkflowVersionList(WorkflowVersion workflowVersion);

    /**
     * 新增工作流版本
     * 
     * @param workflowVersion 工作流版本
     * @return 结果
     */
    int insertWorkflowVersion(WorkflowVersion workflowVersion);

    /**
     * 修改工作流版本
     * 
     * @param workflowVersion 工作流版本
     * @return 结果
     */
    int updateWorkflowVersion(WorkflowVersion workflowVersion);

    /**
     * 删除工作流版本
     * 
     * @param versionId 工作流版本主键
     * @return 结果
     */
    int deleteWorkflowVersionByVersionId(String versionId);

    /**
     * 批量删除工作流版本
     * 
     * @param versionIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteWorkflowVersionByVersionIds(String[] versionIds);

    /**
     * 根据流程定义Key查询版本列表
     * 
     * @param processDefinitionKey 流程定义Key
     * @return 版本列表
     */
    List<WorkflowVersion> selectVersionsByProcessKey(@Param("processDefinitionKey") String processDefinitionKey);

    /**
     * 根据流程定义Key获取默认版本
     * 
     * @param processDefinitionKey 流程定义Key
     * @return 默认版本
     */
    WorkflowVersion selectDefaultVersionByProcessKey(@Param("processDefinitionKey") String processDefinitionKey);

    /**
     * 根据流程定义Key获取已发布版本列表
     * 
     * @param processDefinitionKey 流程定义Key
     * @return 已发布版本列表
     */
    List<WorkflowVersion> selectPublishedVersionsByProcessKey(@Param("processDefinitionKey") String processDefinitionKey);

    /**
     * 更新版本的默认状态
     * 
     * @param processDefinitionKey 流程定义Key
     * @param versionId 版本ID
     * @param isDefault 是否默认
     * @return 结果
     */
    int updateVersionDefaultStatus(@Param("processDefinitionKey") String processDefinitionKey, 
                                 @Param("versionId") String versionId, 
                                 @Param("isDefault") Boolean isDefault);

    /**
     * 更新版本的发布状态
     * 
     * @param versionId 版本ID
     * @param publishStatus 发布状态
     * @return 结果
     */
    int updateVersionPublishStatus(@Param("versionId") String versionId, 
                                 @Param("publishStatus") Integer publishStatus);

    /**
     * 更新版本的实例统计
     * 
     * @param versionId 版本ID
     * @param runningCount 运行中实例数
     * @param completedCount 已完成实例数
     * @return 结果
     */
    int updateVersionInstanceCount(@Param("versionId") String versionId, 
                                 @Param("runningCount") Integer runningCount, 
                                 @Param("completedCount") Integer completedCount);

    /**
     * 根据发布策略查询版本列表
     * 
     * @param processDefinitionKey 流程定义Key
     * @param publishStrategy 发布策略
     * @return 版本列表
     */
    List<WorkflowVersion> selectVersionsByStrategy(@Param("processDefinitionKey") String processDefinitionKey, 
                                                 @Param("publishStrategy") Integer publishStrategy);

    /**
     * 查询版本统计信息
     * 
     * @param processDefinitionKey 流程定义Key
     * @return 统计信息
     */
    List<WorkflowVersion> selectVersionStatistics(@Param("processDefinitionKey") String processDefinitionKey);

    /**
     * 检查版本标签是否唯一
     * 
     * @param processDefinitionKey 流程定义Key
     * @param versionTag 版本标签
     * @param versionId 版本ID（排除自己）
     * @return 数量
     */
    int checkVersionTagUnique(@Param("processDefinitionKey") String processDefinitionKey, 
                            @Param("versionTag") String versionTag, 
                            @Param("versionId") String versionId);

    /**
     * 获取流程的最新版本号
     * 
     * @param processDefinitionKey 流程定义Key
     * @return 最新版本号
     */
    Integer selectMaxVersionNumber(@Param("processDefinitionKey") String processDefinitionKey);

    /**
     * 根据版本标签查询版本
     * 
     * @param processDefinitionKey 流程定义Key
     * @param versionTag 版本标签
     * @return 版本信息
     */
    WorkflowVersion selectVersionByTag(@Param("processDefinitionKey") String processDefinitionKey, 
                                     @Param("versionTag") String versionTag);

    /**
     * 查询用户可访问的版本列表
     * 
     * @param processDefinitionKey 流程定义Key
     * @param userId 用户ID
     * @param department 部门
     * @return 版本列表
     */
    List<WorkflowVersion> selectAccessibleVersions(@Param("processDefinitionKey") String processDefinitionKey, 
                                                  @Param("userId") String userId, 
                                                  @Param("department") String department);

    /**
     * 更新版本配置
     * 
     * @param versionId 版本ID
     * @param versionConfig 版本配置JSON
     * @return 结果
     */
    int updateVersionConfig(@Param("versionId") String versionId, 
                          @Param("versionConfig") String versionConfig);

    /**
     * 更新版本路由规则
     * 
     * @param versionId 版本ID
     * @param routeRules 路由规则JSON
     * @return 结果
     */
    int updateVersionRouteRules(@Param("versionId") String versionId, 
                              @Param("routeRules") String routeRules);

    /**
     * 查询版本的分支信息
     * 
     * @param parentVersionId 父版本ID
     * @return 分支版本列表
     */
    List<WorkflowVersion> selectBranchVersions(@Param("parentVersionId") String parentVersionId);

    /**
     * 更新版本的合并状态
     * 
     * @param versionId 版本ID
     * @param mergeStatus 合并状态
     * @return 结果
     */
    int updateVersionMergeStatus(@Param("versionId") String versionId, 
                               @Param("mergeStatus") Integer mergeStatus);

    /**
     * 查询需要归档的版本
     * 
     * @param days 天数
     * @return 版本列表
     */
    List<WorkflowVersion> selectVersionsForArchive(@Param("days") Integer days);

    /**
     * 批量归档版本
     * 
     * @param versionIds 版本ID列表
     * @return 结果
     */
    int batchArchiveVersions(@Param("versionIds") List<String> versionIds);
}
