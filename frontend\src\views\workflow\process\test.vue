<template>
  <div style="padding: 20px;">
    <h1>🎉 流程管理测试页面</h1>
    <p>如果你能看到这个页面，说明路由配置成功！</p>
    <p>当前时间: {{ currentTime }}</p>
    
    <el-alert 
      title="测试成功" 
      type="success" 
      description="流程管理页面路由正常工作"
      show-icon
      :closable="false"
    />
    
    <div style="margin-top: 20px;">
      <el-button type="primary" @click="showMessage">点击测试</el-button>
      <el-button type="success" @click="goBack">返回首页</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()
const currentTime = ref('')
let timer: any = null

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

const showMessage = () => {
  ElMessage.success('测试点击成功！页面交互正常。')
}

const goBack = () => {
  router.push('/')
}

onMounted(() => {
  updateTime()
  timer = setInterval(updateTime, 1000)
  console.log('流程管理测试页面已加载')
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>
