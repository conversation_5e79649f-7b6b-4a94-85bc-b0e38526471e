<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.research.system.mapper.SysNoticeMapper">

    <resultMap type="com.research.system.domain.SysNotice" id="SysNoticeResult">
        <id     property="noticeId"       column="notice_id"      />
        <result property="noticeTitle"    column="notice_title"   />
        <result property="noticeType"     column="notice_type"    />
        <result property="noticeContent"  column="notice_content" />
        <result property="status"         column="status"         />
        <result property="createBy"       column="create_by"      />
        <result property="createTime"     column="create_time"    />
        <result property="updateBy"       column="update_by"      />
        <result property="updateTime"     column="update_time"    />
        <result property="remark"         column="remark"         />
        <result property="publishBy"      column="publish_by"     />
        <result property="publishTime"    column="publish_time"   />
        <result property="readCount"      column="read_count"     />
        <result property="importance"     column="importance"     />
        <result property="isTop"          column="is_top"         />
        <result property="targetType"     column="target_type"    />
        <result property="targetUsers"    column="target_users"   />
        <result property="targetDepts"    column="target_depts"   />
        <result property="attachmentPath" column="attachment_path"/>
        <result property="validStartTime" column="valid_start_time"/>
        <result property="validEndTime"   column="valid_end_time" />
        <result property="isRead"         column="is_read"        />
    </resultMap>

    <sql id="selectNoticeVo">
        select notice_id, notice_title, notice_type, notice_content, status, create_by, create_time, update_by, update_time, remark,
               publish_by, publish_time, read_count, importance, is_top, target_type, target_users, target_depts,
               attachment_path, valid_start_time, valid_end_time
        from sys_notice
    </sql>

    <!-- 查询通知公告列表 - 暂时注释，使用MyBatis Plus标准方式 -->
    <!--
    <select id="selectNoticeList" resultMap="SysNoticeResult">
        select notice_id, notice_title, notice_type, notice_content, status, create_by, create_time, update_by, update_time, remark,
               publish_by, publish_time, read_count, importance, is_top, target_type, target_users, target_depts,
               attachment_path, valid_start_time, valid_end_time
        from sys_notice
        <where>
            <if test="notice != null">
                <if test="notice.noticeTitle != null and notice.noticeTitle != ''">
                    AND notice_title like concat('%', #{notice.noticeTitle}, '%')
                </if>
                <if test="notice.noticeType != null and notice.noticeType != ''">
                    AND notice_type = #{notice.noticeType}
                </if>
                <if test="notice.status != null and notice.status != ''">
                    AND status = #{notice.status}
                </if>
                <if test="notice.createBy != null and notice.createBy != ''">
                    AND create_by like concat('%', #{notice.createBy}, '%')
                </if>
                <if test="notice.publishBy != null and notice.publishBy != ''">
                    AND publish_by like concat('%', #{notice.publishBy}, '%')
                </if>
                <if test="notice.importance != null and notice.importance != ''">
                    AND importance = #{notice.importance}
                </if>
                <if test="notice.isTop != null and notice.isTop != ''">
                    AND is_top = #{notice.isTop}
                </if>
            </if>
        </where>
        order by is_top desc, importance desc, create_time desc
    </select>
    -->

    <!-- 查询用户可见的通知公告列表 -->
    <select id="selectUserNoticeList" parameterType="com.research.system.domain.SysNotice" resultMap="SysNoticeResult">
        select n.*, 
               case when nr.user_id is not null then '1' else '0' end as is_read
        from sys_notice n
        left join sys_notice_read nr on n.notice_id = nr.notice_id and nr.user_id = #{userId}
        <where>
            n.status = '0'
            <if test="notice.noticeTitle != null and notice.noticeTitle != ''">
                AND n.notice_title like concat('%', #{notice.noticeTitle}, '%')
            </if>
            <if test="notice.noticeType != null and notice.noticeType != ''">
                AND n.notice_type = #{notice.noticeType}
            </if>
            <if test="notice.importance != null and notice.importance != ''">
                AND n.importance = #{notice.importance}
            </if>
            AND (n.target_type = '0'
                 OR (n.target_type = '1' AND FIND_IN_SET(#{userId}, n.target_users) > 0)
                 OR (n.target_type = '2' AND FIND_IN_SET(#{deptId}, n.target_depts) > 0))
        </where>
        order by n.is_top desc, n.importance desc, n.publish_time desc
    </select>

    <!-- 查询通知公告详情 -->
    <select id="selectNoticeDetail" resultMap="SysNoticeResult">
        select n.*, 
               case when nr.user_id is not null then '1' else '0' end as is_read
        from sys_notice n
        left join sys_notice_read nr on n.notice_id = nr.notice_id and nr.user_id = #{userId}
        where n.notice_id = #{noticeId}
    </select>

    <!-- 查询最新公告列表 -->
    <select id="selectLatestNotices" resultMap="SysNoticeResult">
        select n.*, 
               case when nr.user_id is not null then '1' else '0' end as is_read
        from sys_notice n
        left join sys_notice_read nr on n.notice_id = nr.notice_id and nr.user_id = #{userId}
        where n.status = '0'
          and (n.target_type = '0'
               OR (n.target_type = '1' AND FIND_IN_SET(#{userId}, n.target_users) > 0)
               OR (n.target_type = '2' AND FIND_IN_SET(#{deptId}, n.target_depts) > 0))
        order by n.is_top desc, n.importance desc, n.publish_time desc
        limit #{limit}
    </select>

    <!-- 查询用户未读公告数量 -->
    <select id="selectUnreadNoticeCount" resultType="java.lang.Long">
        select count(*)
        from sys_notice n
        where n.status = '0'
          and n.notice_id not in (
              select nr.notice_id 
              from sys_notice_read nr 
              where nr.user_id = #{userId}
          )
          and (n.target_type = '0'
               OR (n.target_type = '1' AND FIND_IN_SET(#{userId}, n.target_users) > 0)
               OR (n.target_type = '2' AND FIND_IN_SET(#{deptId}, n.target_depts) > 0))
    </select>

    <!-- 增加公告阅读次数 -->
    <update id="incrementReadCount">
        update sys_notice 
        set read_count = read_count + 1,
            update_time = now()
        where notice_id = #{noticeId}
    </update>

    <!-- 查询公告阅读统计 -->
    <select id="selectNoticeReadStats" resultType="java.util.Map">
        select 
            u.user_name,
            u.nick_name,
            d.dept_name,
            nr.read_time
        from sys_notice_read nr
        left join sys_user u on nr.user_id = u.user_id
        left join sys_dept d on u.dept_id = d.dept_id
        where nr.notice_id = #{noticeId}
        order by nr.read_time desc
    </select>

    <!-- 全文搜索公告 -->
    <select id="searchNotices" resultMap="SysNoticeResult">
        select n.*, 
               case when nr.user_id is not null then '1' else '0' end as is_read
        from sys_notice n
        left join sys_notice_read nr on n.notice_id = nr.notice_id and nr.user_id = #{userId}
        where n.status = '0'
          and (n.notice_title like concat('%', #{keyword}, '%') 
               or n.notice_content like concat('%', #{keyword}, '%'))
          and (n.target_type = '0'
               OR (n.target_type = '1' AND FIND_IN_SET(#{userId}, n.target_users) > 0)
               OR (n.target_type = '2' AND FIND_IN_SET(#{deptId}, n.target_depts) > 0))
        order by n.is_top desc, n.importance desc, n.publish_time desc
    </select>

</mapper>
