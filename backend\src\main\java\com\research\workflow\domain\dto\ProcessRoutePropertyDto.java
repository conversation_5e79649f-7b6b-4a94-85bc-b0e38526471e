package com.research.workflow.domain.dto;

import java.io.Serializable;

/**
 * 流程路由属性DTO
 * 
 * <AUTHOR>
 */
public class ProcessRoutePropertyDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 属性名称
     */
    private String propertyName;

    /**
     * 属性值
     */
    private String propertyValue;

    /**
     * 属性类型
     */
    private String propertyType;

    /**
     * 属性描述
     */
    private String description;

    // Getters and Setters
    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }

    public String getPropertyValue() {
        return propertyValue;
    }

    public void setPropertyValue(String propertyValue) {
        this.propertyValue = propertyValue;
    }

    public String getPropertyType() {
        return propertyType;
    }

    public void setPropertyType(String propertyType) {
        this.propertyType = propertyType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
