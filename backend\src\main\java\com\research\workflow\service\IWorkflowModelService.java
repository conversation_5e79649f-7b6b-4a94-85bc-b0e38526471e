package com.research.workflow.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.research.workflow.domain.WorkflowModel;

import java.util.List;
import java.util.Map;

/**
 * 工作流模型Service接口
 * 
 * <AUTHOR>
 */
public interface IWorkflowModelService extends IService<WorkflowModel> {

    /**
     * 查询工作流模型列表
     * 
     * @param page 分页对象
     * @param model 工作流模型
     * @return 工作流模型集合
     */
    IPage<WorkflowModel> selectModelList(Page<WorkflowModel> page, WorkflowModel model);

    /**
     * 查询工作流模型详情
     * 
     * @param id 模型ID
     * @return 工作流模型
     */
    WorkflowModel selectModelById(String id);

    /**
     * 新增工作流模型
     * 
     * @param model 工作流模型
     * @return 结果
     */
    boolean insertModel(WorkflowModel model);

    /**
     * 修改工作流模型
     * 
     * @param model 工作流模型
     * @return 结果
     */
    boolean updateModel(WorkflowModel model);

    /**
     * 删除工作流模型
     * 
     * @param id 模型ID
     * @return 结果
     */
    boolean deleteModelById(String id);

    /**
     * 批量删除工作流模型
     * 
     * @param ids 模型ID列表
     * @return 结果
     */
    boolean deleteModelByIds(List<String> ids);

    /**
     * 保存模型JSON内容
     * 
     * @param id 模型ID
     * @param json JSON内容
     * @return 结果
     */
    boolean saveModelJson(String id, String json);

    /**
     * 保存模型XML内容
     * 
     * @param id 模型ID
     * @param xml XML内容
     * @return 结果
     */
    boolean saveModelXml(String id, String xml);

    /**
     * 获取模型JSON内容
     * 
     * @param id 模型ID
     * @return JSON内容
     */
    String getModelJson(String id);

    /**
     * 获取模型XML内容
     * 
     * @param id 模型ID
     * @return XML内容
     */
    String getModelXml(String id);

    /**
     * 复制模型
     * 
     * @param sourceId 源模型ID
     * @param newName 新模型名称
     * @param newKey 新模型Key
     * @return 新模型
     */
    WorkflowModel copyModel(String sourceId, String newName, String newKey);

    /**
     * 部署模型
     * 
     * @param id 模型ID
     * @return 部署结果
     */
    Map<String, Object> deployModel(String id);

    /**
     * 导入模型
     * 
     * @param name 模型名称
     * @param key 模型Key
     * @param xml XML内容
     * @return 导入结果
     */
    WorkflowModel importModel(String name, String key, String xml);

    /**
     * 导出模型
     * 
     * @param id 模型ID
     * @return XML内容
     */
    String exportModel(String id);

    /**
     * 根据模型Key查询模型
     * 
     * @param key 模型Key
     * @return 工作流模型
     */
    WorkflowModel selectModelByKey(String key);

    /**
     * 查询已部署的模型列表
     * 
     * @return 已部署的模型列表
     */
    List<WorkflowModel> selectDeployedModels();

    /**
     * 查询模型统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> selectModelStatistics();

    /**
     * 查询最近创建的模型
     * 
     * @param limit 限制数量
     * @return 模型列表
     */
    List<WorkflowModel> selectRecentModels(Integer limit);

    /**
     * 查询最近更新的模型
     * 
     * @param limit 限制数量
     * @return 模型列表
     */
    List<WorkflowModel> selectRecentUpdatedModels(Integer limit);

    /**
     * 批量更新模型状态
     * 
     * @param ids 模型ID列表
     * @param status 状态
     * @return 结果
     */
    boolean batchUpdateStatus(List<String> ids, String status);

    /**
     * 清理未部署的草稿模型
     * 
     * @param days 天数
     * @return 清理数量
     */
    int cleanDraftModels(Integer days);

    /**
     * 验证模型Key是否唯一
     * 
     * @param key 模型Key
     * @param excludeId 排除的模型ID
     * @return 是否唯一
     */
    boolean checkModelKeyUnique(String key, String excludeId);
}
