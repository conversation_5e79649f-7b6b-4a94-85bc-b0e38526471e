import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/layout/index.vue'

// 公共路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/redirect',
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    meta: { hidden: true }
  },
  {
    path: '/404',
    component: () => import('@/views/error/404.vue'),
    meta: { hidden: true }
  },
  {
    path: '/401',
    component: () => import('@/views/error/401.vue'),
    meta: { hidden: true }
  },
  {
    path: '/',
    redirect: '/workspace'
  },
  {
    path: '/workspace',
    component: Layout,
    children: [
      {
        path: '',
        component: () => import('@/views/workspace/index.vue'),
        name: 'Workspace',
        meta: { title: '个人工作台', icon: 'monitor', affix: true }
      }
    ]
  },
  {
    path: '/dashboard',
    component: Layout,
    children: [
      {
        path: '',
        component: () => import('@/views/dashboard/index.vue'),
        name: 'Dashboard',
        meta: { title: '系统首页', icon: 'dashboard' }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/user',
    name: 'System',
    meta: { title: '系统管理', icon: 'system' },
    children: [
      {
        path: 'user',
        component: () => import('@/views/system/user/index.vue'),
        name: 'User',
        meta: { title: '用户管理', icon: 'user' }
      },
      {
        path: 'role',
        component: () => import('@/views/system/role/index.vue'),
        name: 'Role',
        meta: { title: '角色管理', icon: 'peoples' }
      },
      {
        path: 'menu',
        component: () => import('@/views/system/menu/index.vue'),
        name: 'Menu',
        meta: { title: '菜单管理', icon: 'tree-table' }
      }
    ]
  },

  {
    path: '/workflow',
    component: Layout,
    redirect: '/workflow/designer',
    name: 'Workflow',
    meta: { title: '工作流管理', icon: 'workflow' },
    children: [
      {
        path: 'designer',
        component: () => import('@/views/workflow/designer/index.vue'),
        name: 'ProcessDesigner',
        meta: { title: '流程设计器', icon: 'build' }
      },
      {
        path: 'process',
        component: () => import('@/views/workflow/process/index.vue'),
        name: 'ProcessManagement',
        meta: { title: '流程管理', icon: 'tree-table' }
      },
      {
        path: 'task',
        component: () => import('@/views/workflow/task/index.vue'),
        name: 'Task',
        meta: { title: '我的任务', icon: 'skill' }
      },
      {
        path: 'version',
        component: () => import('@/views/workflow/version/index.vue'),
        name: 'ProcessVersion',
        meta: { title: '版本管理', icon: 'collection-tag' }
      },
      {
        path: 'monitor',
        component: () => import('@/views/workflow/monitor/dashboard.vue'),
        name: 'WorkflowMonitor',
        meta: { title: '监控大屏', icon: 'monitor' }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    meta: { hidden: true },
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index.vue'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  }
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes: RouteRecordRaw[] = [
  {
    path: '/project',
    component: Layout,
    redirect: '/project/vertical',
    name: 'Project',
    meta: { title: '项目管理', icon: 'project' },
    children: [
      {
        path: 'vertical',
        component: () => import('@/views/project/vertical/index.vue'),
        name: 'VerticalProject',
        meta: { title: '纵向项目', icon: 'vertical-project' }
      },
      {
        path: 'horizontal',
        component: () => import('@/views/project/horizontal/index.vue'),
        name: 'HorizontalProject',
        meta: { title: '横向项目', icon: 'horizontal-project' }
      },
      {
        path: 'contract',
        component: () => import('@/views/project/contract/index.vue'),
        name: 'Contract',
        meta: { title: '合同管理', icon: 'contract' }
      },
      {
        path: 'partner',
        component: () => import('@/views/project/partner/index.vue'),
        name: 'Partner',
        meta: { title: '合作单位', icon: 'partner' }
      }
    ]
  },
  {
    path: '/system/notice-detail',
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: ':id(\\d+)',
        component: () => import('@/views/system/notice/detail.vue'),
        name: 'NoticeDetail',
        meta: { title: '公告详情', activeMenu: '/system/notice' }
      }
    ]
  },
  {
    path: '/system/todo-detail',
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: ':id(\\d+)',
        component: () => import('@/views/system/todo/detail.vue'),
        name: 'TodoDetail',
        meta: { title: '待办详情', activeMenu: '/system/todo' }
      }
    ]
  },
  {
    path: '/system/message-detail',
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: ':id(\\d+)',
        component: () => import('@/views/system/message/detail.vue'),
        name: 'MessageDetail',
        meta: { title: '消息详情', activeMenu: '/system/message' }
      }
    ]
  },
  {
    path: '/system/user-auth-role',
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: ':userId(\\d+)',
        component: () => import('@/views/system/user/authRole.vue'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/role-auth-user',
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: ':roleId(\\d+)',
        component: () => import('@/views/system/role/authUser.vue'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

export default router
