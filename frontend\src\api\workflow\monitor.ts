import request from '@/utils/request'

// 获取工作流总览数据
export function getWorkflowOverview() {
  return request({
    url: '/workflow/monitor/overview',
    method: 'get'
  })
}

// 获取版本分布统计
export function getVersionDistribution() {
  return request({
    url: '/workflow/monitor/version/distribution',
    method: 'get'
  })
}

// 获取实时流程活动数据
export function getRealTimeActivity() {
  return request({
    url: '/workflow/monitor/activity/realtime',
    method: 'get'
  })
}

// 获取流程性能统计
export function getProcessPerformance() {
  return request({
    url: '/workflow/monitor/performance',
    method: 'get'
  })
}

// 获取任务分布统计
export function getTaskDistribution() {
  return request({
    url: '/workflow/monitor/task/distribution',
    method: 'get'
  })
}

// 获取历史趋势数据
export function getHistoricalTrends(days: number = 7) {
  return request({
    url: '/workflow/monitor/trends',
    method: 'get',
    params: { days }
  })
}

// 获取版本路由统计
export function getVersionRouteStats() {
  return request({
    url: '/workflow/monitor/route/stats',
    method: 'get'
  })
}

// 获取系统健康状态
export function getSystemHealth() {
  return request({
    url: '/workflow/monitor/health',
    method: 'get'
  })
}

// 获取监控大屏数据（综合接口）
export function getDashboardData() {
  return request({
    url: '/workflow/monitor/dashboard',
    method: 'get'
  })
}

// 获取实时指标（用于定时刷新）
export function getRealTimeMetrics() {
  return request({
    url: '/workflow/monitor/realtime',
    method: 'get'
  })
}
