<template>
  <div class="app-wrapper">
    <sidebar class="sidebar-container" />
    <div class="main-container">
      <navbar />
      <dynamic-content ref="dynamicContentRef" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, provide, nextTick, onMounted } from 'vue'
import { Navbar, Sidebar } from './components'
import DynamicContent from '@/components/DynamicContent/index.vue'

// 动态内容组件引用
const dynamicContentRef = ref()

// 提供给子组件使用 - 提供组件实例而不是ref
provide('dynamicContent', {
  loadPageByPath: (path: string, menuName: string, parentName?: string) => {
    nextTick(() => {
      if (dynamicContentRef.value) {
        dynamicContentRef.value.loadPageByPath(path, menuName, parentName)
      }
    })
  },
  loadPage: (pageInfo: any) => {
    nextTick(() => {
      if (dynamicContentRef.value) {
        dynamicContentRef.value.loadPage(pageInfo)
      }
    })
  },
  loadDefaultPage: () => {
    nextTick(() => {
      if (dynamicContentRef.value) {
        dynamicContentRef.value.loadDefaultPage()
      }
    })
  }
})

// 组件挂载后确保加载个人工作台
onMounted(() => {
  nextTick(() => {
    if (dynamicContentRef.value) {
      // 确保个人工作台已加载（因为已经设置为默认值，这里只是保险）
      console.log('Layout mounted, 个人工作台已设为默认页面')
    }
  })
})
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100vh;
  width: 100%;
  display: flex;
}

.sidebar-container {
  width: 210px;
  height: 100vh;
  background: #ffffff;
  flex-shrink: 0;
  border-right: 1px solid #e4e7ed;
  box-shadow: 2px 0 12px rgba(0, 0, 0, 0.08);
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}
</style>
