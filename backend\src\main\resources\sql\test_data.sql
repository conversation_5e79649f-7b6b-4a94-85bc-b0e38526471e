-- 横向项目管理测试数据
-- 用于测试横向项目管理功能

USE research_db;

-- 清空现有数据
DELETE FROM horizontal_project_member WHERE project_id > 0;
DELETE FROM partner_evaluation WHERE partner_id > 0;
DELETE FROM partner_qualification WHERE partner_id > 0;
DELETE FROM contract_change WHERE contract_id > 0;
DELETE FROM horizontal_project WHERE id > 0;
DELETE FROM contract WHERE id > 0;
DELETE FROM partner WHERE id > 0;
DELETE FROM contract_template WHERE id > 0;

-- 重置自增ID
ALTER TABLE horizontal_project AUTO_INCREMENT = 1;
ALTER TABLE contract AUTO_INCREMENT = 1;
ALTER TABLE partner AUTO_INCREMENT = 1;
ALTER TABLE contract_template AUTO_INCREMENT = 1;
ALTER TABLE partner_qualification AUTO_INCREMENT = 1;
ALTER TABLE partner_evaluation AUTO_INCREMENT = 1;
ALTER TABLE horizontal_project_member AUTO_INCREMENT = 1;
ALTER TABLE contract_change AUTO_INCREMENT = 1;

-- ===========================================
-- 插入合作单位测试数据
-- ===========================================

INSERT INTO partner (partner_code, partner_name, partner_type, legal_representative, contact_person, contact_phone, contact_email, address, postal_code, website, business_scope, registration_capital, establishment_date, credit_code, tax_number, bank_name, bank_account, status, cooperation_level, cooperation_count, total_contract_amount, last_cooperation_date, create_by, create_time, update_by, update_time, remark) VALUES
('P001', '华为技术有限公司', '企业', '任正非', '张三', '***********', '<EMAIL>', '深圳市龙岗区坂田华为基地', '518129', 'https://www.huawei.com', '通信设备研发、生产、销售', **********.00, '1987-09-15', '91440300279174282F', '***************', '中国工商银行深圳分行', '*******************', 1, 'A', 5, ********.00, '2024-06-15', 'admin', NOW(), 'admin', NOW(), '全球领先的ICT解决方案供应商'),
('P002', '清华大学', '高校', '邱勇', '李四', '***********', '<EMAIL>', '北京市海淀区清华园1号', '100084', 'https://www.tsinghua.edu.cn', '高等教育、科学研究', 0.00, '1911-04-29', '12100000400000624X', '***************', '中国银行北京分行', '1234567890123456789', 1, 'A', 8, 8600000.00, '2024-07-10', 'admin', NOW(), 'admin', NOW(), '中国著名高等学府'),
('P003', '中科院计算技术研究所', '科研院所', '孙凝晖', '王五', '13800138003', '<EMAIL>', '北京市海淀区科学院南路6号', '100190', 'http://www.ict.ac.cn', '计算机科学技术研究', 0.00, '1956-06-01', '12100000400001234A', '110108400001234', '中国建设银行北京分行', '9876543210987654321', 1, 'A', 6, 7800000.00, '2024-05-20', 'admin', NOW(), 'admin', NOW(), '国家级计算技术研究机构'),
('P004', '腾讯科技（深圳）有限公司', '企业', '马化腾', '赵六', '13800138004', '<EMAIL>', '深圳市南山区科技中一路腾讯大厦', '518057', 'https://www.tencent.com', '互联网增值服务、移动及电信增值服务', 3300000000.00, '1998-11-11', '91440300708461136T', '440300708461136', '招商银行深圳分行', '1357924680135792468', 1, 'B', 3, 4500000.00, '2024-04-18', 'admin', NOW(), 'admin', NOW(), '中国领先的互联网增值服务提供商'),
('P005', '北京理工大学', '高校', '张军', '钱七', '13800138005', '<EMAIL>', '北京市海淀区中关村南大街5号', '100081', 'https://www.bit.edu.cn', '高等教育、国防科技研究', 0.00, '1940-01-01', '12100000400002468B', '110108400002468', '中国农业银行北京分行', '2468135792468135792', 1, 'B', 4, 3200000.00, '2024-03-25', 'admin', NOW(), 'admin', NOW(), '理工为主的全国重点大学'),
('P006', '阿里巴巴（中国）有限公司', '企业', '张勇', '孙八', '13800138006', '<EMAIL>', '杭州市余杭区文一西路969号', '311121', 'https://www.alibaba.com', '电子商务、云计算服务', 12000000000.00, '1999-09-09', '91330000MA27XL1J2Y', '330000MA27XL1J2', '中国工商银行杭州分行', '3691472583691472583', 1, 'A', 2, 6800000.00, '2024-02-14', 'admin', NOW(), 'admin', NOW(), '全球最大的B2B电子商务公司'),
('P007', '上海交通大学', '高校', '林忠钦', '周九', '13800138007', '<EMAIL>', '上海市闵行区东川路800号', '200240', 'https://www.sjtu.edu.cn', '高等教育、科学研究', 0.00, '1896-04-08', '12310000400003691C', '310108400003691', '交通银行上海分行', '1472583691472583691', 1, 'B', 3, 2800000.00, '2024-01-30', 'admin', NOW(), 'admin', NOW(), '中国历史最悠久的高等学府之一'),
('P008', '百度在线网络技术（北京）有限公司', '企业', '李彦宏', '吴十', '13800138008', '<EMAIL>', '北京市海淀区上地十街10号百度大厦', '100085', 'https://www.baidu.com', '互联网信息服务、人工智能技术', 16000000000.00, '2000-01-18', '91110000802100433B', '110000802100433', '中国银行北京分行', '5836914725836914725', 1, 'B', 4, 5200000.00, '2024-07-05', 'admin', NOW(), 'admin', NOW(), '全球最大的中文搜索引擎'),
('P009', '西安电子科技大学', '高校', '杨宗凯', '郑十一', '13800138009', '<EMAIL>', '陕西省西安市太白南路2号', '710071', 'https://www.xidian.edu.cn', '高等教育、电子信息技术研究', 0.00, '1931-09-01', '12610000400004825D', '610108400004825', '中国工商银行西安分行', '7258369147258369147', 1, 'C', 2, 1800000.00, '2023-12-20', 'admin', NOW(), 'admin', NOW(), '电子信息领域特色鲜明的全国重点大学'),
('P010', '深圳市政府', '政府机构', '覃伟中', '陈十二', '13800138010', '<EMAIL>', '深圳市福田区市民中心C区', '518035', 'http://www.sz.gov.cn', '政府管理、公共服务', 0.00, '1980-08-26', '11440300MB2D16302E', '440300MB2D16302', '中国人民银行深圳分行', '9147258369147258369', 1, 'C', 1, 2500000.00, '2023-11-15', 'admin', NOW(), 'admin', NOW(), '深圳市人民政府');

-- ===========================================
-- 插入合同模板测试数据
-- ===========================================

INSERT INTO contract_template (template_name, template_type, template_category, template_content, version, status, usage_count, create_by, create_time, update_by, update_time, remark) VALUES
('技术开发合同模板', '技术开发', '标准模板', '技术开发合同标准条款...', '1.0', 1, 15, 'admin', NOW(), 'admin', NOW(), '适用于技术开发类项目'),
('技术服务合同模板', '技术服务', '标准模板', '技术服务合同标准条款...', '1.0', 1, 12, 'admin', NOW(), 'admin', NOW(), '适用于技术服务类项目'),
('技术咨询合同模板', '技术咨询', '标准模板', '技术咨询合同标准条款...', '1.0', 1, 8, 'admin', NOW(), 'admin', NOW(), '适用于技术咨询类项目'),
('技术转让合同模板', '技术转让', '标准模板', '技术转让合同标准条款...', '1.0', 1, 5, 'admin', NOW(), 'admin', NOW(), '适用于技术转让类项目');

-- ===========================================
-- 插入合同测试数据
-- ===========================================

INSERT INTO contract (contract_no, contract_name, contract_type, contract_template_id, partner_id, partner_name, contract_amount, signing_date, start_date, end_date, status, principal_id, principal_name, dept_id, dept_name, contract_content, payment_terms, delivery_terms, process_instance_id, current_node, approval_status, create_by, create_time, update_by, update_time, remark) VALUES
('HT2024001', '智能制造系统开发合同', '技术开发', 1, 1, '华为技术有限公司', 2500000.00, '2024-01-15', '2024-01-15', '2024-12-31', 3, 1001, '张教授', 101, '计算机学院', '开发智能制造管理系统，包括生产计划、质量控制、设备监控等模块', '分三期付款：签约30%，中期验收40%，最终验收30%', '分阶段交付：需求分析、系统设计、开发实现、测试部署', 'process_001', '执行阶段', '已批准', 'admin', NOW(), 'admin', NOW(), '重点项目'),
('HT2024002', '大数据分析平台建设', '技术服务', 2, 2, '清华大学', 1800000.00, '2024-02-20', '2024-02-20', '2024-11-30', 2, 1002, '李教授', 102, '软件学院', '构建企业级大数据分析平台，提供数据采集、存储、分析、可视化服务', '分两期付款：签约50%，验收50%', '一次性交付完整平台及相关文档', 'process_002', '已签署', '已批准', 'admin', NOW(), 'admin', NOW(), '合作项目'),
('HT2024003', '人工智能算法优化咨询', '技术咨询', 3, 3, '中科院计算技术研究所', 800000.00, '2024-03-10', '2024-03-10', '2024-09-10', 4, 1003, '王教授', 103, '人工智能学院', '针对现有AI算法进行性能优化，提供技术咨询和改进方案', '按月付款，每月支付总额的1/6', '每月提交咨询报告和优化建议', 'process_003', '已完成', '已批准', 'admin', NOW(), 'admin', NOW(), '咨询服务'),
('HT2024004', '移动应用开发项目', '技术开发', 1, 4, '腾讯科技（深圳）有限公司', 1200000.00, '2024-04-05', '2024-04-05', '2024-10-05', 3, 1004, '赵教授', 104, '移动计算实验室', '开发跨平台移动应用，支持iOS和Android系统', '分三期付款：30%、40%、30%', '分阶段交付：原型、测试版、正式版', 'process_004', '执行阶段', '已批准', 'admin', NOW(), 'admin', NOW(), '移动开发'),
('HT2024005', '网络安全评估服务', '技术服务', 2, 5, '北京理工大学', 600000.00, '2024-05-12', '2024-05-12', '2024-08-12', 1, 1005, '钱教授', 105, '网络安全学院', '对企业网络系统进行全面安全评估和漏洞检测', '签约时支付50%，服务完成后支付50%', '提交安全评估报告和整改建议', 'process_005', '审核中', '审核中', 'admin', NOW(), 'admin', NOW(), '安全评估'),
('HT2024006', '云计算平台技术转让', '技术转让', 4, 6, '阿里巴巴（中国）有限公司', 3500000.00, '2024-06-01', '2024-06-01', '2024-12-01', 2, 1006, '孙教授', 106, '云计算研究中心', '转让自主研发的云计算平台核心技术', '一次性付款', '技术资料、源代码、技术培训一次性交付', 'process_006', '已签署', '已批准', 'admin', NOW(), 'admin', NOW(), '技术转让'),
('HT2024007', '物联网系统集成', '技术开发', 1, 7, '上海交通大学', 950000.00, '2024-07-08', '2024-07-08', '2025-01-08', 0, 1007, '周教授', 107, '物联网工程系', '设计和实现智能楼宇物联网管理系统', '分期付款：签约25%，中期50%，验收25%', '分模块交付：传感器网络、数据平台、应用系统', 'process_007', '草稿', '待提交', 'admin', NOW(), 'admin', NOW(), '物联网项目'),
('HT2024008', '区块链技术研发', '技术开发', 1, 8, '百度在线网络技术（北京）有限公司', 2200000.00, '2024-08-15', '2024-08-15', '2025-02-15', 1, 1008, '吴教授', 108, '区块链实验室', '研发企业级区块链解决方案', '分三期付款：40%、35%、25%', '分阶段交付：架构设计、核心开发、测试部署', 'process_008', '审核中', '审核中', 'admin', NOW(), 'admin', NOW(), '区块链研发');

-- ===========================================
-- 插入横向项目测试数据
-- ===========================================

INSERT INTO horizontal_project (project_no, project_name, project_type, contract_id, partner_id, status, start_date, end_date, total_fund, received_fund, principal_id, principal_name, dept_id, dept_name, project_summary, research_content, expected_results, keywords, subject_category, process_instance_id, current_node, approval_status, create_by, create_time, update_by, update_time, remark) VALUES
('HX2024001', '智能制造系统开发项目', '技术开发', 1, 1, 2, '2024-01-15', '2024-12-31', 2500000.00, 1500000.00, 1001, '张教授', 101, '计算机学院', '开发面向制造业的智能管理系统，提升生产效率和质量控制水平', '研究智能制造关键技术，开发生产计划优化算法，设计质量控制模型，构建设备监控平台', '完成智能制造系统V1.0，申请发明专利2-3项，发表高水平论文3-5篇', '智能制造;生产优化;质量控制;设备监控', '计算机科学与技术', 'process_001', '执行阶段', '已批准', 'admin', NOW(), 'admin', NOW(), '与华为合作的重点项目'),
('HX2024002', '大数据分析平台建设项目', '技术服务', 2, 2, 2, '2024-02-20', '2024-11-30', 1800000.00, 900000.00, 1002, '李教授', 102, '软件学院', '构建企业级大数据分析平台，为企业决策提供数据支撑', '设计分布式数据存储架构，开发实时数据处理引擎，构建可视化分析界面', '交付完整的大数据分析平台，培训技术人员，建立运维体系', '大数据;数据分析;可视化;分布式计算', '软件工程', 'process_002', '执行阶段', '已批准', 'admin', NOW(), 'admin', NOW(), '与清华大学联合开发'),
('HX2024003', '人工智能算法优化项目', '技术咨询', 3, 3, 5, '2024-03-10', '2024-09-10', 800000.00, 800000.00, 1003, '王教授', 103, '人工智能学院', '对现有AI算法进行深度优化，提升算法性能和效率', '分析现有算法瓶颈，设计优化策略，实现算法改进，验证优化效果', '算法性能提升30%以上，形成优化技术报告，提供持续技术支持', '人工智能;算法优化;性能提升;深度学习', '计算机科学与技术', 'process_003', '已结项', '已批准', 'admin', NOW(), 'admin', NOW(), '已成功完成的咨询项目'),
('HX2024004', '移动应用开发项目', '技术开发', 4, 4, 2, '2024-04-05', '2024-10-05', 1200000.00, 600000.00, 1004, '赵教授', 104, '移动计算实验室', '开发跨平台移动应用，支持多种业务场景', '设计移动应用架构，开发核心功能模块，实现跨平台兼容，优化用户体验', '发布iOS和Android应用，用户量达到10万+，应用商店评分4.5+', '移动开发;跨平台;用户体验;移动互联网', '软件工程', 'process_004', '执行阶段', '已批准', 'admin', NOW(), 'admin', NOW(), '与腾讯合作开发'),
('HX2024005', '网络安全评估项目', '技术服务', 5, 5, 1, '2024-05-12', '2024-08-12', 600000.00, 0.00, 1005, '钱教授', 105, '网络安全学院', '为企业提供全面的网络安全评估和防护建议', '网络架构安全分析，漏洞扫描与检测，安全策略制定，应急响应预案', '提交详细安全评估报告，制定安全加固方案，培训安全管理人员', '网络安全;漏洞检测;安全评估;风险管理', '网络空间安全', 'process_005', '审核中', '审核中', 'admin', NOW(), 'admin', NOW(), '网络安全评估服务'),
('HX2024006', '云计算平台技术转让项目', '技术转让', 6, 6, 2, '2024-06-01', '2024-12-01', 3500000.00, 1750000.00, 1006, '孙教授', 106, '云计算研究中心', '转让自主研发的云计算平台核心技术给阿里巴巴', '云计算架构设计，虚拟化技术实现，资源调度算法，服务管理平台', '完成技术转让，提供技术培训，建立长期合作关系', '云计算;虚拟化;资源调度;技术转让', '计算机科学与技术', 'process_006', '执行阶段', '已批准', 'admin', NOW(), 'admin', NOW(), '重要的技术转让项目'),
('HX2024007', '物联网系统集成项目', '技术开发', 7, 7, 0, '2024-07-08', '2025-01-08', 950000.00, 0.00, 1007, '周教授', 107, '物联网工程系', '设计和实现智能楼宇物联网管理系统', '物联网架构设计，传感器网络部署，数据采集与处理，智能控制算法', '建成智能楼宇管理系统，实现设备自动化控制，节能效果显著', '物联网;智能楼宇;传感器网络;自动化控制', '物联网工程', 'process_007', '申请中', '待审核', 'admin', NOW(), 'admin', NOW(), '智能楼宇项目'),
('HX2024008', '区块链技术研发项目', '技术开发', 8, 8, 1, '2024-08-15', '2025-02-15', 2200000.00, 0.00, 1008, '吴教授', 108, '区块链实验室', '研发企业级区块链解决方案', '区块链架构设计，共识算法优化，智能合约开发，安全机制设计', '开发完整的区块链平台，支持企业级应用，申请相关专利', '区块链;共识算法;智能合约;企业应用', '计算机科学与技术', 'process_008', '立项', '已批准', 'admin', NOW(), 'admin', NOW(), '前沿技术研发项目');

-- ===========================================
-- 插入项目成员测试数据
-- ===========================================

INSERT INTO horizontal_project_member (project_id, user_id, user_name, user_no, dept_id, dept_name, member_role, member_role_name, workload, position, specialty, phone, email, responsibility, join_date, status, create_by, create_time, update_by, update_time) VALUES
(1, 1001, '张教授', 'T001', 101, '计算机学院', 0, '项目负责人', 12.0, '教授', '智能制造、系统工程', '13901234567', '<EMAIL>', '项目总体规划和技术指导', '2024-01-15', 0, 'admin', NOW(), 'admin', NOW()),
(1, 1011, '刘博士', 'T011', 101, '计算机学院', 1, '主要参与人', 10.0, '副教授', '算法设计、系统开发', '13901234568', '<EMAIL>', '核心算法设计和系统架构', '2024-01-15', 0, 'admin', NOW(), 'admin', NOW()),
(1, 2001, '陈硕士', 'S001', 101, '计算机学院', 2, '一般参与人', 8.0, '讲师', '软件开发、测试', '13901234569', '<EMAIL>', '系统开发和功能测试', '2024-01-20', 0, 'admin', NOW(), 'admin', NOW()),
(2, 1002, '李教授', 'T002', 102, '软件学院', 0, '项目负责人', 10.0, '教授', '大数据、数据挖掘', '13901234570', '<EMAIL>', '项目管理和技术指导', '2024-02-20', 0, 'admin', NOW(), 'admin', NOW()),
(2, 1012, '王博士', 'T012', 102, '软件学院', 1, '主要参与人', 8.0, '副教授', '数据分析、机器学习', '13901234571', '<EMAIL>', '数据分析算法设计', '2024-02-20', 0, 'admin', NOW(), 'admin', NOW()),
(3, 1003, '王教授', 'T003', 103, '人工智能学院', 0, '项目负责人', 6.0, '教授', '人工智能、深度学习', '13901234572', '<EMAIL>', '算法优化和技术咨询', '2024-03-10', 0, 'admin', NOW(), 'admin', NOW()),
(4, 1004, '赵教授', 'T004', 104, '移动计算实验室', 0, '项目负责人', 8.0, '教授', '移动计算、应用开发', '13901234573', '<EMAIL>', '移动应用架构设计', '2024-04-05', 0, 'admin', NOW(), 'admin', NOW()),
(5, 1005, '钱教授', 'T005', 105, '网络安全学院', 0, '项目负责人', 4.0, '教授', '网络安全、信息安全', '13901234574', '<EMAIL>', '安全评估和方案制定', '2024-05-12', 0, 'admin', NOW(), 'admin', NOW());

-- ===========================================
-- 插入合作单位资质测试数据
-- ===========================================

INSERT INTO partner_qualification (partner_id, qualification_name, qualification_type, qualification_level, issuing_authority, certificate_no, issue_date, expiry_date, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, 'ISO9001质量管理体系认证', '质量认证', '国际标准', '中国质量认证中心', 'CQC9001-2024-001', '2024-01-01', '2027-01-01', 1, 'admin', NOW(), 'admin', NOW(), '质量管理体系认证'),
(1, '高新技术企业证书', '企业资质', '国家级', '科技部', 'GR202444030001', '2024-01-01', '2027-01-01', 1, 'admin', NOW(), 'admin', NOW(), '高新技术企业认定'),
(2, '教育部直属高校资质', '教育资质', '部属高校', '教育部', 'EDU-THU-001', '1952-01-01', '2099-12-31', 1, 'admin', NOW(), 'admin', NOW(), '教育部直属重点大学'),
(3, '中科院科研院所资质', '科研资质', '国家级', '中国科学院', 'CAS-ICT-001', '1956-06-01', '2099-12-31', 1, 'admin', NOW(), 'admin', NOW(), '中科院直属科研院所'),
(4, 'CMMI5级软件能力成熟度认证', '软件认证', '最高级', '软件工程协会', 'CMMI5-2024-001', '2024-01-01', '2027-01-01', 1, 'admin', NOW(), 'admin', NOW(), '软件开发能力认证'),
(5, '国防科工委保密资质', '保密资质', '机密级', '国防科工委', 'COSTIND-2024-001', '2024-01-01', '2026-01-01', 2, 'admin', NOW(), 'admin', NOW(), '即将到期的保密资质');

-- ===========================================
-- 插入合作单位评价测试数据
-- ===========================================

INSERT INTO partner_evaluation (partner_id, project_id, contract_id, evaluation_date, evaluator_id, evaluator_name, cooperation_quality_score, delivery_quality_score, service_attitude_score, communication_score, overall_score, evaluation_content, improvement_suggestions, create_by, create_time, update_by, update_time) VALUES
(1, 1, 1, '2024-06-30', 1001, '张教授', 9, 8, 9, 8, 8.5, '华为在项目合作中表现优秀，技术实力强，项目管理规范，按时交付高质量成果。', '建议在需求变更管理方面进一步优化流程。', 'admin', NOW(), 'admin', NOW()),
(2, 2, 2, '2024-07-15', 1002, '李教授', 9, 9, 8, 9, 8.8, '清华大学在大数据平台建设中展现了深厚的技术底蕴，团队协作能力强，交付质量高。', '希望在项目进度沟通方面更加及时。', 'admin', NOW(), 'admin', NOW()),
(3, 3, 3, '2024-09-10', 1003, '王教授', 10, 9, 9, 9, 9.3, '中科院计算所的算法优化咨询服务非常专业，提供的解决方案切实有效，合作愉快。', '无特别建议，希望继续保持高水平服务。', 'admin', NOW(), 'admin', NOW()),
(4, 4, 4, '2024-08-20', 1004, '赵教授', 8, 8, 9, 8, 8.3, '腾讯在移动应用开发方面经验丰富，技术团队专业，但在某些细节处理上还有提升空间。', '建议在用户体验设计方面投入更多精力。', 'admin', NOW(), 'admin', NOW());

-- ===========================================
-- 插入合同变更记录测试数据
-- ===========================================

INSERT INTO contract_change (contract_id, change_type, change_reason, change_content, change_before, change_after, status, apply_time, approve_time, approver_id, approver_name, create_by, create_time, update_by, update_time, remark) VALUES
(1, '金额变更', '需求增加导致工作量增加', '由于客户需求变更，增加了新的功能模块，合同金额需要相应调整', '原合同金额：2000000.00元', '调整后金额：2500000.00元', 'approved', '2024-03-15 10:00:00', '2024-03-20 14:30:00', 2001, '院长', 'admin', NOW(), 'admin', NOW(), '已批准的金额变更'),
(2, '时间变更', '技术难度超出预期', '由于技术实现复杂度高于预期，需要延长项目周期', '原结束时间：2024-10-30', '调整后结束时间：2024-11-30', 'approved', '2024-05-10 09:00:00', '2024-05-15 16:00:00', 2002, '副院长', 'admin', NOW(), 'admin', NOW(), '已批准的时间延期'),
(4, '内容变更', '技术路线调整', '根据最新技术发展趋势，调整部分技术实现方案', '原技术方案：基于React Native', '新技术方案：基于Flutter', 'pending', '2024-07-01 11:00:00', NULL, NULL, NULL, 'admin', NOW(), 'admin', NOW(), '待审核的技术方案变更');

COMMIT;

-- 查询验证数据
SELECT '合作单位数据' as table_name, COUNT(*) as count FROM partner
UNION ALL
SELECT '合同数据', COUNT(*) FROM contract
UNION ALL
SELECT '横向项目数据', COUNT(*) FROM horizontal_project
UNION ALL
SELECT '项目成员数据', COUNT(*) FROM horizontal_project_member
UNION ALL
SELECT '合作单位资质数据', COUNT(*) FROM partner_qualification
UNION ALL
SELECT '合作单位评价数据', COUNT(*) FROM partner_evaluation
UNION ALL
SELECT '合同变更记录数据', COUNT(*) FROM contract_change;
