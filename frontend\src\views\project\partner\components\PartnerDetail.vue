<template>
  <el-dialog
    title="合作单位详情"
    v-model="visible"
    width="1200px"
    append-to-body
    @close="handleClose"
  >
    <div v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="单位编码">
          {{ partnerInfo.partnerCode }}
        </el-descriptions-item>
        <el-descriptions-item label="单位名称">
          {{ partnerInfo.partnerName }}
        </el-descriptions-item>
        <el-descriptions-item label="单位类型">
          {{ partnerInfo.partnerType }}
        </el-descriptions-item>
        <el-descriptions-item label="合作等级">
          <el-tag :type="getLevelType(partnerInfo.cooperationLevel)">
            {{ partnerInfo.cooperationLevel }}级
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="法定代表人">
          {{ partnerInfo.legalRepresentative }}
        </el-descriptions-item>
        <el-descriptions-item label="统一社会信用代码">
          {{ partnerInfo.creditCode }}
        </el-descriptions-item>
        <el-descriptions-item label="联系人">
          {{ partnerInfo.contactPerson }}
        </el-descriptions-item>
        <el-descriptions-item label="联系电话">
          {{ partnerInfo.contactPhone }}
        </el-descriptions-item>
        <el-descriptions-item label="联系邮箱">
          {{ partnerInfo.contactEmail }}
        </el-descriptions-item>
        <el-descriptions-item label="单位状态">
          <el-tag :type="getStatusType(partnerInfo.status)">
            {{ partnerInfo.statusName }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="注册资本">
          <span class="money">{{ formatMoney(partnerInfo.registrationCapital) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="成立日期">
          {{ parseTime(partnerInfo.establishmentDate, '{y}-{m}-{d}') }}
        </el-descriptions-item>
        <el-descriptions-item label="开户银行">
          {{ partnerInfo.bankName }}
        </el-descriptions-item>
        <el-descriptions-item label="银行账号">
          {{ partnerInfo.bankAccount }}
        </el-descriptions-item>
        <el-descriptions-item label="邮政编码">
          {{ partnerInfo.postalCode }}
        </el-descriptions-item>
        <el-descriptions-item label="官方网站">
          <el-link :href="partnerInfo.website" target="_blank" type="primary">
            {{ partnerInfo.website }}
          </el-link>
        </el-descriptions-item>
        <el-descriptions-item label="单位地址" :span="2">
          {{ partnerInfo.address }}
        </el-descriptions-item>
        <el-descriptions-item label="经营范围" :span="2">
          <div class="text-content">{{ partnerInfo.businessScope }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ parseTime(partnerInfo.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ parseTime(partnerInfo.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="创建人">
          {{ partnerInfo.createBy }}
        </el-descriptions-item>
        <el-descriptions-item label="更新人">
          {{ partnerInfo.updateBy }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2" v-if="partnerInfo.remark">
          <div class="text-content">{{ partnerInfo.remark }}</div>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 合作统计 -->
      <el-divider content-position="left">合作统计</el-divider>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ partnerInfo.cooperationCount || 0 }}</div>
              <div class="stat-label">合作次数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ formatMoney(partnerInfo.totalContractAmount) }}</div>
              <div class="stat-label">累计金额(万元)</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ getAverageAmount() }}</div>
              <div class="stat-label">平均金额(万元)</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ getCooperationDays() }}</div>
              <div class="stat-label">最近合作(天前)</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 合作趋势图表 -->
      <el-row :gutter="20" class="mt-4">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>合作活跃度</span>
            </template>
            <div ref="activityChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>合作金额趋势</span>
            </template>
            <div ref="amountChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 资质文件 -->
      <el-divider content-position="left">资质文件</el-divider>
      <el-row :gutter="20">
        <el-col :span="8" v-if="partnerInfo.qualificationFilePath">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>资质文件</span>
                <el-button type="text" @click="downloadFile(partnerInfo.qualificationFilePath)">
                  <el-icon><Download /></el-icon>
                </el-button>
              </div>
            </template>
            <p>{{ getFileName(partnerInfo.qualificationFilePath) }}</p>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>营业执照</span>
                <el-button type="text" @click="downloadFile('business_license.pdf')">
                  <el-icon><Download /></el-icon>
                </el-button>
              </div>
            </template>
            <p>营业执照.pdf</p>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>税务登记证</span>
                <el-button type="text" @click="downloadFile('tax_certificate.pdf')">
                  <el-icon><Download /></el-icon>
                </el-button>
              </div>
            </template>
            <p>税务登记证.pdf</p>
          </el-card>
        </el-col>
      </el-row>

      <!-- 合作项目列表 -->
      <el-divider content-position="left">合作项目</el-divider>
      <el-table :data="cooperationProjects" style="width: 100%">
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectType" label="项目类型" width="100" />
        <el-table-column prop="contractAmount" label="合同金额" width="120">
          <template #default="scope">
            {{ formatMoney(scope.row.contractAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="开始时间" width="120">
          <template #default="scope">
            {{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}
          </template>
        </el-table-column>
        <el-table-column prop="endDate" label="结束时间" width="120">
          <template #default="scope">
            {{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getProjectStatusType(scope.row.status)">
              {{ scope.row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button link type="primary" @click="viewProject(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 评价记录 -->
      <el-divider content-position="left">评价记录</el-divider>
      <el-table :data="evaluationRecords" style="width: 100%">
        <el-table-column prop="evaluationDate" label="评价日期" width="120">
          <template #default="scope">
            {{ parseTime(scope.row.evaluationDate, '{y}-{m}-{d}') }}
          </template>
        </el-table-column>
        <el-table-column prop="projectName" label="关联项目" />
        <el-table-column prop="overallScore" label="综合评分" width="100">
          <template #default="scope">
            <el-rate v-model="scope.row.overallScore" disabled show-score />
          </template>
        </el-table-column>
        <el-table-column prop="evaluationContent" label="评价内容" />
        <el-table-column prop="evaluator" label="评价人" width="100" />
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleEvaluate">评价合作</el-button>
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getPartner } from "@/api/project/partner"
import * as echarts from 'echarts'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  partnerId: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const loading = ref(false)
const partnerInfo = ref({})
const cooperationProjects = ref([])
const evaluationRecords = ref([])
const activityChartRef = ref()
const amountChartRef = ref()

// 监听合作单位ID变化
watch(() => props.partnerId, (newVal) => {
  if (newVal && visible.value) {
    getPartnerDetail()
  }
}, { immediate: true })

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal && props.partnerId) {
    getPartnerDetail()
  }
})

/** 获取合作单位详情 */
function getPartnerDetail() {
  if (!props.partnerId) return
  
  loading.value = true
  getPartner(props.partnerId).then(response => {
    partnerInfo.value = response.data
    
    // 模拟合作项目数据
    cooperationProjects.value = [
      {
        projectName: '智能制造系统开发',
        projectType: '技术开发',
        contractAmount: 1200000,
        startDate: '2024-01-15',
        endDate: '2024-12-31',
        status: 2,
        statusName: '执行中'
      },
      {
        projectName: '数据分析平台建设',
        projectType: '技术服务',
        contractAmount: 800000,
        startDate: '2023-06-01',
        endDate: '2024-05-31',
        status: 5,
        statusName: '已完成'
      }
    ]
    
    // 模拟评价记录数据
    evaluationRecords.value = [
      {
        evaluationDate: '2024-06-30',
        projectName: '数据分析平台建设',
        overallScore: 4.5,
        evaluationContent: '项目执行效果良好，技术水平较高，按时交付',
        evaluator: '张三'
      },
      {
        evaluationDate: '2023-12-15',
        projectName: '智能制造系统开发',
        overallScore: 4.0,
        evaluationContent: '合作态度积极，技术实力较强',
        evaluator: '李四'
      }
    ]
    
    loading.value = false
    
    // 渲染图表
    nextTick(() => {
      renderActivityChart()
      renderAmountChart()
    })
  }).catch(() => {
    loading.value = false
  })
}

/** 渲染活跃度图表 */
function renderActivityChart() {
  if (!activityChartRef.value) return
  
  const chart = echarts.init(activityChartRef.value)
  
  // 模拟活跃度数据
  const activityData = [
    { month: '2024-01', count: 2 },
    { month: '2024-02', count: 1 },
    { month: '2024-03', count: 3 },
    { month: '2024-04', count: 2 },
    { month: '2024-05', count: 4 },
    { month: '2024-06', count: 3 }
  ]
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: activityData.map(item => item.month)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '合作次数',
        type: 'line',
        data: activityData.map(item => item.count),
        smooth: true,
        itemStyle: {
          color: '#409eff'
        },
        areaStyle: {
          color: 'rgba(64, 158, 255, 0.2)'
        }
      }
    ]
  }
  
  chart.setOption(option)
}

/** 渲染金额趋势图表 */
function renderAmountChart() {
  if (!amountChartRef.value) return
  
  const chart = echarts.init(amountChartRef.value)
  
  // 模拟金额趋势数据
  const amountData = [
    { month: '2024-01', amount: 500000 },
    { month: '2024-02', amount: 300000 },
    { month: '2024-03', amount: 800000 },
    { month: '2024-04', amount: 600000 },
    { month: '2024-05', amount: 1200000 },
    { month: '2024-06', amount: 900000 }
  ]
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        return params[0].name + '<br/>' + 
               params[0].seriesName + ': ' + 
               (params[0].value / 10000).toFixed(2) + '万元'
      }
    },
    xAxis: {
      type: 'category',
      data: amountData.map(item => item.month)
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: function(value) {
          return (value / 10000).toFixed(0) + '万'
        }
      }
    },
    series: [
      {
        name: '合作金额',
        type: 'bar',
        data: amountData.map(item => item.amount),
        itemStyle: {
          color: '#67c23a'
        }
      }
    ]
  }
  
  chart.setOption(option)
}

/** 关闭对话框 */
function handleClose() {
  visible.value = false
}

/** 评价合作 */
function handleEvaluate() {
  // 这里应该打开评价对话框
  console.log('评价合作单位')
}

/** 查看项目 */
function viewProject(project) {
  // 这里应该跳转到项目详情
  console.log('查看项目:', project)
}

/** 获取合作等级类型 */
function getLevelType(level) {
  const levelMap = {
    'A': 'success',
    'B': 'primary',
    'C': 'warning',
    'D': 'danger'
  }
  return levelMap[level] || 'info'
}

/** 获取状态类型 */
function getStatusType(status) {
  const statusMap = {
    0: 'danger',   // 禁用
    1: 'success',  // 正常
    2: 'warning'   // 待审核
  }
  return statusMap[status] || 'info'
}

/** 获取项目状态类型 */
function getProjectStatusType(status) {
  const statusMap = {
    0: 'info',     // 申请中
    1: 'success',  // 立项
    2: 'primary',  // 执行中
    3: 'warning',  // 变更中
    4: 'warning',  // 结项中
    5: 'success',  // 已结项
    6: 'danger'    // 已撤销
  }
  return statusMap[status] || 'info'
}

/** 获取平均金额 */
function getAverageAmount() {
  const count = partnerInfo.value.cooperationCount || 0
  const total = partnerInfo.value.totalContractAmount || 0
  if (count === 0) return '0.00'
  return (total / count / 10000).toFixed(2)
}

/** 获取合作天数 */
function getCooperationDays() {
  if (!partnerInfo.value.lastCooperationDate) return '-'
  const lastDate = new Date(partnerInfo.value.lastCooperationDate)
  const currentDate = new Date()
  const diffTime = Math.abs(currentDate - lastDate)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}

/** 格式化金额 */
function formatMoney(money) {
  if (!money) return '0.00'
  return (parseFloat(money) / 10000).toFixed(2)
}

/** 获取文件名 */
function getFileName(filePath) {
  if (!filePath) return ''
  return filePath.split('/').pop()
}

/** 下载文件 */
function downloadFile(filePath) {
  // 这里应该调用文件下载接口
  console.log('下载文件:', filePath)
}

/** 解析时间 */
function parseTime(time, pattern) {
  if (!time) return ''
  // 这里应该使用实际的时间解析函数
  return new Date(time).toLocaleString()
}
</script>

<style scoped>
.money {
  font-weight: bold;
  color: #409eff;
}

.text-content {
  line-height: 1.6;
  white-space: pre-wrap;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-card {
  text-align: center;
}

.stat-item {
  padding: 20px 0;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.mt-4 {
  margin-top: 16px;
}
</style>
