package com.research.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.research.project.domain.entity.Partner;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 合作单位Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@Mapper
public interface PartnerMapper extends BaseMapper<Partner> {

    /**
     * 查询合作单位列表
     * 
     * @param partner 合作单位
     * @return 合作单位集合
     */
    List<Partner> selectPartnerList(Partner partner);

    /**
     * 根据ID查询合作单位
     * 
     * @param id 合作单位ID
     * @return 合作单位
     */
    Partner selectPartnerById(Long id);

    /**
     * 新增合作单位
     * 
     * @param partner 合作单位
     * @return 结果
     */
    int insertPartner(Partner partner);

    /**
     * 修改合作单位
     * 
     * @param partner 合作单位
     * @return 结果
     */
    int updatePartner(Partner partner);

    /**
     * 删除合作单位
     * 
     * @param id 合作单位ID
     * @return 结果
     */
    int deletePartnerById(Long id);

    /**
     * 批量删除合作单位
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deletePartnerByIds(Long[] ids);

    /**
     * 根据单位编码查询合作单位
     * 
     * @param partnerCode 单位编码
     * @return 合作单位
     */
    Partner selectPartnerByCode(@Param("partnerCode") String partnerCode);

    /**
     * 检查单位编码是否存在
     * 
     * @param partnerCode 单位编码
     * @param id 合作单位ID（排除自身）
     * @return 数量
     */
    int checkPartnerCodeExists(@Param("partnerCode") String partnerCode, @Param("id") Long id);

    /**
     * 根据单位类型查询合作单位列表
     * 
     * @param partnerType 单位类型
     * @return 合作单位列表
     */
    List<Partner> selectPartnersByType(@Param("partnerType") String partnerType);

    /**
     * 根据合作等级查询合作单位列表
     * 
     * @param cooperationLevel 合作等级
     * @return 合作单位列表
     */
    List<Partner> selectPartnersByLevel(@Param("cooperationLevel") String cooperationLevel);

    /**
     * 根据状态查询合作单位列表
     * 
     * @param status 状态
     * @return 合作单位列表
     */
    List<Partner> selectPartnersByStatus(@Param("status") Integer status);

    /**
     * 搜索合作单位
     * 
     * @param keyword 关键词
     * @return 合作单位列表
     */
    List<Partner> searchPartners(@Param("keyword") String keyword);

    /**
     * 查询合作单位统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> selectPartnerStatistics();

    /**
     * 查询单位类型统计
     * 
     * @return 类型统计列表
     */
    List<Map<String, Object>> selectTypeStatistics();

    /**
     * 查询合作等级统计
     * 
     * @return 等级统计列表
     */
    List<Map<String, Object>> selectLevelStatistics();

    /**
     * 查询合作单位状态统计
     * 
     * @return 状态统计列表
     */
    List<Map<String, Object>> selectStatusStatistics();

    /**
     * 查询年度合作统计
     * 
     * @param year 年份
     * @return 年度统计列表
     */
    List<Map<String, Object>> selectYearlyStatistics(@Param("year") Integer year);

    /**
     * 查询合作单位合同金额统计
     * 
     * @return 金额统计列表
     */
    List<Map<String, Object>> selectContractAmountStatistics();

    /**
     * 查询合作活跃度统计
     * 
     * @return 活跃度统计列表
     */
    List<Map<String, Object>> selectActivityStatistics();

    /**
     * 更新合作单位状态
     * 
     * @param id 合作单位ID
     * @param status 状态
     * @return 结果
     */
    int updatePartnerStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 批量更新合作单位状态
     * 
     * @param ids 合作单位ID数组
     * @param status 状态
     * @return 结果
     */
    int batchUpdatePartnerStatus(@Param("ids") Long[] ids, @Param("status") Integer status);

    /**
     * 更新合作统计信息
     * 
     * @param id 合作单位ID
     * @param cooperationCount 合作次数
     * @param totalContractAmount 累计合同金额
     * @param lastCooperationDate 最近合作日期
     * @return 结果
     */
    int updateCooperationStatistics(@Param("id") Long id, 
                                   @Param("cooperationCount") Integer cooperationCount,
                                   @Param("totalContractAmount") java.math.BigDecimal totalContractAmount,
                                   @Param("lastCooperationDate") java.util.Date lastCooperationDate);

    /**
     * 查询优质合作单位
     * 
     * @param minScore 最低评分
     * @param minCooperationCount 最少合作次数
     * @return 合作单位列表
     */
    List<Partner> selectQualityPartners(@Param("minScore") java.math.BigDecimal minScore, 
                                       @Param("minCooperationCount") Integer minCooperationCount);

    /**
     * 查询新增合作单位趋势
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 趋势数据
     */
    List<Map<String, Object>> selectNewPartnerTrend(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 查询合作单位地域分布
     * 
     * @return 地域分布统计
     */
    List<Map<String, Object>> selectRegionDistribution();

    /**
     * 查询即将过期资质的合作单位
     * 
     * @param days 天数
     * @return 合作单位列表
     */
    List<Partner> selectPartnersWithExpiringQualifications(@Param("days") Integer days);
}
