package com.research.workflow.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.research.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 工作流模型对象 workflow_model
 * 
 * <AUTHOR>
 */
@TableName("workflow_model")
public class WorkflowModel extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 模型ID */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /** 模型名称 */
    private String name;

    /** 模型标识 */
    private String key;

    /** 模型分类 */
    private String category;

    /** 模型描述 */
    private String description;

    /** 模型版本 */
    private Integer version;

    /** 模型JSON内容 */
    private String modelJson;

    /** 模型XML内容 */
    private String modelXml;

    /** 是否有编辑器源码 */
    private Boolean hasEditorSource;

    /** 是否有编辑器扩展源码 */
    private Boolean hasEditorSourceExtra;

    /** 最后更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdateTime;

    /** 部署ID */
    private String deploymentId;

    /** 流程定义ID */
    private String processDefinitionId;

    /** 是否已部署 */
    private Boolean deployed;

    /** 部署时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deployTime;

    /** 模型状态 0-草稿 1-已发布 2-已停用 */
    private String status;

    /** 模型图标 */
    private String icon;

    /** 模型颜色 */
    private String color;

    /** 排序号 */
    private Integer orderNum;

    /** 是否可见 0-隐藏 1-显示 */
    private String visible;

    /** 模型标签 */
    private String tags;

    /** 表单配置 */
    private String formConfig;

    /** 权限配置 */
    private String permissionConfig;

    /** 通知配置 */
    private String notificationConfig;

    /** 超时配置 */
    private String timeoutConfig;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getModelJson() {
        return modelJson;
    }

    public void setModelJson(String modelJson) {
        this.modelJson = modelJson;
    }

    public String getModelXml() {
        return modelXml;
    }

    public void setModelXml(String modelXml) {
        this.modelXml = modelXml;
    }

    public Boolean getHasEditorSource() {
        return hasEditorSource;
    }

    public void setHasEditorSource(Boolean hasEditorSource) {
        this.hasEditorSource = hasEditorSource;
    }

    public Boolean getHasEditorSourceExtra() {
        return hasEditorSourceExtra;
    }

    public void setHasEditorSourceExtra(Boolean hasEditorSourceExtra) {
        this.hasEditorSourceExtra = hasEditorSourceExtra;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getDeploymentId() {
        return deploymentId;
    }

    public void setDeploymentId(String deploymentId) {
        this.deploymentId = deploymentId;
    }

    public String getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(String processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }

    public Boolean getDeployed() {
        return deployed;
    }

    public void setDeployed(Boolean deployed) {
        this.deployed = deployed;
    }

    public Date getDeployTime() {
        return deployTime;
    }

    public void setDeployTime(Date deployTime) {
        this.deployTime = deployTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getVisible() {
        return visible;
    }

    public void setVisible(String visible) {
        this.visible = visible;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getFormConfig() {
        return formConfig;
    }

    public void setFormConfig(String formConfig) {
        this.formConfig = formConfig;
    }

    public String getPermissionConfig() {
        return permissionConfig;
    }

    public void setPermissionConfig(String permissionConfig) {
        this.permissionConfig = permissionConfig;
    }

    public String getNotificationConfig() {
        return notificationConfig;
    }

    public void setNotificationConfig(String notificationConfig) {
        this.notificationConfig = notificationConfig;
    }

    public String getTimeoutConfig() {
        return timeoutConfig;
    }

    public void setTimeoutConfig(String timeoutConfig) {
        this.timeoutConfig = timeoutConfig;
    }

    @Override
    public String toString() {
        return "WorkflowModel{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", key='" + key + '\'' +
                ", category='" + category + '\'' +
                ", description='" + description + '\'' +
                ", version=" + version +
                ", hasEditorSource=" + hasEditorSource +
                ", hasEditorSourceExtra=" + hasEditorSourceExtra +
                ", lastUpdateTime=" + lastUpdateTime +
                ", deploymentId='" + deploymentId + '\'' +
                ", processDefinitionId='" + processDefinitionId + '\'' +
                ", deployed=" + deployed +
                ", deployTime=" + deployTime +
                ", status='" + status + '\'' +
                ", icon='" + icon + '\'' +
                ", color='" + color + '\'' +
                ", orderNum=" + orderNum +
                ", visible='" + visible + '\'' +
                ", tags='" + tags + '\'' +
                '}';
    }
}
