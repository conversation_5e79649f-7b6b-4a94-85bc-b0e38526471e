<template>
  <div class="api-test">
    <h2>API测试页面</h2>
    
    <el-card class="test-card">
      <h3>版本管理API测试</h3>
      
      <el-form :inline="true">
        <el-form-item label="流程Key:">
          <el-input v-model="processKey" placeholder="请输入流程定义Key" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="testVersionList">测试版本列表</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="testVersionStats">测试版本统计</el-button>
        </el-form-item>
      </el-form>
      
      <el-divider />
      
      <h4>测试结果:</h4>
      <el-alert
        v-if="testResult.type"
        :title="testResult.title"
        :type="testResult.type"
        :description="testResult.message"
        show-icon
        :closable="false"
      />
      
      <el-divider />
      
      <h4>响应数据:</h4>
      <pre v-if="responseData" class="response-data">{{ JSON.stringify(responseData, null, 2) }}</pre>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { listProcessVersion, getVersionStatistics } from '@/api/workflow/version'

const processKey = ref('test_process')
const testResult = ref({
  type: '',
  title: '',
  message: ''
})
const responseData = ref(null)

// 测试版本列表API
const testVersionList = async () => {
  try {
    testResult.value = {
      type: 'info',
      title: '正在测试...',
      message: '正在调用版本列表API'
    }
    
    const response = await listProcessVersion(processKey.value)
    
    testResult.value = {
      type: 'success',
      title: '测试成功',
      message: `版本列表API调用成功，返回${response.rows?.length || 0}条数据`
    }
    
    responseData.value = response
    
  } catch (error: any) {
    testResult.value = {
      type: 'error',
      title: '测试失败',
      message: `错误信息: ${error.message || error}`
    }
    
    responseData.value = error
    console.error('版本列表API测试失败:', error)
  }
}

// 测试版本统计API
const testVersionStats = async () => {
  try {
    testResult.value = {
      type: 'info',
      title: '正在测试...',
      message: '正在调用版本统计API'
    }
    
    const response = await getVersionStatistics(processKey.value)
    
    testResult.value = {
      type: 'success',
      title: '测试成功',
      message: '版本统计API调用成功'
    }
    
    responseData.value = response
    
  } catch (error: any) {
    testResult.value = {
      type: 'error',
      title: '测试失败',
      message: `错误信息: ${error.message || error}`
    }
    
    responseData.value = error
    console.error('版本统计API测试失败:', error)
  }
}
</script>

<style scoped>
.api-test {
  padding: 20px;
}

.test-card {
  margin-bottom: 20px;
}

.response-data {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;
  font-size: 12px;
  line-height: 1.4;
}
</style>
