package com.research.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.research.common.core.controller.BaseController;
import com.research.common.core.domain.AjaxResult;
import com.research.common.core.page.TableDataInfo;
import com.research.common.utils.SecurityUtils;
import com.research.common.utils.PageUtils;
import com.research.system.domain.SysMessage;
import com.research.system.service.ISysMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 站内消息控制器
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@RestController
@RequestMapping("/system/message")
public class SysMessageController extends BaseController {

    @Autowired
    private ISysMessageService messageService;

    /**
     * 查询收件箱消息列表
     */
    @GetMapping("/inbox")
    public TableDataInfo inbox(SysMessage message) {
        startPage();
        Page<SysMessage> page = new Page<>(PageUtils.getPageNum(), PageUtils.getPageSize());
        Long userId = SecurityUtils.getUserId();
        IPage<SysMessage> list = messageService.selectInboxMessageList(page, message, userId);
        return getDataTable(list);
    }

    /**
     * 查询发件箱消息列表
     */
    @GetMapping("/outbox")
    public TableDataInfo outbox(SysMessage message) {
        startPage();
        Page<SysMessage> page = new Page<>(PageUtils.getPageNum(), PageUtils.getPageSize());
        Long userId = SecurityUtils.getUserId();
        IPage<SysMessage> list = messageService.selectOutboxMessageList(page, message, userId);
        return getDataTable(list);
    }

    /**
     * 根据消息编号获取详细信息
     */
    @GetMapping(value = "/{messageId}")
    public AjaxResult getInfo(@PathVariable Long messageId) {
        Long userId = SecurityUtils.getUserId();
        SysMessage message = messageService.selectMessageDetail(messageId, userId);
        return success(message);
    }

    /**
     * 发送消息
     */
    @PostMapping("/send")
    public AjaxResult send(@Validated @RequestBody SysMessage message) {
        return toAjax(messageService.sendMessage(message));
    }

    /**
     * 发送消息给多个用户
     */
    @PostMapping("/sendToUsers")
    public AjaxResult sendToUsers(@Validated @RequestBody SysMessage message,
                                @RequestParam List<Long> receiverIds) {
        return toAjax(messageService.sendMessageToUsers(message, receiverIds));
    }

    /**
     * 发送消息给部门
     */
    @PostMapping("/sendToDepts")
    public AjaxResult sendToDepts(@Validated @RequestBody SysMessage message,
                                @RequestParam List<Long> deptIds) {
        return toAjax(messageService.sendMessageToDepts(message, deptIds));
    }

    /**
     * 回复消息
     */
    @PostMapping("/reply/{messageId}")
    public AjaxResult reply(@PathVariable Long messageId,
                          @RequestParam String replyContent) {
        return toAjax(messageService.replyMessage(messageId, replyContent));
    }

    /**
     * 转发消息
     */
    @PostMapping("/forward/{messageId}")
    public AjaxResult forward(@PathVariable Long messageId,
                            @RequestParam List<Long> receiverIds,
                            @RequestParam(required = false) String forwardContent) {
        return toAjax(messageService.forwardMessage(messageId, receiverIds, forwardContent));
    }

    /**
     * 标记消息为已读
     */
    @PostMapping("/markRead/{messageId}")
    public AjaxResult markAsRead(@PathVariable Long messageId) {
        Long userId = SecurityUtils.getUserId();
        return toAjax(messageService.markAsRead(messageId, userId));
    }

    /**
     * 批量标记消息为已读
     */
    @PostMapping("/batchMarkRead")
    public AjaxResult batchMarkAsRead(@RequestBody List<Long> messageIds) {
        Long userId = SecurityUtils.getUserId();
        return toAjax(messageService.batchMarkAsRead(messageIds, userId));
    }

    /**
     * 全部标记为已读
     */
    @PostMapping("/markAllRead")
    public AjaxResult markAllAsRead() {
        Long userId = SecurityUtils.getUserId();
        return toAjax(messageService.markAllAsRead(userId));
    }

    /**
     * 删除消息
     */
    @DeleteMapping("/{messageId}")
    public AjaxResult delete(@PathVariable Long messageId) {
        Long userId = SecurityUtils.getUserId();
        return toAjax(messageService.deleteMessage(messageId, userId));
    }

    /**
     * 批量删除消息
     */
    @DeleteMapping("/batch")
    public AjaxResult batchDelete(@RequestBody List<Long> messageIds) {
        Long userId = SecurityUtils.getUserId();
        return toAjax(messageService.batchDeleteMessage(messageIds, userId));
    }

    /**
     * 搜索消息
     */
    @GetMapping("/search")
    public TableDataInfo search(@RequestParam String keyword,
                              @RequestParam(required = false) String messageType) {
        startPage();
        Page<SysMessage> page = new Page<>(PageUtils.getPageNum(), PageUtils.getPageSize());
        Long userId = SecurityUtils.getUserId();
        IPage<SysMessage> list = messageService.searchMessages(page, keyword, userId, messageType);
        return getDataTable(list);
    }

    /**
     * 查询用户未读消息数量
     */
    @GetMapping("/unreadCount")
    public AjaxResult getUnreadCount() {
        Long userId = SecurityUtils.getUserId();
        Long count = messageService.selectUnreadMessageCount(userId);
        return success(count);
    }

    /**
     * 查询消息统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        Long userId = SecurityUtils.getUserId();
        Map<String, Object> statistics = messageService.selectMessageStatistics(userId);
        return success(statistics);
    }

    /**
     * 查询最新消息（用于工作台展示）
     */
    @GetMapping("/latest")
    public AjaxResult getLatestMessages(@RequestParam(defaultValue = "5") Integer limit) {
        Long userId = SecurityUtils.getUserId();
        List<SysMessage> messages = messageService.selectLatestMessages(userId, limit);
        return success(messages);
    }

    /**
     * 发送系统消息
     */
    @PreAuthorize("@ss.hasPermi('system:message:system')")
    @PostMapping("/sendSystem")
    public AjaxResult sendSystemMessage(@RequestParam String title,
                                      @RequestParam String content,
                                      @RequestParam List<Long> receiverIds) {
        return toAjax(messageService.sendSystemMessage(title, content, receiverIds));
    }

    /**
     * 发送通知消息
     */
    @PreAuthorize("@ss.hasPermi('system:message:notification')")
    @PostMapping("/sendNotification")
    public AjaxResult sendNotificationMessage(@RequestParam String title,
                                            @RequestParam String content,
                                            @RequestParam List<Long> receiverIds,
                                            @RequestParam(required = false) String businessId,
                                            @RequestParam(required = false) String businessType) {
        return toAjax(messageService.sendNotificationMessage(title, content, receiverIds, businessId, businessType));
    }

    /**
     * 发送提醒消息
     */
    @PreAuthorize("@ss.hasPermi('system:message:reminder')")
    @PostMapping("/sendReminder")
    public AjaxResult sendReminderMessage(@RequestParam String title,
                                        @RequestParam String content,
                                        @RequestParam Long receiverId,
                                        @RequestParam(required = false) String businessId,
                                        @RequestParam(required = false) String businessType) {
        return toAjax(messageService.sendReminderMessage(title, content, receiverId, businessId, businessType));
    }
}
