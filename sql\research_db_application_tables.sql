-- 科研管理系统网上申报评审模块表结构
-- 强制要求★功能相关表

USE research_db;

-- ===========================================
-- 网上申报评审模块表结构（强制要求★）
-- ===========================================

-- 申报计划表
CREATE TABLE biz_application_plan (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    plan_name VARCHAR(500) COMMENT '申报计划名称',
    plan_code VARCHAR(200) COMMENT '申报计划编号',
    plan_type_id VARCHAR(32) COMMENT '申报类型ID',
    plan_level_id VARCHAR(32) COMMENT '申报级别ID',
    application_start_date DATE COMMENT '申报开始日期',
    application_end_date DATE COMMENT '申报结束日期',
    review_start_date DATE COMMENT '评审开始日期',
    review_end_date DATE COMMENT '评审结束日期',
    plan_status VARCHAR(32) COMMENT '计划状态',
    application_conditions TEXT COMMENT '申报条件',
    application_requirements TEXT COMMENT '申报要求',
    review_criteria TEXT COMMENT '评审标准',
    template_id VARCHAR(32) COMMENT '申报书模板ID',
    max_applications INT COMMENT '最大申报数量',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    note TEXT COMMENT '备注',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='申报计划表';

-- 申报书模板表（强制要求★）
CREATE TABLE biz_application_template (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    template_name VARCHAR(500) COMMENT '模板名称',
    template_code VARCHAR(200) COMMENT '模板编号',
    template_type VARCHAR(32) COMMENT '模板类型',
    template_version VARCHAR(32) COMMENT '模板版本',
    template_content LONGTEXT COMMENT '模板内容（JSON格式）',
    template_fields LONGTEXT COMMENT '模板字段配置（JSON格式）',
    template_rules LONGTEXT COMMENT '模板校验规则（JSON格式）',
    template_status VARCHAR(32) COMMENT '模板状态',
    is_default CHAR(1) DEFAULT 'N' COMMENT '是否默认模板',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    note TEXT COMMENT '备注',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='申报书模板表（强制要求★）';

-- 申报信息表
CREATE TABLE biz_application_info (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    plan_id VARCHAR(32) COMMENT '申报计划ID',
    application_code VARCHAR(200) COMMENT '申报编号',
    project_name VARCHAR(1024) COMMENT '项目名称',
    applicant_id VARCHAR(32) COMMENT '申报人ID',
    applicant_name VARCHAR(500) COMMENT '申报人姓名',
    applicant_account VARCHAR(50) COMMENT '申报人账号',
    unit_id VARCHAR(32) COMMENT '申报单位ID',
    application_date DATE COMMENT '申报日期',
    application_status VARCHAR(32) COMMENT '申报状态',
    template_id VARCHAR(32) COMMENT '使用模板ID',
    application_content LONGTEXT COMMENT '申报内容（JSON格式）',
    submit_date DATE COMMENT '提交日期',
    review_status VARCHAR(32) COMMENT '评审状态',
    final_score DECIMAL(5,2) COMMENT '最终得分',
    final_result VARCHAR(32) COMMENT '最终结果',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    note TEXT COMMENT '备注',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='申报信息表';

-- 评审方案表（强制要求★）
CREATE TABLE biz_review_scheme (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    plan_id VARCHAR(32) COMMENT '申报计划ID',
    scheme_name VARCHAR(500) COMMENT '评审方案名称',
    scheme_type VARCHAR(32) COMMENT '评审方案类型',
    review_method VARCHAR(32) COMMENT '评审方式（online/offline/mixed）',
    review_rounds INT COMMENT '评审轮次',
    experts_per_application INT COMMENT '每个申报项目评审专家数',
    score_method VARCHAR(32) COMMENT '评分方法',
    weight_config LONGTEXT COMMENT '权重配置（JSON格式）',
    review_criteria LONGTEXT COMMENT '评审标准（JSON格式）',
    scheme_status VARCHAR(32) COMMENT '方案状态',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    note TEXT COMMENT '备注',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='评审方案表（强制要求★）';

-- 评审专家表
CREATE TABLE biz_review_expert (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    expert_name VARCHAR(100) COMMENT '专家姓名',
    expert_code VARCHAR(100) COMMENT '专家编号',
    expert_account VARCHAR(50) COMMENT '专家账号',
    expert_title VARCHAR(100) COMMENT '专家职称',
    expert_unit VARCHAR(500) COMMENT '专家单位',
    expert_field VARCHAR(500) COMMENT '专家领域',
    expert_level VARCHAR(32) COMMENT '专家级别',
    contact_phone VARCHAR(50) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    expert_status VARCHAR(32) COMMENT '专家状态',
    expert_type VARCHAR(32) COMMENT '专家类型',
    qualification_file_ids VARCHAR(500) COMMENT '资质文件IDs',
    note TEXT COMMENT '备注',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='评审专家表';

-- 专家指派表（强制要求★）
CREATE TABLE biz_expert_assignment (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    plan_id VARCHAR(32) COMMENT '申报计划ID',
    application_id VARCHAR(32) COMMENT '申报信息ID',
    expert_id VARCHAR(32) COMMENT '专家ID',
    expert_name VARCHAR(100) COMMENT '专家姓名',
    assignment_type VARCHAR(32) COMMENT '指派类型（auto/manual）',
    assignment_date DATE COMMENT '指派日期',
    assignment_status VARCHAR(32) COMMENT '指派状态',
    review_deadline DATE COMMENT '评审截止日期',
    notification_sent CHAR(1) DEFAULT 'N' COMMENT '是否已发送通知',
    note TEXT COMMENT '备注',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='专家指派表（强制要求★）';

-- 在线评审打分表（强制要求★）
CREATE TABLE biz_online_review_score (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    assignment_id VARCHAR(32) COMMENT '专家指派ID',
    application_id VARCHAR(32) COMMENT '申报信息ID',
    expert_id VARCHAR(32) COMMENT '专家ID',
    expert_name VARCHAR(100) COMMENT '专家姓名',
    review_round INT COMMENT '评审轮次',
    score_details LONGTEXT COMMENT '评分详情（JSON格式）',
    total_score DECIMAL(5,2) COMMENT '总分',
    review_comments TEXT COMMENT '评审意见',
    review_date DATE COMMENT '评审日期',
    review_time_spent INT COMMENT '评审用时（分钟）',
    review_status VARCHAR(32) COMMENT '评审状态',
    submit_time DATETIME COMMENT '提交时间',
    ip_address VARCHAR(128) COMMENT 'IP地址',
    note TEXT COMMENT '备注',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='在线评审打分表（强制要求★）';

-- 评审结果汇总表（强制要求★）
CREATE TABLE biz_review_result_summary (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    plan_id VARCHAR(32) COMMENT '申报计划ID',
    application_id VARCHAR(32) COMMENT '申报信息ID',
    project_name VARCHAR(1024) COMMENT '项目名称',
    applicant_name VARCHAR(500) COMMENT '申报人姓名',
    unit_name VARCHAR(500) COMMENT '申报单位名称',
    expert_count INT COMMENT '评审专家数量',
    completed_reviews INT COMMENT '已完成评审数量',
    average_score DECIMAL(5,2) COMMENT '平均分',
    highest_score DECIMAL(5,2) COMMENT '最高分',
    lowest_score DECIMAL(5,2) COMMENT '最低分',
    score_variance DECIMAL(10,4) COMMENT '分数方差',
    final_score DECIMAL(5,2) COMMENT '最终得分',
    final_ranking INT COMMENT '最终排名',
    review_result VARCHAR(32) COMMENT '评审结果',
    result_status VARCHAR(32) COMMENT '结果状态',
    publish_date DATE COMMENT '公布日期',
    objection_period_start DATE COMMENT '异议期开始日期',
    objection_period_end DATE COMMENT '异议期结束日期',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    note TEXT COMMENT '备注',
    create_user_id VARCHAR(50) COMMENT '创建用户ID',
    create_user_name VARCHAR(50) COMMENT '创建用户名',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='评审结果汇总表（强制要求★）';

-- 评审过程跟踪表
CREATE TABLE biz_review_process_track (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    plan_id VARCHAR(32) COMMENT '申报计划ID',
    application_id VARCHAR(32) COMMENT '申报信息ID',
    expert_id VARCHAR(32) COMMENT '专家ID',
    process_type VARCHAR(32) COMMENT '过程类型',
    process_status VARCHAR(32) COMMENT '过程状态',
    process_description TEXT COMMENT '过程描述',
    process_time DATETIME COMMENT '过程时间',
    ip_address VARCHAR(128) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    note TEXT COMMENT '备注',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB COMMENT='评审过程跟踪表';

-- 申报统计表
CREATE TABLE biz_application_statistics (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    plan_id VARCHAR(32) COMMENT '申报计划ID',
    stat_date DATE COMMENT '统计日期',
    total_applications INT COMMENT '总申报数',
    submitted_applications INT COMMENT '已提交申报数',
    under_review_applications INT COMMENT '评审中申报数',
    completed_review_applications INT COMMENT '已完成评审申报数',
    approved_applications INT COMMENT '通过申报数',
    rejected_applications INT COMMENT '未通过申报数',
    total_experts INT COMMENT '总专家数',
    active_experts INT COMMENT '活跃专家数',
    completed_reviews INT COMMENT '已完成评审数',
    pending_reviews INT COMMENT '待评审数',
    average_review_time DECIMAL(10,2) COMMENT '平均评审时间（小时）',
    stat_data LONGTEXT COMMENT '统计数据（JSON格式）',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='申报统计表';
