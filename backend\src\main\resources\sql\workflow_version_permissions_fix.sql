-- =============================================
-- 版本管理权限补充配置
-- =============================================

-- 添加缺少的版本管理权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
(2036, '版本创建', 204, 6, 'F', '0', '0', 'workflow:version:create', '#', 'admin', NOW(), '创建新版本'),
(2037, '版本发布', 204, 7, 'F', '0', '0', 'workflow:version:publish', '#', 'admin', NOW(), '发布版本'),
(2038, '版本停用', 204, 8, 'F', '0', '0', 'workflow:version:deprecate', '#', 'admin', NOW(), '停用版本'),
(2039, '版本回滚', 204, 9, 'F', '0', '0', 'workflow:version:rollback', '#', 'admin', NOW(), '版本回滚'),
(2040, '版本统计', 204, 10, 'F', '0', '0', 'workflow:version:statistics', '#', 'admin', NOW(), '版本统计'),
(2046, '路由管理', 204, 11, 'F', '0', '0', 'workflow:version:route', '#', 'admin', NOW(), '版本路由管理'),
(2047, '路由测试', 204, 12, 'F', '0', '0', 'workflow:version:test', '#', 'admin', NOW(), '版本路由测试'),
(2048, '版本编辑', 204, 13, 'F', '0', '0', 'workflow:version:edit', '#', 'admin', NOW(), '编辑版本信息'),
(2049, '版本导出', 204, 14, 'F', '0', '0', 'workflow:version:export', '#', 'admin', NOW(), '导出版本信息');

-- 为admin角色分配这些权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(1, 2036), (1, 2037), (1, 2038), (1, 2039), (1, 2040),
(1, 2046), (1, 2047), (1, 2048), (1, 2049);

-- 确保admin用户有这些权限（通过角色继承）
-- admin用户ID为1，admin角色ID为1，这些权限会自动继承

-- 检查是否存在版本管理菜单，如果不存在则创建
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
(204, '版本管理', 200, 4, 'version', 'workflow/version/index', 'C', '0', '0', 'workflow:version:view', 'version', 'admin', NOW(), '工作流版本管理');

-- 确保admin角色有版本管理菜单权限
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES (1, 204);

-- 更新现有权限，确保权限标识正确
UPDATE sys_menu SET perms = 'workflow:version:list' WHERE menu_id = 2032;
UPDATE sys_menu SET perms = 'workflow:version:query' WHERE menu_id = 2031;
UPDATE sys_menu SET perms = 'workflow:version:switch' WHERE menu_id = 2033;
UPDATE sys_menu SET perms = 'workflow:version:remove' WHERE menu_id = 2034;
UPDATE sys_menu SET perms = 'workflow:version:compare' WHERE menu_id = 2035;

-- 提交事务
COMMIT;
