package com.research.workflow.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.research.common.core.controller.BaseController;
import com.research.common.core.domain.AjaxResult;
import com.research.common.core.page.TableDataInfo;
import com.research.common.utils.MybatisPlusPageUtils;
import com.research.common.utils.SecurityUtils;
import com.research.workflow.domain.WorkflowTask;
import com.research.workflow.service.IWorkflowTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作流任务管理Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/workflow/task")
public class WorkflowTaskController extends BaseController {

    @Autowired
    private IWorkflowTaskService taskService;

    /**
     * 查询我的任务列表
     */
    @PreAuthorize("@ss.hasPermi('workflow:task:list')")
    @GetMapping("/my/list")
    public TableDataInfo listMyTask(WorkflowTask task) {
        try {
            Page<WorkflowTask> page = MybatisPlusPageUtils.createSafePage();
            Long userId = SecurityUtils.getUserId();
            return MybatisPlusPageUtils.convertToTableDataInfo(
                taskService.selectMyTaskList(page, task, userId)
            );
        } catch (Exception e) {
            logger.error("查询我的任务列表失败", e);
            return MybatisPlusPageUtils.createEmptyTableDataInfo();
        }
    }

    /**
     * 查询候选任务列表
     */
    @PreAuthorize("@ss.hasPermi('workflow:task:list')")
    @GetMapping("/candidate/list")
    public TableDataInfo listCandidateTask(WorkflowTask task) {
        try {
            Page<WorkflowTask> page = MybatisPlusPageUtils.createSafePage();
            Long userId = SecurityUtils.getUserId();
            return MybatisPlusPageUtils.convertToTableDataInfo(
                taskService.selectCandidateTaskList(page, task, userId)
            );
        } catch (Exception e) {
            logger.error("查询候选任务列表失败", e);
            return MybatisPlusPageUtils.createEmptyTableDataInfo();
        }
    }

    /**
     * 查询历史任务列表
     */
    @PreAuthorize("@ss.hasPermi('workflow:task:list')")
    @GetMapping("/history/list")
    public TableDataInfo listHistoryTask(WorkflowTask task) {
        try {
            Page<WorkflowTask> page = MybatisPlusPageUtils.createSafePage();
            Long userId = SecurityUtils.getUserId();
            return MybatisPlusPageUtils.convertToTableDataInfo(
                taskService.selectHistoryTaskList(page, task, userId)
            );
        } catch (Exception e) {
            logger.error("查询历史任务列表失败", e);
            return MybatisPlusPageUtils.createEmptyTableDataInfo();
        }
    }

    /**
     * 获取任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('workflow:task:query')")
    @GetMapping("/{taskId}")
    public AjaxResult getTask(@PathVariable String taskId) {
        try {
            WorkflowTask task = taskService.selectTaskById(taskId);
            return AjaxResult.success(task);
        } catch (Exception e) {
            logger.error("获取任务详情失败", e);
            return AjaxResult.error("获取任务详情失败：" + e.getMessage());
        }
    }

    /**
     * 签收任务
     */
    @PreAuthorize("@ss.hasPermi('workflow:task:claim')")
    @PutMapping("/claim/{taskId}")
    public AjaxResult claimTask(@PathVariable String taskId) {
        try {
            Long userId = SecurityUtils.getUserId();
            String username = SecurityUtils.getUsername();
            taskService.claimTask(taskId, userId, username);
            return AjaxResult.success("签收成功");
        } catch (Exception e) {
            logger.error("签收任务失败", e);
            return AjaxResult.error("签收失败：" + e.getMessage());
        }
    }

    /**
     * 完成任务
     */
    @PreAuthorize("@ss.hasPermi('workflow:task:complete')")
    @PutMapping("/complete/{taskId}")
    public AjaxResult completeTask(@PathVariable String taskId, 
                                 @RequestParam(required = false) String comment,
                                 @RequestBody(required = false) Map<String, Object> variables) {
        try {
            Long userId = SecurityUtils.getUserId();
            String username = SecurityUtils.getUsername();
            taskService.completeTask(taskId, userId, username, comment, variables);
            return AjaxResult.success("完成成功");
        } catch (Exception e) {
            logger.error("完成任务失败", e);
            return AjaxResult.error("完成失败：" + e.getMessage());
        }
    }

    /**
     * 转办任务
     */
    @PreAuthorize("@ss.hasPermi('workflow:task:assign')")
    @PutMapping("/assign/{taskId}")
    public AjaxResult assignTask(@PathVariable String taskId, @RequestParam String userId) {
        try {
            Long currentUserId = SecurityUtils.getUserId();
            String currentUsername = SecurityUtils.getUsername();
            taskService.assignTask(taskId, currentUserId, currentUsername, userId);
            return AjaxResult.success("转办成功");
        } catch (Exception e) {
            logger.error("转办任务失败", e);
            return AjaxResult.error("转办失败：" + e.getMessage());
        }
    }

    /**
     * 委派任务
     */
    @PreAuthorize("@ss.hasPermi('workflow:task:delegate')")
    @PutMapping("/delegate/{taskId}")
    public AjaxResult delegateTask(@PathVariable String taskId, @RequestParam String userId) {
        try {
            Long currentUserId = SecurityUtils.getUserId();
            String currentUsername = SecurityUtils.getUsername();
            taskService.delegateTask(taskId, currentUserId, currentUsername, userId);
            return AjaxResult.success("委派成功");
        } catch (Exception e) {
            logger.error("委派任务失败", e);
            return AjaxResult.error("委派失败：" + e.getMessage());
        }
    }

    /**
     * 归还任务
     */
    @PreAuthorize("@ss.hasPermi('workflow:task:resolve')")
    @PutMapping("/resolve/{taskId}")
    public AjaxResult resolveTask(@PathVariable String taskId) {
        try {
            Long userId = SecurityUtils.getUserId();
            String username = SecurityUtils.getUsername();
            taskService.resolveTask(taskId, userId, username);
            return AjaxResult.success("归还成功");
        } catch (Exception e) {
            logger.error("归还任务失败", e);
            return AjaxResult.error("归还失败：" + e.getMessage());
        }
    }

    /**
     * 获取任务变量
     */
    @PreAuthorize("@ss.hasPermi('workflow:task:query')")
    @GetMapping("/variables/{taskId}")
    public AjaxResult getTaskVariables(@PathVariable String taskId) {
        try {
            Map<String, Object> variables = taskService.getTaskVariables(taskId);
            return AjaxResult.success(variables);
        } catch (Exception e) {
            logger.error("获取任务变量失败", e);
            return AjaxResult.error("获取任务变量失败：" + e.getMessage());
        }
    }

    /**
     * 设置任务变量
     */
    @PreAuthorize("@ss.hasPermi('workflow:task:edit')")
    @PutMapping("/variables/{taskId}")
    public AjaxResult setTaskVariables(@PathVariable String taskId, @RequestBody Map<String, Object> variables) {
        try {
            taskService.setTaskVariables(taskId, variables);
            return AjaxResult.success("设置成功");
        } catch (Exception e) {
            logger.error("设置任务变量失败", e);
            return AjaxResult.error("设置失败：" + e.getMessage());
        }
    }
}
