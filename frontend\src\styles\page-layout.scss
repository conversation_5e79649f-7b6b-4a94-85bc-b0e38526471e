/* ===== 全局页面布局样式 ===== */

/* 页面容器基础样式 */
.app-container {
  padding: 20px;
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 页面内容区域 */
.page-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

/* 查询表单区域 */
.search-form {
  flex-shrink: 0;
  margin-bottom: 16px;
  padding: 16px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 操作按钮区域 */
.action-bar {
  flex-shrink: 0;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 表格容器 */
.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 表格自适应样式 */
.el-table {
  width: 100% !important;
  flex: 1;
}

.el-table__body-wrapper {
  max-height: calc(100vh - 350px) !important;
  overflow-y: auto;
}

/* 分页容器 */
.pagination-container {
  flex-shrink: 0;
  padding: 16px;
  display: flex;
  justify-content: center;
  background: #fff;
  border-top: 1px solid #ebeef5;
}

/* 对话框样式优化 */
.el-dialog {
  border-radius: 8px;
  overflow: hidden;
}

.el-dialog__header {
  padding: 20px 24px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
}

.el-dialog__body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.el-dialog__footer {
  padding: 16px 24px 20px;
  background: #f8f9fa;
  border-top: 1px solid #ebeef5;
  text-align: right;
}

/* 表单样式优化 */
.el-form {
  .el-form-item {
    margin-bottom: 18px;
  }
  
  .el-form-item__label {
    font-weight: 500;
    color: #303133;
  }
  
  .el-input,
  .el-select,
  .el-date-picker {
    width: 100%;
  }
}

/* 卡片样式 */
.content-card {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  font-weight: 500;
  color: #303133;
}

.card-body {
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .app-container {
    padding: 16px;
  }
  
  .el-table__body-wrapper {
    max-height: calc(100vh - 320px) !important;
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 12px;
  }
  
  .search-form,
  .action-bar,
  .table-container {
    margin-bottom: 12px;
  }
  
  .el-table__body-wrapper {
    max-height: calc(100vh - 280px) !important;
  }
}

/* 树形组件样式 */
.tree-border {
  margin-top: 5px;
  border: 1px solid #e5e6e7;
  background: #FFFFFF none;
  border-radius: 4px;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #909399;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #909399;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
  
  .empty-text {
    font-size: 14px;
  }
}
