# 前后端联调测试计划

## 测试概述
本文档描述了第二阶段核心业务功能的前后端联调测试计划，包括测试环境准备、测试用例设计和问题修复流程。

## 测试环境

### 后端环境
- **框架**: Spring Boot 2.7.x
- **数据库**: H2内存数据库
- **端口**: 8080
- **API前缀**: /dev-api

### 前端环境
- **框架**: Vue 3 + TypeScript
- **UI库**: Element Plus
- **构建工具**: Vite
- **端口**: 3000

## 测试准备

### 1. 数据库初始化 ✅
- [x] 更新schema.sql，添加第二阶段表结构
- [x] 更新data.sql，添加示例数据
- [x] 包含以下表：
  - sys_notice (通知公告表)
  - sys_todo (待办事项表)
  - sys_message (站内消息表)
  - sys_notice_read (公告阅读记录表)
  - sys_todo_history (待办处理历史表)
  - sys_user_workspace (用户工作台配置表)
  - sys_quick_app (系统快捷应用表)
  - sys_message_attachment (消息附件表)

### 2. 后端服务检查
- [ ] 编译后端项目
- [ ] 启动Spring Boot应用
- [ ] 验证H2数据库连接
- [ ] 检查API接口可访问性

### 3. 前端项目检查
- [ ] 安装前端依赖
- [ ] 检查TypeScript编译
- [ ] 启动开发服务器
- [ ] 验证代理配置

## 测试用例

### 1. 个人工作台模块

#### 1.1 工作台数据加载
**测试接口**: `GET /workspace/data`
**预期结果**: 
- 返回用户基本信息
- 返回待办事项统计
- 返回通知公告统计
- 返回消息统计
- 返回快捷应用配置

**测试步骤**:
1. 访问工作台页面
2. 检查各个卡片是否正常显示
3. 验证数据的准确性

#### 1.2 快捷应用配置
**测试接口**: 
- `GET /workspace/quickApps`
- `POST /workspace/quickApps`
- `GET /workspace/availableApps`

**测试步骤**:
1. 打开快捷应用配置对话框
2. 测试拖拽排序功能
3. 测试添加/删除应用
4. 保存配置并验证

#### 1.3 个性化设置
**测试接口**:
- `GET /workspace/layoutConfig`
- `POST /workspace/layoutConfig`
- `GET /workspace/themeConfig`
- `POST /workspace/themeConfig`

**测试步骤**:
1. 打开工作台设置面板
2. 修改布局配置
3. 修改主题配置
4. 保存并验证效果

### 2. 通知公告模块

#### 2.1 公告列表管理
**测试接口**: 
- `GET /system/notice/list`
- `GET /system/notice/userList`

**测试步骤**:
1. 访问公告管理页面
2. 测试搜索功能
3. 测试筛选功能
4. 测试分页功能

#### 2.2 公告详情查看
**测试接口**: 
- `GET /system/notice/view/{id}`
- `GET /system/notice/readStats/{id}`

**测试步骤**:
1. 点击公告标题进入详情页
2. 验证阅读记录功能
3. 测试分享功能
4. 测试打印功能

#### 2.3 公告管理操作
**测试接口**:
- `POST /system/notice`
- `PUT /system/notice`
- `DELETE /system/notice/{ids}`
- `POST /system/notice/publish/{id}`
- `POST /system/notice/withdraw/{id}`

**测试步骤**:
1. 测试新增公告
2. 测试编辑公告
3. 测试删除公告
4. 测试发布/撤回功能

### 3. 待办事项模块

#### 3.1 待办列表查询
**测试接口**:
- `GET /system/todo/myList`
- `GET /system/todo/myCreatedList`
- `GET /system/todo/list`

**测试步骤**:
1. 切换不同标签页
2. 测试搜索筛选
3. 验证数据准确性

#### 3.2 待办处理流程
**测试接口**:
- `POST /system/todo/assign/{id}`
- `POST /system/todo/process/{id}`
- `POST /system/todo/complete/{id}`
- `POST /system/todo/cancel/{id}`
- `POST /system/todo/reopen/{id}`

**测试步骤**:
1. 测试待办分配
2. 测试状态更新
3. 测试完成操作
4. 测试取消/重开

#### 3.3 待办统计功能
**测试接口**:
- `GET /system/todo/statistics`
- `GET /system/todo/priorityStats`
- `GET /system/todo/statusStats`

**测试步骤**:
1. 验证统计数据准确性
2. 测试图表展示
3. 检查实时更新

### 4. 站内消息模块

#### 4.1 消息列表管理
**测试接口**:
- `GET /system/message/inbox`
- `GET /system/message/outbox`

**测试步骤**:
1. 切换收件箱/发件箱
2. 测试消息搜索
3. 验证未读消息提醒

#### 4.2 消息操作功能
**测试接口**:
- `POST /system/message/send`
- `POST /system/message/reply/{id}`
- `POST /system/message/forward/{id}`
- `POST /system/message/markRead/{id}`
- `DELETE /system/message/{id}`

**测试步骤**:
1. 测试发送消息
2. 测试回复消息
3. 测试转发消息
4. 测试标记已读
5. 测试删除消息

## 问题记录与修复

### 发现的问题

#### 1. 路径配置问题
**问题描述**: 后端服务启动时遇到路径问题
**影响范围**: 后端服务启动
**修复状态**: 待修复
**修复方案**: 
- 检查项目实际路径
- 修正Maven配置
- 验证Java环境

#### 2. 前端组件依赖问题
**问题描述**: 部分子组件尚未实现
**影响范围**: 前端功能完整性
**修复状态**: 待修复
**修复方案**:
- 实现TodoTable组件
- 实现TodoForm组件
- 实现MessageTable组件
- 实现ComposeForm组件

#### 3. API接口格式问题
**问题描述**: 前后端数据格式可能不匹配
**影响范围**: 数据交互
**修复状态**: 待验证
**修复方案**:
- 统一响应格式
- 添加数据验证
- 完善错误处理

## 测试执行计划

### 第一阶段：环境准备 (预计1小时)
1. 修复后端启动问题
2. 完善前端子组件
3. 验证基础环境

### 第二阶段：接口测试 (预计2小时)
1. 逐个测试API接口
2. 验证数据格式
3. 修复发现的问题

### 第三阶段：功能测试 (预计2小时)
1. 端到端功能测试
2. 用户体验测试
3. 性能测试

### 第四阶段：问题修复 (预计1小时)
1. 修复测试中发现的问题
2. 优化用户体验
3. 完善错误处理

## 测试完成标准

### 功能完整性
- [ ] 所有API接口正常响应
- [ ] 前端页面正常显示
- [ ] 数据交互正确无误
- [ ] 错误处理完善

### 用户体验
- [ ] 页面加载速度合理
- [ ] 交互操作流畅
- [ ] 错误提示友好
- [ ] 响应式布局正常

### 数据一致性
- [ ] 数据库操作正确
- [ ] 前后端数据同步
- [ ] 状态更新及时
- [ ] 权限控制有效

## 后续计划

### 优化改进
1. 性能优化
2. 用户体验提升
3. 代码质量改进
4. 文档完善

### 功能扩展
1. 消息推送功能
2. 文件上传优化
3. 数据导出功能
4. 移动端适配

---

**测试负责人**: 开发团队
**测试时间**: 2024-07-29
**文档版本**: v1.0
