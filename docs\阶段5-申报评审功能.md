# 第五阶段：申报评审功能 (2-3周)

## 阶段概述
**目标**: 实现网上申报和评审功能，支持各类科研项目的在线申报和专家评审
**预计时间**: 2-3周
**人力投入**: 3-4人
**前置条件**: 项目管理功能完成

## 验收标准
- [ ] 网上申报功能完整，支持申报计划管理和条件设定
- [ ] 申报信息录入和材料上传功能正常
- [ ] 网上评审功能完善，支持专家评审和结果汇总
- [ ] 评审流程规范，支持多轮评审和结果公示
- [ ] 申报评审数据统计和报表功能可用

---

## 网上申报管理

### 申报计划管理
- [ ] **申报计划编制接口**
  - [ ] 创建ApplicationPlanController
  - [ ] 实现申报计划创建功能
  - [ ] 支持计划基本信息设置
  - [ ] 实现申报时间安排
  - [ ] 添加申报类型配置
  - [ ] 支持申报指南上传

- [ ] **申报计划发布接口**
  - [ ] 实现计划发布功能
  - [ ] 支持发布范围设置
  - [ ] 实现发布状态管理
  - [ ] 添加发布通知功能
  - [ ] 支持计划修改发布

- [ ] **申报计划查询接口**
  - [ ] 实现计划列表查询
  - [ ] 支持按状态筛选
  - [ ] 实现计划详情查询
  - [ ] 添加计划搜索功能
  - [ ] 支持计划统计分析

### 申报条件设定
- [ ] **申报条件配置接口**
  - [ ] 创建ApplicationConditionController
  - [ ] 实现条件规则配置
  - [ ] 支持条件分类管理
  - [ ] 实现条件权重设置
  - [ ] 添加条件验证规则
  - [ ] 支持条件模板管理

- [ ] **申报资格验证接口**
  - [ ] 实现资格自动验证
  - [ ] 支持条件匹配检查
  - [ ] 实现验证结果记录
  - [ ] 添加验证异常处理
  - [ ] 支持人工复核功能

### 申报信息管理
- [ ] **申报信息录入接口**
  - [ ] 创建ApplicationInfoController
  - [ ] 实现申报表单生成
  - [ ] 支持动态表单配置
  - [ ] 实现申报信息保存
  - [ ] 添加信息验证功能
  - [ ] 支持申报信息修改

- [ ] **申报材料上传接口**
  - [ ] 实现材料上传功能
  - [ ] 支持材料分类管理
  - [ ] 实现材料格式验证
  - [ ] 添加材料版本控制
  - [ ] 支持材料预览功能

- [ ] **申报信息查询接口**
  - [ ] 实现申报列表查询
  - [ ] 支持申报状态筛选
  - [ ] 实现申报详情查询
  - [ ] 添加申报进度跟踪
  - [ ] 支持申报数据导出

---

## 网上评审功能

### 评审方案管理
- [ ] **评审方案制定接口**
  - [ ] 创建ReviewSchemeController
  - [ ] 实现评审方案创建
  - [ ] 支持评审流程设计
  - [ ] 实现评审标准设置
  - [ ] 添加评审权重配置
  - [ ] 支持评审模板管理

- [ ] **评审专家指派接口**
  - [ ] 实现专家库管理
  - [ ] 支持专家筛选功能
  - [ ] 实现专家分组指派
  - [ ] 添加专家回避设置
  - [ ] 支持专家替换功能

- [ ] **评审计划发布接口**
  - [ ] 实现评审计划发布
  - [ ] 支持评审时间安排
  - [ ] 实现评审通知发送
  - [ ] 添加评审材料分发
  - [ ] 支持评审准备检查

### 评审过程管理
- [ ] **专家评审接口**
  - [ ] 创建ExpertReviewController
  - [ ] 实现评审表单生成
  - [ ] 支持评审意见录入
  - [ ] 实现评审打分功能
  - [ ] 添加评审材料查看
  - [ ] 支持评审结果提交

- [ ] **评审进度跟踪接口**
  - [ ] 实现评审进度监控
  - [ ] 支持评审状态更新
  - [ ] 实现进度提醒功能
  - [ ] 添加异常情况处理
  - [ ] 支持进度报告生成

- [ ] **评审结果汇总接口**
  - [ ] 实现评审结果收集
  - [ ] 支持结果统计分析
  - [ ] 实现结果排序功能
  - [ ] 添加结果复核机制
  - [ ] 支持结果公示管理

---

## 申报评审前端页面

### 申报管理页面
- [ ] **申报计划列表页面**
  - [ ] 创建ApplicationPlanList页面
  - [ ] 显示申报计划列表
  - [ ] 实现计划搜索筛选
  - [ ] 添加计划操作功能

- [ ] **申报计划详情页面**
  - [ ] 创建ApplicationPlanDetail页面
  - [ ] 显示计划详细信息
  - [ ] 实现申报指南查看
  - [ ] 添加申报入口链接

- [ ] **申报表单页面**
  - [ ] 创建ApplicationForm页面
  - [ ] 实现动态表单渲染
  - [ ] 添加表单验证功能
  - [ ] 支持表单保存提交

- [ ] **申报材料上传页面**
  - [ ] 创建MaterialUpload页面
  - [ ] 实现文件上传组件
  - [ ] 添加材料预览功能
  - [ ] 支持材料管理操作

- [ ] **申报进度查看页面**
  - [ ] 创建ApplicationProgress页面
  - [ ] 显示申报进度状态
  - [ ] 实现进度时间轴
  - [ ] 添加状态变更通知

### 评审管理页面
- [ ] **评审方案管理页面**
  - [ ] 创建ReviewSchemeManagement页面
  - [ ] 实现方案创建编辑
  - [ ] 添加方案配置功能
  - [ ] 支持方案模板管理

- [ ] **专家管理页面**
  - [ ] 创建ExpertManagement页面
  - [ ] 实现专家信息管理
  - [ ] 添加专家分组功能
  - [ ] 支持专家指派操作

- [ ] **评审任务页面**
  - [ ] 创建ReviewTask页面
  - [ ] 显示评审任务列表
  - [ ] 实现评审表单填写
  - [ ] 添加评审结果提交

- [ ] **评审结果页面**
  - [ ] 创建ReviewResult页面
  - [ ] 显示评审结果统计
  - [ ] 实现结果排序展示
  - [ ] 添加结果导出功能

---

## 数据库设计

### 申报相关表
```sql
-- 申报计划表
CREATE TABLE application_plan (
    id BIGINT PRIMARY KEY,
    plan_name VARCHAR(200),
    plan_type VARCHAR(50),
    status VARCHAR(20),
    start_time DATETIME,
    end_time DATETIME,
    description TEXT,
    guidelines_url VARCHAR(500),
    created_time DATETIME,
    updated_time DATETIME
);

-- 申报信息表
CREATE TABLE application_info (
    id BIGINT PRIMARY KEY,
    plan_id BIGINT,
    applicant_id BIGINT,
    project_name VARCHAR(200),
    project_type VARCHAR(50),
    status VARCHAR(20),
    submit_time DATETIME,
    review_score DECIMAL(5,2),
    created_time DATETIME,
    updated_time DATETIME
);

-- 申报材料表
CREATE TABLE application_material (
    id BIGINT PRIMARY KEY,
    application_id BIGINT,
    material_name VARCHAR(200),
    material_type VARCHAR(50),
    file_path VARCHAR(500),
    file_size BIGINT,
    upload_time DATETIME
);
```

### 评审相关表
```sql
-- 评审方案表
CREATE TABLE review_scheme (
    id BIGINT PRIMARY KEY,
    scheme_name VARCHAR(200),
    plan_id BIGINT,
    review_type VARCHAR(50),
    review_rounds INT,
    status VARCHAR(20),
    created_time DATETIME,
    updated_time DATETIME
);

-- 评审专家表
CREATE TABLE review_expert (
    id BIGINT PRIMARY KEY,
    expert_name VARCHAR(100),
    expert_title VARCHAR(50),
    research_field VARCHAR(200),
    contact_phone VARCHAR(20),
    email VARCHAR(100),
    status VARCHAR(20),
    created_time DATETIME
);

-- 评审结果表
CREATE TABLE review_result (
    id BIGINT PRIMARY KEY,
    application_id BIGINT,
    expert_id BIGINT,
    review_score DECIMAL(5,2),
    review_opinion TEXT,
    review_time DATETIME,
    status VARCHAR(20)
);
```

---

## 业务流程设计

### 申报流程
1. **申报计划发布** → 管理员发布申报计划和指南
2. **资格验证** → 申报人提交基本信息，系统验证资格
3. **信息录入** → 申报人填写申报表单和上传材料
4. **形式审查** → 管理员进行形式审查
5. **申报确认** → 申报人确认提交申报

### 评审流程
1. **评审准备** → 制定评审方案，指派评审专家
2. **材料分发** → 向专家分发评审材料
3. **专家评审** → 专家进行评审打分
4. **结果汇总** → 收集评审结果并统计
5. **结果公示** → 公示评审结果

---

## 系统集成

### 与项目管理集成
- [ ] **申报成功后自动创建项目**
- [ ] **项目信息与申报信息关联**
- [ ] **评审结果影响项目立项**

### 与工作流集成
- [ ] **申报审核流程**
- [ ] **评审流程管理**
- [ ] **结果确认流程**

---

## 阶段总结

### 技术要点
- [ ] 动态表单生成和验证
- [ ] 文件上传和管理
- [ ] 复杂的评审算法
- [ ] 数据统计和分析

### 完成标志
- [ ] 申报评审功能完整可用
- [ ] 流程运行稳定高效
- [ ] 用户操作体验良好
- [ ] 数据统计准确完整

### 下一阶段准备
- [ ] 确认申报评审功能稳定性
- [ ] 准备科研服务功能开发
- [ ] 优化系统响应性能
- [ ] 完善用户操作指南
