package com.research.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.research.system.domain.SysQuickApp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统快捷应用Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Mapper
public interface SysQuickAppMapper extends BaseMapper<SysQuickApp> {

    /**
     * 查询启用的快捷应用列表
     * 
     * @return 快捷应用列表
     */
    List<SysQuickApp> selectEnabledApps();

    /**
     * 查询用户可访问的快捷应用
     * 
     * @param userId 用户ID
     * @return 快捷应用列表
     */
    List<SysQuickApp> selectUserAccessibleApps(@Param("userId") Long userId);

    /**
     * 根据应用编码查询应用
     * 
     * @param appCode 应用编码
     * @return 快捷应用
     */
    SysQuickApp selectByAppCode(@Param("appCode") String appCode);

    /**
     * 查询应用列表（带分页）
     * 
     * @param app 查询条件
     * @return 应用列表
     */
    List<SysQuickApp> selectAppList(@Param("app") SysQuickApp app);
}
