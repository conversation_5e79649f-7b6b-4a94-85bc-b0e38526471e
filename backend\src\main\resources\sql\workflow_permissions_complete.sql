-- =============================================
-- 工作流完整权限配置SQL
-- 包含所有工作流相关的权限标识
-- =============================================

-- 删除现有工作流权限（如果存在）
DELETE FROM sys_role_menu WHERE menu_id BETWEEN 2000 AND 2999;
DELETE FROM sys_menu WHERE menu_id BETWEEN 2000 AND 2999;

-- =============================================
-- 1. 流程设计器权限 (workflow:designer:*)
-- =============================================
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
(2001, '设计器查看', 201, 1, 'F', '0', '0', 'workflow:designer:view', '#', 'admin', NOW(), '查看流程设计器'),
(2002, '模型创建', 201, 2, 'F', '0', '0', 'workflow:designer:create', '#', 'admin', NOW(), '创建流程模型'),
(2003, '模型编辑', 201, 3, 'F', '0', '0', 'workflow:designer:edit', '#', 'admin', NOW(), '编辑流程模型'),
(2004, '模型删除', 201, 4, 'F', '0', '0', 'workflow:designer:delete', '#', 'admin', NOW(), '删除流程模型'),
(2005, '模型保存', 201, 5, 'F', '0', '0', 'workflow:designer:save', '#', 'admin', NOW(), '保存流程模型'),
(2006, '模型部署', 201, 6, 'F', '0', '0', 'workflow:designer:deploy', '#', 'admin', NOW(), '部署流程模型'),
(2007, '模型导入', 201, 7, 'F', '0', '0', 'workflow:designer:import', '#', 'admin', NOW(), '导入流程模型'),
(2008, '模型导出', 201, 8, 'F', '0', '0', 'workflow:designer:export', '#', 'admin', NOW(), '导出流程模型'),
(2009, '流程发布', 201, 9, 'F', '0', '0', 'workflow:designer:publish', '#', 'admin', NOW(), '发布流程模型');

-- =============================================
-- 2. 流程管理权限 (workflow:process:*)
-- =============================================
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
(2011, '流程查询', 202, 1, 'F', '0', '0', 'workflow:process:query', '#', 'admin', NOW(), '查询流程定义'),
(2012, '流程列表', 202, 2, 'F', '0', '0', 'workflow:process:list', '#', 'admin', NOW(), '查看流程列表'),
(2013, '流程启动', 202, 3, 'F', '0', '0', 'workflow:process:start', '#', 'admin', NOW(), '启动流程实例'),
(2014, '流程挂起', 202, 4, 'F', '0', '0', 'workflow:process:suspend', '#', 'admin', NOW(), '挂起流程定义'),
(2015, '流程激活', 202, 5, 'F', '0', '0', 'workflow:process:activate', '#', 'admin', NOW(), '激活流程定义'),
(2016, '流程删除', 202, 6, 'F', '0', '0', 'workflow:process:delete', '#', 'admin', NOW(), '删除流程定义'),
(2017, '实例查看', 202, 7, 'F', '0', '0', 'workflow:process:instance', '#', 'admin', NOW(), '查看流程实例'),
(2018, '历史查看', 202, 8, 'F', '0', '0', 'workflow:process:history', '#', 'admin', NOW(), '查看流程历史'),
(2019, '流程部署', 202, 9, 'F', '0', '0', 'workflow:process:deploy', '#', 'admin', NOW(), '部署流程定义');

-- =============================================
-- 3. 任务管理权限 (workflow:task:*)
-- =============================================
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
(2021, '任务查询', 203, 1, 'F', '0', '0', 'workflow:task:query', '#', 'admin', NOW(), '查询任务信息'),
(2022, '任务列表', 203, 2, 'F', '0', '0', 'workflow:task:list', '#', 'admin', NOW(), '查看任务列表'),
(2023, '任务签收', 203, 3, 'F', '0', '0', 'workflow:task:claim', '#', 'admin', NOW(), '签收任务'),
(2024, '任务完成', 203, 4, 'F', '0', '0', 'workflow:task:complete', '#', 'admin', NOW(), '完成任务'),
(2025, '任务转办', 203, 5, 'F', '0', '0', 'workflow:task:assign', '#', 'admin', NOW(), '转办任务'),
(2026, '任务委派', 203, 6, 'F', '0', '0', 'workflow:task:delegate', '#', 'admin', NOW(), '委派任务'),
(2027, '任务历史', 203, 7, 'F', '0', '0', 'workflow:task:history', '#', 'admin', NOW(), '查看任务历史'),
(2028, '任务详情', 203, 8, 'F', '0', '0', 'workflow:task:detail', '#', 'admin', NOW(), '查看任务详情');

-- =============================================
-- 4. 版本管理权限 (workflow:version:*)
-- =============================================
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
(2031, '版本查询', 204, 1, 'F', '0', '0', 'workflow:version:query', '#', 'admin', NOW(), '查询版本信息'),
(2032, '版本列表', 204, 2, 'F', '0', '0', 'workflow:version:list', '#', 'admin', NOW(), '查看版本列表'),
(2033, '版本切换', 204, 3, 'F', '0', '0', 'workflow:version:switch', '#', 'admin', NOW(), '切换流程版本'),
(2034, '版本删除', 204, 4, 'F', '0', '0', 'workflow:version:remove', '#', 'admin', NOW(), '删除流程版本'),
(2035, '版本比较', 204, 5, 'F', '0', '0', 'workflow:version:compare', '#', 'admin', NOW(), '比较流程版本');

-- =============================================
-- 5. 监控大屏权限 (workflow:monitor:*)
-- =============================================
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
(2041, '监控查看', 205, 1, 'F', '0', '0', 'workflow:monitor:view', '#', 'admin', NOW(), '查看监控大屏'),
(2042, '统计分析', 205, 2, 'F', '0', '0', 'workflow:monitor:statistics', '#', 'admin', NOW(), '查看统计分析'),
(2043, '性能监控', 205, 3, 'F', '0', '0', 'workflow:monitor:performance', '#', 'admin', NOW(), '查看性能监控'),
(2044, '报表导出', 205, 4, 'F', '0', '0', 'workflow:monitor:export', '#', 'admin', NOW(), '导出监控报表'),
(2045, '实时监控', 205, 5, 'F', '0', '0', 'workflow:monitor:realtime', '#', 'admin', NOW(), '实时监控数据');

-- =============================================
-- 6. 流程定义权限 (workflow:definition:*)
-- =============================================
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
(2051, '定义查询', 201, 10, 'F', '0', '0', 'workflow:definition:query', '#', 'admin', NOW(), '查询流程定义'),
(2052, '定义列表', 201, 11, 'F', '0', '0', 'workflow:definition:list', '#', 'admin', NOW(), '查看定义列表'),
(2053, '定义详情', 201, 12, 'F', '0', '0', 'workflow:definition:detail', '#', 'admin', NOW(), '查看定义详情');

-- =============================================
-- 7. 流程实例权限 (workflow:instance:*)
-- =============================================
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
(2061, '实例查询', 202, 10, 'F', '0', '0', 'workflow:instance:query', '#', 'admin', NOW(), '查询流程实例'),
(2062, '实例列表', 202, 11, 'F', '0', '0', 'workflow:instance:list', '#', 'admin', NOW(), '查看实例列表'),
(2063, '实例详情', 202, 12, 'F', '0', '0', 'workflow:instance:detail', '#', 'admin', NOW(), '查看实例详情'),
(2064, '实例挂起', 202, 13, 'F', '0', '0', 'workflow:instance:suspend', '#', 'admin', NOW(), '挂起流程实例'),
(2065, '实例激活', 202, 14, 'F', '0', '0', 'workflow:instance:activate', '#', 'admin', NOW(), '激活流程实例'),
(2066, '实例删除', 202, 15, 'F', '0', '0', 'workflow:instance:delete', '#', 'admin', NOW(), '删除流程实例');

-- =============================================
-- 8. 为超级管理员角色分配所有工作流权限
-- =============================================
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu 
WHERE menu_id BETWEEN 2001 AND 2099 AND menu_type = 'F';

-- =============================================
-- 9. 为科研管理员角色分配部分工作流权限
-- =============================================
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
-- 流程管理权限（只读）
(2, 2011), (2, 2012), (2, 2013), (2, 2017), (2, 2018),
-- 任务管理权限
(2, 2021), (2, 2022), (2, 2023), (2, 2024), (2, 2027), (2, 2028),
-- 监控查看权限
(2, 2041), (2, 2042);

-- =============================================
-- 10. 为项目经理角色分配基础工作流权限
-- =============================================
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
-- 流程查看权限
(3, 2011), (3, 2012), (3, 2017),
-- 任务处理权限
(3, 2021), (3, 2022), (3, 2023), (3, 2024), (3, 2027);

-- =============================================
-- 11. 验证权限配置
-- =============================================
-- 查看所有工作流权限
SELECT 
    menu_id as '权限ID',
    menu_name as '权限名称',
    parent_id as '父菜单ID',
    perms as '权限标识',
    remark as '备注'
FROM sys_menu 
WHERE menu_id BETWEEN 2001 AND 2099 
ORDER BY parent_id, order_num;

-- 查看角色权限分配情况
SELECT 
    r.role_name as '角色名称',
    COUNT(rm.menu_id) as '权限数量'
FROM sys_role r
LEFT JOIN sys_role_menu rm ON r.role_id = rm.role_id
LEFT JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.menu_id BETWEEN 2001 AND 2099
GROUP BY r.role_id, r.role_name
ORDER BY r.role_id;

-- 提交事务
COMMIT;
