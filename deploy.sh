#!/bin/bash

# 科研成果多维敏捷管控中心 - 自动部署脚本
# 使用方法: ./deploy.sh

echo "=========================================="
echo "科研成果多维敏捷管控中心 - 自动部署脚本"
echo "=========================================="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用sudo运行此脚本"
    exit 1
fi

# 设置变量
APP_NAME="research-management"
APP_DIR="/opt/$APP_NAME"
WEB_DIR="/var/www/$APP_NAME"
JAR_FILE="research-management-1.0.0.jar"
BACKEND_PORT="8989"
FRONTEND_PORT="3000"

echo "开始部署..."

# 1. 创建应用目录
echo "1. 创建应用目录..."
mkdir -p $APP_DIR
mkdir -p $WEB_DIR

# 2. 检查Java环境
echo "2. 检查Java环境..."
if ! command -v java &> /dev/null; then
    echo "Java未安装，正在安装OpenJDK 11..."
    apt update
    apt install -y openjdk-11-jdk
else
    echo "Java已安装: $(java -version 2>&1 | head -n 1)"
fi

# 3. 检查Nginx
echo "3. 检查Nginx..."
if ! command -v nginx &> /dev/null; then
    echo "Nginx未安装，正在安装..."
    apt update
    apt install -y nginx
    systemctl start nginx
    systemctl enable nginx
else
    echo "Nginx已安装"
fi

# 4. 部署后端
echo "4. 部署后端..."
if [ -f "backend/target/$JAR_FILE" ]; then
    cp "backend/target/$JAR_FILE" "$APP_DIR/"
    echo "后端JAR文件已复制到 $APP_DIR"
else
    echo "错误: 找不到后端JAR文件 backend/target/$JAR_FILE"
    echo "请先运行 'cd backend && mvn clean package -DskipTests' 进行打包"
    exit 1
fi

# 5. 创建后端启动脚本
echo "5. 创建后端启动脚本..."
cat > "$APP_DIR/start.sh" << EOF
#!/bin/bash
cd $APP_DIR
nohup java -jar $JAR_FILE > app.log 2>&1 &
echo \$! > app.pid
echo "应用已启动，PID: \$(cat app.pid)"
EOF

cat > "$APP_DIR/stop.sh" << EOF
#!/bin/bash
cd $APP_DIR
if [ -f app.pid ]; then
    PID=\$(cat app.pid)
    kill \$PID
    rm app.pid
    echo "应用已停止"
else
    echo "应用未运行"
fi
EOF

chmod +x "$APP_DIR/start.sh" "$APP_DIR/stop.sh"

# 6. 部署前端
echo "6. 部署前端..."
if [ -d "frontend/dist" ]; then
    cp -r frontend/dist/* "$WEB_DIR/"
    chown -R www-data:www-data "$WEB_DIR"
    chmod -R 755 "$WEB_DIR"
    echo "前端文件已部署到 $WEB_DIR"
else
    echo "错误: 找不到前端构建文件 frontend/dist"
    echo "请先运行 'cd frontend && npm run build' 进行构建"
    exit 1
fi

# 7. 配置Nginx
echo "7. 配置Nginx..."

# 检测Nginx配置文件位置
NGINX_CONF=""
if [ -f "/etc/nginx/nginx.conf" ]; then
    NGINX_CONF="/etc/nginx/nginx.conf"
elif [ -f "/usr/local/nginx/conf/nginx.conf" ]; then
    NGINX_CONF="/usr/local/nginx/conf/nginx.conf"
else
    echo "错误: 找不到Nginx配置文件"
    exit 1
fi

echo "检测到Nginx配置文件: $NGINX_CONF"

# 创建独立的配置文件
NGINX_CONF_DIR=$(dirname "$NGINX_CONF")
cat > "$NGINX_CONF_DIR/research-management.conf" << EOF
server {
    listen $FRONTEND_PORT;
    server_name _;
    root $WEB_DIR;
    index index.html;

    # 处理前端路由
    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# 检查主配置文件是否包含include指令
if ! grep -q "include.*research-management.conf" "$NGINX_CONF"; then
    echo "正在添加配置文件包含指令到主配置文件..."
    # 在http块中添加include指令
    sed -i '/http {/a\    include research-management.conf;' "$NGINX_CONF"
fi

# 测试配置
nginx -t
if [ $? -eq 0 ]; then
    nginx -s reload 2>/dev/null || systemctl reload nginx 2>/dev/null || service nginx reload
    echo "Nginx配置已更新"
else
    echo "Nginx配置测试失败，请检查配置"
    exit 1
fi

# 8. 配置防火墙
echo "8. 配置防火墙..."
if command -v ufw &> /dev/null; then
    ufw allow $FRONTEND_PORT
    ufw allow $BACKEND_PORT
    echo "防火墙规则已添加"
fi

# 9. 启动后端服务
echo "9. 启动后端服务..."
cd "$APP_DIR"
./stop.sh  # 停止可能存在的旧进程
sleep 2
./start.sh

# 10. 验证部署
echo "10. 验证部署..."
sleep 5

# 检查后端
if netstat -tlnp | grep ":$BACKEND_PORT " > /dev/null; then
    echo "✓ 后端服务已启动 (端口 $BACKEND_PORT)"
else
    echo "✗ 后端服务启动失败"
    echo "请检查日志: tail -f $APP_DIR/app.log"
fi

# 检查前端
if netstat -tlnp | grep ":$FRONTEND_PORT " > /dev/null; then
    echo "✓ 前端服务已启动 (端口 $FRONTEND_PORT)"
else
    echo "✗ 前端服务启动失败"
    echo "请检查Nginx状态: systemctl status nginx"
fi

echo "=========================================="
echo "部署完成！"
echo "=========================================="
echo "前端访问地址: http://139.196.179.70:$FRONTEND_PORT"
echo "后端API地址: http://139.196.179.70:$BACKEND_PORT"
echo ""
echo "默认登录账号:"
echo "用户名: admin"
echo "密码: admin123"
echo ""
echo "管理命令:"
echo "启动后端: $APP_DIR/start.sh"
echo "停止后端: $APP_DIR/stop.sh"
echo "查看日志: tail -f $APP_DIR/app.log"
echo "重启Nginx: systemctl restart nginx"
echo "=========================================="
