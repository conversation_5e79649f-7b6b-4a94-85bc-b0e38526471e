<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.research.system.mapper.SysTodoHistoryMapper">
    
    <resultMap type="SysTodoHistory" id="SysTodoHistoryResult">
        <result property="historyId"        column="history_id"            />
        <result property="todoId"           column="todo_id"               />
        <result property="actionType"       column="action_type"           />
        <result property="actionDesc"       column="action_desc"           />
        <result property="operatorId"       column="operator_id"           />
        <result property="operatorName"     column="operator_name"         />
        <result property="oldStatus"        column="old_status"            />
        <result property="newStatus"        column="new_status"            />
        <result property="oldAssigneeId"    column="old_assignee_id"       />
        <result property="newAssigneeId"    column="new_assignee_id"       />
        <result property="processResult"    column="process_result"        />
        <result property="createTime"       column="create_time"           />
        <result property="remark"           column="remark"                />
    </resultMap>

    <sql id="selectTodoHistoryVo">
        select h.history_id, h.todo_id, h.action_type, h.action_desc, 
               h.operator_id, h.operator_name, h.old_status, h.new_status,
               h.old_assignee_id, h.new_assignee_id, h.process_result,
               h.create_time, h.remark
        from sys_todo_history h
    </sql>

    <!-- 查询待办处理历史 -->
    <select id="selectHistoryByTodoId" resultMap="SysTodoHistoryResult">
        <include refid="selectTodoHistoryVo"/>
        where h.todo_id = #{todoId}
        order by h.create_time desc
    </select>

    <!-- 插入处理历史记录 -->
    <insert id="insertTodoHistory" parameterType="SysTodoHistory">
        insert into sys_todo_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="todoId != null">todo_id,</if>
            <if test="actionType != null and actionType != ''">action_type,</if>
            <if test="actionDesc != null and actionDesc != ''">action_desc,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorName != null and operatorName != ''">operator_name,</if>
            <if test="oldStatus != null and oldStatus != ''">old_status,</if>
            <if test="newStatus != null and newStatus != ''">new_status,</if>
            <if test="oldAssigneeId != null">old_assignee_id,</if>
            <if test="newAssigneeId != null">new_assignee_id,</if>
            <if test="processResult != null and processResult != ''">process_result,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="todoId != null">#{todoId},</if>
            <if test="actionType != null and actionType != ''">#{actionType},</if>
            <if test="actionDesc != null and actionDesc != ''">#{actionDesc},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null and operatorName != ''">#{operatorName},</if>
            <if test="oldStatus != null and oldStatus != ''">#{oldStatus},</if>
            <if test="newStatus != null and newStatus != ''">#{newStatus},</if>
            <if test="oldAssigneeId != null">#{oldAssigneeId},</if>
            <if test="newAssigneeId != null">#{newAssigneeId},</if>
            <if test="processResult != null and processResult != ''">#{processResult},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            now()
        </trim>
    </insert>

    <!-- 删除待办的所有历史记录 -->
    <delete id="deleteByTodoId">
        delete from sys_todo_history where todo_id = #{todoId}
    </delete>

    <!-- 查询用户的操作历史 -->
    <select id="selectHistoryByOperator" resultMap="SysTodoHistoryResult">
        <include refid="selectTodoHistoryVo"/>
        where h.operator_id = #{operatorId}
        order by h.create_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

</mapper>
