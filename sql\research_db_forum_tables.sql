-- 科研管理系统龙湖讲坛模块表结构

USE research_db;

-- ===========================================
-- 龙湖讲坛模块表结构
-- ===========================================

-- 讲座表
CREATE TABLE biz_lecture (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    lecture_title VARCHAR(500) COMMENT '讲座标题',
    lecture_code VARCHAR(200) COMMENT '讲座编号',
    speaker_id VARCHAR(32) COMMENT '主讲人ID',
    speaker_name VARCHAR(100) COMMENT '主讲人姓名',
    lecture_date DATE COMMENT '讲座日期',
    start_time TIME COMMENT '开始时间',
    end_time TIME COMMENT '结束时间',
    venue VARCHAR(500) COMMENT '讲座地点',
    max_attendees INT COMMENT '最大参与人数',
    current_attendees INT DEFAULT 0 COMMENT '当前报名人数',
    lecture_status VARCHAR(32) COMMENT '讲座状态',
    lecture_abstract TEXT COMMENT '讲座摘要',
    lecture_content LONGTEXT COMMENT '讲座内容',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    note TEXT COMMENT '备注',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='讲座表';

-- 讲座报名表
CREATE TABLE biz_lecture_registration (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    lecture_id VARCHAR(32) COMMENT '讲座ID',
    user_id VARCHAR(32) COMMENT '用户ID',
    user_name VARCHAR(100) COMMENT '用户姓名',
    user_account VARCHAR(50) COMMENT '用户账号',
    registration_date DATE COMMENT '报名日期',
    registration_time DATETIME COMMENT '报名时间',
    attendance_status VARCHAR(32) COMMENT '出席状态',
    sign_in_time DATETIME COMMENT '签到时间',
    note TEXT COMMENT '备注',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='讲座报名表';

-- 讲坛专家表
CREATE TABLE biz_forum_speaker (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    speaker_name VARCHAR(100) COMMENT '专家姓名',
    speaker_code VARCHAR(100) COMMENT '专家编号',
    speaker_title VARCHAR(100) COMMENT '专家职称',
    speaker_unit VARCHAR(500) COMMENT '专家单位',
    speaker_field VARCHAR(500) COMMENT '专家领域',
    contact_phone VARCHAR(50) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    speaker_status VARCHAR(32) COMMENT '专家状态',
    speaker_type VARCHAR(32) COMMENT '专家类型',
    qualification_file_ids VARCHAR(500) COMMENT '资质文件IDs',
    note TEXT COMMENT '备注',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='讲坛专家表';

-- 媒体资源表
CREATE TABLE biz_media_resource (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    resource_name VARCHAR(500) COMMENT '资源名称',
    resource_type VARCHAR(32) COMMENT '资源类型',
    lecture_id VARCHAR(32) COMMENT '讲座ID',
    file_id VARCHAR(32) COMMENT '文件ID',
    file_name VARCHAR(500) COMMENT '文件名',
    file_size BIGINT COMMENT '文件大小',
    file_path VARCHAR(1000) COMMENT '文件路径',
    duration INT COMMENT '时长（秒）',
    resource_status VARCHAR(32) COMMENT '资源状态',
    view_count INT DEFAULT 0 COMMENT '观看次数',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    note TEXT COMMENT '备注',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='媒体资源表';

-- 讲坛网站表
CREATE TABLE biz_forum_website (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    website_name VARCHAR(500) COMMENT '网站名称',
    website_url VARCHAR(1000) COMMENT '网站URL',
    website_description TEXT COMMENT '网站描述',
    template_id VARCHAR(32) COMMENT '模板ID',
    website_status VARCHAR(32) COMMENT '网站状态',
    note TEXT COMMENT '备注',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='讲坛网站表';

-- 网站内容表
CREATE TABLE biz_website_content (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    website_id VARCHAR(32) COMMENT '网站ID',
    content_title VARCHAR(500) COMMENT '内容标题',
    content_type VARCHAR(32) COMMENT '内容类型',
    content_text LONGTEXT COMMENT '内容文本',
    file_ids VARCHAR(500) COMMENT '文件IDs',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    publish_status VARCHAR(32) COMMENT '发布状态',
    publish_date DATE COMMENT '发布日期',
    note TEXT COMMENT '备注',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='网站内容表';

-- 讲座观看记录表
CREATE TABLE biz_lecture_view_record (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    lecture_id VARCHAR(32) COMMENT '讲座ID',
    user_id VARCHAR(32) COMMENT '用户ID',
    user_name VARCHAR(100) COMMENT '用户姓名',
    media_resource_id VARCHAR(32) COMMENT '媒体资源ID',
    start_time DATETIME COMMENT '开始观看时间',
    end_time DATETIME COMMENT '结束观看时间',
    view_duration INT COMMENT '观看时长（秒）',
    completion_rate DECIMAL(5,2) COMMENT '完成率',
    note TEXT COMMENT '备注',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='讲座观看记录表';