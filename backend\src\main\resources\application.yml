# 项目相关配置
research:
  # 名称
  name: 科研成果多维敏捷管控中心
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2024
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/research/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/research/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

# Swagger配置 - 已移除SpringFox配置，使用SpringDoc OpenAPI 3
# springfox:
#   documentation:
#     swagger-ui:
#       enabled: true
#     enabled: true

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8989
  servlet:
    # 应用的访问路径
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.research: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size:  10MB
       # 设置总上传的文件大小
       max-request-size:  20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # 数据源配置由application-druid.yml提供
  # redis 配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  # Session配置（当Redis可用时启用）
  session:
    store-type: none
    timeout: 1800s

  # MVC配置
  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      add-mappings: false

  # Activiti工作流配置
  activiti:
    # 是否自动部署流程定义文件
    check-process-definitions: false
    # 是否自动创建表
    database-schema-update: true
    # 流程定义文件存放目录
    process-definition-location-prefix: classpath:/processes/
    # 流程定义文件后缀
    process-definition-location-suffixes:
      - .bpmn20.xml
      - .bpmn
    # 历史记录级别
    history-level: full
    # 异步执行器配置
    async-executor-activate: true
    # 作业执行器配置
    job-executor-activate: true

# token配置
token:
    # 令牌自定义标识
    header: Authorization
    # 令牌密钥
    secret: abcdefghijklmnopqrstuvwxyz
    # 令牌有效期（默认30分钟）
    expireTime: 30

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.research.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件 - 已移除，使用MyBatis Plus分页
# pagehelper:
#   helperDialect: mysql
#   supportMethodsArguments: true
#   params: count=countSql

# Swagger配置 - 已移除，不使用API文档
# swagger:
#   # 是否开启swagger
#   enabled: true
#   # 请求前缀
#   pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# Activiti工作流配置已合并到上面的spring配置中
