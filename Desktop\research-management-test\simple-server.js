// 简单的HTTP服务器用于测试横向项目管理功能
const http = require('http');
const url = require('url');

// 模拟数据
const mockData = {
  projects: [
    {
      id: 1,
      projectNo: 'HX2024001',
      projectName: '智能制造系统开发项目',
      projectType: '技术开发',
      partnerName: '华为技术有限公司',
      status: 2,
      totalFund: 2500000,
      receivedFund: 1500000,
      progressPercentage: 75,
      principalName: '张教授',
      deptName: '计算机学院'
    },
    {
      id: 2,
      projectNo: 'HX2024002',
      projectName: '大数据分析平台建设项目',
      projectType: '技术服务',
      partnerName: '清华大学',
      status: 2,
      totalFund: 1800000,
      receivedFund: 900000,
      progressPercentage: 60,
      principalName: '李教授',
      deptName: '软件学院'
    }
  ],
  contracts: [
    {
      id: 1,
      contractNo: 'HT2024001',
      contractName: '智能制造系统开发合同',
      contractType: '技术开发',
      partnerName: '华为技术有限公司',
      contractAmount: 2500000,
      status: 3
    }
  ],
  partners: [
    {
      id: 1,
      partnerCode: 'P001',
      partnerName: '华为技术有限公司',
      partnerType: '企业',
      cooperationLevel: 'A',
      cooperationCount: 5,
      totalContractAmount: 12500000
    }
  ]
};

// 设置CORS头
function setCORSHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
}

// 创建服务器
const server = http.createServer((req, res) => {
  setCORSHeaders(res);
  
  // 处理OPTIONS请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const query = parsedUrl.query;
  
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  
  try {
    // 路由处理
    if (path === '/captchaImage') {
      res.writeHead(200);
      res.end(JSON.stringify({
        code: 200,
        msg: '操作成功',
        data: {
          uuid: 'test-uuid-123',
          img: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
        }
      }));
    } else if (path === '/project/horizontal/list') {
      const pageNum = parseInt(query.pageNum) || 1;
      const pageSize = parseInt(query.pageSize) || 10;
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;
      
      res.writeHead(200);
      res.end(JSON.stringify({
        code: 200,
        msg: '查询成功',
        rows: mockData.projects.slice(start, end),
        total: mockData.projects.length
      }));
    } else if (path === '/project/contract/list') {
      res.writeHead(200);
      res.end(JSON.stringify({
        code: 200,
        msg: '查询成功',
        rows: mockData.contracts,
        total: mockData.contracts.length
      }));
    } else if (path === '/project/partner/list') {
      res.writeHead(200);
      res.end(JSON.stringify({
        code: 200,
        msg: '查询成功',
        rows: mockData.partners,
        total: mockData.partners.length
      }));
    } else if (path === '/project/horizontal/statistics') {
      res.writeHead(200);
      res.end(JSON.stringify({
        code: 200,
        msg: '查询成功',
        data: {
          totalCount: mockData.projects.length,
          totalFund: mockData.projects.reduce((sum, p) => sum + p.totalFund, 0),
          receivedFund: mockData.projects.reduce((sum, p) => sum + p.receivedFund, 0),
          expiringCount: 0
        }
      }));
    } else if (path === '/project/horizontal/generate-no') {
      const year = new Date().getFullYear();
      const count = mockData.projects.length + 1;
      const projectNo = `HX${year}${String(count).padStart(3, '0')}`;
      
      res.writeHead(200);
      res.end(JSON.stringify({
        code: 200,
        msg: '生成成功',
        data: projectNo
      }));
    } else {
      res.writeHead(404);
      res.end(JSON.stringify({
        code: 404,
        msg: '接口不存在'
      }));
    }
  } catch (error) {
    res.writeHead(500);
    res.end(JSON.stringify({
      code: 500,
      msg: '服务器错误: ' + error.message
    }));
  }
});

const port = 8080;
server.listen(port, () => {
  console.log(`🚀 Mock server running at http://localhost:${port}`);
  console.log('📋 Available endpoints:');
  console.log('  GET  /captchaImage');
  console.log('  GET  /project/horizontal/list');
  console.log('  GET  /project/contract/list');
  console.log('  GET  /project/partner/list');
  console.log('  GET  /project/horizontal/statistics');
  console.log('  GET  /project/horizontal/generate-no');
  console.log('\n✅ 横向项目管理功能测试服务器已启动！');
});
