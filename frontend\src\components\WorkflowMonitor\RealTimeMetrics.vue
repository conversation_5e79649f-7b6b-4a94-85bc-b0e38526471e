<template>
  <div class="realtime-metrics">
    <div class="metrics-grid">
      <!-- 流程实例指标 -->
      <div class="metric-item">
        <div class="metric-icon instance-icon">
          <el-icon><Operation /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value" :class="{ 'animate-pulse': isUpdating }">
            {{ metrics.runningInstances || 0 }}
          </div>
          <div class="metric-label">运行中实例</div>
          <div class="metric-trend" :class="getTrendClass('instances')">
            <el-icon><TrendCharts /></el-icon>
            <span>{{ getTrendText('instances') }}</span>
          </div>
        </div>
      </div>

      <!-- 待处理任务指标 -->
      <div class="metric-item">
        <div class="metric-icon task-icon">
          <el-icon><List /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value" :class="{ 'animate-pulse': isUpdating }">
            {{ metrics.activeTasks || 0 }}
          </div>
          <div class="metric-label">待处理任务</div>
          <div class="metric-trend" :class="getTrendClass('tasks')">
            <el-icon><TrendCharts /></el-icon>
            <span>{{ getTrendText('tasks') }}</span>
          </div>
        </div>
      </div>

      <!-- 24小时启动数 -->
      <div class="metric-item">
        <div class="metric-icon start-icon">
          <el-icon><VideoPlay /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value" :class="{ 'animate-pulse': isUpdating }">
            {{ metrics.recentStarts || 0 }}
          </div>
          <div class="metric-label">24h启动数</div>
          <div class="metric-trend" :class="getTrendClass('starts')">
            <el-icon><TrendCharts /></el-icon>
            <span>{{ getTrendText('starts') }}</span>
          </div>
        </div>
      </div>

      <!-- 24小时完成数 -->
      <div class="metric-item">
        <div class="metric-icon complete-icon">
          <el-icon><CircleCheck /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value" :class="{ 'animate-pulse': isUpdating }">
            {{ metrics.recentCompletions || 0 }}
          </div>
          <div class="metric-label">24h完成数</div>
          <div class="metric-trend" :class="getTrendClass('completions')">
            <el-icon><TrendCharts /></el-icon>
            <span>{{ getTrendText('completions') }}</span>
          </div>
        </div>
      </div>

      <!-- 系统健康状态 -->
      <div class="metric-item">
        <div class="metric-icon health-icon" :class="getHealthIconClass()">
          <el-icon>
            <CircleCheck v-if="metrics.systemHealth === 'healthy'" />
            <Warning v-else-if="metrics.systemHealth === 'warning'" />
            <CircleClose v-else />
          </el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value health-status" :class="getHealthClass()">
            {{ getHealthText() }}
          </div>
          <div class="metric-label">系统状态</div>
          <div class="system-resources">
            <div class="resource-item">
              <span>CPU: {{ Math.round(metrics.cpuUsage || 0) }}%</span>
              <div class="resource-bar">
                <div 
                  class="resource-fill cpu-fill" 
                  :style="{ width: (metrics.cpuUsage || 0) + '%' }"
                ></div>
              </div>
            </div>
            <div class="resource-item">
              <span>内存: {{ Math.round(metrics.memoryUsage || 0) }}%</span>
              <div class="resource-bar">
                <div 
                  class="resource-fill memory-fill" 
                  :style="{ width: (metrics.memoryUsage || 0) + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 更新时间 -->
      <div class="metric-item">
        <div class="metric-icon time-icon">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value time-value">
            {{ formatTime(metrics.timestamp) }}
          </div>
          <div class="metric-label">最后更新</div>
          <div class="update-status" :class="{ 'updating': isUpdating }">
            <el-icon><Loading v-if="isUpdating" /><Refresh v-else /></el-icon>
            <span>{{ isUpdating ? '更新中...' : '已同步' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Props {
  metrics: Record<string, any>
  isUpdating?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isUpdating: false
})

// 历史数据用于趋势计算
const previousMetrics = ref<Record<string, any>>({})

// 监听指标变化，保存历史数据
watch(() => props.metrics, (newMetrics) => {
  if (Object.keys(previousMetrics.value).length > 0) {
    // 这里可以计算趋势
  }
  previousMetrics.value = { ...newMetrics }
}, { deep: true })

/** 获取趋势类名 */
const getTrendClass = (type: string) => {
  // 这里可以根据历史数据计算趋势
  // 暂时返回随机趋势用于演示
  const trends = ['trend-up', 'trend-down', 'trend-stable']
  return trends[Math.floor(Math.random() * trends.length)]
}

/** 获取趋势文本 */
const getTrendText = (type: string) => {
  // 这里可以根据历史数据计算趋势
  // 暂时返回随机趋势用于演示
  const trends = ['↗ 上升', '↘ 下降', '→ 稳定']
  return trends[Math.floor(Math.random() * trends.length)]
}

/** 获取健康状态类名 */
const getHealthClass = () => {
  switch (props.metrics.systemHealth) {
    case 'healthy':
      return 'health-good'
    case 'warning':
      return 'health-warning'
    case 'error':
      return 'health-error'
    default:
      return 'health-unknown'
  }
}

/** 获取健康状态图标类名 */
const getHealthIconClass = () => {
  switch (props.metrics.systemHealth) {
    case 'healthy':
      return 'health-good-icon'
    case 'warning':
      return 'health-warning-icon'
    case 'error':
      return 'health-error-icon'
    default:
      return 'health-unknown-icon'
  }
}

/** 获取健康状态文本 */
const getHealthText = () => {
  switch (props.metrics.systemHealth) {
    case 'healthy':
      return '正常'
    case 'warning':
      return '警告'
    case 'error':
      return '异常'
    default:
      return '未知'
  }
}

/** 格式化时间 */
const formatTime = (timestamp: number) => {
  if (!timestamp) return '--:--:--'
  return new Date(timestamp).toLocaleTimeString()
}
</script>

<style scoped>
.realtime-metrics {
  width: 100%;
  height: 100%;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  height: 100%;
}

.metric-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.metric-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.instance-icon {
  background: linear-gradient(45deg, #409eff, #36cfc9);
}

.task-icon {
  background: linear-gradient(45deg, #e6a23c, #ffc53d);
}

.start-icon {
  background: linear-gradient(45deg, #67c23a, #95de64);
}

.complete-icon {
  background: linear-gradient(45deg, #f56c6c, #ff7875);
}

.health-good-icon {
  background: linear-gradient(45deg, #67c23a, #95de64);
}

.health-warning-icon {
  background: linear-gradient(45deg, #e6a23c, #ffc53d);
}

.health-error-icon {
  background: linear-gradient(45deg, #f56c6c, #ff7875);
}

.health-unknown-icon {
  background: linear-gradient(45deg, #909399, #c0c4cc);
}

.time-icon {
  background: linear-gradient(45deg, #909399, #c0c4cc);
}

.metric-content {
  flex: 1;
  min-width: 0;
}

.metric-value {
  font-size: 28px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 5px;
  color: #fff;
  transition: all 0.3s ease;
}

.metric-value.animate-pulse {
  animation: pulse 1s ease-in-out;
}

.health-status {
  font-size: 20px;
}

.health-good {
  color: #67c23a;
}

.health-warning {
  color: #e6a23c;
}

.health-error {
  color: #f56c6c;
}

.health-unknown {
  color: #909399;
}

.time-value {
  font-size: 18px;
  font-family: 'Courier New', monospace;
}

.metric-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.trend-up {
  color: #67c23a;
}

.trend-down {
  color: #f56c6c;
}

.trend-stable {
  color: #e6a23c;
}

.system-resources {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.resource-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.resource-item span {
  min-width: 60px;
  color: rgba(255, 255, 255, 0.8);
}

.resource-bar {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.resource-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.cpu-fill {
  background: linear-gradient(90deg, #67c23a, #95de64);
}

.memory-fill {
  background: linear-gradient(90deg, #409eff, #36cfc9);
}

.update-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  transition: color 0.3s ease;
}

.update-status.updating {
  color: #409eff;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .metric-value {
    font-size: 24px;
  }
}

@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .metric-item {
    padding: 15px;
  }
  
  .metric-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
  
  .metric-value {
    font-size: 20px;
  }
}
</style>
