package com.research.common.core.controller;

// import com.github.pagehelper.PageHelper;
// import com.github.pagehelper.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.research.common.core.domain.AjaxResult;
import com.research.common.core.page.PageDomain;
import com.research.common.core.page.TableDataInfo;
import com.research.common.core.page.TableSupport;
import com.research.common.constant.HttpStatus;
import com.research.common.utils.DateUtils;
import com.research.common.utils.PageUtils;
import com.research.common.utils.StringUtils;
import com.research.common.utils.sql.SqlUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

import java.beans.PropertyEditorSupport;
import java.util.Date;
import java.util.List;

/**
 * web层通用数据处理
 *
 * <AUTHOR>
 */
public class BaseController {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 将前台传递过来的日期格式的字符串，自动转化为Date类型
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                setValue(DateUtils.parseDate(text));
            }
        });
    }

    /**
     * 设置请求分页数据
     */
    protected void startPage() {
        PageUtils.startPage();
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    protected AjaxResult getDataTable(List<?> list) {
        TableDataInfo tableData = new TableDataInfo();
        tableData.setRows(list);
        tableData.setTotal(list.size()); // 简化处理，不使用PageInfo
        return AjaxResult.success("查询成功", tableData);
    }

    /**
     * 响应请求分页数据 (MyBatis Plus IPage)
     */
    protected TableDataInfo getDataTable(IPage<?> page) {
        TableDataInfo tableData = new TableDataInfo();
        tableData.setRows(page.getRecords());
        tableData.setTotal(page.getTotal());
        tableData.setCode(200);
        tableData.setMsg("查询成功");
        return tableData;
    }

    /**
     * 返回成功
     */
    public AjaxResult success() {
        return AjaxResult.success();
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error() {
        return AjaxResult.error();
    }

    /**
     * 返回成功消息
     */
    public AjaxResult success(String message) {
        return AjaxResult.success(message);
    }

    /**
     * 返回成功消息
     */
    public AjaxResult success(Object data) {
        return AjaxResult.success(data);
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error(String message) {
        return AjaxResult.error(message);
    }

    /**
     * 返回警告消息
     */
    public AjaxResult warn(String message) {
        return AjaxResult.warn(message);
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected AjaxResult toAjax(int rows) {
        return rows > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    protected AjaxResult toAjax(boolean result) {
        return result ? success() : error();
    }

    /**
     * 获取用户缓存信息
     */
    public String getUsername() {
        // 这里应该从SecurityContext中获取当前用户名
        // 暂时返回默认值
        return "admin";
    }

    /**
     * 获取用户缓存信息
     */
    public Long getUserId() {
        // 这里应该从SecurityContext中获取当前用户ID
        // 暂时返回默认值
        return 1L;
    }
}
