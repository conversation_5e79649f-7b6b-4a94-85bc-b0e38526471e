<template>
  <div ref="scrollContainer" class="scroll-container" @wheel.prevent="handleScroll">
    <div ref="scrollWrapper" class="scroll-wrapper" :style="{transform: `translateX(${left}px)`}">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'

const emit = defineEmits(['scroll'])

const scrollContainer = ref()
const scrollWrapper = ref()
const left = ref(0)

const handleScroll = (e: WheelEvent) => {
  const eventDelta = e.wheelDelta || -e.deltaY * 40
  const $container = scrollContainer.value
  const $containerWidth = $container.offsetWidth
  const $wrapper = scrollWrapper.value
  const $wrapperWidth = $wrapper.offsetWidth

  if (eventDelta > 0) {
    left.value = Math.min(0, left.value + eventDelta)
  } else {
    if ($containerWidth - eventDelta > $wrapperWidth) {
      left.value = Math.max($containerWidth - $wrapperWidth, left.value + eventDelta)
    } else {
      left.value = Math.max($containerWidth - $wrapperWidth, left.value + eventDelta)
    }
  }
  emit('scroll')
}

onMounted(() => {
  scrollContainer.value.addEventListener('wheel', handleScroll, { passive: false })
})

onBeforeUnmount(() => {
  scrollContainer.value.removeEventListener('wheel', handleScroll)
})
</script>

<style lang="scss" scoped>
.scroll-container {
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  width: 100%;

  .scroll-wrapper {
    position: absolute;
    transition: transform 0.3s ease-in-out;
  }
}
</style>
