package com.research.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.research.system.domain.SysMessage;

import java.util.List;
import java.util.Map;

/**
 * 站内消息Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
public interface ISysMessageService extends IService<SysMessage> {

    /**
     * 查询收件箱消息列表
     * 
     * @param page 分页参数
     * @param message 查询条件
     * @param userId 用户ID
     * @return 收件箱消息列表
     */
    IPage<SysMessage> selectInboxMessageList(Page<SysMessage> page, SysMessage message, Long userId);

    /**
     * 查询发件箱消息列表
     * 
     * @param page 分页参数
     * @param message 查询条件
     * @param userId 用户ID
     * @return 发件箱消息列表
     */
    IPage<SysMessage> selectOutboxMessageList(Page<SysMessage> page, SysMessage message, Long userId);

    /**
     * 查询消息详情
     * 
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 消息详情
     */
    SysMessage selectMessageDetail(Long messageId, Long userId);

    /**
     * 发送消息
     * 
     * @param message 消息内容
     * @return 结果
     */
    boolean sendMessage(SysMessage message);

    /**
     * 发送消息给多个用户
     * 
     * @param message 消息内容
     * @param receiverIds 接收人ID列表
     * @return 结果
     */
    boolean sendMessageToUsers(SysMessage message, List<Long> receiverIds);

    /**
     * 发送消息给部门
     * 
     * @param message 消息内容
     * @param deptIds 部门ID列表
     * @return 结果
     */
    boolean sendMessageToDepts(SysMessage message, List<Long> deptIds);

    /**
     * 回复消息
     * 
     * @param originalMessageId 原消息ID
     * @param replyContent 回复内容
     * @return 结果
     */
    boolean replyMessage(Long originalMessageId, String replyContent);

    /**
     * 转发消息
     * 
     * @param messageId 消息ID
     * @param receiverIds 接收人ID列表
     * @param forwardContent 转发说明
     * @return 结果
     */
    boolean forwardMessage(Long messageId, List<Long> receiverIds, String forwardContent);

    /**
     * 标记消息为已读
     * 
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 结果
     */
    boolean markAsRead(Long messageId, Long userId);

    /**
     * 批量标记消息为已读
     * 
     * @param messageIds 消息ID列表
     * @param userId 用户ID
     * @return 结果
     */
    boolean batchMarkAsRead(List<Long> messageIds, Long userId);

    /**
     * 全部标记为已读
     * 
     * @param userId 用户ID
     * @return 结果
     */
    boolean markAllAsRead(Long userId);

    /**
     * 删除消息
     * 
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 结果
     */
    boolean deleteMessage(Long messageId, Long userId);

    /**
     * 批量删除消息
     * 
     * @param messageIds 消息ID列表
     * @param userId 用户ID
     * @return 结果
     */
    boolean batchDeleteMessage(List<Long> messageIds, Long userId);

    /**
     * 搜索消息
     * 
     * @param page 分页参数
     * @param keyword 搜索关键词
     * @param userId 用户ID
     * @param messageType 消息类型
     * @return 搜索结果
     */
    IPage<SysMessage> searchMessages(Page<SysMessage> page, String keyword, Long userId, String messageType);

    /**
     * 查询用户未读消息数量
     * 
     * @param userId 用户ID
     * @return 未读消息数量
     */
    Long selectUnreadMessageCount(Long userId);

    /**
     * 查询消息统计信息
     * 
     * @param userId 用户ID
     * @return 消息统计信息
     */
    Map<String, Object> selectMessageStatistics(Long userId);

    /**
     * 查询最新消息（用于工作台展示）
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最新消息列表
     */
    List<SysMessage> selectLatestMessages(Long userId, Integer limit);

    /**
     * 发送系统消息
     * 
     * @param title 消息标题
     * @param content 消息内容
     * @param receiverIds 接收人ID列表
     * @return 结果
     */
    boolean sendSystemMessage(String title, String content, List<Long> receiverIds);

    /**
     * 发送通知消息
     * 
     * @param title 消息标题
     * @param content 消息内容
     * @param receiverIds 接收人ID列表
     * @param businessId 关联业务ID
     * @param businessType 关联业务类型
     * @return 结果
     */
    boolean sendNotificationMessage(String title, String content, List<Long> receiverIds, 
                                  String businessId, String businessType);

    /**
     * 发送提醒消息
     * 
     * @param title 消息标题
     * @param content 消息内容
     * @param receiverId 接收人ID
     * @param businessId 关联业务ID
     * @param businessType 关联业务类型
     * @return 结果
     */
    boolean sendReminderMessage(String title, String content, Long receiverId, 
                              String businessId, String businessType);
}
