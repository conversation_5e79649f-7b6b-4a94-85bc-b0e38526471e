package com.research.workflow.domain;

import com.research.common.annotation.Excel;
import com.research.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 流程定义对象 wf_process_definition
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProcessDefinition extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 流程定义ID */
    @Excel(name = "流程定义ID")
    private String processDefinitionId;

    /** 流程定义Key */
    @Excel(name = "流程定义Key")
    private String processKey;

    /** 流程名称 */
    @Excel(name = "流程名称")
    private String processName;

    /** 流程分类 */
    @Excel(name = "流程分类")
    private String category;

    /** 流程版本 */
    @Excel(name = "流程版本")
    private Integer version;

    /** 部署ID */
    @Excel(name = "部署ID")
    private String deploymentId;

    /** 流程定义状态 */
    @Excel(name = "流程定义状态", readConverterExp = "0=草稿,1=已部署,2=已激活,3=已挂起")
    private Integer status;

    /** 是否挂起 */
    @Excel(name = "是否挂起", readConverterExp = "0=否,1=是")
    private Boolean suspended;

    /** 流程描述 */
    @Excel(name = "流程描述")
    private String description;

    /** 流程XML内容 */
    private String processXml;

    /** 流程图片 */
    private String processImage;

    /** 表单Key */
    @Excel(name = "表单Key")
    private String formKey;

    /** 表单名称 */
    @Excel(name = "表单名称")
    private String formName;

    /** 租户ID */
    @Excel(name = "租户ID")
    private String tenantId;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sort;

    /** 是否启用 */
    @Excel(name = "是否启用", readConverterExp = "0=禁用,1=启用")
    private Boolean enabled;

    /** 流程标签 */
    @Excel(name = "流程标签")
    private String tags;

    /** 流程图标 */
    @Excel(name = "流程图标")
    private String icon;

    /** 流程颜色 */
    @Excel(name = "流程颜色")
    private String color;

    /** 使用次数 */
    @Excel(name = "使用次数")
    private Long usageCount;

    /** 最后使用时间 */
    @Excel(name = "最后使用时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date lastUsedTime;

    /** 流程配置JSON */
    private String configJson;

    /** 流程权限配置 */
    private String permissionConfig;

    /** 流程通知配置 */
    private String notificationConfig;

    /** 流程超时配置 */
    private String timeoutConfig;

    /** 是否允许撤回 */
    @Excel(name = "是否允许撤回", readConverterExp = "0=否,1=是")
    private Boolean allowWithdraw;

    /** 是否允许转办 */
    @Excel(name = "是否允许转办", readConverterExp = "0=否,1=是")
    private Boolean allowDelegate;

    /** 是否允许加签 */
    @Excel(name = "是否允许加签", readConverterExp = "0=否,1=是")
    private Boolean allowAddSign;

    /** 是否允许跳转 */
    @Excel(name = "是否允许跳转", readConverterExp = "0=否,1=是")
    private Boolean allowJump;

    /** 是否允许并行 */
    @Excel(name = "是否允许并行", readConverterExp = "0=否,1=是")
    private Boolean allowParallel;

    /** 流程引擎类型 */
    @Excel(name = "流程引擎类型")
    private String engineType;

    /** 流程模板ID */
    @Excel(name = "流程模板ID")
    private Long templateId;

    /** 流程模板名称 */
    @Excel(name = "流程模板名称")
    private String templateName;

    // 手动添加getter和setter方法以解决Lombok编译问题
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Boolean getSuspended() {
        return suspended;
    }

    public void setSuspended(Boolean suspended) {
        this.suspended = suspended;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(String processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }

    public String getProcessKey() {
        return processKey;
    }

    public void setProcessKey(String processKey) {
        this.processKey = processKey;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getDeploymentId() {
        return deploymentId;
    }

    public void setDeploymentId(String deploymentId) {
        this.deploymentId = deploymentId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getProcessXml() {
        return processXml;
    }

    public void setProcessXml(String processXml) {
        this.processXml = processXml;
    }

    public String getFormKey() {
        return formKey;
    }

    public void setFormKey(String formKey) {
        this.formKey = formKey;
    }

    public String getFormName() {
        return formName;
    }

    public void setFormName(String formName) {
        this.formName = formName;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getProcessImage() {
        return processImage;
    }

    public void setProcessImage(String processImage) {
        this.processImage = processImage;
    }
}
