package com.research.workflow.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.research.workflow.domain.WorkflowTask;

import java.util.List;
import java.util.Map;

/**
 * 工作流任务Service接口
 *
 * <AUTHOR>
 */
public interface IWorkflowTaskService {

    /**
     * 查询我的任务列表
     *
     * @param page 分页对象
     * @param task 任务查询条件
     * @param userId 用户ID
     * @return 任务集合
     */
    IPage<WorkflowTask> selectMyTaskList(Page<WorkflowTask> page, WorkflowTask task, Long userId);

    /**
     * 查询候选任务列表
     *
     * @param page 分页对象
     * @param task 任务查询条件
     * @param userId 用户ID
     * @return 任务集合
     */
    IPage<WorkflowTask> selectCandidateTaskList(Page<WorkflowTask> page, WorkflowTask task, Long userId);

    /**
     * 查询历史任务列表
     *
     * @param page 分页对象
     * @param task 任务查询条件
     * @param userId 用户ID
     * @return 任务集合
     */
    IPage<WorkflowTask> selectHistoryTaskList(Page<WorkflowTask> page, WorkflowTask task, Long userId);

    /**
     * 查询任务详细信息
     * 
     * @param taskId 任务ID
     * @return 任务
     */
    WorkflowTask selectTaskById(String taskId);

    /**
     * 签收任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param username 用户名
     */
    void claimTask(String taskId, Long userId, String username);

    /**
     * 完成任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param username 用户名
     * @param comment 评论
     * @param variables 变量
     */
    void completeTask(String taskId, Long userId, String username, String comment, Map<String, Object> variables);

    /**
     * 转办任务
     * 
     * @param taskId 任务ID
     * @param currentUserId 当前用户ID
     * @param currentUsername 当前用户名
     * @param targetUserId 目标用户ID
     */
    void assignTask(String taskId, Long currentUserId, String currentUsername, String targetUserId);

    /**
     * 委派任务
     * 
     * @param taskId 任务ID
     * @param currentUserId 当前用户ID
     * @param currentUsername 当前用户名
     * @param targetUserId 目标用户ID
     */
    void delegateTask(String taskId, Long currentUserId, String currentUsername, String targetUserId);

    /**
     * 归还任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param username 用户名
     */
    void resolveTask(String taskId, Long userId, String username);

    /**
     * 获取任务变量
     * 
     * @param taskId 任务ID
     * @return 变量集合
     */
    Map<String, Object> getTaskVariables(String taskId);

    /**
     * 设置任务变量
     * 
     * @param taskId 任务ID
     * @param variables 变量集合
     */
    void setTaskVariables(String taskId, Map<String, Object> variables);

    /**
     * 挂起任务
     * 
     * @param taskId 任务ID
     */
    void suspendTask(String taskId);

    /**
     * 激活任务
     * 
     * @param taskId 任务ID
     */
    void activateTask(String taskId);

    /**
     * 删除任务
     * 
     * @param taskId 任务ID
     * @param reason 删除原因
     */
    void deleteTask(String taskId, String reason);

    /**
     * 获取任务表单数据
     * 
     * @param taskId 任务ID
     * @return 表单数据
     */
    Map<String, Object> getTaskFormData(String taskId);

    /**
     * 提交任务表单
     * 
     * @param taskId 任务ID
     * @param formData 表单数据
     * @param userId 用户ID
     * @param username 用户名
     */
    void submitTaskForm(String taskId, Map<String, Object> formData, Long userId, String username);

    /**
     * 获取任务候选用户
     * 
     * @param taskId 任务ID
     * @return 候选用户列表
     */
    List<Map<String, Object>> getTaskCandidateUsers(String taskId);

    /**
     * 获取任务候选组
     * 
     * @param taskId 任务ID
     * @return 候选组列表
     */
    List<Map<String, Object>> getTaskCandidateGroups(String taskId);

    /**
     * 添加任务候选用户
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     */
    void addTaskCandidateUser(String taskId, String userId);

    /**
     * 删除任务候选用户
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     */
    void deleteTaskCandidateUser(String taskId, String userId);

    /**
     * 添加任务候选组
     * 
     * @param taskId 任务ID
     * @param groupId 组ID
     */
    void addTaskCandidateGroup(String taskId, String groupId);

    /**
     * 删除任务候选组
     * 
     * @param taskId 任务ID
     * @param groupId 组ID
     */
    void deleteTaskCandidateGroup(String taskId, String groupId);

    /**
     * 获取任务统计数据
     * 
     * @param userId 用户ID
     * @return 统计数据
     */
    Map<String, Object> getTaskStatistics(Long userId);

    /**
     * 获取任务趋势数据
     * 
     * @param userId 用户ID
     * @param days 天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getTaskTrendData(Long userId, int days);

    /**
     * 批量处理任务
     * 
     * @param taskIds 任务ID列表
     * @param action 操作类型
     * @param userId 用户ID
     * @param username 用户名
     * @param params 参数
     */
    void batchProcessTasks(List<String> taskIds, String action, Long userId, String username, Map<String, Object> params);
}
