version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: research-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: research_db
      MYSQL_USER: research
      MYSQL_PASSWORD: research123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ../sql:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - research-network

  # Redis缓存
  redis:
    image: redis:6.2-alpine
    container_name: research-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - research-network

  # 应用服务
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: research-app
    restart: always
    ports:
      - "80:80"
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=*************************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=research
      - SPRING_DATASOURCE_PASSWORD=research123
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
    depends_on:
      - mysql
      - redis
    networks:
      - research-network

volumes:
  mysql_data:
  redis_data:

networks:
  research-network:
    driver: bridge
