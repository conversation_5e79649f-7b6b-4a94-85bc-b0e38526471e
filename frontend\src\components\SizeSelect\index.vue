<template>
  <el-dropdown trigger="click" @command="handleSetSize">
    <div>
      <!-- <el-icon>
        <Operation />
      </el-icon> -->
      <span style="font-size: 16px;">⚙️</span>
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item :disabled="size === 'default'" command="default">默认</el-dropdown-item>
        <el-dropdown-item :disabled="size === 'large'" command="large">大型</el-dropdown-item>
        <el-dropdown-item :disabled="size === 'small'" command="small">小型</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
// import { Operation } from '@element-plus/icons-vue'
import { useAppStore } from '@/store/modules/app'

const appStore = useAppStore()

const size = computed(() => appStore.size)

const handleSetSize = (size: string) => {
  appStore.setSize(size)
  ElMessage({
    message: '切换成功',
    type: 'success'
  })
}
</script>
