#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
横向项目管理功能数据库测试脚本
测试数据库连接和数据查询功能
"""

import pymysql
import json
from datetime import datetime
import sys

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'root',
    'database': 'research_db',
    'charset': 'utf8mb4'
}

def test_database_connection():
    """测试数据库连接"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ 数据库连接成功！")
        return connection
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def test_table_structure(connection):
    """测试表结构"""
    print("\n📋 检查表结构...")
    
    tables = [
        'horizontal_project',
        'contract', 
        'partner',
        'contract_template',
        'horizontal_project_member',
        'partner_qualification',
        'partner_evaluation',
        'contract_change'
    ]
    
    cursor = connection.cursor()
    
    for table in tables:
        try:
            cursor.execute(f"DESCRIBE {table}")
            columns = cursor.fetchall()
            print(f"✅ 表 {table}: {len(columns)} 个字段")
        except Exception as e:
            print(f"❌ 表 {table} 不存在: {e}")
    
    cursor.close()

def test_data_query(connection):
    """测试数据查询"""
    print("\n📊 测试数据查询...")
    
    cursor = connection.cursor(pymysql.cursors.DictCursor)
    
    # 测试横向项目数据
    try:
        cursor.execute("SELECT COUNT(*) as count FROM horizontal_project")
        result = cursor.fetchone()
        print(f"✅ 横向项目数据: {result['count']} 条记录")
        
        if result['count'] > 0:
            cursor.execute("""
                SELECT project_no, project_name, project_type, status, 
                       total_fund, received_fund, principal_name, dept_name
                FROM horizontal_project 
                LIMIT 3
            """)
            projects = cursor.fetchall()
            print("📋 项目示例数据:")
            for project in projects:
                print(f"   - {project['project_no']}: {project['project_name']} ({project['project_type']})")
                print(f"     状态: {project['status']}, 经费: {project['total_fund']}, 负责人: {project['principal_name']}")
    except Exception as e:
        print(f"❌ 横向项目查询失败: {e}")
    
    # 测试合同数据
    try:
        cursor.execute("SELECT COUNT(*) as count FROM contract")
        result = cursor.fetchone()
        print(f"✅ 合同数据: {result['count']} 条记录")
        
        if result['count'] > 0:
            cursor.execute("""
                SELECT contract_no, contract_name, contract_type, status,
                       contract_amount, partner_name, principal_name
                FROM contract 
                LIMIT 3
            """)
            contracts = cursor.fetchall()
            print("📋 合同示例数据:")
            for contract in contracts:
                print(f"   - {contract['contract_no']}: {contract['contract_name']} ({contract['contract_type']})")
                print(f"     状态: {contract['status']}, 金额: {contract['contract_amount']}, 合作单位: {contract['partner_name']}")
    except Exception as e:
        print(f"❌ 合同查询失败: {e}")
    
    # 测试合作单位数据
    try:
        cursor.execute("SELECT COUNT(*) as count FROM partner")
        result = cursor.fetchone()
        print(f"✅ 合作单位数据: {result['count']} 条记录")
        
        if result['count'] > 0:
            cursor.execute("""
                SELECT partner_code, partner_name, partner_type, cooperation_level,
                       cooperation_count, total_contract_amount
                FROM partner 
                LIMIT 3
            """)
            partners = cursor.fetchall()
            print("📋 合作单位示例数据:")
            for partner in partners:
                print(f"   - {partner['partner_code']}: {partner['partner_name']} ({partner['partner_type']})")
                print(f"     等级: {partner['cooperation_level']}, 合作次数: {partner['cooperation_count']}")
    except Exception as e:
        print(f"❌ 合作单位查询失败: {e}")
    
    cursor.close()

def test_statistics_query(connection):
    """测试统计查询"""
    print("\n📈 测试统计查询...")
    
    cursor = connection.cursor(pymysql.cursors.DictCursor)
    
    try:
        # 项目状态统计
        cursor.execute("""
            SELECT status, COUNT(*) as count, SUM(total_fund) as total_fund
            FROM horizontal_project 
            GROUP BY status
        """)
        status_stats = cursor.fetchall()
        print("📊 项目状态统计:")
        status_names = {0: '申请中', 1: '立项', 2: '执行中', 3: '变更中', 4: '结项中', 5: '已结项', 6: '已撤销'}
        for stat in status_stats:
            status_name = status_names.get(stat['status'], f"状态{stat['status']}")
            print(f"   - {status_name}: {stat['count']} 个项目, 总经费: {stat['total_fund'] or 0} 元")
        
        # 项目类型统计
        cursor.execute("""
            SELECT project_type, COUNT(*) as count, SUM(total_fund) as total_fund
            FROM horizontal_project 
            GROUP BY project_type
        """)
        type_stats = cursor.fetchall()
        print("📊 项目类型统计:")
        for stat in type_stats:
            print(f"   - {stat['project_type']}: {stat['count']} 个项目, 总经费: {stat['total_fund'] or 0} 元")
        
        # 合作单位等级统计
        cursor.execute("""
            SELECT cooperation_level, COUNT(*) as count, SUM(total_contract_amount) as total_amount
            FROM partner 
            GROUP BY cooperation_level
        """)
        level_stats = cursor.fetchall()
        print("📊 合作单位等级统计:")
        for stat in level_stats:
            print(f"   - {stat['cooperation_level']}级: {stat['count']} 个单位, 累计金额: {stat['total_amount'] or 0} 元")
        
    except Exception as e:
        print(f"❌ 统计查询失败: {e}")
    
    cursor.close()

def test_complex_query(connection):
    """测试复杂查询"""
    print("\n🔍 测试复杂查询...")
    
    cursor = connection.cursor(pymysql.cursors.DictCursor)
    
    try:
        # 项目和合同关联查询
        cursor.execute("""
            SELECT hp.project_no, hp.project_name, hp.total_fund,
                   c.contract_no, c.contract_amount, c.signing_date,
                   p.partner_name, p.cooperation_level
            FROM horizontal_project hp
            LEFT JOIN contract c ON hp.contract_id = c.id
            LEFT JOIN partner p ON hp.partner_id = p.id
            LIMIT 5
        """)
        results = cursor.fetchall()
        print("🔗 项目-合同-合作单位关联查询:")
        for result in results:
            print(f"   - 项目: {result['project_name']}")
            print(f"     合同: {result['contract_no'] or '无'}, 合作单位: {result['partner_name'] or '无'}")
        
        # 经费到账率计算
        cursor.execute("""
            SELECT project_no, project_name, total_fund, received_fund,
                   CASE 
                       WHEN total_fund > 0 THEN ROUND((received_fund / total_fund) * 100, 2)
                       ELSE 0 
                   END as fund_rate
            FROM horizontal_project
            WHERE total_fund > 0
            ORDER BY fund_rate DESC
            LIMIT 5
        """)
        fund_rates = cursor.fetchall()
        print("💰 经费到账率排行:")
        for rate in fund_rates:
            print(f"   - {rate['project_name']}: {rate['fund_rate']}% ({rate['received_fund']}/{rate['total_fund']})")
        
    except Exception as e:
        print(f"❌ 复杂查询失败: {e}")
    
    cursor.close()

def generate_test_report(connection):
    """生成测试报告"""
    print("\n📄 生成测试报告...")
    
    cursor = connection.cursor(pymysql.cursors.DictCursor)
    
    report = {
        "test_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "database_status": "connected",
        "tables": {},
        "data_summary": {},
        "test_results": "success"
    }
    
    # 统计各表数据量
    tables = ['horizontal_project', 'contract', 'partner', 'contract_template', 
              'horizontal_project_member', 'partner_qualification', 'partner_evaluation', 'contract_change']
    
    for table in tables:
        try:
            cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
            result = cursor.fetchone()
            report["tables"][table] = result['count']
        except:
            report["tables"][table] = 0
    
    # 数据汇总
    try:
        cursor.execute("SELECT SUM(total_fund) as total_fund FROM horizontal_project")
        result = cursor.fetchone()
        report["data_summary"]["total_project_fund"] = float(result['total_fund'] or 0)
        
        cursor.execute("SELECT SUM(contract_amount) as total_amount FROM contract")
        result = cursor.fetchone()
        report["data_summary"]["total_contract_amount"] = float(result['total_amount'] or 0)
        
        cursor.execute("SELECT AVG(cooperation_count) as avg_cooperation FROM partner")
        result = cursor.fetchone()
        report["data_summary"]["avg_cooperation_count"] = float(result['avg_cooperation'] or 0)
        
    except Exception as e:
        report["test_results"] = f"error: {e}"
    
    cursor.close()
    
    # 保存报告
    with open('Desktop/research-management-test/test_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print("✅ 测试报告已保存到 test_report.json")
    return report

def main():
    """主函数"""
    print("🚀 开始横向项目管理功能数据库测试")
    print("=" * 50)
    
    # 连接数据库
    connection = test_database_connection()
    if not connection:
        print("❌ 无法连接数据库，测试终止")
        sys.exit(1)
    
    try:
        # 执行各项测试
        test_table_structure(connection)
        test_data_query(connection)
        test_statistics_query(connection)
        test_complex_query(connection)
        
        # 生成测试报告
        report = generate_test_report(connection)
        
        print("\n" + "=" * 50)
        print("🎉 数据库测试完成！")
        print(f"📊 测试结果: {report['test_results']}")
        print(f"📋 项目数量: {report['tables'].get('horizontal_project', 0)}")
        print(f"📋 合同数量: {report['tables'].get('contract', 0)}")
        print(f"📋 合作单位数量: {report['tables'].get('partner', 0)}")
        print(f"💰 项目总经费: {report['data_summary'].get('total_project_fund', 0):,.2f} 元")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    finally:
        connection.close()
        print("🔒 数据库连接已关闭")

if __name__ == "__main__":
    main()
