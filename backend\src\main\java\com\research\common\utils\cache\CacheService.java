package com.research.common.utils.cache;

import com.research.common.utils.redis.MemoryCache;
import com.research.common.utils.redis.RedisCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 统一缓存服务
 * 优先使用Redis，如果Redis不可用则使用内存缓存
 *
 * <AUTHOR>
 */
@Component
public class CacheService {

    @Autowired(required = false)
    private RedisCache redisCache;

    @Autowired(required = false)
    private MemoryCache memoryCache;

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key 缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value) {
        if (redisCache != null) {
            redisCache.setCacheObject(key, value);
        } else if (memoryCache != null) {
            memoryCache.setCacheObject(key, value);
        }
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key 缓存的键值
     * @param value 缓存的值
     * @param timeout 时间
     * @param timeUnit 时间颗粒度
     */
    public <T> void setCacheObject(final String key, final T value, final Integer timeout, final TimeUnit timeUnit) {
        if (redisCache != null) {
            redisCache.setCacheObject(key, value, timeout, timeUnit);
        } else if (memoryCache != null) {
            memoryCache.setCacheObject(key, value, timeout, timeUnit);
        }
    }

    /**
     * 设置有效时间
     *
     * @param key 缓存键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout) {
        if (redisCache != null) {
            return redisCache.expire(key, timeout);
        } else if (memoryCache != null) {
            return memoryCache.expire(key, timeout);
        }
        return false;
    }

    /**
     * 设置有效时间
     *
     * @param key 缓存键
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout, final TimeUnit unit) {
        if (redisCache != null) {
            return redisCache.expire(key, timeout, unit);
        } else if (memoryCache != null) {
            return memoryCache.expire(key, timeout, unit);
        }
        return false;
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    public <T> T getCacheObject(final String key) {
        if (redisCache != null) {
            return redisCache.getCacheObject(key);
        } else if (memoryCache != null) {
            return memoryCache.getCacheObject(key);
        }
        return null;
    }

    /**
     * 删除单个对象
     *
     * @param key 缓存键
     */
    public boolean deleteObject(final String key) {
        if (redisCache != null) {
            return redisCache.deleteObject(key);
        } else if (memoryCache != null) {
            return memoryCache.deleteObject(key);
        }
        return false;
    }

    /**
     * 判断key是否存在
     *
     * @param key 缓存键
     * @return true=存在；false=不存在
     */
    public boolean hasKey(final String key) {
        if (redisCache != null) {
            return redisCache.hasKey(key);
        } else if (memoryCache != null) {
            return memoryCache.hasKey(key);
        }
        return false;
    }

    /**
     * 获取剩余过期时间
     *
     * @param key 缓存键
     * @return 剩余时间（秒）
     */
    public long getExpire(final String key) {
        if (redisCache != null) {
            return redisCache.getExpire(key);
        } else if (memoryCache != null) {
            return memoryCache.getExpire(key);
        }
        return -1;
    }

    /**
     * 获取当前使用的缓存类型
     *
     * @return 缓存类型
     */
    public String getCacheType() {
        if (redisCache != null) {
            return "Redis";
        } else if (memoryCache != null) {
            return "Memory";
        }
        return "None";
    }
}
