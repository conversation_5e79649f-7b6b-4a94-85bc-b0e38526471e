<template>
  <div class="app-container">
    <div class="page-content">
      <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="部门名称" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入部门名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="部门状态" clearable>
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          class="top-action-btn"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          icon="Sort"
          @click="toggleExpandAll"
          class="top-action-btn"
        >展开/折叠</el-button>
      </el-col>
      <!-- 右侧工具栏 -->
      <el-col :span="1.5">
        <el-button icon="Refresh" @click="getList">刷新</el-button>
      </el-col>
    </el-row>

    <!-- 部门表格 -->
    <div class="table-container">
      <el-table
        v-if="refreshTable"
        v-loading="loading"
        :data="deptList"
        row-key="deptId"
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
      <el-table-column prop="deptName" label="部门名称" min-width="200" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="orderNum" label="排序" align="center" min-width="80"></el-table-column>
      <el-table-column prop="status" label="状态" align="center" min-width="80">
        <template #default="scope">
          <el-tag v-if="scope.row.status === '0'" type="success">正常</el-tag>
          <el-tag v-else-if="scope.row.status === '1'" type="danger">停用</el-tag>
          <span v-else>{{ scope.row.status }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="scope">
          <div class="action-buttons">
            <el-button type="primary" size="small" class="table-action-btn" @click="handleUpdate(scope.row)">
              修改
            </el-button>
            <el-button type="success" size="small" class="table-action-btn" @click="handleAdd(scope.row)">
              新增
            </el-button>
            <el-button
              v-if="scope.row.parentId != 0"
              type="danger"
              size="small"
              class="table-action-btn"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </div>
        </template>
      </el-table-column>
      </el-table>
    </div>

    <!-- 添加或修改部门对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form :model="form" :rules="rules" ref="deptRef" label-width="80px">
        <el-row>
          <el-col :span="24" v-if="form.parentId !== 0">
            <el-form-item label="上级部门" prop="parentId">
              <el-tree-select
                v-model="form.parentId"
                :data="deptOptions"
                :props="{ value: 'deptId', label: 'deptName', children: 'children' }"
                value-key="deptId"
                placeholder="选择上级部门"
                check-strictly
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门名称" prop="deptName">
              <el-input v-model="form.deptName" placeholder="请输入部门名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="leader">
              <el-input v-model="form.leader" placeholder="请输入负责人" maxlength="20" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门状态">
              <el-radio-group v-model="form.status">
                <el-radio label="0">正常</el-radio>
                <el-radio label="1">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { listDept, getDept, delDept, addDept, updateDept, listDeptExcludeChild } from '@/api/system/dept'
import RightToolbar from '@/components/RightToolbar/index.vue'

// 遮罩层
const loading = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 部门表格树数据
const deptList = ref([])
// 部门树选项
const deptOptions = ref([])
// 弹出层标题
const title = ref('')
// 是否显示弹出层
const open = ref(false)
// 是否展开，默认全部展开
const isExpandAll = ref(true)
// 重新渲染表格状态
const refreshTable = ref(true)
// 查询参数
const queryParams = ref({
  deptName: '',
  status: ''
})

// 表单参数
const form = ref({})

// 表单校验
const rules = reactive({
  parentId: [
    { required: true, message: '上级部门不能为空', trigger: 'blur' }
  ],
  deptName: [
    { required: true, message: '部门名称不能为空', trigger: 'blur' }
  ],
  orderNum: [
    { required: true, message: '显示排序不能为空', trigger: 'blur' }
  ],
  email: [
    {
      type: 'email',
      message: '请输入正确的邮箱地址',
      trigger: ['blur', 'change']
    }
  ],
  phone: [
    {
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: '请输入正确的11位手机号码',
      trigger: 'blur'
    }
  ]
})

// 字典数据
const sys_normal_disable = ref([
  { label: '正常', value: '0', elTagType: 'primary' },
  { label: '停用', value: '1', elTagType: 'danger' }
])

/** 查询部门列表 */
function getList() {
  loading.value = true
  listDept(queryParams.value).then((response: any) => {
    console.log('部门列表响应:', response)
    deptList.value = handleTree(response, 'deptId', 'parentId')
    loading.value = false
  }).catch((error: any) => {
    console.error('获取部门列表失败:', error)
    deptList.value = []
    loading.value = false
  })
}

/** 转换部门数据结构 */
function handleTree(data: any[], id: string, parentId: string, children?: string) {
  const config = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children'
  }

  const childrenListMap: any = {}
  const nodeIds: any = {}
  const tree = []

  for (const d of data) {
    const parentId = d[config.parentId]
    if (childrenListMap[parentId] == null) {
      childrenListMap[parentId] = []
    }
    nodeIds[d[config.id]] = d
    childrenListMap[parentId].push(d)
  }

  for (const d of data) {
    const parentId = d[config.parentId]
    if (nodeIds[parentId] == null) {
      tree.push(d)
    }
  }

  for (const t of tree) {
    adaptToChildrenList(t)
  }

  function adaptToChildrenList(o: any) {
    if (childrenListMap[o[config.id]] !== null) {
      o[config.childrenList] = childrenListMap[o[config.id]]
    }
    if (o[config.childrenList]) {
      for (const c of o[config.childrenList]) {
        adaptToChildrenList(c)
      }
    }
  }
  return tree
}

/** 搜索按钮操作 */
function handleQuery() {
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.value = {
    deptName: '',
    status: ''
  }
  handleQuery()
}

/** 新增按钮操作 */
function handleAdd(row?: any) {
  reset()
  getTreeselect()
  if (row != null && row.deptId) {
    form.value.parentId = row.deptId
  } else {
    form.value.parentId = 0
  }
  open.value = true
  title.value = '添加部门'
}

/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false
  isExpandAll.value = !isExpandAll.value
  nextTick(() => {
    refreshTable.value = true
  })
}

/** 修改按钮操作 */
function handleUpdate(row: any) {
  reset()
  getTreeselect()
  getDept(row.deptId).then((response: any) => {
    console.log('部门详情响应:', response)
    form.value = response
    open.value = true
    title.value = '修改部门'
  }).catch((error: any) => {
    console.error('获取部门详情失败:', error)
    ElMessage.error('获取部门信息失败')
  })
}

/** 提交按钮 */
function submitForm() {
  // 验证必填字段
  if (!form.value.deptName) {
    ElMessage.error('部门名称不能为空')
    return
  }
  if (form.value.orderNum === undefined || form.value.orderNum === null) {
    ElMessage.error('显示排序不能为空')
    return
  }

  console.log('提交部门数据:', form.value)

  if (form.value.deptId != undefined) {
    updateDept(form.value).then(() => {
      ElMessage.success('修改成功')
      open.value = false
      getList()
    }).catch((error: any) => {
      console.error('修改部门失败:', error)
      ElMessage.error('修改失败')
    })
  } else {
    addDept(form.value).then(() => {
      ElMessage.success('新增成功')
      open.value = false
      getList()
    }).catch((error: any) => {
      console.error('新增部门失败:', error)
      ElMessage.error('新增失败')
    })
  }
}

/** 删除按钮操作 */
function handleDelete(row: any) {
  ElMessageBox.confirm(`是否确认删除名为"${row.deptName}"的数据项？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    console.log('删除部门ID:', row.deptId)
    return delDept(row.deptId)
  }).then(() => {
    getList()
    ElMessage.success('删除成功')
  }).catch((error: any) => {
    if (error !== 'cancel') {
      console.error('删除部门失败:', error)
      ElMessage.error('删除失败')
    }
  })
}

/** 查询部门下拉树结构 */
function getTreeselect() {
  listDept({}).then((response: any) => {
    console.log('部门树响应:', response)
    deptOptions.value = []
    const dept = { deptId: 0, deptName: '主类目', children: [] }
    dept.children = handleTree(response, 'deptId', 'parentId')
    deptOptions.value.push(dept)
  }).catch((error: any) => {
    console.error('获取部门树失败:', error)
    deptOptions.value = []
  })
}

// 表单重置
function reset() {
  form.value = {
    deptId: undefined,
    parentId: undefined,
    deptName: '',
    orderNum: 0,
    leader: '',
    phone: '',
    email: '',
    status: '0'
  }
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 时间格式化
function parseTime(time: any, pattern?: string) {
  if (!time) return ''
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  const date = new Date(time)
  const formatObj: any = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

onMounted(() => {
  getList()
})
</script>

<script lang="ts">
// DictTag 组件
export default {
  components: {
    DictTag: {
      props: ['options', 'value'],
      template: `
        <el-tag :type="getTagType(value)">
          {{ getLabel(value) }}
        </el-tag>
      `,
      methods: {
        getLabel(value: string) {
          const option = this.options.find((item: any) => item.value === value)
          return option ? option.label : value
        },
        getTagType(value: string) {
          const option = this.options.find((item: any) => item.value === value)
          return option ? option.elTagType : 'primary'
        }
      }
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
