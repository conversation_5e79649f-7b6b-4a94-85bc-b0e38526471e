package com.research.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.research.system.domain.SysTodo;
import com.research.system.domain.SysTodoHistory;

import java.util.List;
import java.util.Map;

/**
 * 待办事项Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
public interface ISysTodoService extends IService<SysTodo> {

    /**
     * 查询待办事项列表
     * 
     * @param page 分页参数
     * @param todo 查询条件
     * @return 待办事项列表
     */
    IPage<SysTodo> selectTodoList(Page<SysTodo> page, SysTodo todo);

    /**
     * 查询我的待办事项列表
     * 
     * @param page 分页参数
     * @param todo 查询条件
     * @param userId 用户ID
     * @return 我的待办事项列表
     */
    IPage<SysTodo> selectMyTodoList(Page<SysTodo> page, SysTodo todo, Long userId);

    /**
     * 查询我创建的待办事项列表
     * 
     * @param page 分页参数
     * @param todo 查询条件
     * @param userId 用户ID
     * @return 我创建的待办事项列表
     */
    IPage<SysTodo> selectMyCreatedTodoList(Page<SysTodo> page, SysTodo todo, Long userId);

    /**
     * 查询待办事项详情
     * 
     * @param todoId 待办ID
     * @return 待办事项详情
     */
    SysTodo selectTodoDetail(Long todoId);

    /**
     * 新增待办事项
     * 
     * @param todo 待办事项
     * @return 结果
     */
    boolean insertTodo(SysTodo todo);

    /**
     * 修改待办事项
     * 
     * @param todo 待办事项
     * @return 结果
     */
    boolean updateTodo(SysTodo todo);

    /**
     * 删除待办事项
     * 
     * @param todoIds 需要删除的待办ID
     * @return 结果
     */
    boolean deleteTodoByIds(Long[] todoIds);

    /**
     * 分配待办事项
     * 
     * @param todoId 待办ID
     * @param assigneeId 负责人ID
     * @param assigneeName 负责人姓名
     * @return 结果
     */
    boolean assignTodo(Long todoId, Long assigneeId, String assigneeName);

    /**
     * 处理待办事项
     * 
     * @param todoId 待办ID
     * @param status 新状态
     * @param result 处理结果
     * @return 结果
     */
    boolean processTodo(Long todoId, String status, String result);

    /**
     * 完成待办事项
     * 
     * @param todoId 待办ID
     * @param result 完成结果
     * @return 结果
     */
    boolean completeTodo(Long todoId, String result);

    /**
     * 取消待办事项
     * 
     * @param todoId 待办ID
     * @param reason 取消原因
     * @return 结果
     */
    boolean cancelTodo(Long todoId, String reason);

    /**
     * 重新打开待办事项
     * 
     * @param todoId 待办ID
     * @param reason 重新打开原因
     * @return 结果
     */
    boolean reopenTodo(Long todoId, String reason);

    /**
     * 标记待办为已读
     * 
     * @param todoId 待办ID
     * @param userId 用户ID
     * @return 结果
     */
    boolean markAsRead(Long todoId, Long userId);

    /**
     * 批量更新待办状态
     * 
     * @param todoIds 待办ID列表
     * @param status 新状态
     * @return 结果
     */
    boolean batchUpdateStatus(List<Long> todoIds, String status);

    /**
     * 查询用户待办事项统计
     * 
     * @param userId 用户ID
     * @return 待办事项统计
     */
    Map<String, Object> selectTodoStatistics(Long userId);

    /**
     * 查询最近待办事项（用于工作台展示）
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近待办事项列表
     */
    List<SysTodo> selectRecentTodos(Long userId, Integer limit);

    /**
     * 查询即将到期的待办事项
     * 
     * @param userId 用户ID
     * @param days 天数
     * @return 即将到期的待办事项列表
     */
    List<SysTodo> selectDueSoonTodos(Long userId, Integer days);

    /**
     * 查询逾期的待办事项
     * 
     * @param userId 用户ID
     * @return 逾期的待办事项列表
     */
    List<SysTodo> selectOverdueTodos(Long userId);

    /**
     * 查询待办处理历史
     * 
     * @param todoId 待办ID
     * @return 处理历史列表
     */
    List<SysTodoHistory> selectTodoHistory(Long todoId);

    /**
     * 查询待办事项按优先级统计
     * 
     * @param userId 用户ID
     * @return 优先级统计
     */
    List<Map<String, Object>> selectTodoByPriorityStats(Long userId);

    /**
     * 查询待办事项按状态统计
     * 
     * @param userId 用户ID
     * @return 状态统计
     */
    List<Map<String, Object>> selectTodoByStatusStats(Long userId);

    /**
     * 发送待办提醒
     * 
     * @param todoId 待办ID
     * @return 结果
     */
    boolean sendTodoReminder(Long todoId);

    /**
     * 批量发送待办提醒
     * 
     * @param todoIds 待办ID列表
     * @return 结果
     */
    boolean batchSendTodoReminder(List<Long> todoIds);
}
