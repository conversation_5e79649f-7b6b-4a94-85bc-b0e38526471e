package com.research.workflow.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.research.workflow.domain.WorkflowTask;
import com.research.workflow.service.IWorkflowTaskService;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作流任务Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class WorkflowTaskServiceImpl implements IWorkflowTaskService {

    private static final Logger logger = LoggerFactory.getLogger(WorkflowTaskServiceImpl.class);

    @Autowired
    private TaskService taskService;

    @Autowired
    private org.activiti.engine.RepositoryService repositoryService;

    @Override
    public IPage<WorkflowTask> selectMyTaskList(Page<WorkflowTask> page, WorkflowTask task, Long userId) {
        try {
            logger.info("查询用户任务列表，用户ID: {}, 页码: {}, 页大小: {}", userId, page.getCurrent(), page.getSize());

            // 构建任务查询
            TaskQuery taskQuery = taskService.createTaskQuery();

            // 查询分配给当前用户的任务
            taskQuery.taskAssignee(userId.toString());

            // 添加查询条件
            if (task != null) {
                if (task.getName() != null && !task.getName().isEmpty()) {
                    taskQuery.taskNameLike("%" + task.getName() + "%");
                }
                if (task.getProcessDefinitionKey() != null && !task.getProcessDefinitionKey().isEmpty()) {
                    taskQuery.processDefinitionKeyLike("%" + task.getProcessDefinitionKey() + "%");
                }
                if (task.getBusinessKey() != null && !task.getBusinessKey().isEmpty()) {
                    taskQuery.processInstanceBusinessKeyLike("%" + task.getBusinessKey() + "%");
                }
            }

            // 排序
            taskQuery.orderByTaskCreateTime().desc();

            // 分页查询
            long total = taskQuery.count();
            int offset = (int) ((page.getCurrent() - 1) * page.getSize());
            int limit = (int) page.getSize();

            List<Task> activitiTasks = taskQuery.listPage(offset, limit);

            // 转换为自定义的WorkflowTask对象
            List<WorkflowTask> list = activitiTasks.stream().map(t -> {
                WorkflowTask wt = new WorkflowTask();
                wt.setId(t.getId());
                wt.setName(t.getName());
                wt.setDescription(t.getDescription());
                wt.setTaskKey(t.getTaskDefinitionKey());
                wt.setProcessInstanceId(t.getProcessInstanceId());
                wt.setProcessDefinitionId(t.getProcessDefinitionId());
                // 通过流程定义ID获取流程定义信息
                try {
                    org.activiti.engine.repository.ProcessDefinition processDefinition =
                        repositoryService.getProcessDefinition(t.getProcessDefinitionId());
                    wt.setProcessDefinitionKey(processDefinition.getKey());
                    wt.setProcessDefinitionName(processDefinition.getName());
                } catch (Exception e) {
                    wt.setProcessDefinitionKey("unknown");
                    wt.setProcessDefinitionName("未知流程");
                }
                wt.setAssignee(t.getAssignee());
                wt.setAssigneeName(t.getAssignee()); // 这里可以后续优化为真实姓名
                wt.setStatus("0"); // 0=待处理
                wt.setPriority(t.getPriority());
                wt.setStartTime(t.getCreateTime());
                wt.setDueDate(t.getDueDate());
                wt.setCategory(t.getCategory());
                wt.setFormKey(t.getFormKey());
                wt.setBusinessKey(t.getProcessInstanceId()); // 临时使用实例ID
                return wt;
            }).collect(Collectors.toList());

            page.setRecords(list);
            page.setTotal(total);

            logger.info("查询到 {} 个任务", list.size());
            return page;

        } catch (Exception e) {
            logger.error("查询用户任务列表失败，用户ID: {}", userId, e);
            // 返回空结果
            page.setRecords(new ArrayList<>());
            page.setTotal(0);
            return page;
        }
    }

    @Override
    public IPage<WorkflowTask> selectCandidateTaskList(Page<WorkflowTask> page, WorkflowTask task, Long userId) {
        // 模拟候选任务数据
        List<WorkflowTask> list = new ArrayList<>();
        
        WorkflowTask candidateTask = new WorkflowTask();
        candidateTask.setId("task_candidate_001");
        candidateTask.setName("部门审批");
        candidateTask.setDescription("部门级别审批任务");
        candidateTask.setProcessDefinitionKey("dept_approval");
        candidateTask.setProcessDefinitionName("部门审批流程");
        candidateTask.setCandidateUsers("user1,user2,user3");
        candidateTask.setStatus("0");
        candidateTask.setPriority(70);
        candidateTask.setStartTime(new Date());
        candidateTask.setBusinessKey("DEPT_001");
        list.add(candidateTask);

        page.setRecords(list);
        page.setTotal(list.size());
        return page;
    }

    @Override
    public IPage<WorkflowTask> selectHistoryTaskList(Page<WorkflowTask> page, WorkflowTask task, Long userId) {
        // 模拟历史任务数据
        List<WorkflowTask> list = new ArrayList<>();
        
        WorkflowTask historyTask = new WorkflowTask();
        historyTask.setId("task_history_001");
        historyTask.setName("已完成的项目审批");
        historyTask.setDescription("已完成的项目申请审批");
        historyTask.setProcessDefinitionKey("project_approval");
        historyTask.setProcessDefinitionName("项目申请流程");
        historyTask.setAssignee(userId.toString());
        historyTask.setAssigneeName("当前用户");
        historyTask.setStatus("2");
        historyTask.setPriority(50);
        historyTask.setStartTime(new Date(System.currentTimeMillis() - 86400000)); // 1天前
        historyTask.setEndTime(new Date());
        historyTask.setDuration(86400000L);
        historyTask.setBusinessKey("PROJECT_HISTORY_001");
        list.add(historyTask);

        page.setRecords(list);
        page.setTotal(list.size());
        return page;
    }

    @Override
    public WorkflowTask selectTaskById(String taskId) {
        // 模拟根据ID查询任务
        WorkflowTask task = new WorkflowTask();
        task.setId(taskId);
        task.setName("任务详情 - " + taskId);
        task.setDescription("这是任务 " + taskId + " 的详细信息");
        task.setProcessDefinitionKey("sample_process");
        task.setProcessDefinitionName("示例流程");
        task.setStatus("0");
        task.setPriority(50);
        task.setStartTime(new Date());
        task.setBusinessKey("BUSINESS_" + taskId);
        return task;
    }

    @Override
    public void claimTask(String taskId, Long userId, String username) {
        try {
            logger.info("用户 {} (ID: {}) 开始签收任务: {}", username, userId, taskId);

            // 验证任务是否存在且未被签收
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                throw new RuntimeException("任务不存在");
            }

            if (task.getAssignee() != null) {
                throw new RuntimeException("任务已被签收");
            }

            // 签收任务
            taskService.claim(taskId, userId.toString());

            logger.info("任务签收成功，任务ID: {}, 用户: {}", taskId, username);

        } catch (Exception e) {
            logger.error("签收任务失败，任务ID: {}, 用户: {}", taskId, username, e);
            throw new RuntimeException("签收任务失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void completeTask(String taskId, Long userId, String username, String comment, Map<String, Object> variables) {
        try {
            logger.info("用户 {} (ID: {}) 开始完成任务: {}", username, userId, taskId);

            // 验证任务是否存在且分配给当前用户
            Task task = taskService.createTaskQuery().taskId(taskId).taskAssignee(userId.toString()).singleResult();
            if (task == null) {
                throw new RuntimeException("任务不存在或不属于当前用户");
            }

            // 设置任务变量
            if (variables == null) {
                variables = new HashMap<>();
            }

            // 添加审批信息
            variables.put("approver", username);
            variables.put("approveTime", new Date());
            if (comment != null && !comment.isEmpty()) {
                variables.put("comment", comment);
                // 添加任务评论
                taskService.addComment(taskId, task.getProcessInstanceId(), comment);
            }

            // 完成任务
            taskService.complete(taskId, variables);

            logger.info("任务完成成功，任务ID: {}, 用户: {}", taskId, username);

        } catch (Exception e) {
            logger.error("完成任务失败，任务ID: {}, 用户: {}", taskId, username, e);
            throw new RuntimeException("完成任务失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void assignTask(String taskId, Long currentUserId, String currentUsername, String targetUserId) {
        // 模拟转办任务
        System.out.println("用户 " + currentUsername + " 将任务 " + taskId + " 转办给用户: " + targetUserId);
    }

    @Override
    public void delegateTask(String taskId, Long currentUserId, String currentUsername, String targetUserId) {
        // 模拟委派任务
        System.out.println("用户 " + currentUsername + " 将任务 " + taskId + " 委派给用户: " + targetUserId);
    }

    @Override
    public void resolveTask(String taskId, Long userId, String username) {
        // 模拟归还任务
        System.out.println("用户 " + username + " 归还了任务: " + taskId);
    }

    @Override
    public Map<String, Object> getTaskVariables(String taskId) {
        // 模拟获取任务变量
        Map<String, Object> variables = new HashMap<>();
        variables.put("applicant", "张三");
        variables.put("amount", 10000);
        variables.put("department", "研发部");
        variables.put("approved", false);
        return variables;
    }

    @Override
    public void setTaskVariables(String taskId, Map<String, Object> variables) {
        // 模拟设置任务变量
        System.out.println("为任务 " + taskId + " 设置变量: " + variables);
    }

    @Override
    public void suspendTask(String taskId) {
        // 模拟挂起任务
        System.out.println("挂起任务: " + taskId);
    }

    @Override
    public void activateTask(String taskId) {
        // 模拟激活任务
        System.out.println("激活任务: " + taskId);
    }

    @Override
    public void deleteTask(String taskId, String reason) {
        // 模拟删除任务
        System.out.println("删除任务: " + taskId + ", 原因: " + reason);
    }

    @Override
    public Map<String, Object> getTaskFormData(String taskId) {
        // 模拟获取任务表单数据
        Map<String, Object> formData = new HashMap<>();
        formData.put("title", "项目申请表");
        formData.put("projectName", "新产品研发项目");
        formData.put("budget", 100000);
        formData.put("duration", "6个月");
        return formData;
    }

    @Override
    public void submitTaskForm(String taskId, Map<String, Object> formData, Long userId, String username) {
        // 模拟提交任务表单
        System.out.println("用户 " + username + " 提交任务 " + taskId + " 的表单数据: " + formData);
    }

    @Override
    public List<Map<String, Object>> getTaskCandidateUsers(String taskId) {
        // 模拟获取候选用户
        List<Map<String, Object>> users = new ArrayList<>();
        Map<String, Object> user1 = new HashMap<>();
        user1.put("userId", "user1");
        user1.put("userName", "张三");
        users.add(user1);
        
        Map<String, Object> user2 = new HashMap<>();
        user2.put("userId", "user2");
        user2.put("userName", "李四");
        users.add(user2);
        
        return users;
    }

    @Override
    public List<Map<String, Object>> getTaskCandidateGroups(String taskId) {
        // 模拟获取候选组
        List<Map<String, Object>> groups = new ArrayList<>();
        Map<String, Object> group1 = new HashMap<>();
        group1.put("groupId", "dept_manager");
        group1.put("groupName", "部门经理");
        groups.add(group1);
        
        return groups;
    }

    @Override
    public void addTaskCandidateUser(String taskId, String userId) {
        System.out.println("为任务 " + taskId + " 添加候选用户: " + userId);
    }

    @Override
    public void deleteTaskCandidateUser(String taskId, String userId) {
        System.out.println("为任务 " + taskId + " 删除候选用户: " + userId);
    }

    @Override
    public void addTaskCandidateGroup(String taskId, String groupId) {
        System.out.println("为任务 " + taskId + " 添加候选组: " + groupId);
    }

    @Override
    public void deleteTaskCandidateGroup(String taskId, String groupId) {
        System.out.println("为任务 " + taskId + " 删除候选组: " + groupId);
    }

    @Override
    public Map<String, Object> getTaskStatistics(Long userId) {
        // 模拟任务统计数据
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalTasks", 15);
        statistics.put("pendingTasks", 5);
        statistics.put("completedTasks", 10);
        statistics.put("overdueTasks", 2);
        return statistics;
    }

    @Override
    public List<Map<String, Object>> getTaskTrendData(Long userId, int days) {
        // 模拟任务趋势数据
        List<Map<String, Object>> trendData = new ArrayList<>();
        for (int i = 0; i < days; i++) {
            Map<String, Object> dayData = new HashMap<>();
            dayData.put("date", new Date(System.currentTimeMillis() - (long) i * 86400000));
            dayData.put("completed", (int) (Math.random() * 10));
            dayData.put("created", (int) (Math.random() * 8));
            trendData.add(dayData);
        }
        return trendData;
    }

    @Override
    public void batchProcessTasks(List<String> taskIds, String action, Long userId, String username, Map<String, Object> params) {
        // 模拟批量处理任务
        System.out.println("用户 " + username + " 批量" + action + "任务: " + taskIds);
        if (params != null && !params.isEmpty()) {
            System.out.println("参数: " + params);
        }
    }
}
