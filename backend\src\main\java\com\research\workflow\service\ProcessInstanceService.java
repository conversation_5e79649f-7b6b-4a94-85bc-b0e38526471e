package com.research.workflow.service;

import com.research.workflow.domain.ProcessInstance;
import java.util.List;
import java.util.Map;

/**
 * 流程实例Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
public interface ProcessInstanceService {
    
    /**
     * 查询流程实例
     * 
     * @param id 流程实例主键
     * @return 流程实例
     */
    ProcessInstance selectProcessInstanceById(Long id);

    /**
     * 查询流程实例列表
     * 
     * @param processInstance 流程实例
     * @return 流程实例集合
     */
    List<ProcessInstance> selectProcessInstanceList(ProcessInstance processInstance);

    /**
     * 新增流程实例
     * 
     * @param processInstance 流程实例
     * @return 结果
     */
    int insertProcessInstance(ProcessInstance processInstance);

    /**
     * 修改流程实例
     * 
     * @param processInstance 流程实例
     * @return 结果
     */
    int updateProcessInstance(ProcessInstance processInstance);

    /**
     * 批量删除流程实例
     * 
     * @param ids 需要删除的流程实例主键集合
     * @return 结果
     */
    int deleteProcessInstanceByIds(Long[] ids);

    /**
     * 删除流程实例信息
     * 
     * @param id 流程实例主键
     * @return 结果
     */
    int deleteProcessInstanceById(Long id);

    /**
     * 启动流程实例
     * 
     * @param processDefinitionKey 流程定义Key
     * @param businessKey 业务Key
     * @param variables 流程变量
     * @param startUserId 启动用户ID
     * @return 流程实例ID
     */
    String startProcessInstance(String processDefinitionKey, String businessKey, Map<String, Object> variables, String startUserId);

    /**
     * 挂起流程实例
     * 
     * @param processInstanceId 流程实例ID
     */
    void suspendProcessInstance(String processInstanceId);

    /**
     * 激活流程实例
     * 
     * @param processInstanceId 流程实例ID
     */
    void activateProcessInstance(String processInstanceId);

    /**
     * 删除流程实例
     * 
     * @param processInstanceId 流程实例ID
     * @param deleteReason 删除原因
     */
    void deleteProcessInstance(String processInstanceId, String deleteReason);

    /**
     * 获取流程实例历史
     * 
     * @param processInstanceId 流程实例ID
     * @return 历史信息
     */
    List<Map<String, Object>> getProcessInstanceHistory(String processInstanceId);

    /**
     * 获取流程实例变量
     * 
     * @param processInstanceId 流程实例ID
     * @return 变量Map
     */
    Map<String, Object> getProcessInstanceVariables(String processInstanceId);

    /**
     * 设置流程实例变量
     * 
     * @param processInstanceId 流程实例ID
     * @param variables 变量Map
     */
    void setProcessInstanceVariables(String processInstanceId, Map<String, Object> variables);

    /**
     * 根据业务Key查询流程实例
     * 
     * @param businessKey 业务Key
     * @return 流程实例
     */
    ProcessInstance selectProcessInstanceByBusinessKey(String businessKey);

    /**
     * 查询用户相关的流程实例
     * 
     * @param userId 用户ID
     * @return 流程实例集合
     */
    List<ProcessInstance> selectProcessInstanceByUserId(String userId);

    /**
     * 查询正在运行的流程实例
     * 
     * @return 流程实例集合
     */
    List<ProcessInstance> selectRunningProcessInstances();

    /**
     * 查询已完成的流程实例
     * 
     * @return 流程实例集合
     */
    List<ProcessInstance> selectFinishedProcessInstances();

    /**
     * 获取流程实例图片
     * 
     * @param processInstanceId 流程实例ID
     * @return 图片Base64
     */
    String getProcessInstanceImage(String processInstanceId);

    /**
     * 获取流程实例当前节点
     * 
     * @param processInstanceId 流程实例ID
     * @return 当前节点信息
     */
    Map<String, Object> getCurrentActivity(String processInstanceId);
}
