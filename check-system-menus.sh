#!/bin/bash

# 系统管理菜单检查和修复脚本

echo "=========================================="
echo "系统管理菜单检查和修复脚本"
echo "=========================================="

# 数据库连接信息
DB_HOST="*************"
DB_USER="root"
DB_PASS="123456"
DB_NAME="research_db"

echo "1. 检查当前菜单配置..."

# 检查菜单基本信息
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -D $DB_NAME -e "
SELECT 
    menu_id as '菜单ID',
    menu_name as '菜单名称',
    path as '路径',
    component as '组件',
    visible as '可见',
    status as '状态'
FROM sys_menu 
WHERE menu_id IN (104, 105, 106, 107, 108) 
ORDER BY menu_id;
"

echo ""
echo "2. 检查角色菜单关联..."

# 检查角色菜单关联
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -D $DB_NAME -e "
SELECT 
    m.menu_id as '菜单ID',
    m.menu_name as '菜单名称',
    CASE WHEN rm.role_id IS NOT NULL THEN '已分配' ELSE '未分配' END as '角色分配状态'
FROM sys_menu m
LEFT JOIN sys_role_menu rm ON m.menu_id = rm.menu_id AND rm.role_id = 1
WHERE m.menu_id IN (104, 105, 106, 107, 108)
ORDER BY m.menu_id;
"

echo ""
echo "3. 修复菜单配置..."

# 修复部门管理
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -D $DB_NAME -e "
UPDATE sys_menu SET 
    menu_name = '部门管理',
    parent_id = 100,
    order_num = 4,
    path = 'dept',
    component = 'system/dept/index',
    is_frame = 1,
    is_cache = 0,
    menu_type = 'C',
    visible = '0',
    status = '0',
    perms = 'system:dept:list',
    icon = 'tree',
    remark = '部门管理菜单'
WHERE menu_id = 104;
"

# 修复岗位管理
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -D $DB_NAME -e "
UPDATE sys_menu SET 
    menu_name = '岗位管理',
    parent_id = 100,
    order_num = 5,
    path = 'post',
    component = 'system/post/index',
    is_frame = 1,
    is_cache = 0,
    menu_type = 'C',
    visible = '0',
    status = '0',
    perms = 'system:post:list',
    icon = 'post',
    remark = '岗位管理菜单'
WHERE menu_id = 105;
"

# 修复通知公告
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -D $DB_NAME -e "
UPDATE sys_menu SET 
    menu_name = '通知公告',
    parent_id = 100,
    order_num = 6,
    path = 'notice',
    component = 'system/notice/index',
    is_frame = 1,
    is_cache = 0,
    menu_type = 'C',
    visible = '0',
    status = '0',
    perms = 'system:notice:list',
    icon = 'message',
    remark = '通知公告菜单'
WHERE menu_id = 106;
"

# 修复待办事项
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -D $DB_NAME -e "
UPDATE sys_menu SET 
    menu_name = '待办事项',
    parent_id = 100,
    order_num = 7,
    path = 'todo',
    component = 'system/todo/index',
    is_frame = 1,
    is_cache = 0,
    menu_type = 'C',
    visible = '0',
    status = '0',
    perms = 'system:todo:list',
    icon = 'list',
    remark = '待办事项菜单'
WHERE menu_id = 107;
"

# 修复站内消息
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -D $DB_NAME -e "
UPDATE sys_menu SET 
    menu_name = '站内消息',
    parent_id = 100,
    order_num = 8,
    path = 'message',
    component = 'system/message/index',
    is_frame = 1,
    is_cache = 0,
    menu_type = 'C',
    visible = '0',
    status = '0',
    perms = 'system:message:list',
    icon = 'chat',
    remark = '站内消息菜单'
WHERE menu_id = 108;
"

echo "4. 确保角色菜单关联..."

# 确保超级管理员角色有这些菜单权限
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -D $DB_NAME -e "
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES 
(1, 104), 
(1, 105), 
(1, 106), 
(1, 107), 
(1, 108);
"

echo "5. 验证修复结果..."

# 验证修复结果
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -D $DB_NAME -e "
SELECT 
    m.menu_id as '菜单ID',
    m.menu_name as '菜单名称',
    m.path as '路径',
    m.component as '组件',
    m.visible as '可见',
    m.status as '状态',
    CASE WHEN rm.role_id IS NOT NULL THEN '✓' ELSE '✗' END as '角色分配'
FROM sys_menu m
LEFT JOIN sys_role_menu rm ON m.menu_id = rm.menu_id AND rm.role_id = 1
WHERE m.menu_id IN (104, 105, 106, 107, 108)
ORDER BY m.order_num;
"

echo ""
echo "=========================================="
echo "菜单配置修复完成！"
echo "=========================================="
echo "请重新登录系统查看菜单是否正常显示"
echo "如果问题仍然存在，请检查："
echo "1. 前端组件是否存在"
echo "2. 后端控制器是否正常"
echo "3. 用户是否有正确的角色权限"
echo "=========================================="
