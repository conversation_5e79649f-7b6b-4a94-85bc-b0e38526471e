-- 简化版菜单测试SQL
-- 只包含基础菜单结构，用于快速测试

-- 清空现有数据
DELETE FROM sys_role_menu WHERE role_id > 0;
DELETE FROM sys_menu WHERE menu_id > 0;

-- 重置自增ID
ALTER TABLE sys_menu AUTO_INCREMENT = 1;

-- 插入基础菜单数据
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES

-- 一级菜单
(1, '个人工作台', 0, 1, 'workspace', 'workspace/index', '', '1', '0', 'C', '0', '0', 'workspace:view', 'monitor', 'admin', NOW(), '', NULL, '个人工作台菜单'),

-- 系统管理
(100, '系统管理', 0, 2, 'system', NULL, '', '1', '0', 'M', '0', '0', '', 'system', 'admin', NOW(), '', NULL, '系统管理目录'),
(101, '用户管理', 100, 1, 'user', 'system/user/index', '', '1', '0', 'C', '0', '0', 'system:user:list', 'user', 'admin', NOW(), '', NULL, '用户管理菜单'),
(102, '角色管理', 100, 2, 'role', 'system/role/index', '', '1', '0', 'C', '0', '0', 'system:role:list', 'peoples', 'admin', NOW(), '', NULL, '角色管理菜单'),
(103, '菜单管理', 100, 3, 'menu', 'system/menu/index', '', '1', '0', 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', NOW(), '', NULL, '菜单管理菜单'),
(104, '部门管理', 100, 4, 'dept', 'system/dept/index', '', '1', '0', 'C', '0', '0', 'system:dept:list', 'tree', 'admin', NOW(), '', NULL, '部门管理菜单'),

-- 用户管理按钮权限
(1001, '用户查询', 101, 1, '', '', '', '1', '0', 'F', '0', '0', 'system:user:query', '#', 'admin', NOW(), '', NULL, ''),
(1002, '用户新增', 101, 2, '', '', '', '1', '0', 'F', '0', '0', 'system:user:add', '#', 'admin', NOW(), '', NULL, ''),
(1003, '用户修改', 101, 3, '', '', '', '1', '0', 'F', '0', '0', 'system:user:edit', '#', 'admin', NOW(), '', NULL, ''),
(1004, '用户删除', 101, 4, '', '', '', '1', '0', 'F', '0', '0', 'system:user:remove', '#', 'admin', NOW(), '', NULL, ''),

-- 角色管理按钮权限
(1008, '角色查询', 102, 1, '', '', '', '1', '0', 'F', '0', '0', 'system:role:query', '#', 'admin', NOW(), '', NULL, ''),
(1009, '角色新增', 102, 2, '', '', '', '1', '0', 'F', '0', '0', 'system:role:add', '#', 'admin', NOW(), '', NULL, ''),
(1010, '角色修改', 102, 3, '', '', '', '1', '0', 'F', '0', '0', 'system:role:edit', '#', 'admin', NOW(), '', NULL, ''),
(1011, '角色删除', 102, 4, '', '', '', '1', '0', 'F', '0', '0', 'system:role:remove', '#', 'admin', NOW(), '', NULL, '');

-- 确保角色数据存在
INSERT IGNORE INTO sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES
(1, '超级管理员', 'admin', 1, '1', '1', '1', '0', '0', 'admin', NOW(), '', NULL, '超级管理员'),
(2, '科研管理员', 'research_admin', 2, '2', '1', '1', '0', '0', 'admin', NOW(), '', NULL, '科研管理员');

-- 为超级管理员角色分配所有菜单权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(1, 1),   -- 个人工作台
(1, 100), -- 系统管理
(1, 101), -- 用户管理
(1, 102), -- 角色管理
(1, 103), -- 菜单管理
(1, 104), -- 部门管理
(1, 1001), (1, 1002), (1, 1003), (1, 1004), -- 用户管理权限
(1, 1008), (1, 1009), (1, 1010), (1, 1011); -- 角色管理权限

-- 为科研管理员角色分配部分菜单权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(2, 1),   -- 个人工作台
(2, 100), -- 系统管理
(2, 101), -- 用户管理
(2, 104), -- 部门管理
(2, 1001), (2, 1002), (2, 1003); -- 用户管理部分权限

-- 确保用户角色关联存在
INSERT IGNORE INTO sys_user_role (user_id, role_id) VALUES
(1, 1), -- admin用户分配超级管理员角色
(2, 2); -- research用户分配科研管理员角色

-- 提交事务
COMMIT;
