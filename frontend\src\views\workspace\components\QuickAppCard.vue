<template>
  <el-card class="quick-app-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span class="card-title">快捷应用</span>
        <div class="header-actions">
          <el-button type="text" @click="$emit('refresh')">
            <el-icon><Refresh /></el-icon>
          </el-button>
          <el-button type="text" @click="configApps">
            <el-icon><Setting /></el-icon>
          </el-button>
        </div>
      </div>
    </template>
    
    <div class="quick-app-content">
      <div class="app-grid" v-if="quickApps && quickApps.length > 0">
        <div 
          v-for="app in quickApps" 
          :key="app.appId"
          class="app-item"
          @click="openApp(app)"
        >
          <div class="app-icon">
            <el-icon v-if="app.appIcon" :class="app.appIcon"></el-icon>
            <el-icon v-else><Grid /></el-icon>
          </div>
          <div class="app-name">{{ app.appName }}</div>
          <div class="app-desc" v-if="app.appDesc">{{ app.appDesc }}</div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="empty-state">
        <el-icon class="empty-icon"><Grid /></el-icon>
        <p>暂无快捷应用</p>
        <el-button type="primary" size="small" @click="configApps">配置应用</el-button>
      </div>
    </div>

    <!-- 应用配置对话框 -->
    <el-dialog v-model="configVisible" title="配置快捷应用" width="600px">
      <div class="app-config">
        <div class="config-tip">
          <el-alert
            title="拖拽应用图标可以调整顺序，最多可选择8个应用"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
        
        <div class="selected-apps">
          <h4>已选应用</h4>
          <draggable 
            v-model="selectedApps" 
            class="app-list selected"
            item-key="appId"
            @end="onDragEnd"
          >
            <template #item="{ element }">
              <div class="config-app-item selected">
                <div class="app-info">
                  <el-icon v-if="element.appIcon" :class="element.appIcon"></el-icon>
                  <el-icon v-else><Grid /></el-icon>
                  <span>{{ element.appName }}</span>
                </div>
                <el-button type="text" @click="removeApp(element)">
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>
            </template>
          </draggable>
        </div>
        
        <div class="available-apps">
          <h4>可用应用</h4>
          <div class="app-list available">
            <div 
              v-for="app in availableApps" 
              :key="app.appId"
              class="config-app-item available"
              @click="addApp(app)"
            >
              <div class="app-info">
                <el-icon v-if="app.appIcon" :class="app.appIcon"></el-icon>
                <el-icon v-else><Grid /></el-icon>
                <span>{{ app.appName }}</span>
              </div>
              <el-button type="text">
                <el-icon><Plus /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="configVisible = false">取消</el-button>
          <el-button type="primary" @click="saveConfig">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Refresh, Setting, Grid, Close, Plus } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import { getAvailableApps, saveQuickApps } from '@/api/workspace'

// Props
interface Props {
  quickApps: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits(['refresh'])

const router = useRouter()

// 响应式数据
const configVisible = ref(false)
const selectedApps = ref<any[]>([])
const allAvailableApps = ref<any[]>([])

// 计算可用应用（排除已选择的）
const availableApps = computed(() => {
  const selectedIds = selectedApps.value.map(app => app.appId)
  return allAvailableApps.value.filter(app => !selectedIds.includes(app.appId))
})

// 打开应用
const openApp = (app: any) => {
  if (app.appType === '1') {
    // 内部应用
    router.push(app.appUrl)
  } else {
    // 外部链接
    window.open(app.appUrl, '_blank')
  }
}

// 配置应用
const configApps = async () => {
  try {
    // 获取所有可用应用
    const { data } = await getAvailableApps()
    allAvailableApps.value = data || []
    
    // 初始化已选应用
    selectedApps.value = [...props.quickApps]
    
    configVisible.value = true
  } catch (error) {
    console.error('获取可用应用失败:', error)
    ElMessage.error('获取可用应用失败')
  }
}

// 添加应用
const addApp = (app: any) => {
  if (selectedApps.value.length >= 8) {
    ElMessage.warning('最多只能选择8个应用')
    return
  }
  selectedApps.value.push(app)
}

// 移除应用
const removeApp = (app: any) => {
  const index = selectedApps.value.findIndex(item => item.appId === app.appId)
  if (index > -1) {
    selectedApps.value.splice(index, 1)
  }
}

// 拖拽结束
const onDragEnd = () => {
  // 拖拽结束后的处理逻辑
}

// 保存配置
const saveConfig = async () => {
  try {
    const appCodes = selectedApps.value.map(app => app.appCode)
    await saveQuickApps(appCodes)
    ElMessage.success('快捷应用配置保存成功')
    configVisible.value = false
    emit('refresh')
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  }
}
</script>

<style scoped>
.quick-app-card {
  height: 100%;
  min-height: 300px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 5px;
}

.quick-app-content {
  height: 100%;
}

.app-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 15px;
  padding: 10px 0;
}

.app-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fafafa;
}

.app-item:hover {
  background-color: #e6f7ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.app-icon {
  font-size: 32px;
  color: #409eff;
  margin-bottom: 8px;
}

.app-name {
  font-size: 12px;
  color: #303133;
  text-align: center;
  font-weight: 500;
  margin-bottom: 4px;
}

.app-desc {
  font-size: 10px;
  color: #909399;
  text-align: center;
  line-height: 1.2;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

/* 配置对话框样式 */
.app-config {
  max-height: 500px;
  overflow-y: auto;
}

.config-tip {
  margin-bottom: 20px;
}

.selected-apps, .available-apps {
  margin-bottom: 20px;
}

.selected-apps h4, .available-apps h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #303133;
}

.app-list {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  min-height: 60px;
  padding: 10px;
}

.app-list.selected {
  background-color: #f0f9ff;
}

.config-app-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.config-app-item.selected {
  background-color: white;
  border: 1px solid #b3d8ff;
}

.config-app-item.available {
  background-color: #fafafa;
}

.config-app-item:hover {
  background-color: #e6f7ff;
}

.config-app-item:last-child {
  margin-bottom: 0;
}

.app-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.app-info .el-icon {
  font-size: 16px;
  color: #409eff;
}

.app-info span {
  font-size: 14px;
  color: #303133;
}

@media (max-width: 768px) {
  .app-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 10px;
  }
  
  .app-item {
    padding: 10px 5px;
  }
  
  .app-icon {
    font-size: 24px;
  }
  
  .app-name {
    font-size: 11px;
  }
  
  .app-desc {
    font-size: 9px;
  }
}
</style>
