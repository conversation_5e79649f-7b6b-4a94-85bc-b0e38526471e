package com.research.common.core.page;



import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * 分页请求基类
 * 
 * <AUTHOR>
 */
public class PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 默认页码 */
    public static final int DEFAULT_PAGE_NUM = 1;
    
    /** 默认每页大小 */
    public static final int DEFAULT_PAGE_SIZE = 10;
    
    /** 最大每页大小 */
    public static final int MAX_PAGE_SIZE = 500;

    /** 当前页码 */
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum = DEFAULT_PAGE_NUM;

    /** 每页显示记录数 */
    @Min(value = 1, message = "每页显示记录数不能小于1")
    @Max(value = MAX_PAGE_SIZE, message = "每页显示记录数不能超过" + MAX_PAGE_SIZE)
    private Integer pageSize = DEFAULT_PAGE_SIZE;

    /** 排序字段 */
    private String orderBy;

    /** 排序方向 */
    private String sortOrder = "desc";

    /** 是否查询总数 */
    private Boolean searchCount = true;

    public PageRequest() {
    }

    public PageRequest(Integer pageNum, Integer pageSize) {
        this.pageNum = pageNum != null ? pageNum : DEFAULT_PAGE_NUM;
        this.pageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
    }

    public PageRequest(Integer pageNum, Integer pageSize, String orderBy, String sortOrder) {
        this(pageNum, pageSize);
        this.orderBy = orderBy;
        this.sortOrder = sortOrder;
    }

    /**
     * 获取偏移量
     */
    public long getOffset() {
        return (long) (pageNum - 1) * pageSize;
    }

    /**
     * 获取限制数量
     */
    public long getLimit() {
        return pageSize;
    }

    /**
     * 获取排序SQL
     */
    public String getOrderByClause() {
        if (orderBy == null || orderBy.trim().isEmpty()) {
            return null;
        }
        return orderBy + " " + (sortOrder != null ? sortOrder : "desc");
    }

    /**
     * 验证分页参数
     */
    public void validate() {
        if (pageNum == null || pageNum < 1) {
            pageNum = DEFAULT_PAGE_NUM;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        }
        if (pageSize > MAX_PAGE_SIZE) {
            pageSize = MAX_PAGE_SIZE;
        }
        if (sortOrder != null && !"asc".equalsIgnoreCase(sortOrder) && !"desc".equalsIgnoreCase(sortOrder)) {
            sortOrder = "desc";
        }
    }

    // Getters and Setters
    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Boolean getSearchCount() {
        return searchCount;
    }

    public void setSearchCount(Boolean searchCount) {
        this.searchCount = searchCount;
    }

    @Override
    public String toString() {
        return "PageRequest{" +
                "pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", orderBy='" + orderBy + '\'' +
                ", sortOrder='" + sortOrder + '\'' +
                ", searchCount=" + searchCount +
                '}';
    }
}
