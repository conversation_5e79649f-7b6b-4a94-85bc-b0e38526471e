import request from '@/utils/request'

// 获取模型列表
export function listModel(query?: any) {
  return request({
    url: '/workflow/designer/models',
    method: 'get',
    params: query
  })
}

// 创建模型
export function createModel(data: any) {
  return request({
    url: '/workflow/designer/model',
    method: 'post',
    data: data
  })
}

// 获取模型JSON
export function getModelJson(modelId: string) {
  return request({
    url: '/workflow/designer/model/' + modelId + '/json',
    method: 'get'
  })
}

// 保存模型JSON
export function saveModelJson(modelId: string, json: string) {
  return request({
    url: '/workflow/designer/model/' + modelId + '/json',
    method: 'post',
    data: json,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 部署模型
export function deployModel(modelId: string) {
  return request({
    url: '/workflow/designer/model/' + modelId + '/deploy',
    method: 'post'
  })
}

// 删除模型
export function delModel(modelId: string) {
  return request({
    url: '/workflow/designer/model/' + modelId,
    method: 'delete'
  })
}

// 获取模型XML
export function getModelXml(modelId: string) {
  return request({
    url: '/workflow/designer/model/' + modelId + '/xml',
    method: 'get'
  })
}

// 下载模型XML
export function downloadModelXml(modelId: string) {
  return '/dev-api/workflow/designer/model/' + modelId + '/download'
}

// 从XML导入模型
export function importModelFromXml(name: string, xml: string) {
  return request({
    url: '/workflow/designer/model/import',
    method: 'post',
    params: {
      name
    },
    data: xml,
    headers: {
      'Content-Type': 'application/xml'
    }
  })
}

// 复制模型
export function copyModel(modelId: string, name: string) {
  return request({
    url: '/workflow/designer/model/' + modelId + '/copy',
    method: 'post',
    params: {
      name
    }
  })
}

// 获取用户列表
export function getUserList() {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 100
    }
  })
}

// 获取部门列表
export function getDeptList() {
  return request({
    url: '/system/dept/list',
    method: 'get'
  })
}

// 获取角色列表
export function getRoleList() {
  return request({
    url: '/system/role/list',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 100
    }
  })
}
