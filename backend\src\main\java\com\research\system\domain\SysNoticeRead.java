package com.research.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 公告阅读记录实体类
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_notice_read")
public class SysNoticeRead implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 公告ID */
    @TableField("notice_id")
    private Long noticeId;

    /** 用户ID */
    @TableField("user_id")
    private Long userId;

    /** 用户姓名 */
    @TableField("user_name")
    private String userName;

    /** 阅读时间 */
    @TableField("read_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime readTime;

    /** 阅读时长（秒） */
    @TableField("read_duration")
    private Integer readDuration;

    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
