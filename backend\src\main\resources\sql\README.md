# 数据初始化脚本使用说明

## 📁 文件说明

### 1. `init_data.sql` - 完整初始化脚本
- **用途**: 生产环境或完整测试环境的数据初始化
- **内容**: 包含完整的系统基础数据和科研管理示例数据
- **数据量**: 较大，包含详细的示例数据

**包含的数据**:
- ✅ 10个部门（包含层级关系）
- ✅ 9个岗位类型
- ✅ 8个角色（不同权限级别）
- ✅ 10个用户（不同角色）
- ✅ 完整的菜单权限体系
- ✅ 角色权限关联
- ✅ 通知公告示例
- ✅ 待办事项示例
- ✅ 系统消息示例
- ✅ 5个科研项目示例
- ✅ 5个科研成果示例
- ✅ 5个科研团队示例

### 2. `quick_init.sql` - 快速初始化脚本
- **用途**: 开发环境或快速测试的数据初始化
- **内容**: 仅包含核心必要数据
- **数据量**: 精简，快速部署

**包含的数据**:
- ✅ 5个核心部门
- ✅ 4个基础岗位
- ✅ 3个核心角色
- ✅ 3个测试用户
- ✅ 基础菜单权限
- ✅ 简单示例数据

## 🚀 使用方法

### 方法一：MySQL命令行执行

```bash
# 1. 登录MySQL
mysql -u root -p

# 2. 选择数据库
USE research_management;

# 3. 执行初始化脚本（选择其中一个）
# 完整版本
SOURCE /path/to/init_data.sql;

# 或者快速版本
SOURCE /path/to/quick_init.sql;
```

### 方法二：MySQL Workbench执行

1. 打开MySQL Workbench
2. 连接到数据库
3. 打开SQL文件（File -> Open SQL Script）
4. 选择对应的初始化脚本
5. 点击执行（⚡ 图标）

### 方法三：命令行直接执行

```bash
# 完整版本
mysql -u root -p research_management < init_data.sql

# 快速版本
mysql -u root -p research_management < quick_init.sql
```

## 👥 测试账号

执行初始化脚本后，可以使用以下账号登录系统：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | admin123 | 超级管理员 | 拥有所有权限 |
| research | admin123 | 科研管理员 | 科研管理权限 |
| teacher | admin123 | 普通用户 | 基础用户权限 |

> **注意**: 所有用户的默认密码都是 `admin123`

## 📋 数据验证

执行脚本后，可以运行以下SQL验证数据是否正确初始化：

```sql
-- 检查用户数据
SELECT user_name, nick_name, email FROM sys_user;

-- 检查角色分配
SELECT u.user_name, r.role_name 
FROM sys_user u 
JOIN sys_user_role ur ON u.user_id = ur.user_id 
JOIN sys_role r ON ur.role_id = r.role_id;

-- 检查部门数据
SELECT dept_name, leader, phone FROM sys_dept ORDER BY dept_id;

-- 检查菜单权限
SELECT menu_name, perms, icon FROM sys_menu WHERE menu_type = 'C';
```

## ⚠️ 注意事项

### 1. 数据清理
- 脚本中包含清理现有数据的SQL（已注释）
- 如需清理现有数据，请谨慎取消注释
- **生产环境请勿执行清理操作**

### 2. 自增ID重置
- 脚本会重置相关表的自增ID
- 确保不会与现有数据冲突

### 3. 密码安全
- 所有用户的默认密码都是 `admin123`
- **生产环境部署后请立即修改密码**

### 4. 权限配置
- 脚本配置了基础的角色权限
- 可根据实际需求调整权限配置

## 🔧 自定义配置

### 修改默认密码
如需修改默认密码，请：

1. 生成新的BCrypt密码哈希
2. 替换脚本中的password字段值

```sql
-- 示例：将密码改为 "newpassword123"
-- 需要先生成BCrypt哈希值
UPDATE sys_user SET password = '$2a$10$新的哈希值' WHERE user_id = 1;
```

### 添加自定义数据
可以在脚本末尾添加自定义的初始化数据：

```sql
-- 添加自定义部门
INSERT INTO sys_dept (...) VALUES (...);

-- 添加自定义用户
INSERT INTO sys_user (...) VALUES (...);
```

## 📞 技术支持

如果在使用过程中遇到问题，请检查：

1. **数据库连接**: 确保数据库连接正常
2. **表结构**: 确保数据库表结构已创建
3. **权限**: 确保数据库用户有足够的操作权限
4. **字符编码**: 确保使用UTF-8编码

## 📝 更新日志

- **v1.0** (2024-01-30): 初始版本，包含基础系统数据
- **v1.1** (2024-01-30): 添加科研管理示例数据
- **v1.2** (2024-01-30): 创建快速初始化版本

---

**祝您使用愉快！** 🎉
