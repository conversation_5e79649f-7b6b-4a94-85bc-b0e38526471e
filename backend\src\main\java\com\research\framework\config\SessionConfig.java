package com.research.framework.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;

/**
 * Session配置
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnBean(RedisConnectionFactory.class)
@EnableRedisHttpSession(maxInactiveIntervalInSeconds = 1800) // 30分钟
public class SessionConfig {
    
    // Spring Session会自动配置Redis作为Session存储
    // maxInactiveIntervalInSeconds: Session超时时间（秒）
    // redisNamespace: Redis中Session的命名空间，默认为spring:session
}
