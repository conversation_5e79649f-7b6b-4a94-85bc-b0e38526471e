# 全局样式系统使用指南

## 概述

本项目提供了一套完整的全局样式系统，解决了页面布局自适应和按钮显示问题。所有页面都可以使用这套样式系统来保持一致的视觉效果和用户体验。

## 文件结构

```
src/styles/
├── index.scss          # 主样式文件，导入所有样式
├── variables.scss      # 变量定义
├── element-ui.scss     # Element Plus 组件样式增强
├── sidebar.scss        # 侧边栏样式
├── page-layout.scss    # 页面布局样式（新增）
├── button-styles.scss  # 按钮样式（新增）
└── README.md          # 使用指南
```

## 页面布局类名

### 基础容器
- `.app-container` - 页面根容器，自动铺满视口
- `.page-content` - 页面内容区域，flex布局
- `.content-card` - 内容卡片样式

### 功能区域
- `.search-form` - 查询表单区域
- `.action-bar` - 操作按钮区域
- `.table-container` - 表格容器
- `.pagination-container` - 分页容器

### 特殊组件
- `.tree-border` - 树形组件边框
- `.loading-container` - 加载状态容器
- `.empty-container` - 空状态容器

## 按钮样式类名

### 顶部操作按钮
- `.top-action-btn` - 页面顶部操作按钮
- `.action-btn` - 通用操作按钮

### 表格操作按钮
- `.table-action-btn` - 表格行操作按钮
- `.action-buttons` - 表格操作按钮容器

### 特殊场景按钮
- `.search-btn` - 搜索按钮
- `.dialog-btn` - 对话框按钮
- `.toolbar-btn` - 工具栏按钮
- `.icon-btn` - 图标按钮
- `.floating-btn` - 浮动按钮

## 使用方法

### 1. 基础页面结构

```vue
<template>
  <div class="app-container">
    <div class="page-content">
      <!-- 查询表单 -->
      <div class="search-form">
        <el-form>
          <!-- 表单内容 -->
        </el-form>
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-bar">
        <el-button type="primary" class="top-action-btn">新增</el-button>
        <el-button type="success" class="top-action-btn">修改</el-button>
        <el-button type="danger" class="top-action-btn">删除</el-button>
      </div>
      
      <!-- 表格 -->
      <div class="table-container">
        <el-table :data="tableData">
          <!-- 表格列 -->
          <el-table-column label="操作" width="300">
            <template #default="scope">
              <div class="action-buttons">
                <el-button type="primary" size="small" class="table-action-btn">修改</el-button>
                <el-button type="danger" size="small" class="table-action-btn">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination />
        </div>
      </div>
    </div>
  </div>
</template>
```

### 2. 使用组件

```vue
<template>
  <!-- 使用页面布局组件 -->
  <PageLayout>
    <template #search>
      <el-form>
        <!-- 搜索表单 -->
      </el-form>
    </template>
    
    <template #actions>
      <ActionButton type="primary" variant="top" @click="handleAdd">新增</ActionButton>
      <ActionButton type="success" variant="top" @click="handleEdit">修改</ActionButton>
    </template>
    
    <template #content>
      <el-table :data="tableData">
        <el-table-column label="操作">
          <template #default="scope">
            <TableActions>
              <ActionButton type="primary" variant="table" @click="handleEdit(scope.row)">修改</ActionButton>
              <ActionButton type="danger" variant="table" @click="handleDelete(scope.row)">删除</ActionButton>
            </TableActions>
          </template>
        </el-table-column>
      </el-table>
    </template>
    
    <template #pagination>
      <el-pagination />
    </template>
  </PageLayout>
</template>

<script setup>
import PageLayout from '@/components/PageLayout/index.vue'
import ActionButton from '@/components/ActionButton/index.vue'
import TableActions from '@/components/TableActions/index.vue'
</script>
```

## 响应式设计

样式系统内置了响应式设计：

- **大屏幕 (>1200px)**: 正常尺寸
- **中屏幕 (768px-1200px)**: 适当缩小
- **小屏幕 (<768px)**: 移动端优化

## 自定义主题

可以通过修改 `variables.scss` 来自定义主题色彩：

```scss
:root {
  --el-color-primary: #409eff;
  --el-color-success: #67c23a;
  --el-color-warning: #e6a23c;
  --el-color-danger: #f56c6c;
  --el-color-info: #909399;
}
```

## 注意事项

1. **按钮文字显示**: 所有按钮样式都已修复文字显示问题
2. **页面自适应**: 使用 `.app-container` 和 `.page-content` 确保页面自适应
3. **表格高度**: 表格会自动计算合适的高度，无需手动设置
4. **兼容性**: 样式使用了现代CSS特性，支持主流浏览器

## 迁移现有页面

对于现有页面，只需要：

1. 确保根容器使用 `.app-container` 类
2. 内容区域使用 `.page-content` 类
3. 按钮添加对应的样式类名
4. 表格操作列使用 `.action-buttons` 容器

这样就能享受到统一的样式效果和自适应布局。
