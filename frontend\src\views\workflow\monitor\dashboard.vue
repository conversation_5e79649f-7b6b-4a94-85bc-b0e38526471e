<template>
  <div class="monitor-dashboard">
    <!-- 头部标题 -->
    <div class="dashboard-header">
      <div class="header-left">
        <h1 class="dashboard-title">
          <el-icon><Monitor /></el-icon>
          工作流监控大屏
        </h1>
        <div class="refresh-info">
          <span>最后更新: {{ lastUpdateTime }}</span>
          <el-button 
            type="primary" 
            size="small" 
            @click="refreshData"
            :loading="loading"
            class="ml-2"
          >
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
      <div class="header-right">
        <div class="system-status" :class="systemHealthClass">
          <el-icon><CircleCheck v-if="systemHealth === 'healthy'" /><Warning v-else /></el-icon>
          <span>{{ systemHealthText }}</span>
        </div>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="metric-card">
            <div class="metric-icon process-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ overview.totalProcessDefinitions || 0 }}</div>
              <div class="metric-label">流程定义总数</div>
              <div class="metric-sub">活跃: {{ overview.activeProcessDefinitions || 0 }}</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card">
            <div class="metric-icon instance-icon">
              <el-icon><Operation /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ overview.runningInstances || 0 }}</div>
              <div class="metric-label">运行中实例</div>
              <div class="metric-sub">总计: {{ overview.totalInstances || 0 }}</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card">
            <div class="metric-icon task-icon">
              <el-icon><List /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ overview.activeTasks || 0 }}</div>
              <div class="metric-label">待处理任务</div>
              <div class="metric-sub">已完成: {{ overview.completedTasks || 0 }}</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card">
            <div class="metric-icon activity-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ realTimeActivity.recentStarts || 0 }}</div>
              <div class="metric-label">24h新启动</div>
              <div class="metric-sub">完成: {{ realTimeActivity.recentCompletions || 0 }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 版本分布饼图 -->
        <el-col :span="8">
          <div class="chart-card">
            <div class="chart-header">
              <h3>版本分布统计</h3>
              <el-tag type="info" size="small">实时</el-tag>
            </div>
            <div class="chart-content">
              <div ref="versionPieChart" class="chart-container"></div>
            </div>
          </div>
        </el-col>

        <!-- 任务分布柱状图 -->
        <el-col :span="8">
          <div class="chart-card">
            <div class="chart-header">
              <h3>任务分布统计</h3>
              <el-tag type="success" size="small">实时</el-tag>
            </div>
            <div class="chart-content">
              <div ref="taskBarChart" class="chart-container"></div>
            </div>
          </div>
        </el-col>

        <!-- 系统资源使用率 -->
        <el-col :span="8">
          <div class="chart-card">
            <div class="chart-header">
              <h3>系统资源监控</h3>
              <el-tag type="warning" size="small">实时</el-tag>
            </div>
            <div class="chart-content">
              <div ref="resourceGaugeChart" class="chart-container"></div>
            </div>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="mt-4">
        <!-- 历史趋势折线图 -->
        <el-col :span="16">
          <div class="chart-card">
            <div class="chart-header">
              <h3>流程活动趋势</h3>
              <div class="chart-controls">
                <el-radio-group v-model="trendDays" size="small" @change="updateTrendChart">
                  <el-radio-button :label="7">7天</el-radio-button>
                  <el-radio-button :label="15">15天</el-radio-button>
                  <el-radio-button :label="30">30天</el-radio-button>
                </el-radio-group>
              </div>
            </div>
            <div class="chart-content">
              <div ref="trendLineChart" class="chart-container-large"></div>
            </div>
          </div>
        </el-col>

        <!-- 版本路由统计 -->
        <el-col :span="8">
          <div class="chart-card">
            <div class="chart-header">
              <h3>版本路由分布</h3>
              <el-tag type="primary" size="small">实时</el-tag>
            </div>
            <div class="chart-content">
              <div ref="routeChart" class="chart-container"></div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 详细数据表格 -->
    <div class="data-tables">
      <el-row :gutter="20">
        <!-- 流程性能统计表 -->
        <el-col :span="12">
          <div class="table-card">
            <div class="table-header">
              <h3>流程性能统计</h3>
              <el-button size="small" @click="refreshPerformanceData">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
            <el-table :data="performanceStats" size="small" max-height="300">
              <el-table-column prop="processKey" label="流程Key" width="150" />
              <el-table-column prop="instanceCount" label="实例数" width="80" />
              <el-table-column prop="avgDuration" label="平均耗时" width="100">
                <template #default="scope">
                  {{ formatDuration(scope.row.avgDuration) }}
                </template>
              </el-table-column>
              <el-table-column prop="minDuration" label="最短耗时" width="100">
                <template #default="scope">
                  {{ formatDuration(scope.row.minDuration) }}
                </template>
              </el-table-column>
              <el-table-column prop="maxDuration" label="最长耗时">
                <template #default="scope">
                  {{ formatDuration(scope.row.maxDuration) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>

        <!-- 版本详细信息表 -->
        <el-col :span="12">
          <div class="table-card">
            <div class="table-header">
              <h3>版本运行状态</h3>
              <div class="table-controls">
                <el-button size="small" @click="refreshVersionData">
                  <el-icon><Refresh /></el-icon>
                </el-button>
                <el-button size="small" @click="showVersionAnalysis">
                  <el-icon><TrendCharts /></el-icon>
                  分析
                </el-button>
              </div>
            </div>
            <el-table :data="versionDetails" size="small" max-height="300">
              <el-table-column prop="processName" label="流程名称" width="120" />
              <el-table-column prop="version" label="版本" width="60" />
              <el-table-column prop="runningInstances" label="运行中" width="80" />
              <el-table-column prop="completedInstances" label="已完成" width="80" />
              <el-table-column label="状态" width="80">
                <template #default="scope">
                  <el-tag
                    :type="scope.row.runningInstances > 0 ? 'success' : 'info'"
                    size="small"
                  >
                    {{ scope.row.runningInstances > 0 ? '活跃' : '空闲' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template #default="scope">
                  <el-button
                    link
                    type="primary"
                    size="small"
                    @click="viewVersionDetail(scope.row)"
                  >
                    详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 告警和通知区域 -->
    <div class="alerts-section">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="alert-card">
            <div class="alert-header">
              <h3>系统告警</h3>
              <el-badge :value="alerts.length" class="alert-badge">
                <el-icon><Warning /></el-icon>
              </el-badge>
            </div>
            <div class="alert-list">
              <div
                v-for="alert in alerts.slice(0, 5)"
                :key="alert.id"
                class="alert-item"
                :class="alert.level"
              >
                <div class="alert-content">
                  <div class="alert-title">{{ alert.title }}</div>
                  <div class="alert-time">{{ alert.time }}</div>
                </div>
                <el-button size="small" text @click="handleAlert(alert)">
                  处理
                </el-button>
              </div>
            </div>
          </div>
        </el-col>

        <el-col :span="8">
          <div class="notification-card">
            <div class="notification-header">
              <h3>实时通知</h3>
              <el-switch
                v-model="notificationEnabled"
                @change="toggleNotification"
                active-text="开启"
                inactive-text="关闭"
              />
            </div>
            <div class="notification-list">
              <div
                v-for="notification in notifications.slice(0, 5)"
                :key="notification.id"
                class="notification-item"
              >
                <el-icon class="notification-icon">
                  <Bell v-if="notification.type === 'info'" />
                  <Warning v-else-if="notification.type === 'warning'" />
                  <CircleCheck v-else />
                </el-icon>
                <div class="notification-content">
                  <div class="notification-text">{{ notification.message }}</div>
                  <div class="notification-time">{{ notification.time }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-col>

        <el-col :span="8">
          <div class="quick-actions-card">
            <div class="actions-header">
              <h3>快速操作</h3>
            </div>
            <div class="actions-grid">
              <el-button
                v-for="action in quickActions"
                :key="action.key"
                :type="action.type"
                :icon="action.icon"
                @click="executeQuickAction(action.key)"
                class="action-button"
              >
                {{ action.label }}
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 版本分析对话框 -->
    <el-dialog title="版本分析" v-model="versionAnalysisDialog.visible" width="800px">
      <div class="version-analysis">
        <el-tabs v-model="versionAnalysisDialog.activeTab">
          <el-tab-pane label="性能对比" name="performance">
            <div ref="versionPerformanceChart" style="height: 300px;"></div>
          </el-tab-pane>
          <el-tab-pane label="使用统计" name="usage">
            <div ref="versionUsageChart" style="height: 300px;"></div>
          </el-tab-pane>
          <el-tab-pane label="错误分析" name="errors">
            <el-table :data="versionAnalysisDialog.errorData" style="width: 100%">
              <el-table-column prop="version" label="版本" width="100" />
              <el-table-column prop="errorCount" label="错误数" width="100" />
              <el-table-column prop="errorRate" label="错误率" width="100" />
              <el-table-column prop="lastError" label="最近错误" />
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="versionAnalysisDialog.visible = false">关闭</el-button>
          <el-button type="primary" @click="exportVersionAnalysis">导出报告</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  getDashboardData,
  getRealTimeMetrics,
  getHistoricalTrends,
  getProcessPerformance,
  getVersionDistribution
} from '@/api/workflow/monitor'

// 响应式数据
const loading = ref(false)
const lastUpdateTime = ref('')
const systemHealth = ref('healthy')
const systemHealthClass = ref('healthy')
const systemHealthText = ref('系统正常')
const trendDays = ref(7)

// 监控数据
const overview = reactive({})
const realTimeActivity = reactive({})
const performanceStats = ref([])
const versionDetails = ref([])

// 图表实例
let versionPieChart: echarts.ECharts | null = null
let taskBarChart: echarts.ECharts | null = null
let resourceGaugeChart: echarts.ECharts | null = null
let trendLineChart: echarts.ECharts | null = null
let routeChart: echarts.ECharts | null = null

// 定时器
let refreshTimer: NodeJS.Timeout | null = null

/** 初始化 */
onMounted(() => {
  initDashboard()
  startAutoRefresh()
})

/** 组件销毁 */
onUnmounted(() => {
  stopAutoRefresh()
  destroyCharts()
})

/** 初始化监控大屏 */
const initDashboard = async () => {
  await loadDashboardData()
  await nextTick()
  initCharts()
}

/** 加载监控数据 */
const loadDashboardData = async () => {
  loading.value = true
  try {
    const response = await getDashboardData()
    const data = response.data

    // 更新总览数据
    Object.assign(overview, data.overview)
    Object.assign(realTimeActivity, data.realTimeActivity)

    // 更新性能统计
    performanceStats.value = data.performance.performanceStats || []

    // 更新版本详细信息
    const versionData = data.versionDistribution.versionStats || []
    versionDetails.value = []
    versionData.forEach((process: any) => {
      process.versions.forEach((version: any) => {
        versionDetails.value.push({
          processName: process.processName,
          version: `v${version.version}`,
          runningInstances: version.runningInstances,
          completedInstances: version.completedInstances
        })
      })
    })

    // 更新系统健康状态
    const health = data.systemHealth
    systemHealth.value = health.overallStatus
    updateSystemHealthDisplay(health.overallStatus)

    // 更新时间
    lastUpdateTime.value = new Date().toLocaleTimeString()

    // 更新图表数据
    updateCharts(data)

  } catch (error) {
    console.error('加载监控数据失败:', error)
  } finally {
    loading.value = false
  }
}

/** 初始化图表 */
const initCharts = () => {
  // 版本分布饼图
  const versionPieEl = document.querySelector('.chart-container') as HTMLElement
  if (versionPieEl) {
    versionPieChart = echarts.init(versionPieEl)
  }

  // 任务分布柱状图
  const taskBarEl = document.querySelectorAll('.chart-container')[1] as HTMLElement
  if (taskBarEl) {
    taskBarChart = echarts.init(taskBarEl)
  }

  // 系统资源仪表盘
  const resourceGaugeEl = document.querySelectorAll('.chart-container')[2] as HTMLElement
  if (resourceGaugeEl) {
    resourceGaugeChart = echarts.init(resourceGaugeEl)
  }

  // 历史趋势折线图
  const trendLineEl = document.querySelector('.chart-container-large') as HTMLElement
  if (trendLineEl) {
    trendLineChart = echarts.init(trendLineEl)
  }

  // 版本路由图
  const routeEl = document.querySelectorAll('.chart-container')[3] as HTMLElement
  if (routeEl) {
    routeChart = echarts.init(routeEl)
  }
}

/** 更新图表数据 */
const updateCharts = (data: any) => {
  updateVersionPieChart(data.versionDistribution)
  updateTaskBarChart(data.taskDistribution)
  updateResourceGaugeChart(data.systemHealth)
  updateTrendLineChart(data.trends)
  updateRouteChart(data.routeStats)
}

/** 更新版本分布饼图 */
const updateVersionPieChart = (versionData: any) => {
  if (!versionPieChart) return

  const pieData = []
  const stats = versionData.versionStats || []
  
  stats.forEach((process: any) => {
    process.versions.forEach((version: any) => {
      pieData.push({
        name: `${process.processName} v${version.version}`,
        value: version.runningInstances + version.completedInstances
      })
    })
  })

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: { fontSize: 12 }
    },
    series: [
      {
        name: '版本分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: pieData
      }
    ]
  }

  versionPieChart.setOption(option)
}

/** 更新任务分布柱状图 */
const updateTaskBarChart = (taskData: any) => {
  if (!taskBarChart) return

  const taskCountByAssignee = taskData.taskCountByAssignee || {}
  const assignees = Object.keys(taskCountByAssignee).slice(0, 10) // 只显示前10个
  const counts = assignees.map(assignee => taskCountByAssignee[assignee])

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: assignees,
      axisLabel: {
        rotate: 45,
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '任务数量',
        type: 'bar',
        data: counts,
        itemStyle: {
          color: '#409EFF'
        }
      }
    ]
  }

  taskBarChart.setOption(option)
}

/** 更新系统资源仪表盘 */
const updateResourceGaugeChart = (healthData: any) => {
  if (!resourceGaugeChart) return

  const cpuUsage = healthData.cpuUsage || 0
  const memoryUsage = healthData.memoryUsage || 0
  const diskUsage = healthData.diskUsage || 0

  const option = {
    series: [
      {
        name: 'CPU使用率',
        type: 'gauge',
        center: ['25%', '50%'],
        radius: '60%',
        min: 0,
        max: 100,
        splitNumber: 5,
        axisLine: {
          lineStyle: {
            color: [[0.2, '#67e0e3'], [0.8, '#37a2da'], [1, '#fd666d']],
            width: 3
          }
        },
        pointer: {
          itemStyle: {
            color: 'auto'
          }
        },
        axisTick: {
          distance: -30,
          length: 8,
          lineStyle: {
            color: '#fff',
            width: 2
          }
        },
        splitLine: {
          distance: -30,
          length: 30,
          lineStyle: {
            color: '#fff',
            width: 4
          }
        },
        axisLabel: {
          color: 'auto',
          distance: 40,
          fontSize: 10
        },
        detail: {
          valueAnimation: true,
          formatter: '{value}%',
          color: 'auto',
          fontSize: 12
        },
        data: [
          {
            value: Math.round(cpuUsage),
            name: 'CPU'
          }
        ]
      },
      {
        name: '内存使用率',
        type: 'gauge',
        center: ['75%', '50%'],
        radius: '60%',
        min: 0,
        max: 100,
        splitNumber: 5,
        axisLine: {
          lineStyle: {
            color: [[0.2, '#67e0e3'], [0.8, '#37a2da'], [1, '#fd666d']],
            width: 3
          }
        },
        pointer: {
          itemStyle: {
            color: 'auto'
          }
        },
        axisTick: {
          distance: -30,
          length: 8,
          lineStyle: {
            color: '#fff',
            width: 2
          }
        },
        splitLine: {
          distance: -30,
          length: 30,
          lineStyle: {
            color: '#fff',
            width: 4
          }
        },
        axisLabel: {
          color: 'auto',
          distance: 40,
          fontSize: 10
        },
        detail: {
          valueAnimation: true,
          formatter: '{value}%',
          color: 'auto',
          fontSize: 12
        },
        data: [
          {
            value: Math.round(memoryUsage),
            name: '内存'
          }
        ]
      }
    ]
  }

  resourceGaugeChart.setOption(option)
}

/** 更新历史趋势折线图 */
const updateTrendLineChart = (trendsData: any) => {
  if (!trendLineChart) return

  const dailyStats = trendsData.dailyStats || []
  const dates = dailyStats.map((stat: any) => new Date(stat.date).toLocaleDateString())
  const startedInstances = dailyStats.map((stat: any) => stat.startedInstances)
  const completedInstances = dailyStats.map((stat: any) => stat.completedInstances)
  const completedTasks = dailyStats.map((stat: any) => stat.completedTasks)

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['启动实例', '完成实例', '完成任务']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: {}
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dates
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '启动实例',
        type: 'line',
        stack: 'Total',
        data: startedInstances,
        smooth: true,
        itemStyle: {
          color: '#409EFF'
        }
      },
      {
        name: '完成实例',
        type: 'line',
        stack: 'Total',
        data: completedInstances,
        smooth: true,
        itemStyle: {
          color: '#67C23A'
        }
      },
      {
        name: '完成任务',
        type: 'line',
        stack: 'Total',
        data: completedTasks,
        smooth: true,
        itemStyle: {
          color: '#E6A23C'
        }
      }
    ]
  }

  trendLineChart.setOption(option)
}

/** 更新版本路由图 */
const updateRouteChart = (routeData: any) => {
  if (!routeChart) return

  const routeDistribution = routeData.routeDistribution || []
  const data = routeDistribution.map((route: any) => ({
    name: `${route.processKey} ${route.versionTag}`,
    value: route.instanceCount
  }))

  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '版本路由',
        type: 'pie',
        radius: '50%',
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  routeChart.setOption(option)
}

/** 更新趋势图表 */
const updateTrendChart = async () => {
  try {
    const response = await getHistoricalTrends(trendDays.value)
    updateTrendLineChart(response.data)
  } catch (error) {
    console.error('更新趋势图表失败:', error)
  }
}

/** 刷新数据 */
const refreshData = () => {
  loadDashboardData()
}

/** 刷新性能数据 */
const refreshPerformanceData = async () => {
  try {
    const response = await getProcessPerformance()
    performanceStats.value = response.data.performanceStats || []
  } catch (error) {
    console.error('刷新性能数据失败:', error)
  }
}

/** 刷新版本数据 */
const refreshVersionData = async () => {
  try {
    const response = await getVersionDistribution()
    const versionData = response.data.versionStats || []
    versionDetails.value = []
    versionData.forEach((process: any) => {
      process.versions.forEach((version: any) => {
        versionDetails.value.push({
          processName: process.processName,
          version: `v${version.version}`,
          runningInstances: version.runningInstances,
          completedInstances: version.completedInstances
        })
      })
    })
  } catch (error) {
    console.error('刷新版本数据失败:', error)
  }
}

/** 开始自动刷新 */
const startAutoRefresh = () => {
  refreshTimer = setInterval(async () => {
    try {
      const response = await getRealTimeMetrics()
      const metrics = response.data
      
      // 更新关键指标
      overview.runningInstances = metrics.runningInstances
      overview.activeTasks = metrics.activeTasks
      realTimeActivity.recentStarts = metrics.recentStarts
      realTimeActivity.recentCompletions = metrics.recentCompletions
      
      // 更新系统健康状态
      systemHealth.value = metrics.systemHealth
      updateSystemHealthDisplay(metrics.systemHealth)
      
      // 更新时间
      lastUpdateTime.value = new Date().toLocaleTimeString()
      
    } catch (error) {
      console.error('自动刷新失败:', error)
    }
  }, 30000) // 30秒刷新一次
}

/** 停止自动刷新 */
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

/** 销毁图表 */
const destroyCharts = () => {
  if (versionPieChart) {
    versionPieChart.dispose()
    versionPieChart = null
  }
  if (taskBarChart) {
    taskBarChart.dispose()
    taskBarChart = null
  }
  if (resourceGaugeChart) {
    resourceGaugeChart.dispose()
    resourceGaugeChart = null
  }
  if (trendLineChart) {
    trendLineChart.dispose()
    trendLineChart = null
  }
  if (routeChart) {
    routeChart.dispose()
    routeChart = null
  }
}

/** 更新系统健康状态显示 */
const updateSystemHealthDisplay = (status: string) => {
  switch (status) {
    case 'healthy':
      systemHealthClass.value = 'healthy'
      systemHealthText.value = '系统正常'
      break
    case 'warning':
      systemHealthClass.value = 'warning'
      systemHealthText.value = '系统警告'
      break
    case 'error':
      systemHealthClass.value = 'error'
      systemHealthText.value = '系统异常'
      break
    default:
      systemHealthClass.value = 'unknown'
      systemHealthText.value = '状态未知'
  }
}

/** 格式化持续时间 */
const formatDuration = (milliseconds: number) => {
  if (!milliseconds) return '-'

  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) return `${days}天${hours % 24}小时`
  if (hours > 0) return `${hours}小时${minutes % 60}分钟`
  if (minutes > 0) return `${minutes}分钟${seconds % 60}秒`
  return `${seconds}秒`
}

// 新增的数据和方法
const alerts = ref([
  { id: 1, title: '流程执行超时', level: 'error', time: '2分钟前' },
  { id: 2, title: '版本切换异常', level: 'warning', time: '5分钟前' },
  { id: 3, title: '系统负载过高', level: 'error', time: '10分钟前' },
  { id: 4, title: '任务积压告警', level: 'warning', time: '15分钟前' }
])

const notifications = ref([
  { id: 1, type: 'success', message: '新版本v2.2发布成功', time: '刚刚' },
  { id: 2, type: 'info', message: '系统维护将于今晚进行', time: '1小时前' },
  { id: 3, type: 'warning', message: '流程实例数量达到阈值', time: '2小时前' }
])

const notificationEnabled = ref(true)

const quickActions = ref([
  { key: 'refresh', label: '刷新数据', type: 'primary', icon: 'Refresh' },
  { key: 'export', label: '导出报告', type: 'success', icon: 'Download' },
  { key: 'settings', label: '监控设置', type: 'info', icon: 'Setting' },
  { key: 'alert', label: '告警配置', type: 'warning', icon: 'Bell' }
])

const versionAnalysisDialog = reactive({
  visible: false,
  activeTab: 'performance',
  errorData: [
    { version: 'v2.1', errorCount: 3, errorRate: '0.2%', lastError: '连接超时' },
    { version: 'v1.8', errorCount: 1, errorRate: '0.1%', lastError: '参数验证失败' },
    { version: 'v3.0', errorCount: 0, errorRate: '0%', lastError: '无' }
  ]
})

/** 显示版本分析 */
const showVersionAnalysis = () => {
  versionAnalysisDialog.visible = true
}

/** 查看版本详情 */
const viewVersionDetail = (version: any) => {
  proxy.$modal.msgSuccess(`查看版本 ${version.version} 详情功能开发中...`)
}

/** 处理告警 */
const handleAlert = (alert: any) => {
  proxy.$modal.confirm(
    `确认处理告警"${alert.title}"吗？`,
    '处理告警',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = alerts.value.findIndex(a => a.id === alert.id)
    if (index > -1) {
      alerts.value.splice(index, 1)
    }
    proxy.$modal.msgSuccess('告警已处理')
  })
}

/** 切换通知 */
const toggleNotification = (enabled: boolean) => {
  if (enabled) {
    proxy.$modal.msgSuccess('实时通知已开启')
  } else {
    proxy.$modal.msgInfo('实时通知已关闭')
  }
}

/** 执行快速操作 */
const executeQuickAction = (actionKey: string) => {
  switch (actionKey) {
    case 'refresh':
      loadDashboardData()
      proxy.$modal.msgSuccess('数据已刷新')
      break
    case 'export':
      exportMonitorReport()
      break
    case 'settings':
      proxy.$modal.msgSuccess('监控设置功能开发中...')
      break
    case 'alert':
      proxy.$modal.msgSuccess('告警配置功能开发中...')
      break
  }
}

/** 导出监控报告 */
const exportMonitorReport = () => {
  const reportData = {
    overview: overviewData.value,
    versionDetails: versionDetails.value,
    systemHealth: systemHealth.value,
    alerts: alerts.value,
    exportTime: new Date().toISOString()
  }

  const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `workflow-monitor-report-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)

  proxy.$modal.msgSuccess('监控报告已导出')
}

/** 导出版本分析报告 */
const exportVersionAnalysis = () => {
  proxy.$modal.msgSuccess('版本分析报告导出功能开发中...')
}
</script>

<style scoped>
.monitor-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  color: #fff;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.dashboard-title {
  margin: 0;
  font-size: 28px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 10px;
}

.refresh-info {
  display: flex;
  align-items: center;
  font-size: 14px;
  opacity: 0.8;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
}

.system-status.healthy {
  background: rgba(103, 194, 58, 0.2);
  color: #67c23a;
}

.system-status.warning {
  background: rgba(230, 162, 60, 0.2);
  color: #e6a23c;
}

.system-status.error {
  background: rgba(245, 108, 108, 0.2);
  color: #f56c6c;
}

.metrics-cards {
  margin-bottom: 30px;
}

.metric-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  backdrop-filter: blur(10px);
  transition: transform 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-5px);
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.process-icon {
  background: linear-gradient(45deg, #409eff, #36cfc9);
}

.instance-icon {
  background: linear-gradient(45deg, #67c23a, #95de64);
}

.task-icon {
  background: linear-gradient(45deg, #e6a23c, #ffc53d);
}

.activity-icon {
  background: linear-gradient(45deg, #f56c6c, #ff7875);
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 32px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 16px;
  opacity: 0.8;
  margin-bottom: 3px;
}

.metric-sub {
  font-size: 12px;
  opacity: 0.6;
}

.charts-section {
  margin-bottom: 30px;
}

.chart-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  backdrop-filter: blur(10px);
  height: 400px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chart-content {
  height: calc(100% - 60px);
}

.chart-container {
  width: 100%;
  height: 100%;
}

.chart-container-large {
  width: 100%;
  height: 100%;
}

.data-tables {
  margin-bottom: 30px;
}

.table-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
}

.mt-4 {
  margin-top: 20px;
}

.ml-2 {
  margin-left: 8px;
}

/* 深色主题下的表格样式 */
:deep(.el-table) {
  background: transparent;
  color: #fff;
}

:deep(.el-table th) {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border-color: rgba(255, 255, 255, 0.1);
}

:deep(.el-table td) {
  border-color: rgba(255, 255, 255, 0.1);
}

:deep(.el-table tr) {
  background: transparent;
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background: rgba(255, 255, 255, 0.1);
}

/* 告警和通知区域样式 */
.alerts-section {
  margin-top: 30px;
}

.alert-card, .notification-card, .quick-actions-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  backdrop-filter: blur(10px);
  height: 300px;
}

.alert-header, .notification-header, .actions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}

.alert-badge {
  color: #ff6b6b;
}

.alert-list, .notification-list {
  max-height: 240px;
  overflow-y: auto;
}

.alert-item, .notification-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  margin-bottom: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  border-left: 4px solid transparent;
}

.alert-item.error {
  border-left-color: #ff6b6b;
}

.alert-item.warning {
  border-left-color: #ffa726;
}

.alert-content, .notification-content {
  flex: 1;
}

.alert-title, .notification-text {
  font-size: 14px;
  margin-bottom: 4px;
}

.alert-time, .notification-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.notification-item {
  padding: 8px 12px;
}

.notification-icon {
  margin-right: 10px;
  font-size: 16px;
}

.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.action-button {
  height: 50px;
  font-size: 14px;
}

.table-controls {
  display: flex;
  gap: 8px;
}

/* 版本分析对话框样式 */
.version-analysis {
  min-height: 400px;
}

.version-info p {
  margin: 8px 0;
  font-size: 14px;
}
</style>
