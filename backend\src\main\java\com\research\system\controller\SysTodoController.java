package com.research.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.research.common.core.controller.BaseController;
import com.research.common.core.domain.AjaxResult;
import com.research.common.core.page.TableDataInfo;
import com.research.common.utils.SecurityUtils;
import com.research.common.utils.PageUtils;
import com.research.common.utils.MybatisPlusPageUtils;
import com.research.system.domain.SysTodo;
import com.research.system.domain.SysTodoHistory;
import com.research.system.service.ISysTodoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 待办事项控制器
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@RestController
@RequestMapping("/system/todo")
public class SysTodoController extends BaseController {

    @Autowired
    private ISysTodoService todoService;

    /**
     * 查询待办事项列表
     */
    @PreAuthorize("@ss.hasPermi('system:todo:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysTodo todo) {
        startPage();
        Page<SysTodo> page = new Page<>(PageUtils.getPageNum(), PageUtils.getPageSize());
        IPage<SysTodo> list = todoService.selectTodoList(page, todo);
        return getDataTable(list);
    }

    /**
     * 查询我的待办事项列表
     */
    @GetMapping("/myList")
    public TableDataInfo myList(SysTodo todo) {
        try {
            Page<SysTodo> page = MybatisPlusPageUtils.createSafePage();
            Long userId = SecurityUtils.getUserId();
            IPage<SysTodo> result = todoService.selectMyTodoList(page, todo, userId);
            return MybatisPlusPageUtils.convertToTableDataInfo(result);
        } catch (Exception e) {
            System.err.println("查询我的待办事项列表失败: " + e.getMessage());
            return MybatisPlusPageUtils.createEmptyTableDataInfo();
        }
    }

    /**
     * 查询我创建的待办事项列表
     */
    @GetMapping("/myCreatedList")
    public TableDataInfo myCreatedList(SysTodo todo) {
        try {
            Page<SysTodo> page = MybatisPlusPageUtils.createSafePage();
            Long userId = SecurityUtils.getUserId();
            IPage<SysTodo> result = todoService.selectMyCreatedTodoList(page, todo, userId);
            return MybatisPlusPageUtils.convertToTableDataInfo(result);
        } catch (Exception e) {
            System.err.println("查询我创建的待办事项列表失败: " + e.getMessage());
            return MybatisPlusPageUtils.createEmptyTableDataInfo();
        }
    }

    /**
     * 根据待办事项编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:todo:query')")
    @GetMapping(value = "/{todoId}")
    public AjaxResult getInfo(@PathVariable Long todoId) {
        SysTodo todo = todoService.selectTodoDetail(todoId);
        return success(todo);
    }

    /**
     * 新增待办事项
     */
    @PreAuthorize("@ss.hasPermi('system:todo:add')")
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysTodo todo) {
        return toAjax(todoService.insertTodo(todo));
    }

    /**
     * 修改待办事项
     */
    @PreAuthorize("@ss.hasPermi('system:todo:edit')")
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysTodo todo) {
        return toAjax(todoService.updateTodo(todo));
    }

    /**
     * 删除待办事项
     */
    @PreAuthorize("@ss.hasPermi('system:todo:remove')")
    @DeleteMapping("/{todoIds}")
    public AjaxResult remove(@PathVariable Long[] todoIds) {
        return toAjax(todoService.deleteTodoByIds(todoIds));
    }

    /**
     * 分配待办事项
     */
    @PreAuthorize("@ss.hasPermi('system:todo:assign')")
    @PostMapping("/assign/{todoId}")
    public AjaxResult assign(@PathVariable Long todoId,
                           @RequestParam Long assigneeId,
                           @RequestParam String assigneeName) {
        return toAjax(todoService.assignTodo(todoId, assigneeId, assigneeName));
    }

    /**
     * 处理待办事项
     */
    @PostMapping("/process/{todoId}")
    public AjaxResult process(@PathVariable Long todoId,
                            @RequestParam String status,
                            @RequestParam(required = false) String result) {
        return toAjax(todoService.processTodo(todoId, status, result));
    }

    /**
     * 完成待办事项
     */
    @PostMapping("/complete/{todoId}")
    public AjaxResult complete(@PathVariable Long todoId,
                             @RequestParam(required = false) String result) {
        return toAjax(todoService.completeTodo(todoId, result));
    }

    /**
     * 取消待办事项
     */
    @PostMapping("/cancel/{todoId}")
    public AjaxResult cancel(@PathVariable Long todoId,
                           @RequestParam(required = false) String reason) {
        return toAjax(todoService.cancelTodo(todoId, reason));
    }

    /**
     * 重新打开待办事项
     */
    @PostMapping("/reopen/{todoId}")
    public AjaxResult reopen(@PathVariable Long todoId,
                           @RequestParam(required = false) String reason) {
        return toAjax(todoService.reopenTodo(todoId, reason));
    }

    /**
     * 标记待办为已读
     */
    @PostMapping("/markRead/{todoId}")
    public AjaxResult markAsRead(@PathVariable Long todoId) {
        Long userId = SecurityUtils.getUserId();
        return toAjax(todoService.markAsRead(todoId, userId));
    }

    /**
     * 批量更新待办状态
     */
    @PostMapping("/batchUpdateStatus")
    public AjaxResult batchUpdateStatus(@RequestBody List<Long> todoIds,
                                      @RequestParam String status) {
        return toAjax(todoService.batchUpdateStatus(todoIds, status));
    }

    /**
     * 查询用户待办事项统计
     */
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        Long userId = SecurityUtils.getUserId();
        Map<String, Object> statistics = todoService.selectTodoStatistics(userId);
        return success(statistics);
    }

    /**
     * 查询最近待办事项（用于工作台展示）
     */
    @GetMapping("/recent")
    public AjaxResult getRecentTodos(@RequestParam(defaultValue = "5") Integer limit) {
        Long userId = SecurityUtils.getUserId();
        List<SysTodo> todos = todoService.selectRecentTodos(userId, limit);
        return success(todos);
    }

    /**
     * 查询即将到期的待办事项
     */
    @GetMapping("/dueSoon")
    public AjaxResult getDueSoonTodos(@RequestParam(defaultValue = "3") Integer days) {
        Long userId = SecurityUtils.getUserId();
        List<SysTodo> todos = todoService.selectDueSoonTodos(userId, days);
        return success(todos);
    }

    /**
     * 查询逾期的待办事项
     */
    @GetMapping("/overdue")
    public AjaxResult getOverdueTodos() {
        Long userId = SecurityUtils.getUserId();
        List<SysTodo> todos = todoService.selectOverdueTodos(userId);
        return success(todos);
    }

    /**
     * 查询待办处理历史
     */
    @GetMapping("/history/{todoId}")
    public AjaxResult getTodoHistory(@PathVariable Long todoId) {
        List<SysTodoHistory> history = todoService.selectTodoHistory(todoId);
        return success(history);
    }

    /**
     * 查询待办事项按优先级统计
     */
    @GetMapping("/priorityStats")
    public AjaxResult getPriorityStats() {
        Long userId = SecurityUtils.getUserId();
        List<Map<String, Object>> stats = todoService.selectTodoByPriorityStats(userId);
        return success(stats);
    }

    /**
     * 查询待办事项按状态统计
     */
    @GetMapping("/statusStats")
    public AjaxResult getStatusStats() {
        Long userId = SecurityUtils.getUserId();
        List<Map<String, Object>> stats = todoService.selectTodoByStatusStats(userId);
        return success(stats);
    }

    /**
     * 发送待办提醒
     */
    @PreAuthorize("@ss.hasPermi('system:todo:remind')")
    @PostMapping("/remind/{todoId}")
    public AjaxResult sendReminder(@PathVariable Long todoId) {
        return toAjax(todoService.sendTodoReminder(todoId));
    }

    /**
     * 批量发送待办提醒
     */
    @PreAuthorize("@ss.hasPermi('system:todo:remind')")
    @PostMapping("/batchRemind")
    public AjaxResult batchSendReminder(@RequestBody List<Long> todoIds) {
        return toAjax(todoService.batchSendTodoReminder(todoIds));
    }
}
