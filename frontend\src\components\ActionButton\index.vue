<template>
  <el-button
    :type="type"
    :size="size"
    :icon="icon"
    :disabled="disabled"
    :loading="loading"
    :class="buttonClass"
    @click="handleClick"
  >
    <span v-if="$slots.default">
      <slot></slot>
    </span>
    <span v-else-if="text">{{ text }}</span>
  </el-button>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default'
  size?: 'large' | 'default' | 'small'
  icon?: string
  disabled?: boolean
  loading?: boolean
  text?: string
  variant?: 'top' | 'table' | 'search' | 'dialog' | 'toolbar'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  size: 'default',
  disabled: false,
  loading: false,
  variant: 'top'
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const buttonClass = computed(() => {
  const classes = []
  
  switch (props.variant) {
    case 'top':
      classes.push('top-action-btn')
      break
    case 'table':
      classes.push('table-action-btn')
      break
    case 'search':
      classes.push('search-btn')
      break
    case 'dialog':
      classes.push('dialog-btn')
      break
    case 'toolbar':
      classes.push('toolbar-btn')
      break
    default:
      classes.push('action-btn')
  }
  
  return classes.join(' ')
})

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style scoped>
/* 按钮样式已在全局样式中定义 */
</style>
