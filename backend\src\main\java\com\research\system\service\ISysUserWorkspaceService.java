package com.research.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.research.system.domain.SysQuickApp;
import com.research.system.domain.SysUserWorkspace;

import java.util.List;
import java.util.Map;

/**
 * 用户工作台Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
public interface ISysUserWorkspaceService extends IService<SysUserWorkspace> {

    /**
     * 获取用户工作台数据
     * 
     * @param userId 用户ID
     * @return 工作台数据
     */
    Map<String, Object> getUserWorkspaceData(Long userId);

    /**
     * 获取用户个人信息
     * 
     * @param userId 用户ID
     * @return 个人信息
     */
    Map<String, Object> getUserInfo(Long userId);

    /**
     * 获取用户待办统计
     * 
     * @param userId 用户ID
     * @return 待办统计
     */
    Map<String, Object> getUserTodoStats(Long userId);

    /**
     * 获取用户公告统计
     * 
     * @param userId 用户ID
     * @return 公告统计
     */
    Map<String, Object> getUserNoticeStats(Long userId);

    /**
     * 获取用户消息统计
     * 
     * @param userId 用户ID
     * @return 消息统计
     */
    Map<String, Object> getUserMessageStats(Long userId);

    /**
     * 获取用户快捷应用配置
     * 
     * @param userId 用户ID
     * @return 快捷应用列表
     */
    List<SysQuickApp> getUserQuickApps(Long userId);

    /**
     * 保存用户快捷应用配置
     * 
     * @param userId 用户ID
     * @param appCodes 应用编码列表
     * @return 结果
     */
    boolean saveUserQuickApps(Long userId, List<String> appCodes);

    /**
     * 获取用户工作台布局配置
     * 
     * @param userId 用户ID
     * @return 布局配置
     */
    Map<String, Object> getUserLayoutConfig(Long userId);

    /**
     * 保存用户工作台布局配置
     * 
     * @param userId 用户ID
     * @param layoutConfig 布局配置
     * @return 结果
     */
    boolean saveUserLayoutConfig(Long userId, Map<String, Object> layoutConfig);

    /**
     * 获取用户主题配置
     * 
     * @param userId 用户ID
     * @return 主题配置
     */
    Map<String, Object> getUserThemeConfig(Long userId);

    /**
     * 保存用户主题配置
     * 
     * @param userId 用户ID
     * @param themeConfig 主题配置
     * @return 结果
     */
    boolean saveUserThemeConfig(Long userId, Map<String, Object> themeConfig);

    /**
     * 获取用户配置
     * 
     * @param userId 用户ID
     * @param configType 配置类型
     * @return 配置列表
     */
    List<SysUserWorkspace> getUserConfig(Long userId, String configType);

    /**
     * 获取用户特定配置
     * 
     * @param userId 用户ID
     * @param configType 配置类型
     * @param configKey 配置键
     * @return 配置信息
     */
    SysUserWorkspace getUserConfigByKey(Long userId, String configType, String configKey);

    /**
     * 保存或更新用户配置
     * 
     * @param userId 用户ID
     * @param configType 配置类型
     * @param configKey 配置键
     * @param configValue 配置值
     * @param sortOrder 排序
     * @return 结果
     */
    boolean saveOrUpdateUserConfig(Long userId, String configType, String configKey, 
                                 String configValue, Integer sortOrder);

    /**
     * 删除用户配置
     * 
     * @param userId 用户ID
     * @param configType 配置类型
     * @param configKey 配置键
     * @return 结果
     */
    boolean deleteUserConfig(Long userId, String configType, String configKey);

    /**
     * 重置用户工作台配置
     * 
     * @param userId 用户ID
     * @return 结果
     */
    boolean resetUserWorkspace(Long userId);

    /**
     * 获取系统可用的快捷应用
     * 
     * @param userId 用户ID（用于权限过滤）
     * @return 可用应用列表
     */
    List<SysQuickApp> getAvailableQuickApps(Long userId);

    /**
     * 获取工作台统计数据
     * 
     * @param userId 用户ID
     * @return 统计数据
     */
    Map<String, Object> getWorkspaceStatistics(Long userId);
}
