package com.research.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 用户和角色关联 sys_user_role
 * 
 * <AUTHOR>
 */
@TableName("sys_user_role")
public class SysUserRole {
    /** 用户ID */
    private Long userId;
    
    /** 角色ID */
    private Long roleId;

    public SysUserRole() {}

    public SysUserRole(Long userId, Long roleId) {
        this.userId = userId;
        this.roleId = roleId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    @Override
    public String toString() {
        return "SysUserRole{" +
                "userId=" + userId +
                ", roleId=" + roleId +
                '}';
    }
}
