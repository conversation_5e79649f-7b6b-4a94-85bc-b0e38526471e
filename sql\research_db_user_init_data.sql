-- 科研管理系统用户管理模块初始化数据
-- 兼容MySQL 8.0+

USE research_db;

-- 清空现有数据（谨慎使用）
-- DELETE FROM sys_user_role;
-- DELETE FROM sys_user WHERE user_id > 1;

-- 插入管理员用户
INSERT INTO sys_user (user_id, dept_id, user_name, nick_name, user_type, email, phonenumber, sex, avatar, password, status, del_flag, login_ip, login_date, create_by, create_time, update_by, update_time, remark) VALUES
(1, 100, 'admin', '管理员', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$7JB720yubVSOfvam/RtmyO3T1dEjTvVTbXWZljmQQXiLqDOHFJlAa', '0', '0', '127.0.0.1', NOW(), 'admin', NOW(), '', NOW(), '管理员')
ON DUPLICATE KEY UPDATE 
    nick_name = VALUES(nick_name),
    email = VALUES(email),
    phonenumber = VALUES(phonenumber),
    update_time = NOW();

-- 插入测试用户
INSERT INTO sys_user (user_name, nick_name, user_type, email, phonenumber, sex, password, status, del_flag, create_by, create_time, remark) VALUES
('researcher1', '研究员张三', '00', '<EMAIL>', '13800138001', '1', '$2a$10$7JB720yubVSOfvam/RtmyO3T1dEjTvVTbXWZljmQQXiLqDOHFJlAa', '0', '0', 'admin', NOW(), '科研人员'),
('researcher2', '研究员李四', '00', '<EMAIL>', '13800138002', '0', '$2a$10$7JB720yubVSOfvam/RtmyO3T1dEjTvVTbXWZljmQQXiLqDOHFJlAa', '0', '0', 'admin', NOW(), '科研人员'),
('teacher1', '教师王五', '00', '<EMAIL>', '13800138003', '1', '$2a$10$7JB720yubVSOfvam/RtmyO3T1dEjTvVTbXWZljmQQXiLqDOHFJlAa', '0', '0', 'admin', NOW(), '教学人员'),
('expert1', '专家赵六', '00', '<EMAIL>', '13800138004', '1', '$2a$10$7JB720yubVSOfvam/RtmyO3T1dEjTvVTbXWZljmQQXiLqDOHFJlAa', '0', '0', 'admin', NOW(), '评审专家'),
('student1', '学生小明', '00', '<EMAIL>', '13800138005', '1', '$2a$10$7JB720yubVSOfvam/RtmyO3T1dEjTvVTbXWZljmQQXiLqDOHFJlAa', '0', '0', 'admin', NOW(), '研究生');

-- 插入角色数据
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) VALUES
('超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', NOW(), '超级管理员'),
('科研管理员', 'research_admin', 2, '2', 1, 1, '0', '0', 'admin', NOW(), '科研管理员'),
('普通用户', 'common', 3, '3', 1, 1, '0', '0', 'admin', NOW(), '普通用户'),
('评审专家', 'expert', 4, '4', 1, 1, '0', '0', 'admin', NOW(), '评审专家'),
('项目负责人', 'project_leader', 5, '4', 1, 1, '0', '0', 'admin', NOW(), '项目负责人');

-- 插入部门数据
INSERT INTO sys_dept (dept_name, parent_id, ancestors, order_num, leader, phone, email, status, del_flag, create_by, create_time) VALUES
('龙湖大学', 0, '0', 0, '校长', '15888888888', '<EMAIL>', '0', '0', 'admin', NOW()),
(100, '科研处', 1, '0,1', 1, '科研处长', '15888888889', '<EMAIL>', '0', '0', 'admin', NOW()),
('教务处', 1, '0,1', 2, '教务处长', '15888888890', '<EMAIL>', '0', '0', 'admin', NOW()),
('计算机学院', 1, '0,1', 3, '院长', '15888888891', '<EMAIL>', '0', '0', 'admin', NOW()),
('数学学院', 1, '0,1', 4, '院长', '15888888892', '<EMAIL>', '0', '0', 'admin', NOW());

-- 插入用户角色关联数据
INSERT INTO sys_user_role (user_id, role_id) VALUES
(1, 1),  -- admin -> 超级管理员
((SELECT user_id FROM sys_user WHERE user_name = 'researcher1'), 3),  -- researcher1 -> 普通用户
((SELECT user_id FROM sys_user WHERE user_name = 'researcher2'), 3),  -- researcher2 -> 普通用户
((SELECT user_id FROM sys_user WHERE user_name = 'teacher1'), 3),     -- teacher1 -> 普通用户
((SELECT user_id FROM sys_user WHERE user_name = 'expert1'), 4),      -- expert1 -> 评审专家
((SELECT user_id FROM sys_user WHERE user_name = 'student1'), 3);     -- student1 -> 普通用户

-- 更新用户部门信息
UPDATE sys_user SET dept_id = 100 WHERE user_name IN ('admin', 'researcher1', 'researcher2');
UPDATE sys_user SET dept_id = (SELECT dept_id FROM sys_dept WHERE dept_name = '教务处' LIMIT 1) WHERE user_name = 'teacher1';
UPDATE sys_user SET dept_id = (SELECT dept_id FROM sys_dept WHERE dept_name = '计算机学院' LIMIT 1) WHERE user_name = 'expert1';
UPDATE sys_user SET dept_id = (SELECT dept_id FROM sys_dept WHERE dept_name = '计算机学院' LIMIT 1) WHERE user_name = 'student1';

-- 查询验证数据
SELECT 
    u.user_id,
    u.user_name,
    u.nick_name,
    u.email,
    u.phonenumber,
    u.status,
    d.dept_name,
    GROUP_CONCAT(r.role_name) as roles
FROM sys_user u
LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
LEFT JOIN sys_role r ON ur.role_id = r.role_id
WHERE u.del_flag = '0'
GROUP BY u.user_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, d.dept_name
ORDER BY u.user_id;

-- 显示统计信息
SELECT 
    '用户总数' as item,
    COUNT(*) as count
FROM sys_user 
WHERE del_flag = '0'
UNION ALL
SELECT 
    '正常用户数' as item,
    COUNT(*) as count
FROM sys_user 
WHERE del_flag = '0' AND status = '0'
UNION ALL
SELECT 
    '停用用户数' as item,
    COUNT(*) as count
FROM sys_user 
WHERE del_flag = '0' AND status = '1';
