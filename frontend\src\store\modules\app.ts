import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 侧边栏状态
  const sidebar = ref({
    opened: true,
    withoutAnimation: false
  })

  // 设备类型
  const device = ref('desktop')

  // 页面大小
  const size = ref('default')

  // 切换侧边栏
  const toggleSidebar = () => {
    sidebar.value.opened = !sidebar.value.opened
    sidebar.value.withoutAnimation = false
  }

  // 关闭侧边栏
  const closeSidebar = (withoutAnimation: boolean = false) => {
    sidebar.value.opened = false
    sidebar.value.withoutAnimation = withoutAnimation
  }

  // 切换设备
  const toggleDevice = (deviceType: string) => {
    device.value = deviceType
  }

  // 设置页面大小
  const setSize = (sizeType: string) => {
    size.value = sizeType
  }

  return {
    sidebar,
    device,
    size,
    toggleSidebar,
    closeSidebar,
    toggleDevice,
    setSize
  }
})
