<template>
  <el-dialog
    title="合作单位统计分析"
    v-model="visible"
    width="1400px"
    append-to-body
    @close="handleClose"
  >
    <div v-loading="loading">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="mb-4">
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.totalCount || 0 }}</div>
              <div class="stat-label">合作单位总数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.normalCount || 0 }}</div>
              <div class="stat-label">正常合作单位</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.levelACount || 0 }}</div>
              <div class="stat-label">A级合作单位</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ formatMoney(statistics.totalContractAmount) }}</div>
              <div class="stat-label">累计合作金额(万元)</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 图表区域 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>单位类型分布</span>
            </template>
            <div ref="typeChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>合作等级分布</span>
            </template>
            <div ref="levelChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="mt-4">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>地域分布统计</span>
            </template>
            <div ref="regionChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>合作活跃度分析</span>
            </template>
            <div ref="activityChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="mt-4">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>合同金额分布</span>
            </template>
            <div ref="amountChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>单位状态统计</span>
            </template>
            <div ref="statusChartRef" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="mt-4">
        <el-col :span="24">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>新增合作单位趋势</span>
                <el-select v-model="selectedYear" @change="getYearlyData" style="width: 120px;">
                  <el-option
                    v-for="year in yearOptions"
                    :key="year"
                    :label="year + '年'"
                    :value="year"
                  />
                </el-select>
              </div>
            </template>
            <div ref="trendChartRef" style="height: 400px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 详细统计表格 -->
      <el-row :gutter="20" class="mt-4">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>单位类型详情</span>
            </template>
            <el-table :data="typeData" style="width: 100%">
              <el-table-column prop="partnerType" label="类型" />
              <el-table-column prop="partnerCount" label="单位数量" />
              <el-table-column prop="totalCooperationCount" label="合作次数" />
              <el-table-column prop="totalContractAmount" label="合作金额">
                <template #default="scope">
                  {{ formatMoney(scope.row.totalContractAmount) }}
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>合作等级详情</span>
            </template>
            <el-table :data="levelData" style="width: 100%">
              <el-table-column prop="cooperationLevel" label="等级">
                <template #default="scope">
                  <el-tag :type="getLevelType(scope.row.cooperationLevel)">
                    {{ scope.row.cooperationLevel }}级
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="partnerCount" label="单位数量" />
              <el-table-column prop="totalCooperationCount" label="合作次数" />
              <el-table-column prop="totalContractAmount" label="合作金额">
                <template #default="scope">
                  {{ formatMoney(scope.row.totalContractAmount) }}
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>

      <!-- 优质合作单位排行 -->
      <el-row :gutter="20" class="mt-4">
        <el-col :span="24">
          <el-card shadow="hover">
            <template #header>
              <span>优质合作单位排行榜</span>
            </template>
            <el-table :data="topPartners" style="width: 100%">
              <el-table-column type="index" label="排名" width="60" />
              <el-table-column prop="partnerName" label="单位名称" />
              <el-table-column prop="partnerType" label="单位类型" width="100" />
              <el-table-column prop="cooperationLevel" label="合作等级" width="100">
                <template #default="scope">
                  <el-tag :type="getLevelType(scope.row.cooperationLevel)">
                    {{ scope.row.cooperationLevel }}级
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="cooperationCount" label="合作次数" width="100" />
              <el-table-column prop="totalContractAmount" label="累计金额" width="120">
                <template #default="scope">
                  {{ formatMoney(scope.row.totalContractAmount) }}
                </template>
              </el-table-column>
              <el-table-column prop="lastCooperationDate" label="最近合作" width="120">
                <template #default="scope">
                  {{ parseTime(scope.row.lastCooperationDate, '{y}-{m}-{d}') }}
                </template>
              </el-table-column>
              <el-table-column prop="activityLevel" label="活跃度" width="100">
                <template #default="scope">
                  <el-tag :type="getActivityType(scope.row.activityLevel)">
                    {{ scope.row.activityLevel }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="exportStatistics">导出统计</el-button>
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { 
  getPartnerStatistics,
  getPartnerTypeStatistics,
  getPartnerLevelStatistics,
  getPartnerStatusStatistics,
  getPartnerYearlyStatistics,
  getPartnerContractAmountStatistics,
  getPartnerActivityStatistics,
  getPartnerRegionDistribution,
  getNewPartnerTrend
} from "@/api/project/partner"
import * as echarts from 'echarts'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const loading = ref(false)
const statistics = ref({})
const typeData = ref([])
const levelData = ref([])
const statusData = ref([])
const regionData = ref([])
const activityData = ref([])
const amountData = ref([])
const trendData = ref([])
const topPartners = ref([])
const selectedYear = ref(new Date().getFullYear())

// 图表引用
const typeChartRef = ref()
const levelChartRef = ref()
const regionChartRef = ref()
const activityChartRef = ref()
const amountChartRef = ref()
const statusChartRef = ref()
const trendChartRef = ref()

// 年份选项
const yearOptions = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let i = currentYear; i >= currentYear - 5; i--) {
    years.push(i)
  }
  return years
})

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal) {
    loadStatistics()
  }
})

/** 加载统计数据 */
async function loadStatistics() {
  loading.value = true
  try {
    // 并行加载所有统计数据
    const [
      statisticsRes,
      typeRes,
      levelRes,
      statusRes,
      regionRes,
      activityRes,
      amountRes,
      trendRes
    ] = await Promise.all([
      getPartnerStatistics(),
      getPartnerTypeStatistics(),
      getPartnerLevelStatistics(),
      getPartnerStatusStatistics(),
      getPartnerRegionDistribution(),
      getPartnerActivityStatistics(),
      getPartnerContractAmountStatistics(),
      getNewPartnerTrend(selectedYear.value + '-01-01', selectedYear.value + '-12-31')
    ])

    statistics.value = statisticsRes.data
    typeData.value = typeRes.data
    levelData.value = levelRes.data
    statusData.value = statusRes.data
    regionData.value = regionRes.data
    activityData.value = activityRes.data
    amountData.value = amountRes.data
    trendData.value = trendRes.data
    
    // 生成优质合作单位排行榜（模拟数据）
    topPartners.value = amountData.value.slice(0, 10).map((item, index) => ({
      ...item,
      cooperationLevel: ['A', 'A', 'B', 'A', 'B', 'C', 'A', 'B', 'A', 'C'][index],
      activityLevel: ['活跃', '活跃', '一般', '活跃', '一般', '不活跃', '活跃', '一般', '活跃', '不活跃'][index],
      lastCooperationDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    }))

    // 渲染图表
    nextTick(() => {
      renderTypeChart()
      renderLevelChart()
      renderRegionChart()
      renderActivityChart()
      renderAmountChart()
      renderStatusChart()
      renderTrendChart()
    })
  } catch (error) {
    console.error('加载统计数据失败:', error)
  } finally {
    loading.value = false
  }
}

/** 获取年度数据 */
async function getYearlyData() {
  try {
    const response = await getNewPartnerTrend(selectedYear.value + '-01-01', selectedYear.value + '-12-31')
    trendData.value = response.data
    renderTrendChart()
  } catch (error) {
    console.error('获取年度数据失败:', error)
  }
}

/** 渲染单位类型图表 */
function renderTypeChart() {
  if (!typeChartRef.value || !typeData.value.length) return
  
  const chart = echarts.init(typeChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '单位类型',
        type: 'pie',
        radius: '50%',
        data: typeData.value.map(item => ({
          value: item.partnerCount,
          name: item.partnerType
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 渲染合作等级图表 */
function renderLevelChart() {
  if (!levelChartRef.value || !levelData.value.length) return
  
  const chart = echarts.init(levelChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: levelData.value.map(item => item.cooperationLevel + '级')
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '单位数量',
        type: 'bar',
        data: levelData.value.map(item => item.partnerCount),
        itemStyle: {
          color: function(params) {
            const colors = ['#67c23a', '#409eff', '#e6a23c', '#f56c6c']
            return colors[params.dataIndex] || '#909399'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 渲染地域分布图表 */
function renderRegionChart() {
  if (!regionChartRef.value || !regionData.value.length) return
  
  const chart = echarts.init(regionChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: regionData.value.slice(0, 10).map(item => item.region),
      axisLabel: {
        width: 60,
        overflow: 'truncate'
      }
    },
    series: [
      {
        name: '单位数量',
        type: 'bar',
        data: regionData.value.slice(0, 10).map(item => item.partnerCount),
        itemStyle: {
          color: '#67c23a'
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 渲染活跃度图表 */
function renderActivityChart() {
  if (!activityChartRef.value || !activityData.value.length) return
  
  const chart = echarts.init(activityChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '活跃度',
        type: 'pie',
        radius: ['40%', '70%'],
        data: activityData.value.map(item => ({
          value: item.partnerCount,
          name: item.activityLevel
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 渲染金额分布图表 */
function renderAmountChart() {
  if (!amountChartRef.value || !amountData.value.length) return
  
  const chart = echarts.init(amountChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: amountData.value.slice(0, 10).map(item => item.partnerName),
      axisLabel: {
        rotate: 45,
        width: 80,
        overflow: 'truncate'
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: function(value) {
          return (value / 10000).toFixed(0) + '万'
        }
      }
    },
    series: [
      {
        name: '合作金额',
        type: 'bar',
        data: amountData.value.slice(0, 10).map(item => item.totalContractAmount),
        itemStyle: {
          color: '#e6a23c'
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 渲染状态统计图表 */
function renderStatusChart() {
  if (!statusChartRef.value || !statusData.value.length) return
  
  const chart = echarts.init(statusChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '单位状态',
        type: 'pie',
        radius: '50%',
        data: statusData.value.map(item => ({
          value: item.partnerCount,
          name: item.statusName
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 渲染新增趋势图表 */
function renderTrendChart() {
  if (!trendChartRef.value || !trendData.value.length) return
  
  const chart = echarts.init(trendChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: trendData.value.map(item => item.createDate)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '新增单位数量',
        type: 'line',
        data: trendData.value.map(item => item.partnerCount),
        smooth: true,
        itemStyle: {
          color: '#409eff'
        },
        areaStyle: {
          color: 'rgba(64, 158, 255, 0.2)'
        }
      }
    ]
  }
  chart.setOption(option)
}

/** 获取合作等级类型 */
function getLevelType(level) {
  const levelMap = {
    'A': 'success',
    'B': 'primary',
    'C': 'warning',
    'D': 'danger'
  }
  return levelMap[level] || 'info'
}

/** 获取活跃度类型 */
function getActivityType(activity) {
  const activityMap = {
    '活跃': 'success',
    '一般': 'warning',
    '不活跃': 'danger',
    '沉睡': 'info'
  }
  return activityMap[activity] || 'info'
}

/** 格式化金额 */
function formatMoney(money) {
  if (!money) return '0.00'
  return (parseFloat(money) / 10000).toFixed(2)
}

/** 解析时间 */
function parseTime(time, pattern) {
  if (!time) return ''
  return new Date(time).toLocaleDateString()
}

/** 导出统计 */
function exportStatistics() {
  // 这里应该调用导出接口
  console.log('导出合作单位统计数据')
}

/** 关闭对话框 */
function handleClose() {
  visible.value = false
}
</script>

<style scoped>
.stat-card {
  text-align: center;
}

.stat-item {
  padding: 20px 0;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}
</style>
