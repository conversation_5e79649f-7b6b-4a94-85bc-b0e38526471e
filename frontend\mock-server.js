const express = require('express');
const cors = require('cors');
const app = express();
const port = 8080;

// 中间件
app.use(cors());
app.use(express.json());

// 模拟数据
const mockProjects = [
  {
    id: 1,
    projectNo: 'HX2024001',
    projectName: '智能制造系统开发项目',
    projectType: '技术开发',
    partnerId: 1,
    partnerName: '华为技术有限公司',
    status: 2,
    startDate: '2024-01-15',
    endDate: '2024-12-31',
    totalFund: 2500000.00,
    receivedFund: 1500000.00,
    fundReceiveRate: 60.0,
    progressPercentage: 75,
    remainingDays: 120,
    isExpiringSoon: false,
    principalId: 1001,
    principalName: '张教授',
    deptId: 101,
    deptName: '计算机学院',
    keywords: '智能制造;生产优化;质量控制;设备监控',
    projectSummary: '开发面向制造业的智能管理系统，提升生产效率和质量控制水平',
    researchContent: '研究智能制造关键技术，开发生产计划优化算法，设计质量控制模型，构建设备监控平台',
    expectedResults: '完成智能制造系统V1.0，申请发明专利2-3项，发表高水平论文3-5篇',
    createTime: '2024-01-15 10:00:00',
    updateTime: '2024-07-30 15:30:00'
  },
  {
    id: 2,
    projectNo: 'HX2024002',
    projectName: '大数据分析平台建设项目',
    projectType: '技术服务',
    partnerId: 2,
    partnerName: '清华大学',
    status: 2,
    startDate: '2024-02-20',
    endDate: '2024-11-30',
    totalFund: 1800000.00,
    receivedFund: 900000.00,
    fundReceiveRate: 50.0,
    progressPercentage: 60,
    remainingDays: 90,
    isExpiringSoon: false,
    principalId: 1002,
    principalName: '李教授',
    deptId: 102,
    deptName: '软件学院',
    keywords: '大数据;数据分析;可视化;分布式计算',
    projectSummary: '构建企业级大数据分析平台，为企业决策提供数据支撑',
    researchContent: '设计分布式数据存储架构，开发实时数据处理引擎，构建可视化分析界面',
    expectedResults: '交付完整的大数据分析平台，培训技术人员，建立运维体系',
    createTime: '2024-02-20 09:00:00',
    updateTime: '2024-07-30 14:20:00'
  }
];

const mockContracts = [
  {
    id: 1,
    contractNo: 'HT2024001',
    contractName: '智能制造系统开发合同',
    contractType: '技术开发',
    partnerId: 1,
    partnerName: '华为技术有限公司',
    contractAmount: 2500000.00,
    signingDate: '2024-01-15',
    startDate: '2024-01-15',
    endDate: '2024-12-31',
    status: 3,
    principalId: 1001,
    principalName: '张教授',
    deptId: 101,
    deptName: '计算机学院',
    progressPercentage: 75,
    remainingDays: 120,
    isExpiringSoon: false,
    createTime: '2024-01-15 10:00:00'
  }
];

const mockPartners = [
  {
    id: 1,
    partnerCode: 'P001',
    partnerName: '华为技术有限公司',
    partnerType: '企业',
    legalRepresentative: '任正非',
    contactPerson: '张三',
    contactPhone: '13800138001',
    contactEmail: '<EMAIL>',
    address: '深圳市龙岗区坂田华为基地',
    cooperationLevel: 'A',
    cooperationCount: 5,
    totalContractAmount: 12500000.00,
    lastCooperationDate: '2024-06-15',
    status: 1,
    createTime: '2024-01-01 10:00:00'
  }
];

// API路由

// 验证码接口
app.get('/captchaImage', (req, res) => {
  res.json({
    code: 200,
    msg: '操作成功',
    data: {
      uuid: 'test-uuid-123',
      img: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
    }
  });
});

// 横向项目API
app.get('/project/horizontal/list', (req, res) => {
  const { pageNum = 1, pageSize = 10 } = req.query;
  const start = (pageNum - 1) * pageSize;
  const end = start + parseInt(pageSize);
  
  res.json({
    code: 200,
    msg: '查询成功',
    rows: mockProjects.slice(start, end),
    total: mockProjects.length
  });
});

app.get('/project/horizontal/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const project = mockProjects.find(p => p.id === id);
  
  if (project) {
    res.json({
      code: 200,
      msg: '查询成功',
      data: project
    });
  } else {
    res.status(404).json({
      code: 404,
      msg: '项目不存在'
    });
  }
});

app.post('/project/horizontal', (req, res) => {
  const newProject = {
    id: mockProjects.length + 1,
    ...req.body,
    createTime: new Date().toISOString(),
    updateTime: new Date().toISOString()
  };
  mockProjects.push(newProject);
  
  res.json({
    code: 200,
    msg: '新增成功',
    data: newProject
  });
});

app.put('/project/horizontal', (req, res) => {
  const { id } = req.body;
  const index = mockProjects.findIndex(p => p.id === id);
  
  if (index !== -1) {
    mockProjects[index] = {
      ...mockProjects[index],
      ...req.body,
      updateTime: new Date().toISOString()
    };
    res.json({
      code: 200,
      msg: '修改成功',
      data: mockProjects[index]
    });
  } else {
    res.status(404).json({
      code: 404,
      msg: '项目不存在'
    });
  }
});

app.delete('/project/horizontal/:ids', (req, res) => {
  const ids = req.params.ids.split(',').map(id => parseInt(id));
  
  ids.forEach(id => {
    const index = mockProjects.findIndex(p => p.id === id);
    if (index !== -1) {
      mockProjects.splice(index, 1);
    }
  });
  
  res.json({
    code: 200,
    msg: '删除成功'
  });
});

// 合同API
app.get('/project/contract/list', (req, res) => {
  const { pageNum = 1, pageSize = 10 } = req.query;
  const start = (pageNum - 1) * pageSize;
  const end = start + parseInt(pageSize);
  
  res.json({
    code: 200,
    msg: '查询成功',
    rows: mockContracts.slice(start, end),
    total: mockContracts.length
  });
});

// 合作单位API
app.get('/project/partner/list', (req, res) => {
  const { pageNum = 1, pageSize = 10 } = req.query;
  const start = (pageNum - 1) * pageSize;
  const end = start + parseInt(pageSize);
  
  res.json({
    code: 200,
    msg: '查询成功',
    rows: mockPartners.slice(start, end),
    total: mockPartners.length
  });
});

// 统计API
app.get('/project/horizontal/statistics', (req, res) => {
  res.json({
    code: 200,
    msg: '查询成功',
    data: {
      totalCount: mockProjects.length,
      totalFund: mockProjects.reduce((sum, p) => sum + p.totalFund, 0),
      receivedFund: mockProjects.reduce((sum, p) => sum + p.receivedFund, 0),
      expiringCount: mockProjects.filter(p => p.isExpiringSoon).length
    }
  });
});

// 生成项目编号
app.get('/project/horizontal/generate-no', (req, res) => {
  const year = new Date().getFullYear();
  const count = mockProjects.length + 1;
  const projectNo = `HX${year}${String(count).padStart(3, '0')}`;
  
  res.json({
    code: 200,
    msg: '生成成功',
    data: projectNo
  });
});

// 启动服务器
app.listen(port, () => {
  console.log(`🚀 Mock server running at http://localhost:${port}`);
  console.log('📋 Available endpoints:');
  console.log('  GET  /captchaImage');
  console.log('  GET  /project/horizontal/list');
  console.log('  GET  /project/horizontal/:id');
  console.log('  POST /project/horizontal');
  console.log('  PUT  /project/horizontal');
  console.log('  DELETE /project/horizontal/:ids');
  console.log('  GET  /project/contract/list');
  console.log('  GET  /project/partner/list');
  console.log('  GET  /project/horizontal/statistics');
  console.log('  GET  /project/horizontal/generate-no');
});
