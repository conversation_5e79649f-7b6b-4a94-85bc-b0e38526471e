package com.research.workflow.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.research.workflow.domain.ProcessDefinition;
import com.research.workflow.domain.ProcessInstance;

import java.util.List;
import java.util.Map;

/**
 * 工作流流程管理Service接口
 * 
 * <AUTHOR>
 */
public interface IWorkflowProcessService {

    // ==================== 流程定义相关方法 ====================

    /**
     * 查询流程定义列表
     * 
     * @param page 分页对象
     * @param processDefinition 流程定义
     * @return 流程定义集合
     */
    IPage<ProcessDefinition> selectDefinitionList(Page<ProcessDefinition> page, ProcessDefinition processDefinition);

    /**
     * 查询流程定义详情
     * 
     * @param id 流程定义ID
     * @return 流程定义
     */
    ProcessDefinition selectDefinitionById(String id);

    /**
     * 启动流程实例
     * 
     * @param definitionId 流程定义ID
     * @param variables 流程变量
     * @return 流程实例
     */
    ProcessInstance startProcess(String definitionId, Map<String, Object> variables);

    /**
     * 挂起流程定义
     * 
     * @param id 流程定义ID
     */
    void suspendDefinition(String id);

    /**
     * 激活流程定义
     * 
     * @param id 流程定义ID
     */
    void activateDefinition(String id);

    /**
     * 删除流程定义
     *
     * @param id 流程定义ID
     */
    void deleteDefinition(String id);

    /**
     * 部署BPMN流程文件
     *
     * @param name 部署名称
     * @param bpmnXml BPMN XML内容
     * @return 部署ID
     */
    String deployProcess(String name, String bpmnXml);

    // ==================== 流程实例相关方法 ====================

    /**
     * 查询流程实例列表
     * 
     * @param page 分页对象
     * @param processInstance 流程实例
     * @return 流程实例集合
     */
    IPage<ProcessInstance> selectInstanceList(Page<ProcessInstance> page, ProcessInstance processInstance);

    /**
     * 查询流程实例详情
     * 
     * @param id 流程实例ID
     * @return 流程实例
     */
    ProcessInstance selectInstanceById(String id);

    /**
     * 挂起流程实例
     * 
     * @param id 流程实例ID
     */
    void suspendInstance(String id);

    /**
     * 激活流程实例
     * 
     * @param id 流程实例ID
     */
    void activateInstance(String id);

    /**
     * 终止流程实例
     * 
     * @param id 流程实例ID
     * @param reason 终止原因
     */
    void terminateInstance(String id, String reason);

    /**
     * 批量终止流程实例
     * 
     * @param instanceIds 流程实例ID列表
     */
    void batchTerminateInstances(List<String> instanceIds);

    /**
     * 获取流程图
     * 
     * @param instanceId 流程实例ID
     * @return 流程图XML
     */
    String getProcessDiagram(String instanceId);

    /**
     * 获取流程历史
     * 
     * @param instanceId 流程实例ID
     * @return 流程历史
     */
    List<Map<String, Object>> getProcessHistory(String instanceId);

    // ==================== 流程统计相关方法 ====================

    /**
     * 获取流程统计数据
     * 
     * @return 统计数据
     */
    Map<String, Object> getProcessStatistics();

    /**
     * 获取流程趋势数据
     * 
     * @param days 天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getProcessTrendData(int days);

    /**
     * 获取流程详细统计
     * 
     * @return 详细统计数据
     */
    List<Map<String, Object>> getProcessDetailStatistics();

    /**
     * 获取流程类型统计
     * 
     * @return 类型统计数据
     */
    List<Map<String, Object>> getProcessTypeStatistics();

    /**
     * 获取流程状态分布
     * 
     * @return 状态分布数据
     */
    List<Map<String, Object>> getProcessStatusDistribution();

    /**
     * 获取平均处理时长统计
     * 
     * @return 处理时长统计
     */
    List<Map<String, Object>> getProcessDurationStatistics();

    /**
     * 获取用户参与统计
     * 
     * @return 用户参与统计
     */
    List<Map<String, Object>> getUserParticipationStatistics();

    /**
     * 获取流程性能指标
     * 
     * @return 性能指标
     */
    Map<String, Object> getProcessPerformanceMetrics();
}
