import request from '@/utils/request'

// 查询我的任务列表
export function listMyTask(query: any) {
  return request({
    url: '/workflow/task/my/list',
    method: 'get',
    params: query
  })
}

// 查询候选任务列表
export function listCandidateTask(query: any) {
  return request({
    url: '/workflow/task/candidate/list',
    method: 'get',
    params: query
  })
}

// 查询历史任务列表
export function listHistoryTask(query: any) {
  return request({
    url: '/workflow/task/history/list',
    method: 'get',
    params: query
  })
}

// 查询任务详细
export function getTask(taskId: string) {
  return request({
    url: '/workflow/task/' + taskId,
    method: 'get'
  })
}

// 签收任务
export function claimTask(taskId: string) {
  return request({
    url: '/workflow/task/claim/' + taskId,
    method: 'put'
  })
}

// 完成任务
export function completeTask(taskId: string, data: any) {
  return request({
    url: '/workflow/task/complete/' + taskId,
    method: 'put',
    params: {
      comment: data.comment
    },
    data: data.variables
  })
}

// 转办任务
export function assignTask(taskId: string, userId: string) {
  return request({
    url: '/workflow/task/assign/' + taskId,
    method: 'put',
    params: {
      userId
    }
  })
}

// 委派任务
export function delegateTask(taskId: string, userId: string) {
  return request({
    url: '/workflow/task/delegate/' + taskId,
    method: 'put',
    params: {
      userId
    }
  })
}

// 归还任务
export function resolveTask(taskId: string) {
  return request({
    url: '/workflow/task/resolve/' + taskId,
    method: 'put'
  })
}

// 获取任务变量
export function getTaskVariables(taskId: string) {
  return request({
    url: '/workflow/task/variables/' + taskId,
    method: 'get'
  })
}

// 设置任务变量
export function setTaskVariables(taskId: string, variables: any) {
  return request({
    url: '/workflow/task/variables/' + taskId,
    method: 'put',
    data: variables
  })
}
