<template>
  <el-form ref="formRef" :model="localForm" :rules="rules" label-width="80px">
    <el-form-item v-if="type === 'new' || type === 'forward'" label="收件人" prop="receiverName">
      <el-input v-model="localForm.receiverName" placeholder="请输入收件人姓名" />
    </el-form-item>
    <el-form-item v-else label="收件人" prop="receiverName">
      <el-input v-model="localForm.receiverName" disabled />
    </el-form-item>
    
    <el-form-item label="消息标题" prop="title">
      <el-input v-model="localForm.title" placeholder="请输入消息标题" />
    </el-form-item>
    
    <el-form-item v-if="type === 'new'" label="消息类型" prop="messageType">
      <el-select v-model="localForm.messageType" placeholder="请选择消息类型">
        <el-option label="私信消息" value="3" />
        <el-option label="通知消息" value="2" />
      </el-select>
    </el-form-item>
    
    <el-form-item v-if="type === 'new'" label="优先级" prop="priority">
      <el-select v-model="localForm.priority" placeholder="请选择优先级">
        <el-option label="普通" value="1" />
        <el-option label="重要" value="2" />
        <el-option label="紧急" value="3" />
      </el-select>
    </el-form-item>
    
    <el-form-item label="消息内容" prop="content">
      <el-input
        v-model="localForm.content"
        type="textarea"
        :rows="6"
        placeholder="请输入消息内容"
      />
    </el-form-item>
    
    <div class="form-actions">
      <el-button type="primary" @click="handleSubmit">发 送</el-button>
      <el-button @click="handleCancel">取 消</el-button>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { sendMessage, replyMessage, forwardMessage } from '@/api/system/message'

// Props
interface Props {
  form: any
  type: 'new' | 'reply' | 'forward'
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits(['submit', 'cancel'])

// 响应式数据
const formRef = ref()
const localForm = reactive({
  receiverId: undefined,
  receiverName: '',
  title: '',
  content: '',
  messageType: '3',
  priority: '1',
  originalMessageId: undefined
})

// 表单验证规则
const rules = {
  receiverName: [
    { required: true, message: '请输入收件人', trigger: 'blur' }
  ],
  title: [
    { required: true, message: '请输入消息标题', trigger: 'blur' },
    { min: 2, max: 200, message: '标题长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入消息内容', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.form, (newVal) => {
  if (newVal) {
    Object.assign(localForm, newVal)
  }
}, { immediate: true, deep: true })

// 提交表单
const handleSubmit = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        // 这里应该先通过用户名查询用户ID，简化处理直接使用姓名
        const receiverId = 1 // 实际应该通过API查询用户ID
        localForm.receiverId = receiverId
        
        if (props.type === 'reply') {
          await replyMessage(localForm.originalMessageId, localForm.content)
        } else if (props.type === 'forward') {
          await forwardMessage(localForm.originalMessageId, [receiverId], localForm.content)
        } else {
          await sendMessage(localForm)
        }
        
        ElMessage.success('发送成功')
        emit('submit')
      } catch (error) {
        console.error('发送失败:', error)
        ElMessage.error('发送失败')
      }
    } else {
      ElMessage.error('请检查表单输入')
    }
  })
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.form-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}
</style>
