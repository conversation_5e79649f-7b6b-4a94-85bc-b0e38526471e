package com.research.workflow.service;

import java.util.Map;

/**
 * 工作流监控Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IWorkflowMonitorService {
    
    /**
     * 获取工作流总览数据
     * 
     * @return 总览数据
     */
    Map<String, Object> getWorkflowOverview();

    /**
     * 获取监控大屏综合数据
     * 
     * @return 大屏数据
     */
    Map<String, Object> getDashboardData();

    /**
     * 获取实时指标数据
     * 
     * @return 实时指标
     */
    Map<String, Object> getRealTimeMetrics();

    /**
     * 获取版本分布统计
     * 
     * @return 版本分布
     */
    Map<String, Object> getVersionDistribution();

    /**
     * 获取实时流程活动数据
     * 
     * @return 实时活动数据
     */
    Map<String, Object> getRealTimeActivity();

    /**
     * 获取流程性能统计
     * 
     * @return 性能统计
     */
    Map<String, Object> getProcessPerformance();

    /**
     * 获取任务分布统计
     * 
     * @return 任务分布
     */
    Map<String, Object> getTaskDistribution();

    /**
     * 获取历史趋势数据
     * 
     * @param days 天数
     * @return 历史趋势
     */
    Map<String, Object> getHistoricalTrends(Integer days);

    /**
     * 获取版本路由统计
     * 
     * @return 路由统计
     */
    Map<String, Object> getVersionRouteStats();

    /**
     * 获取系统健康状态
     * 
     * @return 系统健康状态
     */
    Map<String, Object> getSystemHealth();

    /**
     * 获取流程执行统计
     * 
     * @param processDefinitionKey 流程定义Key
     * @param days 天数
     * @return 执行统计
     */
    Map<String, Object> getExecutionStats(String processDefinitionKey, Integer days);

    /**
     * 获取任务处理效率统计
     * 
     * @param assignee 处理人
     * @param days 天数
     * @return 效率统计
     */
    Map<String, Object> getTaskEfficiency(String assignee, Integer days);

    /**
     * 获取流程瓶颈分析
     * 
     * @param processDefinitionKey 流程定义Key
     * @return 瓶颈分析
     */
    Map<String, Object> getBottleneckAnalysis(String processDefinitionKey);

    /**
     * 获取用户活跃度统计
     * 
     * @param days 天数
     * @return 用户活跃度
     */
    Map<String, Object> getUserActivity(Integer days);

    /**
     * 获取部门工作量统计
     * 
     * @param days 天数
     * @return 部门工作量
     */
    Map<String, Object> getDepartmentWorkload(Integer days);

    /**
     * 导出监控报表
     * 
     * @param reportType 报表类型
     * @param days 天数
     * @param processDefinitionKey 流程定义Key
     * @return 文件路径
     */
    String exportReport(String reportType, Integer days, String processDefinitionKey);

    /**
     * 获取告警信息
     * 
     * @return 告警信息
     */
    Map<String, Object> getAlerts();

    /**
     * 获取资源使用情况
     * 
     * @return 资源使用情况
     */
    Map<String, Object> getResourceUsage();

    /**
     * 获取流程热力图数据
     * 
     * @param processDefinitionKey 流程定义Key
     * @param days 天数
     * @return 热力图数据
     */
    Map<String, Object> getProcessHeatmap(String processDefinitionKey, Integer days);

    /**
     * 获取SLA达成率统计
     * 
     * @param processDefinitionKey 流程定义Key
     * @param days 天数
     * @return SLA统计
     */
    Map<String, Object> getSLACompliance(String processDefinitionKey, Integer days);

    /**
     * 获取流程实例状态分布
     * 
     * @return 状态分布
     */
    Map<String, Object> getInstanceStatusDistribution();

    /**
     * 获取任务状态分布
     * 
     * @return 任务状态分布
     */
    Map<String, Object> getTaskStatusDistribution();

    /**
     * 获取流程耗时分析
     * 
     * @param processDefinitionKey 流程定义Key
     * @param days 天数
     * @return 耗时分析
     */
    Map<String, Object> getProcessDurationAnalysis(String processDefinitionKey, Integer days);

    /**
     * 获取异常流程统计
     * 
     * @param days 天数
     * @return 异常统计
     */
    Map<String, Object> getExceptionProcessStats(Integer days);

    /**
     * 获取流程覆盖率统计
     * 
     * @return 覆盖率统计
     */
    Map<String, Object> getProcessCoverageStats();

    /**
     * 获取用户操作日志统计
     * 
     * @param days 天数
     * @return 操作日志统计
     */
    Map<String, Object> getUserOperationStats(Integer days);

    /**
     * 获取流程节点执行统计
     * 
     * @param processDefinitionKey 流程定义Key
     * @param days 天数
     * @return 节点执行统计
     */
    Map<String, Object> getNodeExecutionStats(String processDefinitionKey, Integer days);

    /**
     * 获取流程变量使用统计
     * 
     * @param processDefinitionKey 流程定义Key
     * @return 变量使用统计
     */
    Map<String, Object> getVariableUsageStats(String processDefinitionKey);

    /**
     * 获取流程版本对比分析
     * 
     * @param processDefinitionKey 流程定义Key
     * @return 版本对比分析
     */
    Map<String, Object> getVersionComparisonAnalysis(String processDefinitionKey);

    /**
     * 获取实时监控指标
     * 
     * @return 实时监控指标
     */
    Map<String, Object> getRealTimeMonitoringMetrics();

    /**
     * 获取预警规则配置
     * 
     * @return 预警规则
     */
    Map<String, Object> getAlertRules();

    /**
     * 更新预警规则
     * 
     * @param rules 规则配置
     * @return 结果
     */
    int updateAlertRules(Map<String, Object> rules);

    /**
     * 获取监控配置
     * 
     * @return 监控配置
     */
    Map<String, Object> getMonitorConfig();

    /**
     * 更新监控配置
     * 
     * @param config 配置信息
     * @return 结果
     */
    int updateMonitorConfig(Map<String, Object> config);
}
