{"name": "research-management-frontend", "version": "1.0.0", "description": "科研成果多维敏捷管控中心前端", "author": "research", "license": "MIT", "type": "module", "scripts": {"dev": "vite --open", "build": "vite build", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"axios": "^1.4.0", "bpmn-js": "^17.0.2", "bpmn-js-properties-panel": "^5.0.0", "camunda-bpmn-moddle": "^7.0.1", "dayjs": "^1.11.9", "echarts": "^5.4.3", "element-plus": "^2.3.8", "fuse.js": "^6.6.2", "highlight.js": "^11.8.0", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.1", "pinia": "^2.1.6", "quill": "^1.3.7", "screenfull": "^6.0.2", "sortablejs": "^1.15.0", "vue": "^3.3.4", "vue-cropper": "^1.0.3", "vue-router": "^4.2.4", "vuedraggable": "^2.24.3"}, "devDependencies": {"@types/js-cookie": "^3.0.3", "@types/node": "^20.4.5", "@types/nprogress": "^0.2.0", "@types/sortablejs": "^1.15.1", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "@vitejs/plugin-vue": "^4.2.3", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/tsconfig": "^0.4.0", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "prettier": "^3.0.0", "sass": "^1.64.1", "typescript": "~5.1.6", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.5.14", "vue-tsc": "^1.8.6"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}