package com.research.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.research.project.domain.entity.ProjectMember;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.List;
import java.util.Map;

/**
 * 项目成员Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Mapper
public interface ProjectMemberMapper extends BaseMapper<ProjectMember> {

    /**
     * 查询项目成员列表
     * 
     * @param projectMember 项目成员
     * @return 项目成员集合
     */
    List<ProjectMember> selectProjectMemberList(ProjectMember projectMember);

    /**
     * 根据项目ID查询成员列表
     * 
     * @param projectId 项目ID
     * @return 成员列表
     */
    @Select("SELECT * FROM project_member WHERE project_id = #{projectId} ORDER BY sort_order ASC, create_time ASC")
    List<ProjectMember> selectByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据用户ID查询参与的项目成员记录
     * 
     * @param userId 用户ID
     * @return 成员记录列表
     */
    @Select("SELECT * FROM project_member WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<ProjectMember> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据项目ID和用户ID查询成员记录
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 成员记录
     */
    @Select("SELECT * FROM project_member WHERE project_id = #{projectId} AND user_id = #{userId}")
    ProjectMember selectByProjectIdAndUserId(@Param("projectId") Long projectId, @Param("userId") Long userId);

    /**
     * 根据项目ID和成员角色查询成员列表
     * 
     * @param projectId 项目ID
     * @param memberRole 成员角色
     * @return 成员列表
     */
    @Select("SELECT * FROM project_member WHERE project_id = #{projectId} AND member_role = #{memberRole} ORDER BY sort_order ASC")
    List<ProjectMember> selectByProjectIdAndRole(@Param("projectId") Long projectId, @Param("memberRole") Integer memberRole);

    /**
     * 查询项目负责人
     * 
     * @param projectId 项目ID
     * @return 项目负责人
     */
    @Select("SELECT * FROM project_member WHERE project_id = #{projectId} AND member_role = 0")
    ProjectMember selectPrincipalByProjectId(@Param("projectId") Long projectId);

    /**
     * 查询项目主要参与人
     * 
     * @param projectId 项目ID
     * @return 主要参与人列表
     */
    @Select("SELECT * FROM project_member WHERE project_id = #{projectId} AND member_role = 1 ORDER BY sort_order ASC")
    List<ProjectMember> selectMainMembersByProjectId(@Param("projectId") Long projectId);

    /**
     * 查询外聘专家
     * 
     * @param projectId 项目ID
     * @return 外聘专家列表
     */
    @Select("SELECT * FROM project_member WHERE project_id = #{projectId} AND is_external = 1 ORDER BY sort_order ASC")
    List<ProjectMember> selectExternalMembersByProjectId(@Param("projectId") Long projectId);

    /**
     * 统计项目成员数量
     * 
     * @param projectId 项目ID
     * @return 成员数量
     */
    @Select("SELECT COUNT(*) FROM project_member WHERE project_id = #{projectId} AND status = 0")
    Integer countByProjectId(@Param("projectId") Long projectId);

    /**
     * 统计用户参与的项目数量
     * 
     * @param userId 用户ID
     * @return 项目数量
     */
    @Select("SELECT COUNT(DISTINCT project_id) FROM project_member WHERE user_id = #{userId} AND status = 0")
    Integer countProjectsByUserId(@Param("userId") Long userId);

    /**
     * 查询成员角色统计
     * 
     * @param projectId 项目ID
     * @return 角色统计
     */
    @Select("SELECT " +
            "member_role, " +
            "member_role_name, " +
            "COUNT(*) as member_count " +
            "FROM project_member " +
            "WHERE project_id = #{projectId} AND status = 0 " +
            "GROUP BY member_role, member_role_name " +
            "ORDER BY member_role")
    List<Map<String, Object>> selectRoleStatistics(@Param("projectId") Long projectId);

    /**
     * 查询部门成员统计
     * 
     * @param projectId 项目ID
     * @return 部门统计
     */
    @Select("SELECT " +
            "dept_id, " +
            "dept_name, " +
            "COUNT(*) as member_count " +
            "FROM project_member " +
            "WHERE project_id = #{projectId} AND status = 0 " +
            "GROUP BY dept_id, dept_name " +
            "ORDER BY member_count DESC")
    List<Map<String, Object>> selectDeptStatistics(@Param("projectId") Long projectId);

    /**
     * 查询成员工作量统计
     * 
     * @param projectId 项目ID
     * @return 工作量统计
     */
    @Select("SELECT " +
            "SUM(workload) as total_workload, " +
            "AVG(workload) as avg_workload, " +
            "MAX(workload) as max_workload, " +
            "MIN(workload) as min_workload " +
            "FROM project_member " +
            "WHERE project_id = #{projectId} AND status = 0 AND workload IS NOT NULL")
    Map<String, Object> selectWorkloadStatistics(@Param("projectId") Long projectId);

    /**
     * 查询活跃成员（参与项目最多的成员）
     * 
     * @param limit 限制数量
     * @return 活跃成员列表
     */
    @Select("SELECT " +
            "user_id, " +
            "user_name, " +
            "COUNT(DISTINCT project_id) as project_count, " +
            "SUM(workload) as total_workload " +
            "FROM project_member " +
            "WHERE status = 0 " +
            "GROUP BY user_id, user_name " +
            "ORDER BY project_count DESC, total_workload DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> selectActiveMemberStatistics(@Param("limit") Integer limit);

    /**
     * 根据项目ID删除所有成员
     * 
     * @param projectId 项目ID
     * @return 删除数量
     */
    @Delete("DELETE FROM project_member WHERE project_id = #{projectId}")
    int deleteByProjectId(@Param("projectId") Long projectId);

    /**
     * 批量插入项目成员
     * 
     * @param members 成员列表
     * @return 插入数量
     */
    int batchInsert(@Param("members") List<ProjectMember> members);

    /**
     * 更新成员状态
     * 
     * @param projectId 项目ID
     * @param userId 用户ID
     * @param status 状态
     * @return 更新数量
     */
    @Select("UPDATE project_member SET status = #{status}, update_time = NOW() " +
            "WHERE project_id = #{projectId} AND user_id = #{userId}")
    int updateMemberStatus(@Param("projectId") Long projectId, @Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 查询即将离开的成员
     * 
     * @param days 天数
     * @return 成员列表
     */
    @Select("SELECT * FROM project_member " +
            "WHERE status = 0 " +
            "AND leave_date IS NOT NULL " +
            "AND leave_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL #{days} DAY) " +
            "ORDER BY leave_date ASC")
    List<ProjectMember> selectLeavingMembers(@Param("days") Integer days);
}
