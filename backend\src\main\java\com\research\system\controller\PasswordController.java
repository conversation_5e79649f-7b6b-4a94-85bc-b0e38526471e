package com.research.system.controller;

import com.research.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 密码工具控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/password")
public class PasswordController {
    
    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * 生成密码哈希值
     */
    @GetMapping("/encode")
    public AjaxResult encodePassword(@RequestParam String password) {
        String encoded = passwordEncoder.encode(password);
        return AjaxResult.success("原密码: " + password + ", 哈希值: " + encoded);
    }
}
