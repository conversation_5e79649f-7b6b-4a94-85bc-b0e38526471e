#!/bin/bash

# 科研成果多维敏捷管控中心构建脚本

echo "开始构建科研成果多维敏捷管控中心..."

# 检查是否安装了必要的工具
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo "错误: $1 未安装，请先安装 $1"
        exit 1
    fi
}

# 检查Java
check_command java
echo "Java版本: $(java -version 2>&1 | head -n 1)"

# 检查Maven
check_command mvn
echo "Maven版本: $(mvn -version | head -n 1)"

# 检查Node.js
check_command node
echo "Node.js版本: $(node -v)"

# 检查npm
check_command npm
echo "npm版本: $(npm -v)"

echo "=========================================="

# 构建后端
echo "开始构建后端项目..."
cd backend

# 清理并编译
mvn clean compile
if [ $? -ne 0 ]; then
    echo "后端编译失败"
    exit 1
fi

# 运行测试
mvn test
if [ $? -ne 0 ]; then
    echo "后端测试失败"
    exit 1
fi

# 打包
mvn package -DskipTests
if [ $? -ne 0 ]; then
    echo "后端打包失败"
    exit 1
fi

echo "后端构建完成"
cd ..

echo "=========================================="

# 构建前端
echo "开始构建前端项目..."
cd frontend

# 安装依赖
npm install
if [ $? -ne 0 ]; then
    echo "前端依赖安装失败"
    exit 1
fi

# 类型检查
npm run type-check
if [ $? -ne 0 ]; then
    echo "前端类型检查失败"
    exit 1
fi

# 构建生产版本
npm run build:prod
if [ $? -ne 0 ]; then
    echo "前端构建失败"
    exit 1
fi

echo "前端构建完成"
cd ..

echo "=========================================="
echo "构建完成！"
echo "后端jar包位置: backend/target/"
echo "前端构建文件位置: frontend/dist/"
