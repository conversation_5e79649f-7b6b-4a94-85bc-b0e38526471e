// Vite 兼容的图标列表
// 由于 Vite 不支持 require.context，我们手动定义图标列表
const icons = [
  'user',
  'setting',
  'menu',
  'tree-table',
  'system',
  'monitor',
  'tool',
  'guide',
  'dashboard',
  'form',
  'table',
  'nested',
  'example',
  'bug',
  'chart',
  'clipboard',
  'component',
  'drag',
  'edit',
  'education',
  'email',
  'excel',
  'eye',
  'eye-open',
  'international',
  'language',
  'list',
  'lock',
  'message',
  'money',
  'password',
  'people',
  'peoples',
  'permission',
  'phone',
  'post',
  'qq',
  'redis',
  'search',
  'server',
  'shopping',
  'size',
  'skill',
  'star',
  'swagger',
  'tab',
  'theme',
  'time-range',
  'tree',
  'upload',
  'validCode',
  'wechat',
  'zip'
]

export default icons
