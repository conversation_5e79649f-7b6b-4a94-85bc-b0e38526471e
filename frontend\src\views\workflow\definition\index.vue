<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="流程名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入流程名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="流程Key" prop="key">
        <el-input
          v-model="queryParams.key"
          placeholder="请输入流程Key"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="流程分类" prop="category">
        <el-input
          v-model="queryParams.category"
          placeholder="请输入流程分类"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleDeploy"
          v-hasPermi="['workflow:processDefinition:add']"
        >部署流程</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['workflow:processDefinition:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="definitionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="流程名称" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="流程Key" align="center" prop="key" :show-overflow-tooltip="true" />
      <el-table-column label="版本" align="center" prop="version" width="80">
        <template #default="scope">
          <el-tag>v{{ scope.row.version }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="流程分类" align="center" prop="category" />
      <el-table-column label="状态" align="center" prop="suspended" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.suspended ? 'danger' : 'success'">
            {{ scope.row.suspended ? '挂起' : '激活' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="部署时间" align="center" prop="deploymentTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.deploymentTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
        <template #default="scope">
          <el-button link type="primary" @click="handleView(scope.row)">
            <el-icon><View /></el-icon>查看
          </el-button>
          <el-button link type="primary" @click="handleDiagram(scope.row)">
            <el-icon><PictureRounded /></el-icon>流程图
          </el-button>
          <el-button link type="primary" @click="handleXml(scope.row)">
            <el-icon><Document /></el-icon>XML
          </el-button>
          <el-button 
            link 
            :type="scope.row.suspended ? 'success' : 'warning'" 
            @click="handleUpdateState(scope.row)"
            v-hasPermi="['workflow:processDefinition:edit']"
          >
            <el-icon><Switch /></el-icon>{{ scope.row.suspended ? '激活' : '挂起' }}
          </el-button>
          <el-button 
            link 
            type="danger" 
            @click="handleDelete(scope.row)"
            v-hasPermi="['workflow:processDefinition:remove']"
          >
            <el-icon><Delete /></el-icon>删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 部署流程对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".bpmn,.bpmn20.xml,.zip"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
      >
        <template #trigger>
          <el-button type="primary">选取文件</el-button>
        </template>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport" />是否更新已有流程
            </div>
            <span>仅允许导入bpmn/zip格式文件。</span>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 流程图对话框 -->
    <el-dialog title="流程图" v-model="diagram.open" width="70%" append-to-body>
      <div class="diagram-container">
        <img :src="diagram.src" alt="流程图" style="width: 100%; height: auto;" />
      </div>
    </el-dialog>

    <!-- XML查看对话框 -->
    <el-dialog title="流程XML" v-model="xml.open" width="70%" append-to-body>
      <el-input
        v-model="xml.content"
        type="textarea"
        :rows="20"
        readonly
        style="font-family: 'Courier New', monospace;"
      />
    </el-dialog>
  </div>
</template>

<script setup name="ProcessDefinition" lang="ts">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { 
  listProcessDefinition, 
  getProcessDefinition, 
  delProcessDefinition, 
  updateProcessDefinitionState,
  getProcessDefinitionXml,
  getProcessDiagram
} from '@/api/workflow/processDefinition'
import { getToken } from '@/utils/auth'

const { proxy } = getCurrentInstance() as any
const { parseTime } = proxy

const definitionList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: '',
  key: '',
  category: ''
})

// 上传参数
const upload = reactive({
  open: false,
  title: '',
  isUploading: false,
  updateSupport: 0,
  headers: { Authorization: 'Bearer ' + getToken() },
  url: import.meta.env.VITE_APP_BASE_API + '/workflow/processDefinition/deploy'
})

// 流程图参数
const diagram = reactive({
  open: false,
  src: ''
})

// XML参数
const xml = reactive({
  open: false,
  content: ''
})

/** 查询流程定义列表 */
function getList() {
  loading.value = true
  listProcessDefinition(queryParams).then(response => {
    definitionList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef')
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.deploymentId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 部署流程按钮操作 */
function handleDeploy() {
  upload.title = '部署流程'
  upload.open = true
}

/** 查看按钮操作 */
function handleView(row: any) {
  const processDefinitionId = row.id
  getProcessDefinition(processDefinitionId).then(response => {
    proxy.$modal.msgSuccess('查看功能开发中...')
  })
}

/** 流程图按钮操作 */
function handleDiagram(row: any) {
  diagram.src = getProcessDiagram(row.id)
  diagram.open = true
}

/** XML按钮操作 */
function handleXml(row: any) {
  getProcessDefinitionXml(row.id).then(response => {
    xml.content = response.data
    xml.open = true
  })
}

/** 激活/挂起按钮操作 */
function handleUpdateState(row: any) {
  const text = row.suspended ? '激活' : '挂起'
  proxy.$modal.confirm('确认要"' + text + '""' + row.name + '"流程吗？').then(() => {
    return updateProcessDefinitionState(row.id, !row.suspended)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess(text + '成功')
  }).catch(() => {})
}

/** 删除按钮操作 */
function handleDelete(row?: any) {
  const deploymentIds = row?.deploymentId || ids.value
  proxy.$modal.confirm('是否确认删除流程定义编号为"' + deploymentIds + '"的数据项？').then(() => {
    return delProcessDefinition(deploymentIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess('删除成功')
  }).catch(() => {})
}

/** 文件上传中处理 */
const handleFileUploadProgress = () => {
  upload.isUploading = true
}

/** 文件上传成功处理 */
const handleFileSuccess = (response: any) => {
  upload.open = false
  upload.isUploading = false
  proxy.$refs['uploadRef'].clearFiles()
  proxy.$alert('<div style="overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;">' + response.msg + '</div>', '导入结果', { dangerouslyUseHTMLString: true })
  getList()
}

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs['uploadRef'].submit()
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.diagram-container {
  text-align: center;
  max-height: 600px;
  overflow: auto;
}
</style>
