#!/bin/bash

# 科研成果多维敏捷管控中心开发环境启动脚本

echo "启动科研成果多维敏捷管控中心开发环境..."

# 检查是否安装了必要的工具
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo "错误: $1 未安装，请先安装 $1"
        exit 1
    fi
}

# 检查Java
check_command java

# 检查Maven
check_command mvn

# 检查Node.js
check_command node

# 检查npm
check_command npm

# 检查MySQL
check_mysql() {
    if ! command -v mysql &> /dev/null; then
        echo "警告: MySQL客户端未安装，请确保MySQL服务器正在运行"
    else
        echo "检查MySQL连接..."
        mysql -h localhost -u root -p123456 -e "SELECT 1;" 2>/dev/null
        if [ $? -ne 0 ]; then
            echo "警告: 无法连接到MySQL，请检查数据库配置"
        else
            echo "MySQL连接正常"
        fi
    fi
}

# 检查Redis
check_redis() {
    if ! command -v redis-cli &> /dev/null; then
        echo "警告: Redis客户端未安装，请确保Redis服务器正在运行"
    else
        echo "检查Redis连接..."
        redis-cli ping 2>/dev/null
        if [ $? -ne 0 ]; then
            echo "警告: 无法连接到Redis，请检查Redis配置"
        else
            echo "Redis连接正常"
        fi
    fi
}

check_mysql
check_redis

echo "=========================================="

# 启动后端
echo "启动后端开发服务器..."
cd backend

# 安装依赖并启动
mvn clean compile
if [ $? -ne 0 ]; then
    echo "后端编译失败"
    exit 1
fi

# 后台启动后端服务
nohup mvn spring-boot:run > ../logs/backend.log 2>&1 &
BACKEND_PID=$!
echo "后端服务已启动，PID: $BACKEND_PID"

cd ..

echo "=========================================="

# 启动前端
echo "启动前端开发服务器..."
cd frontend

# 安装依赖
if [ ! -d "node_modules" ]; then
    echo "安装前端依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "前端依赖安装失败"
        kill $BACKEND_PID
        exit 1
    fi
fi

# 启动前端开发服务器
npm run dev &
FRONTEND_PID=$!
echo "前端服务已启动，PID: $FRONTEND_PID"

cd ..

echo "=========================================="
echo "开发环境启动完成！"
echo "后端服务: http://localhost:8080"
echo "前端服务: http://localhost:80"
echo "API文档: http://localhost:8080/doc.html"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 创建停止脚本
cat > scripts/stop-dev.sh << EOF
#!/bin/bash
echo "停止开发服务..."
kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
echo "服务已停止"
EOF

chmod +x scripts/stop-dev.sh

# 等待用户中断
trap "bash scripts/stop-dev.sh; exit" INT
wait
