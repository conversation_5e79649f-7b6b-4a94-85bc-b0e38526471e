#!/bin/bash

# 编译检查脚本
echo "开始检查编译错误..."

# 检查ProcessInstanceServiceImpl
echo "检查 ProcessInstanceServiceImpl..."
javac -cp "target/classes:lib/*" src/main/java/com/research/workflow/service/impl/ProcessInstanceServiceImpl.java 2>&1 | grep -E "(error|Error)"

# 检查SysNoticeServiceImpl  
echo "检查 SysNoticeServiceImpl..."
javac -cp "target/classes:lib/*" src/main/java/com/research/system/service/impl/SysNoticeServiceImpl.java 2>&1 | grep -E "(error|Error)"

echo "编译检查完成"
