<template>
  <el-card class="todo-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span class="card-title">待办事项</span>
        <div class="header-actions">
          <el-button type="text" @click="$emit('refresh')">
            <el-icon><Refresh /></el-icon>
          </el-button>
          <el-button type="text" @click="goToTodoList">
            <el-icon><More /></el-icon>
          </el-button>
        </div>
      </div>
    </template>
    
    <div class="todo-content">
      <!-- 统计数据 -->
      <div class="todo-stats">
        <div class="stat-item">
          <div class="stat-number total">{{ todoStats.total || 0 }}</div>
          <div class="stat-label">总计</div>
        </div>
        <div class="stat-item">
          <div class="stat-number pending">{{ todoStats.pending || 0 }}</div>
          <div class="stat-label">待处理</div>
        </div>
        <div class="stat-item">
          <div class="stat-number processing">{{ todoStats.processing || 0 }}</div>
          <div class="stat-label">处理中</div>
        </div>
        <div class="stat-item">
          <div class="stat-number completed">{{ todoStats.completed || 0 }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>
      
      <!-- 最近待办 -->
      <div class="recent-todos" v-if="todoStats.recentTodos && todoStats.recentTodos.length > 0">
        <h4 class="section-title">最近待办</h4>
        <div class="todo-list">
          <div 
            v-for="todo in todoStats.recentTodos" 
            :key="todo.todoId"
            class="todo-item"
            @click="viewTodo(todo.todoId)"
          >
            <div class="todo-info">
              <div class="todo-title">{{ todo.title }}</div>
              <div class="todo-meta">
                <el-tag 
                  :type="getPriorityType(todo.priority)" 
                  size="small"
                >
                  {{ getPriorityText(todo.priority) }}
                </el-tag>
                <span class="todo-time">{{ formatDate(todo.createTime) }}</span>
              </div>
            </div>
            <div class="todo-status">
              <el-tag 
                :type="getStatusType(todo.status)" 
                size="small"
              >
                {{ getStatusText(todo.status) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 即将到期 -->
      <div class="due-soon-todos" v-if="todoStats.dueSoonTodos && todoStats.dueSoonTodos.length > 0">
        <h4 class="section-title warning">即将到期</h4>
        <div class="todo-list">
          <div 
            v-for="todo in todoStats.dueSoonTodos" 
            :key="todo.todoId"
            class="todo-item urgent"
            @click="viewTodo(todo.todoId)"
          >
            <div class="todo-info">
              <div class="todo-title">{{ todo.title }}</div>
              <div class="todo-meta">
                <span class="due-time">{{ formatDate(todo.dueTime) }}</span>
              </div>
            </div>
            <el-icon class="warning-icon"><Warning /></el-icon>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="!todoStats.recentTodos || todoStats.recentTodos.length === 0" class="empty-state">
        <el-icon class="empty-icon"><Document /></el-icon>
        <p>暂无待办事项</p>
        <el-button type="primary" size="small" @click="createTodo">创建待办</el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { Refresh, More, Warning, Document } from '@element-plus/icons-vue'

// Props
interface Props {
  todoStats: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits(['refresh'])

const router = useRouter()

// 获取优先级类型
const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    '1': 'info',    // 低
    '2': '',        // 中
    '3': 'warning', // 高
    '4': 'danger'   // 紧急
  }
  return types[priority] || ''
}

// 获取优先级文本
const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    '1': '低',
    '2': '中',
    '3': '高',
    '4': '紧急'
  }
  return texts[priority] || '未知'
}

// 获取状态类型
const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    '0': 'warning',  // 待处理
    '1': 'primary',  // 处理中
    '2': 'success',  // 已完成
    '3': 'info'      // 已取消
  }
  return types[status] || ''
}

// 获取状态文本
const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    '0': '待处理',
    '1': '处理中',
    '2': '已完成',
    '3': '已取消'
  }
  return texts[status] || '未知'
}

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return ''
  const d = new Date(date)
  const now = new Date()
  const diff = now.getTime() - d.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  return d.toLocaleDateString('zh-CN')
}

// 查看待办详情
const viewTodo = (todoId: number) => {
  router.push(`/system/todo/detail/${todoId}`)
}

// 跳转到待办列表
const goToTodoList = () => {
  router.push('/system/todo')
}

// 创建待办
const createTodo = () => {
  router.push('/system/todo/create')
}
</script>

<style scoped>
.todo-card {
  height: 100%;
  min-height: 300px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 5px;
}

.todo-content {
  height: 100%;
}

.todo-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 5px;
}

.stat-number.total { color: #303133; }
.stat-number.pending { color: #e6a23c; }
.stat-number.processing { color: #409eff; }
.stat-number.completed { color: #67c23a; }

.stat-label {
  font-size: 12px;
  color: #909399;
}

.section-title {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.section-title.warning {
  color: #e6a23c;
}

.todo-list {
  max-height: 200px;
  overflow-y: auto;
}

.todo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.3s;
}

.todo-item:hover {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding-left: 8px;
  padding-right: 8px;
}

.todo-item.urgent {
  background-color: #fef0f0;
}

.todo-item:last-child {
  border-bottom: none;
}

.todo-info {
  flex: 1;
}

.todo-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.todo-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.todo-time, .due-time {
  font-size: 12px;
  color: #909399;
}

.due-time {
  color: #e6a23c;
}

.todo-status {
  margin-left: 10px;
}

.warning-icon {
  color: #e6a23c;
  font-size: 16px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

@media (max-width: 768px) {
  .todo-stats {
    flex-wrap: wrap;
  }
  
  .stat-item {
    flex: 0 0 50%;
    margin-bottom: 10px;
  }
  
  .todo-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .todo-status {
    margin-left: 0;
    margin-top: 5px;
  }
}
</style>
