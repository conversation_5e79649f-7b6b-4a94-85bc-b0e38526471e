#!/bin/bash

# 科研成果多维敏捷管控中心 - 安全部署脚本
# 不影响现有项目的部署方案

echo "=========================================="
echo "科研成果多维敏捷管控中心 - 安全部署脚本"
echo "=========================================="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用sudo运行此脚本"
    exit 1
fi

# 设置变量
APP_NAME="research-management"
APP_DIR="/opt/$APP_NAME"
WEB_DIR="/var/www/$APP_NAME"
JAR_FILE="research-management-1.0.0.jar"
BACKEND_PORT="8989"
FRONTEND_PORT="3000"
NGINX_CONF="/usr/local/nginx/conf/nginx.conf"

echo "开始安全部署..."

# 1. 创建应用目录
echo "1. 创建应用目录..."
mkdir -p $APP_DIR
mkdir -p $WEB_DIR

# 2. 检查端口是否被占用
echo "2. 检查端口占用情况..."
if netstat -tlnp | grep ":$BACKEND_PORT " > /dev/null; then
    echo "警告: 端口 $BACKEND_PORT 已被占用"
    echo "当前占用进程:"
    netstat -tlnp | grep ":$BACKEND_PORT "
    read -p "是否继续部署? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

if netstat -tlnp | grep ":$FRONTEND_PORT " > /dev/null; then
    echo "警告: 端口 $FRONTEND_PORT 已被占用"
    echo "当前占用进程:"
    netstat -tlnp | grep ":$FRONTEND_PORT "
    read -p "是否继续部署? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 3. 检查Java环境
echo "3. 检查Java环境..."
if ! command -v java &> /dev/null; then
    echo "Java未安装，正在安装OpenJDK 11..."
    apt update
    apt install -y openjdk-11-jdk
else
    echo "Java已安装: $(java -version 2>&1 | head -n 1)"
fi

# 4. 部署后端
echo "4. 部署后端..."
if [ -f "backend/target/$JAR_FILE" ]; then
    # 停止可能存在的旧进程
    if [ -f "$APP_DIR/app.pid" ]; then
        OLD_PID=$(cat "$APP_DIR/app.pid")
        if kill -0 $OLD_PID 2>/dev/null; then
            echo "停止旧的后端进程 (PID: $OLD_PID)"
            kill $OLD_PID
            sleep 3
        fi
        rm -f "$APP_DIR/app.pid"
    fi
    
    cp "backend/target/$JAR_FILE" "$APP_DIR/"
    echo "后端JAR文件已复制到 $APP_DIR"
else
    echo "错误: 找不到后端JAR文件 backend/target/$JAR_FILE"
    echo "请先运行 'cd backend && mvn clean package -DskipTests' 进行打包"
    exit 1
fi

# 5. 创建后端启动脚本
echo "5. 创建后端启动脚本..."
cat > "$APP_DIR/start.sh" << EOF
#!/bin/bash
cd $APP_DIR
if [ -f app.pid ]; then
    PID=\$(cat app.pid)
    if kill -0 \$PID 2>/dev/null; then
        echo "应用已在运行 (PID: \$PID)"
        exit 1
    fi
fi
nohup java -jar $JAR_FILE > app.log 2>&1 &
echo \$! > app.pid
echo "应用已启动，PID: \$(cat app.pid)"
EOF

cat > "$APP_DIR/stop.sh" << EOF
#!/bin/bash
cd $APP_DIR
if [ -f app.pid ]; then
    PID=\$(cat app.pid)
    if kill -0 \$PID 2>/dev/null; then
        kill \$PID
        echo "正在停止应用 (PID: \$PID)..."
        sleep 3
        if kill -0 \$PID 2>/dev/null; then
            kill -9 \$PID
            echo "强制停止应用"
        fi
    fi
    rm -f app.pid
    echo "应用已停止"
else
    echo "应用未运行"
fi
EOF

cat > "$APP_DIR/restart.sh" << EOF
#!/bin/bash
cd $APP_DIR
./stop.sh
sleep 2
./start.sh
EOF

chmod +x "$APP_DIR/start.sh" "$APP_DIR/stop.sh" "$APP_DIR/restart.sh"

# 6. 部署前端
echo "6. 部署前端..."
if [ -d "frontend/dist" ]; then
    cp -r frontend/dist/* "$WEB_DIR/"
    chown -R www-data:www-data "$WEB_DIR" 2>/dev/null || chown -R nginx:nginx "$WEB_DIR" 2>/dev/null || true
    chmod -R 755 "$WEB_DIR"
    echo "前端文件已部署到 $WEB_DIR"
else
    echo "错误: 找不到前端构建文件 frontend/dist"
    echo "请先运行 'cd frontend && npm run build' 进行构建"
    exit 1
fi

# 7. 创建独立的Nginx配置文件
echo "7. 创建Nginx配置..."
NGINX_CONF_DIR="/usr/local/nginx/conf"
cat > "$NGINX_CONF_DIR/research-management.conf" << EOF
# 科研成果多维敏捷管控中心配置
server {
    listen $FRONTEND_PORT;
    server_name _;
    root $WEB_DIR;
    index index.html;

    # 处理前端路由
    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
}
EOF

echo "Nginx配置文件已创建: $NGINX_CONF_DIR/research-management.conf"

# 8. 提示用户手动添加配置
echo "8. Nginx配置说明..."
echo "=========================================="
echo "请手动将以下行添加到 $NGINX_CONF 的 http 块中："
echo ""
echo "    include research-management.conf;"
echo ""
echo "添加位置示例："
echo "http {"
echo "    # 现有配置..."
echo "    include research-management.conf;  # 添加这一行"
echo "    # 其他配置..."
echo "}"
echo "=========================================="
read -p "配置添加完成后，按回车键继续..." -r

# 9. 测试Nginx配置
echo "9. 测试Nginx配置..."
nginx -t
if [ $? -eq 0 ]; then
    echo "Nginx配置测试通过"
    read -p "是否重新加载Nginx配置? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        nginx -s reload
        echo "Nginx配置已重新加载"
    fi
else
    echo "Nginx配置测试失败，请检查配置文件"
    echo "配置文件位置: $NGINX_CONF_DIR/research-management.conf"
    exit 1
fi

# 10. 配置防火墙
echo "10. 配置防火墙..."
if command -v ufw &> /dev/null; then
    ufw allow $FRONTEND_PORT
    ufw allow $BACKEND_PORT
    echo "防火墙规则已添加"
elif command -v firewall-cmd &> /dev/null; then
    firewall-cmd --permanent --add-port=$FRONTEND_PORT/tcp
    firewall-cmd --permanent --add-port=$BACKEND_PORT/tcp
    firewall-cmd --reload
    echo "防火墙规则已添加"
fi

# 11. 启动后端服务
echo "11. 启动后端服务..."
cd "$APP_DIR"
./start.sh

# 12. 验证部署
echo "12. 验证部署..."
sleep 5

# 检查后端
if netstat -tlnp | grep ":$BACKEND_PORT " > /dev/null; then
    echo "✓ 后端服务已启动 (端口 $BACKEND_PORT)"
else
    echo "✗ 后端服务启动失败"
    echo "请检查日志: tail -f $APP_DIR/app.log"
fi

# 检查前端
if netstat -tlnp | grep ":$FRONTEND_PORT " > /dev/null; then
    echo "✓ 前端服务已启动 (端口 $FRONTEND_PORT)"
else
    echo "✗ 前端服务未启动，请检查Nginx配置是否正确添加"
fi

echo "=========================================="
echo "部署完成！"
echo "=========================================="
echo "前端访问地址: http://$(hostname -I | awk '{print $1}'):$FRONTEND_PORT"
echo "后端API地址: http://$(hostname -I | awk '{print $1}'):$BACKEND_PORT"
echo ""
echo "默认登录账号:"
echo "用户名: admin"
echo "密码: admin123"
echo ""
echo "管理命令:"
echo "启动后端: $APP_DIR/start.sh"
echo "停止后端: $APP_DIR/stop.sh"
echo "重启后端: $APP_DIR/restart.sh"
echo "查看日志: tail -f $APP_DIR/app.log"
echo ""
echo "配置文件位置:"
echo "Nginx配置: $NGINX_CONF_DIR/research-management.conf"
echo "应用目录: $APP_DIR"
echo "网站目录: $WEB_DIR"
echo "=========================================="
