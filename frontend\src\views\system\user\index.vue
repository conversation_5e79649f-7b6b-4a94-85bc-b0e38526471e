<template>
  <div class="app-container">
    <div class="page-content">
      <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户名称" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="phonenumber">
        <el-input
          v-model="queryParams.phonenumber"
          placeholder="请输入手机号码"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="用户状态" clearable style="width: 240px">
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          class="top-action-btn"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          class="top-action-btn"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          class="top-action-btn"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          icon="Upload"
          @click="handleImport"
          class="top-action-btn"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="Download"
          @click="handleExport"
          class="top-action-btn"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 用户表格 -->
    <div class="table-container">
      <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="用户编号" align="center" key="userId" prop="userId" v-if="columns[0].visible" />
      <el-table-column label="用户名称" align="center" key="userName" prop="userName" v-if="columns[1].visible" :show-overflow-tooltip="true" />
      <el-table-column label="用户昵称" align="center" key="nickName" prop="nickName" v-if="columns[2].visible" :show-overflow-tooltip="true" />
      <el-table-column label="部门" align="center" key="deptName" prop="dept.deptName" v-if="columns[3].visible" :show-overflow-tooltip="true" />
      <el-table-column label="手机号码" align="center" key="phonenumber" prop="phonenumber" v-if="columns[4].visible" width="120" />
      <el-table-column label="状态" align="center" key="status" v-if="columns[5].visible">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" v-if="columns[6].visible" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width">
        <template #default="scope">
          <div class="action-buttons" v-if="scope.row.userId !== 1">
            <el-button type="primary" size="small" class="table-action-btn" @click="handleUpdate(scope.row)">
              修改
            </el-button>
            <el-button type="danger" size="small" class="table-action-btn" @click="handleDelete(scope.row)">
              删除
            </el-button>
            <el-button type="warning" size="small" class="table-action-btn" @click="handleResetPwd(scope.row)">
              重置密码
            </el-button>
            <el-button type="success" size="small" class="table-action-btn" @click="handleAuthRole(scope.row)">
              分配角色
            </el-button>
          </div>
        </template>
      </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
      </div>
    </div>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form :model="form" :rules="rules" ref="userRef" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户昵称" prop="nickName">
              <el-input v-model="form.nickName" placeholder="请输入用户昵称" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属部门" prop="deptId">
              <el-tree-select
                v-model="form.deptId"
                :data="deptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择归属部门"
                check-strictly
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phonenumber">
              <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户名称" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入用户名称" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户密码" prop="password">
              <el-input v-model="form.password" placeholder="请输入用户密码" type="password" maxlength="20" show-password />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户性别">
              <el-select v-model="form.sex" placeholder="请选择性别">
                <el-option label="男" value="0" />
                <el-option label="女" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio label="0">正常</el-radio>
                <el-radio label="1">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="角色">
              <el-select v-model="form.roleIds" multiple placeholder="请选择角色">
                <el-option
                  v-for="item in roleOptions"
                  :key="item.roleId"
                  :label="item.roleName"
                  :value="item.roleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { listUser, getUser, delUser, addUser, updateUser, resetUserPwd, changeUserStatus, deptTreeSelect } from '@/api/system/user'
import { treeselect } from '@/api/system/dept'
import { listRole } from '@/api/system/role'
import Pagination from '@/components/Pagination/index.vue'
import RightToolbar from '@/components/RightToolbar/index.vue'

const router = useRouter()

// 遮罩层
const loading = ref(true)
// 选中数组
const ids = ref<Array<string | number>>([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 总条数
const total = ref(0)
// 用户表格数据
const userList = ref([])
// 弹出层标题
const title = ref('')
// 部门树选项
const deptOptions = ref([])
// 角色选项
const roleOptions = ref([])
// 是否显示弹出层
const open = ref(false)
// 日期范围
const dateRange = ref([])
// 表单参数
const form = ref({})
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  userName: '',
  phonenumber: '',
  status: '',
  deptId: ''
})

// 列显隐信息
const columns = ref([
  { key: 0, label: `用户编号`, visible: true },
  { key: 1, label: `用户名称`, visible: true },
  { key: 2, label: `用户昵称`, visible: true },
  { key: 3, label: `部门`, visible: true },
  { key: 4, label: `手机号码`, visible: true },
  { key: 5, label: `状态`, visible: true },
  { key: 6, label: `创建时间`, visible: true }
])

// 表单校验
const rules = reactive({
  userName: [
    { required: true, message: '用户名称不能为空', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' }
  ],
  nickName: [
    { required: true, message: '用户昵称不能为空', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '用户密码不能为空', trigger: 'blur' },
    { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' }
  ],
  email: [
    {
      type: 'email',
      message: '请输入正确的邮箱地址',
      trigger: ['blur', 'change']
    }
  ],
  phonenumber: [
    {
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur'
    }
  ]
})

/** 查询用户列表 */
function getList() {
  loading.value = true
  listUser(addDateRange(queryParams.value, dateRange.value)).then((response: any) => {
    console.log('用户列表响应:', response)
    // 响应拦截器已经返回了data部分，直接使用
    userList.value = response?.rows || []
    total.value = response?.total || 0
    loading.value = false
  }).catch((error: any) => {
    console.error('获取用户列表失败:', error)
    userList.value = []
    total.value = 0
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    userName: '',
    phonenumber: '',
    status: '',
    deptId: ''
  }
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.userId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  getTreeselect()
  getRoleList()
  open.value = true
  title.value = '添加用户'
  form.value.password = ''
}

/** 修改按钮操作 */
function handleUpdate(row?: any) {
  reset()
  getTreeselect()
  getRoleList()
  const userId = row?.userId || ids.value[0]
  console.log('编辑用户ID:', userId)
  getUser(userId).then((response: any) => {
    console.log('用户详情响应:', response)
    form.value = response?.user || response
    // 设置用户角色
    if (response?.roles) {
      form.value.roleIds = response.roles.map((role: any) => role.roleId)
    }
    open.value = true
    title.value = '修改用户'
    form.value.password = ''
  }).catch((error: any) => {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户信息失败')
  })
}

/** 重置密码按钮操作 */
function handleResetPwd(row: any) {
  ElMessageBox.prompt('请输入"' + row.userName + '"的新密码', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    closeOnClickModal: false,
    inputPattern: /^.{5,20}$/,
    inputErrorMessage: '用户密码长度必须介于 5 和 20 之间'
  }).then(({ value }) => {
    resetUserPwd(row.userId, value).then(() => {
      ElMessage.success('修改成功，新密码是：' + value)
    })
  }).catch(() => {})
}

/** 分配角色操作 */
function handleAuthRole(row: any) {
  const userId = row.userId
  router.push(`/system/user-auth-role/${userId}`)
}

/** 提交按钮 */
function submitForm() {
  // 表单验证
  const userRef = document.querySelector('form') as any
  if (!userRef) {
    ElMessage.error('表单验证失败')
    return
  }

  // 验证必填字段
  if (!form.value.nickName) {
    ElMessage.error('用户昵称不能为空')
    return
  }
  if (!form.value.userName && form.value.userId == undefined) {
    ElMessage.error('用户名称不能为空')
    return
  }
  if (!form.value.password && form.value.userId == undefined) {
    ElMessage.error('用户密码不能为空')
    return
  }

  console.log('提交表单数据:', form.value)

  if (form.value.userId != undefined) {
    updateUser(form.value).then(() => {
      ElMessage.success('修改成功')
      open.value = false
      getList()
    }).catch((error: any) => {
      console.error('修改用户失败:', error)
      ElMessage.error('修改失败')
    })
  } else {
    addUser(form.value).then(() => {
      ElMessage.success('新增成功')
      open.value = false
      getList()
    }).catch((error: any) => {
      console.error('新增用户失败:', error)
      ElMessage.error('新增失败')
    })
  }
}

/** 删除按钮操作 */
function handleDelete(row?: any) {
  const userIds = row?.userId || ids.value
  const userName = row?.userName || '选中的用户'

  ElMessageBox.confirm(`是否确认删除用户"${userName}"？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    console.log('删除用户ID:', userIds)
    return delUser(userIds)
  }).then(() => {
    getList()
    ElMessage.success('删除成功')
  }).catch((error: any) => {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除失败')
    }
  })
}

/** 用户状态修改 */
function handleStatusChange(row: any) {
  let text = row.status === '0' ? '启用' : '停用'
  ElMessageBox.confirm(`确认要"${text}"用户"${row.userName}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    console.log('修改用户状态:', row.userId, row.status)
    return changeUserStatus(row.userId, row.status)
  }).then(() => {
    ElMessage.success(text + '成功')
  }).catch((error: any) => {
    if (error !== 'cancel') {
      console.error('修改用户状态失败:', error)
      ElMessage.error(text + '失败')
    }
    // 恢复原状态
    row.status = row.status === '0' ? '1' : '0'
  })
}

/** 导入按钮操作 */
function handleImport() {
  ElMessage.info('导入功能待开发')
}

/** 导出按钮操作 */
function handleExport() {
  ElMessage.info('导出功能待开发')
}

/** 查询部门下拉树结构 */
function getTreeselect() {
  treeselect().then((response: any) => {
    console.log('部门树响应:', response)
    // 转换数据格式以适配el-tree-select
    const deptData = response?.map((item: any) => ({
      id: item.deptId,
      label: item.deptName,
      children: item.children ? item.children.map((child: any) => ({
        id: child.deptId,
        label: child.deptName,
        children: child.children || []
      })) : []
    })) || []
    deptOptions.value = deptData
    console.log('转换后的部门数据:', deptData)
  }).catch((error: any) => {
    console.error('获取部门树失败:', error)
    deptOptions.value = []
  })
}

/** 查询角色列表 */
function getRoleList() {
  listRole({}).then((response: any) => {
    console.log('角色列表响应:', response)
    roleOptions.value = response?.rows || []
  }).catch((error: any) => {
    console.error('获取角色列表失败:', error)
    roleOptions.value = []
  })
}

// 表单重置
function reset() {
  form.value = {
    userId: undefined,
    deptId: undefined,
    userName: '',
    nickName: '',
    password: '',
    phonenumber: '',
    email: '',
    sex: '0',
    status: '0',
    remark: '',
    postIds: [],
    roleIds: []
  }
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 时间格式化
function parseTime(time: any, pattern?: string) {
  if (!time) return ''
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  const date = new Date(time)
  const formatObj: any = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

// 添加日期范围
function addDateRange(params: any, dateRange: any, propName?: string) {
  const search = { ...params }
  const dateRangeName = propName || 'params'
  if (dateRange != null && dateRange !== '') {
    if (typeof dateRangeName === 'string') {
      search['beginTime'] = dateRange[0]
      search['endTime'] = dateRange[1]
    } else {
      search[dateRangeName[0]] = dateRange[0]
      search[dateRangeName[1]] = dateRange[1]
    }
  }
  return search
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
